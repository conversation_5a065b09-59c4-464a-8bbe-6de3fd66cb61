/*
 * @Author: “liuxiamei” “<EMAIL>”
 * @Date: 2025-09-03 17:48:13
 * @LastEditors: “liuxiamei” “<EMAIL>”
 * @LastEditTime: 2025-09-05 18:06:45
 * @FilePath: \rhro_web2\src\pages\empwelfare\Ebmtransact\BusinessProcessingProject\components\AddCodal.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
/*
 * @Author: “liuxiamei” “<EMAIL>”
 * @Date: 2025-08-26 16:30:06
 * @LastEditors: “liuxiamei” “<EMAIL>”
 * @LastEditTime: 2025-09-04 09:52:17
 * @FilePath: \rhro_web2\src\pages\empwelfare\Ebmtransact\BusinessProcessingProject\components\AddCodal.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

import React, { useEffect, useState } from 'react';
import { Button, Form, Input, message, Typography } from 'antd';
import Codal from '@/components/Codal';
import { EditeFormProps, EnumerateFields } from '@/components/CachedPage/EnumerateFields';
import { FormElement3 } from '@/components/Forms/FormLayouts';
import { AsyncButton } from '@/components/Forms/Confirm';
import { CachedPage } from '@/components/CachedPage';
import { useWritable, WritableInstance } from '@/components/Writable';
import { WritableColumnProps } from '@/utils/writable/types';
import {
  CitySelector,
  GetBaseBusnameClassDropdownList,
  mapToSelectors,
  renderListToSelectors,
} from '@/components/Selectors';
import {
  busSourceMap,
  busTypeMap,
  customSizeMap,
  materialConfirmStatusMap,
  modeOfOperationList,
  processObjectMap,
  signatoryPartyMap,
  statusMap,
  transactPropertyMap,
  wechatProgressQueryMap,
} from '@/utils/settings/empwelfare/businessProcessing';
import { yesNoDataMap, yesNoNumberDataMap } from '@/utils/settings/basedata/comboManage';
import { getCurrentUser, getCurrentUserCityId } from '@/utils/model';
import {
  BaseDataAreaSelector,
  GetBusContentSelector,
  GetEmpInfoSelector,
  GetNodeListByIdSelector,
} from '@/components/Selectors/BaseDataSelectors';
import { customerSize } from '@/utils/settings/sysmanage/allocateService';
import { CustomerPop } from '@/components/StandardPop/CustomerPop';
import { EmployeePop } from '@/components/StandardPop/EmployeePop';
import { InnerUserPop } from '@/components/StandardPop/InnerUserPop';
import { msgErr, msgOk } from '@/utils/methods/message';
import EbmtransactGetEmpInfoPop from '@/components/StandardPop/EbmtransactGetEmpInfoPop';
import { downloadFileWithAlert, downloadFileWithId } from '@/utils/methods/file';
import { statusList } from '@/utils/settings/emphiresep/emporder/queryEmpOrderListForEdit';

interface Props {
  [props: string]: any;
  //锁定类型
  visible: string | undefined;
  hideHandle: CallableFunction;
  title: string;
  initValues?: any;
}

const initRules = {};

const initDisabledFields = {};

const servicePackage = API.welfaremanage.ebmBusiness.getMaterialsByNodeId;
const getSsProcessInfoService = API.welfaremanage.ebmtransact.getSsProcessInfo; // 获取员工业务信息

const AddCodal: React.FC<Props> = (props) => {
  let options: WritableInstance;
  const { Paragraph } = Typography;
  const currentUser = getCurrentUser();
  const { userId, userName, governingArea, governingBranch } = currentUser.profile;
  const currentUserCityId = getCurrentUserCityId() || '';
  const writable = useWritable({ service: servicePackage });

  const { visible, hideHandle, title } = props;
  const [disabledFields1, setDisabledFields1] = useState({ ...initDisabledFields });
  const [rules1, setRules1] = useState({ ...initRules });
  const [form1] = Form.useForm();
  const [form2] = Form.useForm();
  const [form3] = Form.useForm();
  const [form4] = Form.useForm();

  const [transactObject, setTransactObject] = useState<string | undefined>(); // 办理对象
  const [transactProperty, setTransactProperty] = useState<string | undefined>(); // 办理属性
  const [busCityNodeConfiglist, setBusCityNodeConfigList] = useState<any>([]);

  useEffect(() => {
    if (!visible) {
      form1.resetFields();
      form2.resetFields();
      form3.resetFields();
    } else {
      form1.setFieldsValue({
        cityId: currentUserCityId,
        isConfirmHandle: '0',
        projectCsId: userId,
        projectCsName: userName,
      });
    }
  }, [visible]);

  const columns: WritableColumnProps<any>[] = [
    { title: '材料编号', dataIndex: 'materialsId' },
    { title: '材料名称', dataIndex: 'materialName' },
    {
      title: '是否原件',
      dataIndex: 'isOriginal',
      render: (value) => yesNoNumberDataMap.get(value),
    },
    { title: '材料数量', dataIndex: 'materialCount' },
    {
      title: '是否返还材料',
      dataIndex: 'isReturnMaterial',
      render: (value) => yesNoDataMap.get(value),
    },
    {
      title: '材料模板',
      dataIndex: 'materialFileId',
      render: (value, record) => (
        <Typography.Link
          disabled={!value}
          onClick={() => {
            // if (!record.materialFileId) return msgErr('暂无文件下载');
            API.minio.minIo.downloadFile
              .request(
                { minioPath: value, fileName: record.templateName },
                { responseType: 'blob' },
              )
              .then((res) => {
                if (res) {
                  downloadFileWithAlert(res, record.templateName);
                }
              });
            // downloadFileWithId(record.materialFileId, value);
          }}
        >
          下载
        </Typography.Link>
      ),
    },
    {
      title: '材料上传情况',
      dataIndex: 'materialUploadStatus',
      render: (value, record) => {
        return record.materialUploadId ? (
          <Typography.Text>已上传</Typography.Text>
        ) : (
          <Typography.Text>未上传</Typography.Text>
        );
      },
    },
    {
      title: '材料确认情况',
      dataIndex: 'materialConfirmStatus',
      render: (value) => materialConfirmStatusMap.get(value),
    },
    {
      title: '用印签字方',
      dataIndex: 'signatoryParty',
      hidden: visible !== '2',
      render: (value) => signatoryPartyMap.get(value),
    },
    { title: '材料收集进度', dataIndex: 'collectionProgress', hidden: visible !== '2' },
    {
      title: '原件是否已寄出',
      dataIndex: 'isOriginalSent',
      render: (value) => yesNoDataMap.get(value),
    },
    {
      title: '操作',
      dataIndex: 'materialUploadId',
      render: (text, record, index) => (
        <Typography.Link
          disabled={!text}
          onClick={() => {
            // if(!text) return msgErr('暂无文件下载');
            API.minio.minIo.downloadFile
              .request({ minioPath: text, fileName: record.materialName }, { responseType: 'blob' })
              .then((res) => {
                if (res) {
                  downloadFileWithAlert(res, record.materialName);
                }
              });
          }}
        >
          下载
        </Typography.Link>
      ),
    },
  ];

  const formColumns1: EditeFormProps[] = [
    {
      label: '城市',
      fieldName: 'cityId',
      inputRender: () => <CitySelector />,
      rules: [{ required: true, message: '请选择' }],
    },
    {
      label: '业务类型',
      fieldName: 'categoryId',
      inputRender: (outerForm) =>
        mapToSelectors(busTypeMap, {
          onChange: (value) => {
            if (value === '1' || value === '2') {
              outerForm.setFieldsValue({
                isChargeBusiness: 0,
                busnameClassId: undefined,
                busCityConfigId: undefined,
              });
              setRules1({
                ...rules1,
                isChargeCust: [{ required: false }],
                chargeAmount: [{ required: false }],
              });
            } else if (value === '3') {
              outerForm.setFieldsValue({
                isChargeBusiness: 1,
                busnameClassId: undefined,
                busCityConfigId: undefined,
              });
              setRules1({
                ...rules1,
                isChargeCust: [{ required: true, message: '请选择' }],
                chargeAmount: [{ required: true, message: '请输入金额' }],
              });
            }
          },
        }),
      rules: [{ required: true, message: '请选择' }],
    },
    {
      label: '业务项目',
      fieldName: 'busnameClassId',
      shouldUpdate: (prevValues, curValues) => {
        return prevValues.categoryId !== curValues.categoryId;
      },
      inputRender: (outerForm) => {
        const { categoryId } = outerForm.getFieldsValue();
        return (
          <GetBaseBusnameClassDropdownList
            allowClear
            params={{
              pageNum: 1,
              pageSize: 2147483647,
              categoryId,
            }}
            onChange={() => {
              outerForm.setFieldsValue({
                busCityConfigId: undefined,
              });
            }}
          />
        );
      },
      rules: [{ required: true, message: '请选择' }],
    },
    {
      label: '业务内容',
      fieldName: 'busCityConfigId',
      shouldUpdate: (prevValues, curValues) => {
        return (
          prevValues.cityId !== curValues.cityId ||
          prevValues.categoryId !== curValues.categoryId ||
          prevValues.busnameClassId !== curValues.busnameClassId
        );
      },
      rules: [{ required: true, message: '请选择' }],
      inputRender: (outerForm) => {
        const { categoryId, busnameClassId, cityId } = outerForm.getFieldsValue();
        return (
          <GetBusContentSelector
            params={{ categoryId, busnameClassId, cityId }}
            onChange={async (value: string, record: POJO) => {
              const transactObject = record?.optiondata?.transactObject;
              const sourceType = record?.optiondata?.sourceType || '1';
              const transactProperty = record?.optiondata?.transactProperty;
              setTransactObject(transactObject);
              setTransactProperty(transactProperty);
              let newRules = {};
              let newFormValues = {};
              if (transactObject == '1') {
                // 办理对象 1：客户，
                newRules = {
                  ...newRules,
                  custId: [{ required: true, message: '请选择' }],
                  empName: [{ required: false }],
                  custPayEntityName: [{ required: false }],
                };
                // newFormValues = { projectCsId: userId, projectCsName: userName }
                form3.setFieldsValue({ projectCsId: userId, projectCsName: userName });
              } else if (transactObject == '2') {
                // 2 员工
                newRules = {
                  ...newRules,
                  custId: [{ required: false }],
                  empName: [{ required: true, message: '请选择员工' }],
                  custPayEntityName: [{ required: false }],
                };
                // setRules1({
                //   ...rules1,
                //   custId: [{ required: false }],
                //   empName: [{ required: true, message: '请选择员工' }],
                //   custPayEntityName: [{ required: false }],
                // });
              }
              if (sourceType === '1' || sourceType === '2') {
                newRules = {
                  ...newRules,
                  needEmpMaterial: [{ required: true, message: '请选择' }],
                };
                newFormValues = { ...newFormValues, needEmpMaterial: undefined };
              } else if (sourceType === '3') {
                newRules = {
                  ...newRules,
                  needEmpMaterial: [{ required: false, message: '请选择' }],
                };
                newFormValues = { ...newFormValues, needEmpMaterial: '1' };
              }
              setRules1({
                ...rules1,
                ...newRules,
              });
              outerForm.setFieldsValue({
                ...newFormValues,
                transactObject,
                empName: undefined,
                idCardNum: undefined,
                empCode: undefined,
                contactTel: undefined,
                sepStatus: undefined,
                custId: undefined,
                isIndependent: undefined,
                custPayEntityName: undefined,
                transactProperty,
                sourceType,
              });
              if (!value) return;
              const res = await API.welfaremanage.ebmBusiness.getNodeListById.requests({
                busCityConfigId: value,
              });
              if (!res.length) {
                form4.resetFields();
                return;
              }

              setBusCityNodeConfigList(res);
              const resPackage = await API.welfaremanage.ebmBusiness.getMaterialsByNodeId.requests(
                {
                  nodeId: 1,
                },
                { params: { nodeId: 1 } },
              );

              writable.setNewData(resPackage);
              form4.setFieldsValue({
                ...res[0],
                busCityNodeConfigId: res[0]['busCityNodeConfigId'].toString(),
              });
            }}
          />
        );
      },
    },
    {
      label: '办理属性',
      fieldName: 'transactProperty',
      inputRender: () => mapToSelectors(transactPropertyMap, { disabled: true }),
    },
    {
      label: '办理对象',
      fieldName: 'transactObject',
      inputRender: (outerForm) => mapToSelectors(processObjectMap, { disabled: true }),
    },
    {
      label: '业务来源',
      fieldName: 'sourceType',
      inputRender: (outerForm) => mapToSelectors(busSourceMap, { disabled: true }),
    },
    {
      label: '客户名称',
      fieldName: 'custId',
      inputRender: () => (
        <CustomerPop
          rowValue="custId-custSize-custName"
          keyMap={{
            custId: 'custId',
            custName: 'custName',
            custSize: 'custSize',
          }}
          // 当“办理对象”为 员工 时，根据所选员工对应的客户默认载入只读。办理对象 1：客户，2员工

          disabled={transactObject !== '1'}
        />
      ),
    },
    {
      label: '客户规模',
      fieldName: 'custSize',
      inputRender: 'string',
      inputProps: {
        disabled: true,
      },
    },
    {
      label: '姓名',
      fieldName: 'empName',
      inputRender: (outerForm) => {
        const { cityId, categoryId, busnameClassId, transactObject } = outerForm.getFieldsValue();
        return (
          <EbmtransactGetEmpInfoPop
            rowValue="empId-custName-custId-empCode-idCardNum--sepStatus-isIndependent-custPayEntityName-contactTel-empName"
            fixedValues={{ cityId, categoryId, busnameClassId, transactObject, userId }}
            disabled={transactObject === '1'}
            handdleConfirm={async (record) => {
              const { projectCsId, projectCsName, assigneeCsId, assigneeCsName, empHireSepId } =
                record;
              form3.setFieldsValue({ projectCsId, projectCsName, assigneeCsId, assigneeCsName });
              // 员工信息
              const res = await getSsProcessInfoService.requests({ empHireSepId });
              if (res?.list?.length) {
                const { list } = res;
                const filterProduct = (id) => {
                  const result = list.filter((item) => item.productId === id);
                  return {
                    statusName: result[0]?.ssStatusName,
                    employerName: result[0]?.employerName,
                    welfareProcessMon: result[0]?.welfareProcessMon,
                    welfareStartMon: result[0]?.welfareStartMon,
                  };
                };
                let getInfo = {};
                // infoFormInfo[1](res);
                const retirementInfo = filterProduct('200');
                const medicalInfo = filterProduct('201');
                const unemploymentInfo = filterProduct('202');
                const injuryInfo = filterProduct('203');
                const birthInfo = filterProduct('204');
                const housingInfo = filterProduct('240');

                getInfo = {
                  retirementStatusName: retirementInfo.statusName,
                  retirementEmployerName: retirementInfo.employerName,
                  retirementWelfareProcessMon: retirementInfo.welfareProcessMon,
                  retirementWelfareStartMon: retirementInfo.welfareStartMon,
                  medicalStatusName: medicalInfo.statusName,
                  medicalEmployerName: medicalInfo.employerName,
                  medicalWelfareProcessMon: medicalInfo.welfareProcessMon,
                  medicalWelfareStartMon: medicalInfo.welfareStartMon,
                  unemploymentStatusName: unemploymentInfo.statusName,
                  unemploymentEmployerName: unemploymentInfo.employerName,
                  unemploymentWelfareProcessMon: unemploymentInfo.welfareProcessMon,
                  unemploymentWelfareStartMon: unemploymentInfo.welfareStartMon,
                  injuryStatusName: injuryInfo.statusName,
                  injuryEmployerName: injuryInfo.employerName,
                  injuryWelfareProcessMon: injuryInfo.welfareProcessMon,
                  injuryWelfareStartMon: injuryInfo.welfareStartMon,
                  birthStatusName: birthInfo.statusName,
                  birthEmployerName: birthInfo.employerName,
                  birthWelfareProcessMon: birthInfo.welfareProcessMon,
                  birthWelfareStartMon: birthInfo.welfareStartMon,
                  housingStatusName: housingInfo.statusName,
                  housingEmployerName: housingInfo.employerName,
                  housingWelfareProcessMon: housingInfo.welfareProcessMon,
                  housingWelfareStartMon: housingInfo.welfareStartMon,
                };
                form2.setFieldsValue(getInfo);
              }
            }}
          />
        );
      },
    },
    {
      label: '证件号码',
      fieldName: 'idCardNum',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    {
      label: '唯一号',
      fieldName: 'empCode',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    {
      label: '手机号码',
      fieldName: 'contactTel',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    {
      label: '入离职状态',
      fieldName: 'sepStatus',
      inputRender: () => {
        return mapToSelectors(statusList, { disabled: true });
      },
    },
    {
      label: '是否单立户',
      fieldName: 'isIndependent',
      inputRender: (outerForm) =>
        mapToSelectors(modeOfOperationList, {
          disabled: transactObject === '2',
          onChange: (value) => {
            outerForm.setFieldsValue({
              custPayEntityName: undefined,
            });
            if (transactObject === '1' && value === 1) {
              setRules1({
                ...rules1,
                custPayEntityName: [{ required: true, message: '请输入缴费实体名称' }],
              });
            } else {
              setRules1({
                ...rules1,
                custPayEntityName: [{ required: false }],
              });
            }
          },
        }),
      rules: [{ required: true, message: '请选择' }],
    },
    {
      label: '缴费实体名称',
      fieldName: 'custPayEntityName',
      shouldUpdate: (prevValues, curValues) => {
        return (
          prevValues.isIndependent !== curValues.isIndependent ||
          prevValues.transactObject !== curValues.transactObject
        );
      },
      inputRender: (outerForm) => {
        const { isIndependent } = outerForm.getFieldsValue();
        return (
          <Input
            maxLength={200}
            disabled={(isIndependent === '0' && transactObject === '1') || transactObject === '2'}
          />
        );
      },
      //  inputProps: { maxLength: 200,disabled:transactObject==='2'} , // 当“办理对象”为 员工 时，默认值只读
    },
    {
      label: '是否收费业务',
      fieldName: 'isChargeBusiness',
      inputRender: () => mapToSelectors(yesNoNumberDataMap, { disabled: true }),
    },
    {
      label: '是否收取客户费用',
      fieldName: 'isChargeCust',
      inputRender: () => mapToSelectors(yesNoDataMap),
    },
    {
      label: '收取金额',
      fieldName: 'chargeAmount',
      inputRender: 'string',
      inputProps: { maxLength: 200 },
    },
    {
      label: '是否需要联系员工提交材料',
      fieldName: 'needEmpMaterial',
      inputRender: () => mapToSelectors(yesNoDataMap),
    },
    {
      label: '微信端业务进度查询',
      fieldName: 'wechatProgressQuery',
      inputRender: () => mapToSelectors(wechatProgressQueryMap),
      rules: [{ required: true, message: '请选择' }],
    },
    {
      label: '是否确认办理',
      fieldName: 'isConfirmHandle',
      inputRender: () => mapToSelectors(yesNoDataMap),
    },
    {
      label: '业务状态',
      fieldName: 'status',
      inputRender: () => mapToSelectors(statusMap, { disabled: true }),
    },
    {
      label: '业务进度',
      fieldName: 'progress',
      inputRender: () => <BaseDataAreaSelector params={{ type: 12336 }} disabled />,
    },
  ];

  const formColumns2: EditeFormProps[] = [
    { label: '养老状态', fieldName: 'retirementStatusName', inputRender: 'string' },
    { label: '养老福利办理方', fieldName: 'retirementEmployerName', inputRender: 'string' },
    { label: '养老福利办理月', fieldName: 'retirementWelfareProcessMon', inputRender: 'string' },
    { label: '养老福利起始月', fieldName: 'retirementWelfareStartMon', inputRender: 'string' },
    { label: '失业状态', fieldName: 'unemploymentStatusName', inputRender: 'string' },
    { label: '失业福利办理方', fieldName: 'unemploymentEmployerName', inputRender: 'string' },
    { label: '失业福利办理月', fieldName: 'unemploymentWelfareProcessMon', inputRender: 'string' },
    { label: '失业福利起始月', fieldName: 'unemploymentWelfareStartMon', inputRender: 'string' },
    { label: '工伤状态', fieldName: 'injuryStatusName', inputRender: 'string' },
    { label: '工伤福利办理方', fieldName: 'injuryEmployerName', inputRender: 'string' },
    { label: '工伤福利办理月', fieldName: 'injuryWelfareProcessMon', inputRender: 'string' },
    { label: '工伤福利起始月', fieldName: 'injuryWelfareStartMon', inputRender: 'string' },
    { label: '医疗状态', fieldName: 'medicalStatusName', inputRender: 'string' },
    { label: '医疗福利办理方', fieldName: 'medicalEmployerName', inputRender: 'string' },
    { label: '医疗福利办理月', fieldName: 'medicalWelfareProcessMon', inputRender: 'string' },
    { label: '医疗福利起始月', fieldName: 'medicalWelfareStartMon', inputRender: 'string' },
    { label: '生育状态', fieldName: 'birthStatusName', inputRender: 'string' },
    { label: '生育福利办理方', fieldName: 'birthEmployerName', inputRender: 'string' },
    { label: '生育福利办理月', fieldName: 'birthWelfareProcessMon', inputRender: 'string' },
    { label: '生育福利起始月', fieldName: 'birthWelfareStartMon', inputRender: 'string' },
    { label: '公积金状态', fieldName: 'housingStatusName', inputRender: 'string' },
    { label: '公积金福利办理方', fieldName: 'housingEmployerName', inputRender: 'string' },
    { label: '公积金福利办理月', fieldName: 'housingWelfareProcessMon', inputRender: 'string' },
    { label: '公积金福利起始月', fieldName: 'housingWelfareStartMon', inputRender: 'string' },
  ];

  const formColumns3: EditeFormProps[] = [
    {
      label: '项目客服',
      fieldName: 'projectCsId',
      inputRender: () => (
        <EmployeePop
          rowValue="projectCsId-projectCsName"
          keyMap={{
            projectCsId: 'EMPID',
            projectCsName: 'REALNAME',
          }}
        />
      ),
    },
    {
      label: '接单客服',
      fieldName: 'assigneeCsId',
      inputRender: () => (
        <EmployeePop
          rowValue="assigneeCsId-assigneeCsName"
          keyMap={{
            assigneeCsId: 'EMPID',
            assigneeCsName: 'REALNAME',
          }}
          fixedValues={{
            branchId: governingBranch,
            areaId: governingArea,
          }}
        />
      ),
    },
    {
      label: '后道客服',
      fieldName: 'bankendCsId',
      inputRender: () => (
        <EmployeePop
          rowValue="bankendCsId-bankendCsName"
          keyMap={{
            bankendCsId: 'EMPID',
            bankendCsName: 'REALNAME',
          }}
          disabled
        />
      ),
    },
    { label: '备注', fieldName: 'remark', inputRender: 'text', colNumber: 1 },
  ];

  const formColumns5: EditeFormProps[] = [
    {
      label: '当前业务节点',
      fieldName: 'busCityNodeConfigId',
      inputRender: () => {
        return renderListToSelectors(busCityNodeConfiglist, [
          'busCityNodeConfigId',
          'busCityNodeConfigName',
        ]);
      },
    },
    { label: '是否收取材料', fieldName: 'isCollectMaterialName', inputRender: 'string' },
    { label: '是否支持津贴待遇', fieldName: 'isAllowanceName', inputRender: 'string' },
    { label: '办理周期', fieldName: 'transactPeriod', inputRender: 'string' },
    { label: '是否有政府性收费', fieldName: 'isGovernFeeName', inputRender: 'string' },
    { label: '政府性收费金额', fieldName: 'governFee', inputRender: 'string' },
  ];

  const renderButtons = (_options: WritableInstance) => {
    options = _options;
    return (
      <>
        <Button disabled> 材料原件寄件维护</Button>
      </>
    );
  };
  // 保存、提交
  const save_clickHandler = async (type: string) => {
    const values1 = await form1.validateFields();
    const values3 = await form3.validateFields();
    const values4 = await form4.validateFields();

    await API.welfaremanage.ebmBusiness.saveApplication.requests({
      ...values1,
      ...values3,
      ...values4,
    });
    msgOk('创建成功');
    hideHandle('refresh');
  };

  const onSubmit = async (type: string) => {
    const values1 = await form1.validateFields();
    const values3 = await form3.validateFields();
    const values4 = await form4.validateFields();

    if (type === 'assignee') {
      await API.welfaremanage.ebmBusiness.submitToJdCs.requests({
        ...values1,
        ...values3,
        ...values4,
      });
    } else if (type === 'project') {
      await API.welfaremanage.ebmBusiness.submitToProjectCs.requests({
        ...values1,
        ...values3,
        ...values4,
      });
    }
    hideHandle('refresh');
  };
  // 操作按钮
  const CodalButtons = () => {
    return (
      <>
        <AsyncButton type="primary" onClick={() => save_clickHandler('add')}>
          保存
        </AsyncButton>
        {visible === '1' && (
          <AsyncButton onClick={() => onSubmit('assignee')}>提交接单确认</AsyncButton>
        )}
        {visible === '2' && (
          <>
            <AsyncButton onClick={() => onSubmit('project')}>提交项目确认</AsyncButton>
            <AsyncButton onClick={() => save_clickHandler('submit')}>确认办理</AsyncButton>
          </>
        )}

        <Button onClick={() => hideHandle()}>取消</Button>
      </>
    );
  };
  return (
    <Codal
      title={title}
      width="90vw"
      visible={!!visible}
      onCancel={() => hideHandle()}
      footer={CodalButtons()}
    >
      <Paragraph>业务基本信息</Paragraph>
      <FormElement3 form={form1}>
        <EnumerateFields outerForm={form1} formColumns={formColumns1} rules={rules1} />
      </FormElement3>
      {transactObject === '2' && (
        <>
          <Paragraph style={{ marginTop: '50px' }}>员工基本信息</Paragraph>
          <FormElement3 form={form2}>
            <EnumerateFields outerForm={form2} formColumns={formColumns2} disabled={true} />
          </FormElement3>
        </>
      )}
      {transactProperty == '2' && (
        <>
          <FormElement3 form={form4} style={{ marginTop: '30px' }}>
            <EnumerateFields outerForm={form4} formColumns={formColumns5} disabled={false} />
          </FormElement3>
        </>
      )}
      {transactObject === '2' && (
        <>
          <Paragraph>材料清单</Paragraph>
          <CachedPage
            service={servicePackage}
            columns={columns}
            formColumns={[]}
            renderButtons={renderButtons}
            notShowPagination
            wriTable={writable}
          />
        </>
      )}

      <FormElement3 form={form3}>
        <EnumerateFields outerForm={form3} formColumns={formColumns3} />
      </FormElement3>
    </Codal>
  );
};

export default AddCodal;
