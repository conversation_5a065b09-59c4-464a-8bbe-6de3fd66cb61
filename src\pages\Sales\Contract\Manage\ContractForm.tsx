/*
 * @Author: 侯成
 * @Email: <EMAIL>
 * @Date: 2020-09-10 18:37:47
 * @LastAuthor: 侯成
 * @LastTime: 2021-04-19 14:38:53
 * @message: message
 * tagId: "165618921", attName: "需求-开发 标準流程.doc", fileId: "905750937"
 * {"contractStatus":"2","contractSvcState":"0", approveRelatedAttachment != null}
 * 信美人寿相互保险社---(上海、深圳残保金专项补充协议
 */
import React, { useEffect, useState } from 'react';
import { Button, Tabs, Form, Input, InputNumber, Upload, Image } from 'antd';
import { WritableColumnProps } from '@/utils/writable/types';
import { CachedPage } from '@/components/CachedPage';
import { EditeFormProps, FormInstance } from '@/components/EditeForm';
import Codal from '@/components/Codal';
import { WritableInstance, Writable, useWritable } from '@/components/Writable';
import {
  FormElement1,
  FormElement3,
  RowElementButton,
  RowElement,
  ColElementButton,
} from '@/components/Forms/FormLayouts';
import { EnumerateFields } from '@/components/CachedPage/EnumerateFields';
import { BooleanSelector, CustProductLineSelector } from '@/components/Selectors/BaseDropDown';
import { CustomerPop } from '@/components/StandardPop/CustomerPop';
import {
  ContractTypeSelector,
  mapToSelectors,
  GetSignFlagManualSelect,
} from '@/components/Selectors';
import { useSelector } from 'umi';
import { ConnectState } from '@/models/connect';
import {
  BranchTitleSpeSelector,
  ContractgetSubTypeSelector,
} from '@/components/Selectors/BaseDataSelectors';
import {
  validateCurrency,
  validateEmail,
  validateNaturalDay,
  validateNaturalPositive,
} from '@/utils/forms/validate';
import { column3 } from '@/utils/forms/typography';
import { stdDateFormat, today } from '@/utils/methods/times';
import { DateRange } from '@/components/DateRange4';
import {
  agereedAmtReceiveMonMap,
  contractAreaTypeMap,
  contractEndDateTypeNameMap,
  paymentModeMap,
  payMonthMapMap,
} from '@/utils/settings/sales/contract';
import {
  CurrentSalesSelector,
  CompetitorSelector,
  PayTypeSelector,
  ContractCategorySelector,
} from './Seletors';
import { CODE_SUCCESS, msgErr, msgOk, resError } from '@/utils/methods/message';
import { isEmpty } from 'lodash';
import { Calculator } from '@/utils/methods/calculator';
import { stdBoolTrue, stdBoolTrueSrting } from '@/utils/settings';
import { Switchs } from '@/components/Selectors/Switch';
import { CsPop, InnerUserPop } from '@/components/StandardPop/InnerUserPop';
import { addedInLocal, tableIndexKey } from '@/utils/settings/forms';
import QuerySelectedQutationWin from './QuerySelectedQutationWin';
import { QuotationFullPop } from '@/components/StandardPop/QuotationFullPop';
import QuotationUpdateForm from '@/pages/Sales/Quotation/QuotationManage/Forms/QuotationDetailForm';
import { Typography } from 'antd';
import { NonStaCoctApprPop } from '@/components/StandardPop/NonStaCoctApprPop';
import { UploadChangeParam } from 'antd/lib/upload';
import { UploadFile } from 'antd/lib/upload/interface';
import { BizUtil } from '@/utils/settings/bizUtil';
import { Store } from 'antd/lib/form/interface';
import { CustPayerPop } from '@/components/StandardPop/CustPayerPop';
import { getDepartInfo } from '@/utils/model';
import { shallowEqual } from 'react-redux';
import { PopconfirmButton } from '@/components/Forms/PopconfirmButton';
import { AlertTabPane } from '@/components/AlertTabPane';
import { AlertTab } from '@/components/AlertTabPane/AlertTab';
import { FileSize } from '@/components/UploadForm';
import Group from '@/components/Group';
import AddForm from '@/components/EditeForm/AddForm';
import { UploadOutlined, DeleteOutlined } from '@ant-design/icons';
import { EmployeePop } from '@/components/StandardPop/EmployeePop';
import ImportForm from '@/components/UploadForm/ImportForm';
import { QuostatusMap } from '@/pages/emphiresep/sendorder/CustomerSubcontract/ContractView';
import { downloadFileWithAlert } from '@/utils/methods/file';
import QuotationAddForm from '../../Quotation/QuotationManage/Forms/QuotationAddForm';
import { multipleNum } from '../../Quotation/QuotationManage';
import { AsyncButton } from '@/components/Forms/Confirm';
import { PreviewCodal } from './PreviewFile';
const { Link } = Typography;

const { TabPane } = Tabs;
export const validDateCost = {
  pattern: new RegExp(/^\d+(\.\d{0,2})?$/, 'g'),
  message: '只能输入大于等于0数字，且保留两位小数',
};
const serviceSave = API.sale.contract.save;
const serviceQuotation: IServiceType = {
  ...API.sale.contract.save,
  cacheKey: API.sale.contract.save + '_quotation',
};
const serviceProductLine: IServiceType = {
  ...API.sale.contract.save,
  cacheKey: API.sale.contract.save + '_productLine',
};
const serviceCustPayer: IServiceType = {
  ...API.sale.contract.save,
  cacheKey: API.sale.contract.save + '_custPayer',
};
enum tabScene {
  sales = '1',
  cs = '2',
  quotation = '3',
  legal = '4',
  prepay = '5',
  custPayer = '6',
  reject = '7',
}

interface QueryContractDetailInfoWinProps {
  [props: string]: any;
  modal: [boolean, CallableFunction];
  contract?: Partial<defs.sale.ContractDTO>;
  additional?: Partial<defs.sale.ContractDTO>;
  onConfirm: () => void;
  // 新增 | 修改 | 提交审核 | 续签
  scene: 'add' | 'update' | 'examine' | 'renew' | undefined;
}

const service = API.sale.customer.sel;
const apis = [
  'contractManageService,getContractAttachmentByContractId',
  'contractManageService,getContractApproveRelatedAttachmentByContractId',
  'contractManageService,queryContractById',
  'baseServiceCLS,getDorpDownList',
  'contractManageService,getAreaIdByCurrentSales',
  'contractManageService,getDepartmentIdByCurrentSales',
  'baseServiceCLS,getDorpDownList',
  'contractManageService,getPayerByContractId',
  'contractManageService,getQuotationByContractId',
  'contractManageService,getProductLineByContractId',
  'baseServiceCLS,getDorpDownList',
  'contractManageService,getAreaIdByCurrentSales',
  'contractManageService,getDepartmentIdByCurrentSales',
  'baseServiceCLS,getDorpDownList',
  'contractManageService,initData',
  'baseServiceCLS,getDorpDownList',
  'contractManageService,save',
  'serviceTypeService,getSub',
  'quotationService,getQuotation',
  'contractManageService,getAreaIdByCurrentSales',
  'contractManageService,getDepartmentIdByCurrentSales',
  'baseServiceCLS,getDorpDownList',
  'baseServiceCLS,getDorpDownList',
  'contractManageService,delQuotationInContract',
  'contractManageService,delProductLineInContract',
];

const codalNames = ['QuerySelectedQutationWin'];

type TContractDTO = defs.sale.ContractDTO;
const dafaultContractFormConfig = {
  winIsRenew: false,
  winIsRenewOperate: false,
  isPaymentQAResultUpdate: false,
  isBeijingDepartment: false,
  isCommitApprove: false,
  isSelectFirstLegalId: false,
  isUploadApproveRelatedAttachment: false,
  isSelectCsBySale: false,
  isCalculateGrossProfit: false,
  isPhysicalExamination: false,
  isIssuingSalary: false,
  isEditeAdvancePaymentRatio: false,
  isUpdateContractCategery: true,
  isExecuteStatus: false,
  internalMoneyNeeded: false,
  isTravelServices: false,
  isEditeTravelServicesRatio: false,
  isRiskRatio: false,
  isRetireTransact: false,
  isHealthCheckup: false,
  isWelfareGather: false,
  isHealthOnther: false,
  isHealthPlatform: false,
  isRetQuotaGrantedRatio: false,
  isRetirementBusiness: false,
  isRetirementBusinessList: false,
};
const contractFormConfig = { ...dafaultContractFormConfig };
export const getFileExtension = (url) => {
  return url.split('.').pop().toLowerCase();
};
const ContractForm: React.FC<QueryContractDetailInfoWinProps> = (props) => {
  const { scene, modal, contract: _contract, additional, onConfirm } = props;
  // console.log(_contract)
  const [visible, setVisible] = modal;
  if (!visible) {
    Object.keys(dafaultContractFormConfig).forEach((key) => {
      contractFormConfig[key] = dafaultContractFormConfig[key];
    });
    return null;
  }

  if (scene !== 'add' && !_contract) return null;

  const contract: POJO =
    scene === 'add'
      ? {
          ..._contract,
          contractType: _contract!.contractType,
          contractCategery: _contract!.contractCategery,
          custId: _contract!.custId,
          custCode: _contract!.custCode,
          currentSales: _contract!.currentSales,
          custName: _contract!.custName,
          contractSubType: _contract!.contractSubType,
          departmentId: _contract!.departmentId,
          cityName: _contract!.cityName,
          formerGoverningAreaName: _contract!.formerGoverningAreaName,
        }
      : {
          contractId: _contract!.contractId,
          contractType: _contract!.contractType,
          contractCategery: _contract!.contractCategery,
          custId: _contract!.custId,
          currentSales: _contract!.currentSales,
        };

  const getContractById =
    scene === 'renew'
      ? API.crm.contractManage.getRenewContractById
      : API.crm.contractManage.getContractById;
  // const getContractById = API.crm.contractManage.getContractById
  // console.log('contract in ContractForm:', contract)
  const { contractId } = contract;
  const currentUser = useSelector((state: ConnectState) => state.user.currentUser, shallowEqual);
  const userBranchId = currentUser.profile?.governingBranch;
  const querySelectedQutationWin = useState(false);
  const quotationViewModal = useState(false);
  const quotationDetail = useState({});
  const [contractFormConfigCount, setContractFormConfigCount] = useState(0);
  const setContractFormConfig = (data: POJO<boolean>, noState?: boolean) => {
    Object.keys(data).forEach((key) => {
      contractFormConfig[key] = data[key];
    });
    if (noState) return;
    setContractFormConfigCount(contractFormConfigCount + 1);
  };
  const [contractType, setContractType] = useState(contract.contractType);
  const [contractCategory, setContractCategory] = useState(contract.contractCategery);
  const [currentContract, setCurrentContarct] = useState<TContractDTO>({});
  const [importFile, setimportFile] = useState<Partial<TContractDTO>>({});
  const [isCustCode, setIsCustCode] = useState<boolean>(false);
  const [mainPrepayAmt, setMainPrepayAmt] = useState<boolean>(false);
  const [branchTitleDepartId, setBranchTitleDepartId] = useState<string>();
  const [getSubTypeId, setSubTypeId] = useState<string>();
  const [whInvoiceOrderId, setWhInvoiceOrderId] = useState<string>();
  const [formUpdateVisible, setFormUpdateVisible] = useState(false);
  const infoLoading = useState(false);

  const [currentCustId, setcurrentCustId] = useState<string | undefined>(contract.custId);

  const [salesId, setSalesId] = useState<string | undefined>(contract.currentSales);
  const [onShowImport, setOnShowImport] = useState(false);
  const [isRetireeProduct, setIsRetireeProduct] = useState(false);
  const [importType, setImportType] = useState<string>('');
  const [retireFile, setRetireFileFile] = useState<any>({});
  const [file, setFile] = useState<any>({});
  const [fileData, setFileData] = useState<any>([]);
  const [previewUrl, setPreviewUrl] = useState<string>('');
  const [previewFile, setPreviewFile] = useState<string>('');
  const [previewFileType, setPreviewFileType] = useState<string>('');
  const previewModal = useState(false);

  const [currentSales, setcurrentSales] = useState<string | undefined>(
    contract.currentSales || currentUser.userId,
  );
  // const [contractFormConfig, setContractFormConfig] = contractId ? useState({} as any) : useState(dafaultContractFormConfig);
  const [mainForm] = Form.useForm();
  const [salesForm] = Form.useForm();
  const [csForm] = Form.useForm();
  const [legalForm] = Form.useForm();
  const [prepayForm] = Form.useForm();
  const [approveOpinionForm] = Form.useForm();

  const writableQuotation = useWritable({ service: serviceQuotation });
  const writableProductLine = useWritable({ service: serviceProductLine });
  const writableCustPayer = useWritable({ service: serviceCustPayer });
  const writableUpload = useWritable({ service: serviceCustPayer });
  const writableRetire = useWritable({
    service: { ...serviceCustPayer, cachekey: serviceCustPayer + '1' },
  });

  const [tabErrors, setTabErrors] = useState({
    mainForm: false,
    salesForm: false,
    csForm: false,
    legalForm: false,
    prepayForm: false,
    approveOpinionForm: false,
    writableQuotation: false,
    writableProductLine: false,
    writableCustPayer: false,
    writableUpload: false,
    writableRetire: false,
  });

  useEffect(() => {
    if (scene === 'examine') {
      setContractFormConfig({ isCommitApprove: true });
    } else {
      setContractFormConfig({ isCommitApprove: false });
    }
    if (scene === 'add') {
      //大合同新增从上一步带过来参数
      mainForm.setFieldsValue({
        whInvoiceOrder: '2',
        ...contract,
        whPeRate: '1',
      });
      contract?.contractSubType && setSubTypeId(contract?.contractSubType);
      contract?.departmentId && setBranchTitleDepartId(contract?.departmentId);
      setWhInvoiceOrderId('2');
      setBranchTitleDepartId(contract?.departmentId);
      contract?.contractType && setContractType(contract?.contractType);
      onContractSubTypeChange(contract?.contractSubType);
      if (contract?.contractSubType !== '7' && contract?.contractType === '4') {
        setContractFormConfig({ isUpdateContractCategery: false });
      } else {
        setContractFormConfig({ isUpdateContractCategery: true });
      }
      setCurrentContarct({ ...contract });
    }
  }, [scene]);

  useEffect(() => {
    if (!contractId) return;
    infoLoading[1](true);
    (async () => {
      // *********, 	20110614-G-0731-008, available
      let isPaymentQAResultUpdate = false;
      let isBeijingDepartment = false;
      let isSelectCsBySale = false;
      let isSelectFirstLegalId = false;
      let isCalculateGrossProfit = false;
      let isUpdateContractCategery = false;
      let isPhysicalExamination = false;
      let isEditeAdvancePaymentRatio = false;
      let isEditeTravelServicesRatio = false;
      let isUploadApproveRelatedAttachment = false;
      let isTravelServices = false;
      let isRiskRatio = false;
      let isRetireTransact = false; //退休办理
      let isHealthCheckup = false; //健康体检
      let isWelfareGather = false; //福利采集
      let isHealthOnther = false; //福利其他三项
      let isHealthPlatform = false; //福利平台业务
      let isRetirementBusiness = false; //是否有退休业务
      let isRetirementBusinessList = false; //是否有退休业务
      let isRetQuotaGrantedRatio = false;

      let isProduct = false;
      const contr: TContractDTO = await getContractById.requests({ contractId });
      const contrAddition = { ...additional } as Partial<TContractDTO>;
      contr?.contractSubType && setSubTypeId(contr?.contractSubType);
      const {
        areaType,
        contractHeadcount,
        contractAvgAmt,
        agreedWageArriveDay: _agreedWageArriveDay,
        currentSales,
        paymentMode,
        isAddedAttachment,
        liabilityCs,
        contractType,
        contractSubType,
        contractVersion,
        isTravelServices: _isTravelServices,
        departmentId,
        governingArea,
        governingBranchName,
      } = contr;
      if (scene === 'renew') {
        //续签时有custName弹出核查按钮
        if (contr.custName) {
          setIsCustCode(true);
        }
        contr.whMargin = '';
        contr.nonStandardLabelList = [];
        contr.contractRelationLabelList = [];
        contr.paymentDate = '';
        contr.paymentDateApprover = '';
        contr.outSourcingMarginApprover = '';
        contr.outSourcingMargin = '';
        contr.oldContractCode = '';
        contr.labelRemark = '';
        contr.contractRelationLabel = '';
        contr.nonStandardLabel = '';
      }
      if (_contract.isCopyContract) {
        mainForm.setFieldsValue({
          custCode: '',
        });
        contr.currentSales = '';
        contr.liabilityCs = '';
        contr.contractCode = '';
        contr.contractEndDateType = '';
        contr.contractEndDate = '';
        contr.signFlagManual = '';
        contr.contractStatusName = '';
        contr.contractSvcStateName = '';
        contr.sealOpinion = '';
        contr.qaApprove = '';
        contr.salesApprove = '';
        contr.csApproval = '';
        contr.legalApproval = '';
        contr.prepayApproval = '';
        contr.firstOughtMonth = '';
        contr.firstAccountMonth = '';
        contr.firstBillDate = '';
        contr.firstLegalApproveId = '';
        contr.firstLegalApproveName = '';
        contr.whMargin = '';
        contr.nonStandardLabelList = []; //复制合同 父合同id不传
        contr.contractRelationLabelList = [];
        contr.paymentDate = '';
        contr.paymentDateApprover = '';
        contr.outSourcingMarginApprover = '';
        contr.outSourcingMargin = '';
        contr.oldContractCode = '';
        contr.labelRemark = '';
        contr.contractRelationLabel = '';
        contr.nonStandardLabel = '';
        setcurrentCustId('');
      }
      setContractCategory(contr.contractCategery);
      const isIssuingSalary = contr.isIssuingSalary === stdBoolTrueSrting;
      const agreedWageArriveDay = Number(_agreedWageArriveDay);
      // if (contractHeadcount) {
      //   contrAddition.totalPrice = Calculator.multiply(
      //     contractHeadcount,
      //     contractAvgAmt!,
      //   ).toString();
      // }
      if (
        (areaType === '3' && agreedWageArriveDay > 15) ||
        (areaType === '1' && agreedWageArriveDay > 20)
      ) {
        isPaymentQAResultUpdate = true;
      }
      if (contr?.prepayAmt) {
        if (Number(contr?.prepayAmt) > 0) {
          setMainPrepayAmt(true);
        }
      }
      if (currentSales) {
        isSelectCsBySale = true;
        contrAddition.areaId = governingArea;
        contrAddition.departmentId = departmentId;
        contrAddition.departmentName = governingBranchName;
        // const getAreaIdByCurrentSalesRes =
        //   await API.crm.contractManage.getAreaIdByCurrentSales.request({
        //     saleId: currentSales,
        //   });
        // if (!resError(getAreaIdByCurrentSalesRes)) {
        //   contrAddition.areaId = getAreaIdByCurrentSalesRes.data;
        // }

        // const departmentId = await API.crm.contractManage.getDepartmentIdByCurrentSales.requests({
        //   saleId: currentSales,
        // });
        // contrAddition.departmentId = departmentId;
        if (departmentId) {
          // const departmentInfo = await getDepartInfo(departmentId);
          // contrAddition.departmentName = departmentInfo.departmentName;
          // contrAddition.areaId = departmentInfo.governingAreaId;
          setBranchTitleDepartId(departmentId);
          if (departmentId === '11504') {
            isBeijingDepartment = true;
          }
        }
      } else {
        isSelectCsBySale = false;
        isSelectFirstLegalId = false;
        contrAddition.firstLegalApproveId = undefined;
        contrAddition.firstLegalApproveName = undefined;
      }
      if (liabilityCs) {
        // 不用怀疑，这里就是为了查个名字
        const empInfo = await API.admin.employee.queryEmployee.requests({ empId: liabilityCs });
        contrAddition.liabilityCsName = empInfo?.employeeName;
      }
      if (contractType) {
        isUpdateContractCategery = true;
      }
      if (!_contract.isCopyContract) {
        const quotations = await API.crm.contractManage.getQuotationByContractId.requests({
          contractId,
        });
        for (const item of quotations?.list) {
          if (item?.isRetireeProduct !== '0') {
            setIsRetireeProduct(true);
            isProduct = true;
          }
        }
        writableQuotation.setNewData(quotations);

        const productLines = await API.crm.contractManage.getProductLineByContractId.requests({
          contractId,
        });
        writableProductLine.setNewData(productLines);

        const payers = await API.crm.contractManage.getPayerByContractId.requests({ contractId });
        writableCustPayer.setNewData(payers);
      }
      if (contractSubType) {
        if (['8', '9'].includes(contractSubType)) {
          isCalculateGrossProfit = true;
        }
        if (contractSubType === '10') {
          isPhysicalExamination = true;
        }
        if (contractSubType === '*********') {
          isTravelServices = true;
        }
        if (contractSubType === '406') {
          isRiskRatio = true;
        }
        if (
          contractSubType === '210' ||
          contractSubType === '15' ||
          contractSubType === '14' ||
          contractSubType === '11' ||
          contractSubType === '12' ||
          contractSubType === '13' ||
          contractSubType === '*********'
        ) {
          //退休
          isRetireTransact = true;
        }
        if (contractSubType === '501') {
          //福利集采
          isWelfareGather = true;
        }
        if (contractSubType === '502') {
          //平台业务-福利
          isHealthPlatform = true;
        }
        if (contractSubType === '503') {
          //健康体检
          isHealthCheckup = true;
        }
        if (contractSubType === '504' || contractSubType === '505' || contractSubType === '506') {
          //合同小类为健康管理、雇主险、补医保
          isHealthOnther = true;
        }
        if (contractSubType === '210') {
          //合同小类为退休捆绑小类
          isRetirementBusiness = true;
          isRetirementBusinessList = true;
        }
        if (contr.isRetirementBusiness === '1') {
          isRetirementBusinessList = true;
        }
        if (isProduct && isRetireTransact) {
          mainForm.setFieldsValue({
            isRetirementBusiness: '1',
          });
          isRetirementBusinessList = true;
          isRetirementBusiness = true;
        }
      }
      if (contr?.isRetQuotaGranted === '1') {
        isRetQuotaGrantedRatio = true;
      }
      if (!contractVersion) {
        if (!contractSubType || !contractType) {
          contrAddition.contractVersion = '未匹配';
        } else {
          const contractVersionType = contractType === '4' ? '2' : '1';
          const contractVersionRes = await API.sale.contract.getContractVersion.request({
            contractVersionType: contractVersionType,
            contractType: contractType,
            contractSubType: contractSubType,
          });
          if (resError(contractVersionRes)) {
            contrAddition.contractVersion = '未匹配';
          } else {
            contrAddition.contractVersion = contractVersionRes.data;
          }
        }
      }

      if (paymentMode === '3') {
        isEditeAdvancePaymentRatio = true;
      }
      if (_isTravelServices == '1') {
        isEditeTravelServicesRatio = true;
      }
      let importFileInfo = {};

      if (scene !== 'renew' && !_contract.isCopyContract) {
        if (contractType !== '5') {
          const attachments =
            await API.crm.contractManage.getContractAttachmentByContractId.request({
              contractId,
            });
          if (!resError(attachments)) {
            const { data } = attachments;
            // if (data?.fileId) {
            importFileInfo = {
              importFileId: data.fileId,
              importFileName: data.attName,
              // contractFileUploadDt: data.createDt,
              contractFileRemark: data.remark,
            };
            setimportFile({ ...importFileInfo });
            // }
            legalForm.setFieldsValue({ ...importFileInfo, contractFileRemark: data.remark });
          }
        }

        const relatedAttachment =
          await API.crm.contractManage.getContractApproveRelatedAttachmentByContractId.request({
            contractId,
          });
        if (!resError(relatedAttachment)) {
          // console.log('relatedAttachment in getContractApproveRelatedAttachmentByContractId:', relatedAttachment)
          if (relatedAttachment.data) {
            contrAddition.approveRelatedAttachment = relatedAttachment.data.fileId;
            contrAddition.approveRelatedAttachmentName = relatedAttachment.data.attName;
            isUploadApproveRelatedAttachment = true;
          }
        }
      }
      setContractFormConfig({
        isPaymentQAResultUpdate,
        isBeijingDepartment,
        isSelectCsBySale,
        isSelectFirstLegalId,
        isCalculateGrossProfit,
        isUpdateContractCategery,
        isPhysicalExamination,
        isEditeAdvancePaymentRatio,
        isEditeTravelServicesRatio,
        isUploadApproveRelatedAttachment,
        isIssuingSalary,
        isTravelServices,
        isRiskRatio,
        isRetireTransact,
        isHealthCheckup, //健康体检
        isWelfareGather, //福利采集
        isHealthOnther, //福利其他三项
        isHealthPlatform, //福利平台业务
        isRetirementBusiness, //是否有退休业务
        isRetirementBusinessList,
        isRetQuotaGrantedRatio,
      });
      // 依次是，getContractById接口返回的值
      // 外部父级组件传入的contract对象
      // 本次回调中，其他接口获取到的额外的值
      const fullContract = {
        ...contr,
        ...contrAddition,
        ...importFileInfo,
        ...(contr?.travelServicesRatio && {
          travelServicesRatio: contr?.travelServicesRatio * 100,
        }),
      };
      delete fullContract.processInstanceId;
      if (scene === 'renew') {
        // 虽然没看出来老系统是怎么搞的，但是续签合同是，这五个字段确实没有。删了，一了百了。
        delete fullContract.contractCode;
        delete fullContract.firstLegalApproveId;
        delete fullContract.firstLegalApproveName;
        delete fullContract.contractEndDateType;
        delete fullContract.contractEndDate;
        delete fullContract.approveRelatedAttachment;
        delete fullContract.approveRelatedAttachmentName;
      } else {
        if (fullContract.contractEndDateType) {
          fullContract.contractEndDateType =
            contractEndDateTypeNameMap.get(fullContract.contractEndDateType) ||
            fullContract.contractEndDateType;
        }
      }
      if (_contract.isCopyContract) {
        //复制合同时清除部分状态
        delete fullContract.liabilityCsName;
        delete fullContract.liabilityCs;
        fullContract.contractId = '';
        fullContract.custCode = '';
        fullContract.custName = '';
        fullContract.custId = '';
        fullContract.hrContract = '';
        fullContract.email = '';
        fullContract.contactTel = '';
        fullContract.signBranchTitleId = '';
        fullContract.firstLegalApproveId = '';
        fullContract.contractAvgAmt = '';
        fullContract.estimatedHeadcount = '';
        fullContract.estimateFirstBillDate = '';
        fullContract.furtureOpportunity = '';
        fullContract.transferRemark = '';
        fullContract.contractFileUploadDt = '';
        fullContract.contractFileRemark = '';
        fullContract.totalPrice = '';
        fullContract.contractFileList = [];
        fullContract.contractRetireeList = [];
      }
      // if (contractEndDateTypeNameMap.has(fullContract.contractEndDateType)) {
      //   fullContract.contractEndDateType = contractEndDateTypeNameMap.get(fullContract.contractEndDateType)
      // }
      // console.log('fullContract in useEffect:', fullContract)
      // console.log('fullContract.paymentMode in useEffect:', fullContract.paymentMode)
      setCurrentContarct(fullContract);
      mainForm.setFieldsValue(fullContract);
      salesForm.setFieldsValue(fullContract);
      csForm.setFieldsValue(fullContract);
      legalForm.setFieldsValue(fullContract);
      prepayForm.setFieldsValue(fullContract);
      writableUpload.setNewData(fullContract?.contractFileList || []);
      writableRetire.setNewData(fullContract?.contractRetireeList || []);
      // getContractApproveRelatedAttachmentByContractId若获得数据，则下一句不需要
      if (!isUploadApproveRelatedAttachment) isAddedAttachmentOnChange(isAddedAttachment);
      infoLoading[1](false);
    })();
  }, [contractId]);
  const onCurrentSalesChange = async (sales: defs.sale.DropdownList) => {
    if (!sales || isEmpty(sales)) {
      setSalesId('');
      setContractFormConfig({
        isSelectFirstLegalId: false,
        isSelectCsBySale: false,
      });
      legalForm.setFieldsValue({
        firstLegalApproveId: null,
        firstLegalApproveName: null,
      });
      csForm.setFieldsValue({
        liabilityCs: null,
        liabilityCsDepartmentId: null,
        liabilityCsName: null,
      });
      mainForm.setFieldsValue({ cityName: '', formerGoverningAreaName: '' });
      return;
    }

    let isBeijingDepartment = false;
    let isSelectCsBySale = false;
    let isSelectFirstLegalId = false;

    const { key } = sales;
    setcurrentSales(key);
    setSalesId(key);
    const contractAddition: Store = {};
    let departmentId: string | undefined = undefined;
    contractAddition.areaId = await API.crm.contractManage.getAreaIdByCurrentSales.requests({
      saleId: key!,
    });
    departmentId = await API.crm.contractManage.getDepartmentIdByCurrentSales.requests({
      saleId: key!,
    });
    if (departmentId) {
      contractAddition.departmentId = departmentId;
      const departmentInfo = await getDepartInfo(departmentId);
      contractAddition.departmentName = departmentInfo.departmentName;
      contractAddition.areaId = departmentInfo.governingAreaId;
      setBranchTitleDepartId(departmentId);
      isBeijingDepartment = departmentId === '11504';
      mainForm.setFieldsValue({
        cityName: sales?.cityName,
        formerGoverningAreaName: sales?.governingArea,
      });
    }

    isSelectCsBySale = true;
    isSelectFirstLegalId = false;
    setContractFormConfig({
      isSelectFirstLegalId,
      isSelectCsBySale,
      isBeijingDepartment,
    });
    setCurrentContarct({
      ...currentContract,
      ...contractAddition,
    });
  };
  // const handlePrepayAmt=(value)=>{
  //   console.log(value)
  // }
  const handleMainPrepayAmt = (value) => {
    if (value > 0) {
      setMainPrepayAmt(true);
    } else {
      setMainPrepayAmt(false);
    }
  };
  // 上传
  const onImport = (option: any) => {
    const { file } = option;
    setFile({
      fileName: file?.name,
    });
  };
  const handleFileType = (v, o) => {
    setRetireFileFile({ ...retireFile, fileTypeName: o.title });
  };
  let filelist: any = [];
  const customRequest = async (option: any) => {
    const { file, onError, onSuccess } = option;
    await API.minio.minIo.uploadFile
      .request({
        file,
        functionId: '10702000',
      })
      .then((res) => {
        filelist = filelist.concat({
          fileName: file.name,
          filePath: res?.message,
        });
        onSuccess(res, file);
        setFileData(filelist);
      })
      .catch(onError);
  };
  const showUploadList = {
    showRemoveIcon: true,
    removeIcon: <DeleteOutlined />,
  };
  const onRemove = (file) => {
    const list = fileData.filter((item) => item.fileName !== file.name);
    setFileData(list);
  };
  const ImportFormColumns: EditeFormProps[] = [
    {
      label: '附件类型',
      fieldName: 'fileType',
      inputRender: () => (
        <GetSignFlagManualSelect
          params={{
            type: '9132',
          }}
          onChange={(value, option) => handleFileType(value, option)}
        />
      ),
      rules: [{ required: true, message: '请选择附件类型' }],
      colNumber: 3,
    },
    {
      label: '备注',
      fieldName: 'remark',
      inputRender: 'text',
    },
    {
      label: '上传文件',
      fieldName: 'upload',
      rules: [{ required: true, message: '请选择文件' }],
      inputRender: () => (
        <div style={{ display: 'inline-block' }}>
          <Upload
            customRequest={customRequest}
            maxCount={10}
            showUploadList={showUploadList}
            // onPreview={() => onPreview(type)}
            // fileList={fileList}
            multiple
            onRemove={onRemove}
          >
            <Button>
              <UploadOutlined /> 附件{' '}
            </Button>
          </Upload>
        </div>
      ),
    },
  ];
  const ImportFormColumns1: EditeFormProps[] = [
    {
      label: '上传文件',
      fieldName: 'upload',
      rules: [{ required: true, message: '请选择文件' }],
      inputRender: 'upload',
      inputProps: {
        maxSize: FileSize._20MB, // 最大只能传_20MB
        bizType: '10702000',
        onChange: onImport,
      },
    },
  ];
  const onContractHeadcountChange = (value: string | number | undefined) => {
    if (!value || mainForm.getFieldValue('contractType') === '5') {
      setCurrentContarct({ ...currentContract, totalPrice: '0' });
      return;
    }
    const contractHeadcount = String(value);
    const contractAvgAmt = salesForm.getFieldValue('contractAvgAmt');
    if (contractAvgAmt) {
      const totalPrice = String(Calculator.multiply(contractHeadcount, contractAvgAmt));
      mainForm.setFieldsValue({ totalPrice });
      setCurrentContarct({ ...currentContract, totalPrice });
    }
    setCurrentContarct({ ...currentContract, isQuarterlyPaymentLess20: undefined });
  };

  const onAreaTypeChange = (value: string) => {
    const agreedWageArriveDay = Number(prepayForm.getFieldValue('agreedWageArriveDay'));
    if (
      (value === '3' && agreedWageArriveDay > 15) ||
      (value === '1' && agreedWageArriveDay > 20)
    ) {
      setContractFormConfig({ isPaymentQAResultUpdate: true });
    } else {
      setContractFormConfig({ isPaymentQAResultUpdate: false });
    }
    mainForm.setFieldsValue({ isPaymentQAResult: '' });
  };

  const onAgreedWageArriveDayChange = (value: string) => {
    const agreedWageArriveDay = Number(value);
    const areaType = mainForm.getFieldValue('areaType');
    if (
      (areaType === '3' && agreedWageArriveDay > 15) ||
      (areaType === '1' && agreedWageArriveDay > 20)
    ) {
      setContractFormConfig({ isPaymentQAResultUpdate: true });
    } else {
      setContractFormConfig({ isPaymentQAResultUpdate: false });
    }
    mainForm.setFieldsValue({ isPaymentQAResult: '' });
  };

  const onContractCategoryChange = (contractCategory: string) => {
    setContractCategory(contractCategory);
    const { contractSubType } = mainForm.getFieldsValue(['contractType', 'contractSubType']);
    if (!contractSubType || !contractCategory) {
      mainForm.setFieldsValue({ contractVersion: '未匹配' });
      return;
    }
    getContractVersion({ contractCategery: contractCategory });
  };

  const isAddedAttachmentOnChange = (value?: string) => {
    if (value === stdBoolTrueSrting) {
      return setContractFormConfig({
        isUploadApproveRelatedAttachment: true,
      });
    }
    setContractFormConfig({ isUploadApproveRelatedAttachment: false });
    mainForm.setFieldsValue({
      approveRelatedAttachment: undefined,
      approveRelatedAttachmentName: undefined,
    });
  };

  const onGrossProfitChange = (value: string | number | undefined) => {
    // 如果当前输入为空，则计算结果不会产生变化，可以直接返回。
    if (!value) return;
    // 下面的getFieldsValue能否得到最新的值，尚待验证。
    const { income, tax, executionCost, agentBusiness } = mainForm.getFieldsValue([
      'income',
      'tax',
      'executionCost',
      'agentBusiness',
    ]);
    const grossProfit = new Calculator(0);
    grossProfit.plus(income).minus(tax).minus(executionCost).minus(agentBusiness);
    mainForm.setFieldsValue({ grossProfit: grossProfit.toNumber() });
  };

  const paymentModeOnChange = (value: string | undefined) => {
    if (value === '3') {
      return setContractFormConfig({ isEditeAdvancePaymentRatio: true });
    }
    setContractFormConfig({ isEditeAdvancePaymentRatio: false });
    mainForm.setFieldsValue({ advancePaymentRatio: undefined });
  };

  const travelServicesOnChange = (value: string | undefined) => {
    if (value === '1') {
      return setContractFormConfig({ isEditeTravelServicesRatio: true });
    }
    setContractFormConfig({ isEditeTravelServicesRatio: false });
    mainForm.setFieldsValue({ travelServicesRatio: undefined });
  };
  const isRetQuotaGrantedOnChange = (value: string | undefined) => {
    if (value === '1') {
      return setContractFormConfig({ isRetQuotaGrantedRatio: true });
    }
    setContractFormConfig({ isRetQuotaGrantedRatio: false });
    mainForm.setFieldsValue({ retirementGiftCount: undefined });
  };
  const isRetirementBusinessChange = (value: string | undefined) => {
    if (value === '1') {
      return setContractFormConfig({ isRetirementBusinessList: true });
    }
    writableRetire.resetFields();
    writableRetire.setNewData([]);
    setContractFormConfig({ isRetirementBusinessList: false, isRetQuotaGrantedRatio: false });
    mainForm.setFieldsValue({ retirementGiftCount: undefined, isRetQuotaGranted: '0' });
  };
  const isInternalPaymentOnChange = (value: string | undefined) => {
    if (value === stdBoolTrueSrting) {
      return setContractFormConfig({ internalMoneyNeeded: true });
    }
    setContractFormConfig({ internalMoneyNeeded: false });
    mainForm.setFieldsValue({ internalMoney: undefined });
  };

  const handdleCustConfirm = (value?: defs.sale.Customer) => {
    const custInfo = {
      custId: undefined,
      custName: undefined,
      custCode: undefined,
      hrContract: undefined,
      contactTel: undefined,
      email: undefined,
    };
    if (!value.custCode) {
      mainForm.setFieldsValue(custInfo);
      setIsCustCode(false);
      return;
    }
    custInfo.custId = value.custId;
    custInfo.custName = value.custName;
    custInfo.custCode = value.custCode;
    custInfo.hrContract = value.hrContract;
    custInfo.contactTel = value.contactTel;
    custInfo.email = value.email;
    mainForm.setFieldsValue(custInfo);
    setcurrentCustId(value.custId);
    setIsCustCode(true);
    writableQuotation.setData({ list: [], pagination: {} });
    writableCustPayer.setData({ list: [], pagination: {} });
    writableProductLine.setData({ list: [], pagination: {} });
  };

  const onContractSubTypeSelectorLoaded = (data: POJO[]) => {
    if (!data) return;
    // const contractSubType = data[0]?.value;
    // const contractSubType = mainForm.getFieldValue('contractSubType') || data[0]?.value;
    // setSubTypeId(contractSubType);
    // mainForm.setFieldsValue({ contractSubType });
    // // if (contractSubType) {
    // //   onContractSubTypeFlush(contractSubType);
    // // }
    // getContractVersion(contract);
  };

  const onContractSubTypeFlush = (subTypeId: string | undefined) => {
    if (!subTypeId) return;
    let isUpdateContractCategery: boolean;
    let isCalculateGrossProfit: boolean;
    let isPhysicalExamination: boolean;
    let isTravelServices: boolean;
    let isRetQuotaGrantedRatio: boolean;
    let isRiskRatio: boolean; //风险金
    let isRetireTransact: boolean; //退休办理
    let isHealthCheckup: boolean; //健康体检
    let isWelfareGather: boolean; //福利采集
    let isHealthOnther: boolean; //福利其他三项
    let isHealthPlatform: boolean; //福利平台业务
    let isRetirementBusiness: boolean; //是否有退休业务
    let isRetirementBusinessList: boolean; //是否有退休业务
    const contractType = mainForm.getFieldValue('contractType');
    if (subTypeId !== '7' && contractType === '4') {
      mainForm.setFieldsValue({ contractCategery: '2' });
      setContractCategory('2');
      isUpdateContractCategery = false;
    } else {
      if (scene !== 'add') {
        mainForm.setFieldsValue({ contractCategery: null });
        setContractCategory(undefined);
        isUpdateContractCategery = true;
      }
    }
    if (['8', '9'].includes(subTypeId)) {
      isCalculateGrossProfit = true;
    } else {
      isCalculateGrossProfit = false;
      mainForm.setFieldsValue({
        income: null,
        tax: null,
        executionCost: null,
        agentBusiness: null,
        grossProfit: null,
      });
    }
    if (subTypeId === '10') {
      isPhysicalExamination = true;
    } else {
      isPhysicalExamination = false;
      mainForm.setFieldsValue({
        peIncome: null,
        peExecutionCost: null,
        peGrossProfit: null,
        paymentMode: null,
        isInternalPayment: null,
        internalMoney: null,
        advancePaymentRatio: null,
      });
    }
    if (subTypeId === '*********') {
      isTravelServices = true;
    } else {
      isTravelServices = false;
      mainForm.setFieldsValue({
        isTravelServices: null,
        travelServicesRatio: null,
      });
    }
    if (subTypeId === '406') {
      isRiskRatio = true;
    } else {
      isRiskRatio = false;
      mainForm.setFieldsValue({
        riskSharingRatio: null,
        riskPremiumRatio: null,
      });
    }
    if (
      subTypeId === '210' ||
      subTypeId === '15' ||
      subTypeId === '14' ||
      subTypeId === '11' ||
      subTypeId === '12' ||
      subTypeId === '13' ||
      subTypeId === '*********'
    ) {
      //退休
      isRetireTransact = true;
      isRetirementBusinessList = true;
    } else {
      isRetireTransact = false;
      isRetirementBusinessList = false;
    }

    if (subTypeId === '501') {
      //福利集采
      isWelfareGather = true;
    } else {
      isWelfareGather = false;
    }
    if (subTypeId === '502') {
      //平台业务-福利
      isHealthPlatform = true;
    } else {
      isHealthPlatform = false;
    }
    if (subTypeId === '503') {
      //健康体检
      isHealthCheckup = true;
    } else {
      isHealthCheckup = false;
    }
    if (subTypeId === '504' || subTypeId === '505' || subTypeId === '506') {
      //合同小类为健康管理、雇主险、补医保
      isHealthOnther = true;
    } else {
      isHealthOnther = false;
    }
    if (subTypeId === '210') {
      //合同小类为退休捆绑小类
      isRetirementBusiness = true;
      isRetirementBusinessList = true;
      mainForm.setFieldsValue({
        isRetirementBusiness: '1',
      });
    } else {
      isRetirementBusiness = false;
      mainForm.setFieldsValue({
        isRetirementBusiness: '',
        isRetQuotaGranted: '0',
        retirementGiftCount: '',
      });
      // isRetQuotaGrantedRatio = false;
      isRetirementBusinessList = false;
    }
    if (isRetireeProduct) {
      mainForm.setFieldsValue({
        isRetirementBusiness: '1',
      });
      isRetirementBusinessList = true;
      isRetirementBusiness = true;
    }
    // console.log('-----------------------')
    // console.log('subTypeId in onContractSubTypeFlush:', subTypeId)
    // console.log('contractType in onContractSubTypeFlush:', contractType)
    // console.log('isUpdateContractCategery in onContractSubTypeFlush:', isUpdateContractCategery)
    // console.log('isCalculateGrossProfit in onContractSubTypeFlush:', isCalculateGrossProfit)
    // console.log('isPhysicalExamination in onContractSubTypeFlush:', isPhysicalExamination)
    // console.log('contractFormConfig before setContractFormConfig:', contractFormConfig)

    setContractFormConfig({
      isUpdateContractCategery,
      isCalculateGrossProfit,
      isPhysicalExamination,
      isTravelServices,
      isRiskRatio,
      isRetireTransact,
      isHealthCheckup,
      isWelfareGather,
      isHealthPlatform,
      isHealthOnther,
      isRetirementBusiness,
      isRetirementBusinessList,
      isRetQuotaGrantedRatio,
    });
    // console.log('contractFormConfig after setContractFormConfig:', contractFormConfig)
  };

  const onContractSubTypeChange = (subTypeId: string | undefined) => {
    // console.log('subTypeId in onContractSubTypeChange:', subTypeId)
    onContractSubTypeFlush(subTypeId);
    if (subTypeId) {
      getContractVersion({ contractSubType: subTypeId });
      setSubTypeId(subTypeId);
      mainForm.setFieldsValue({
        whInvoiceOrder: '2',
        whPeRate: '1',
        signBranchTitleId: '',
      });
      setWhInvoiceOrderId('2');
    } else {
      mainForm.setFieldsValue({
        contractVersion: '未匹配',
        peIncome: null,
        peExecutionCost: null,
        peGrossProfit: null,
        paymentMode: null,
        isInternalPayment: null,
        internalMoney: null,
        advancePaymentRatio: null,
        travelServicesRatio: null,
        isTravelServices: null,
        signBranchTitleId: '',
      });
      setContractFormConfig({
        isUpdateContractCategery: false,
        isCalculateGrossProfit: false,
        isPhysicalExamination: false,
        isTravelServices: false,
      });
    }
  };
  const approveRejectColumns: WritableColumnProps<any>[] = [
    { title: '步骤', dataIndex: 'activityNameCn' },
    {
      title: '驳回原因',
      dataIndex: 'reasonStr',
      // inputRender: () => {
      //   return <CommonBaseDataSelector params={{ type: '36505' }} allowClear />;
      // },
      // rules: [{ required: true, message: '驳回原因必填' }],
    },
    {
      title: '驳回备注',
      dataIndex: 'reasonBz',
      // inputRender: 'string',
      // rules: [{ required: true, message: '驳回备注必填' }],
    },
    { title: '操作人', dataIndex: 'createByStr' },
    { title: '创建时间', dataIndex: 'createDt' },
    { title: '驳回批次', dataIndex: 'disaBatchId' },
  ];
  const retireColumns: WritableColumnProps<any>[] = [
    { title: '证件号码', dataIndex: 'idCardNum', inputRender: 'string' },
    {
      title: '姓名',
      dataIndex: 'empName',
      inputRender: 'string',
      rules: [{ required: true, message: '请填写姓名' }],
    },
    {
      title: '备注',
      dataIndex: 'bz',
      inputRender: 'string',
    },
  ];
  const ImportColumns: WritableColumnProps<any>[] = [
    { title: '附件类型', dataIndex: 'fileTypeName' },
    // { title: '附件名称', dataIndex: 'fileTypeName' },
    { title: '备注', dataIndex: 'remark' },
    { title: '上传步骤', dataIndex: 'uploadStep' },
    { title: '上传人', dataIndex: 'createBy' },
    { title: '上传时间', dataIndex: 'createDt' },
    {
      title: '下载',
      dataIndex: 'filePath',
      render: (text, record) => {
        return (
          <Link onClick={() => exportImportFile(record.filePath, record.fileName)}>
            {record.fileName ? record.fileName : ''}
          </Link>
        );
      },
    },
    {
      title: '预览',
      dataIndex: '',
      render: (text, record) => {
        return (
          <Link onClick={() => filePreview(record.filePath, record.fileName)}>
            {record.fileName ? record.fileName : ''}
          </Link>
        );
      },
    },
  ];
  const exportImportFile = (fileId: string, fileName: string) => {
    if (!fileId || !fileName) {
      return msgErr('无文件下载');
    }
    API.minio.minIo.downloadFile
      .request({ minioPath: fileId, fileName }, { responseType: 'blob' })
      .then((res) => {
        if (res) {
          downloadFileWithAlert(res, fileName);
        }
      });
  };

  const filePreview = (fileId: string, fileName: string) => {
    if (!fileId || !fileName) {
      return msgErr('无文件预览');
    }
    API.minio.minIo.downloadFile
      .request({ minioPath: fileId, fileName }, { responseType: 'blob' })
      .then((res) => {
        if (res) {
          setPreviewFileType(getFileExtension(fileId));
          const url = URL.createObjectURL(res);
          setPreviewUrl(url);
          setPreviewFile(res);
          previewModal[1](true);
        }
      });
  };
  const onContractTypeChange = (svcTypeId: string) => {
    mainForm.setFieldsValue({ contractSubType: null, signBranchTitleId: '' });
    onContractSubTypeChange(undefined);
    if (!svcTypeId) return;

    // if(svcTypeId == "4"){
    //   isUpdateContractCategery = true;
    // }else{
    //   isUpdateContractCategery = true;
    // }
    if (svcTypeId == '5') {
      //健康大类时总售价重新计算
      const quotationList = writableQuotation.getList().all;
      if (quotationList.length > 0) {
        mainForm.setFieldsValue({
          totalPrice: sumArray(quotationList, 'quotationTotalOhPrice').toFixed(2),
        });
      }
    } else {
      const contractHeadcount = mainForm.getFieldValue('contractHeadcount');
      const contractAvgAmt = salesForm.getFieldValue('contractAvgAmt');
      if (contractAvgAmt) {
        const totalPrice = String(Calculator.multiply(contractHeadcount, contractAvgAmt));
        mainForm.setFieldsValue({ totalPrice });
        setCurrentContarct({ ...currentContract, totalPrice });
      }
    }
    setContractType(svcTypeId);
  };

  const contractAvgAmtonChange = (value: string | undefined) => {
    if (!value || mainForm.getFieldValue('contractType') === '5') {
      setCurrentContarct({ ...currentContract, totalPrice: '0' });
      mainForm.setFieldsValue({ totalPrice: '0' });
      return;
    }

    const contractAvgAmt = String(value);
    const contractHeadcount = mainForm.getFieldValue('contractHeadcount');
    if (contractHeadcount) {
      const totalPrice = String(Calculator.multiply(contractHeadcount, contractAvgAmt));
      mainForm.setFieldsValue({ totalPrice });
      setCurrentContarct({ ...currentContract, totalPrice });
    }
  };

  const getContractVersion = (contract?: Partial<TContractDTO>) => {
    // 由于form.setFieldsValue可能存在延时问题，此处的contract接受一个当前及时更改的最新值
    let mainFormData = mainForm.getFieldsValue([
      'contractType',
      'contractSubType',
      'contractCategery',
    ]);
    if (contract) {
      mainFormData = { ...mainFormData, ...contract };
    }
    const { contractType, contractSubType, contractCategery } = mainFormData;
    // console.log('contractType, contractSubType, contractCategery in getContractVersion:', contractType, contractSubType, contractCategery)
    if (!contractSubType || !contractType || !contractCategery) return;

    API.sale.contract.getContractVersion
      .request({
        contractVersionType: contractCategery,
        contractType,
        contractSubType,
      })
      .then((res: StdRes<string>) => {
        if (resError(res)) {
          return mainForm.setFieldsValue({ contractVersion: '未匹配' });
        }
        mainForm.setFieldsValue({ contractVersion: res.data || '未匹配' });
      });
  };

  const handdleMultiConfirmQoutaions = (quotations?: defs.emphiresep.Quotation[]) => {
    if (!quotations) return;
    const fullQ = quotations.map((q) => ({
      ...q,
      suppltMedInsurHeadCount: q.suppltMedInsurHeadcount,
      status: String(q.status),
      quotationTotalOhPrice: q?.quotationTotalOhPrice ? Number(q?.quotationTotalOhPrice) : '',
    }));
    let isProd = 0;
    for (const item of fullQ) {
      if (item?.isRetireeProduct !== '0') {
        isProd += 1;
      }
    }
    if (contractType === '5') {
      mainForm.setFieldsValue({ totalPrice: sumArray(fullQ, 'quotationTotalOhPrice').toFixed(2) });
    }
    if (isProd > 0 && contractFormConfig?.isRetireTransact) {
      setIsRetireeProduct(true);
      mainForm.setFieldsValue({
        isRetirementBusiness: '1',
      });
      setContractFormConfig({
        isRetirementBusinessList: true,
        isRetirementBusiness: true,
      });
    } else {
      setIsRetireeProduct(false);
      setContractFormConfig({
        isRetirementBusiness: false,
      });
    }
    writableQuotation.setNewData(fullQ);
  };
  const sumArray = (arr: any[], prop: string) => {
    return arr.reduce((sum, obj) => {
      const value = typeof obj[prop] === 'number' ? obj[prop] : 0;
      return sum + value;
    }, 0);
  };
  const viewQuotation = async (quotationId: string) => {
    const data = await API.sale.quotation.getQuotationData.requests(
      { quotationId: quotationId },
      { params: { quotationId: quotationId } },
    );
    let initData: POJO = {};
    if (data.quotationList.length > 0) {
      initData = { ...initData, ...data, ...data.quotationList[0], ...data.saleList[0] };
    }
    if (data.saleList.length > 0) {
      initData = { ...initData, atr: multipleNum(data.saleList[0].atr) };
    }

    if (data.businessDetailList.length > 0) {
      initData = {
        ...initData,
        wvatr: data.businessDetailList[0].vatr,
        watr: parseFloat(data.businessDetailList[0].atr) * 100,
      };
    }
    if (data.healthDetailList.length > 0) {
      initData = {
        ...initData,
        wvatr: data.healthDetailList[0].vatr,
        watr: parseFloat(data.healthDetailList[0].atr) * 100,
      };
    }

    if (data.EmployerDetailList.length > 0) {
      initData = {
        ...initData,
        wvatr: data.EmployerDetailList[0].vatr,
        watr: parseFloat(data.EmployerDetailList[0].atr) * 100,
      };
    }
    quotationDetail[1]({ ...initData, type: 'VIEW', text: '', title: '查看报价单' });
    quotationViewModal[1](true);
  };

  const viewNonStaCoctApprId = (nonStaCoctApprId: string) => {
    // console.log('nonStaCoctApprId in viewNonStaCoctApprId:', nonStaCoctApprId)
  };
  const addonNonStaCoctApprId = (disabled: boolean) => {
    // console.log('currentContract in addonNonStaCoctApprId:', currentContract)
    const { nonStaCoctApprId } = currentContract;
    return (
      <Typography.Link
        disabled={disabled || !nonStaCoctApprId}
        onClick={() => viewNonStaCoctApprId}
      >
        查看
      </Typography.Link>
    );
  };

  const searchCustTianyan = (disabled: boolean) => {
    if ((scene === 'add' || scene === 'renew' || _contract.isCopyContract) && disabled) {
      return (
        <Typography.Link
          // disabled={disabled || !nonStaCoctApprId}
          onClick={() =>
            window.open(
              'https://www.tianyancha.com/search?key=' + mainForm.getFieldValue('custName'),
            )
          }
        >
          核查客户
        </Typography.Link>
      );
    } else {
      return null;
    }
  };
  const onisIssuingSalaryChange = (isIssuingSalary: string) => {
    setContractFormConfig({ isIssuingSalary: isIssuingSalary === stdBoolTrueSrting });
  };
  const handleWhInvoiceOrder = (value: string) => {
    setWhInvoiceOrderId(value);
  };
  const formColumns: EditeFormProps[] = [
    {
      label: '合同编号',
      fieldName: 'contractCode',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    {
      label: '合同名称',
      fieldName: 'contractName',
      rules: [{ required: true, message: '请输入合同名称' }],
      inputRender: 'string',
    },
    {
      label: '总售价',
      fieldName: 'totalPrice',
      rules: [{ required: true, message: '总售价无法计算得出，请填写相关数据。' }],
      inputProps: { disabled: true },
      inputRender: 'string',
    },
    {
      label: '合同大类名称',
      fieldName: 'contractType',
      rules: [{ required: true, message: '请输入合同大类名称' }],
      inputRender: () => <ContractTypeSelector onChange={onContractTypeChange} />,
      inputProps: { disabled: scene === 'renew' || scene === 'add' },
    },
    {
      label: '合同小类名称',
      fieldName: 'contractSubType',
      rules: [{ required: true, message: '请输入合同小类名称' }],
      inputRender: () => (
        <ContractgetSubTypeSelector
          skipEmptyParam
          params={{ svcTypeId: contractType }}
          onLoaded={onContractSubTypeSelectorLoaded}
          onChange={onContractSubTypeChange}
        />
      ),
      inputProps: { disabled: scene === 'renew' || scene === 'add' },
    },
    {
      label: '预计签约人数',
      fieldName: 'contractHeadcount',
      rules: [
        { validator: validateNaturalPositive('预计签约人数格式错误') },
        { required: true, message: '请输入预计签约人数' },
      ],
      inputRender: () => <InputNumber />,
      inputProps: { onChange: onContractHeadcountChange },
    },
    {
      label: '客户编号',
      fieldName: 'custCode',
      rules: [{ required: true, message: '请输入客户编号' }],
      inputRender: (outerForm: FormInstance) => (
        <CustomerPop
          rowValue="custId-custName-custCode"
          handdleConfirm={handdleCustConfirm}
          keyMap={{
            custId: 'custId',
            custCode: 'custCode',
            custName: 'custName',
            hrContract: 'hrContract',
            contactTel: 'contactTel',
            email: 'email',
          }}
          // disabled={scene === 'update' && !_contract.isCopyContract}
          fixedValues={{
            userRoleType: '1',
            // custCode: '20111103026',
            // custId: '900077841'
          }}
          addonAfter={searchCustTianyan(isCustCode)}
        />
      ),
      inputProps: { disabled: scene === 'renew' || scene === 'add' },
    },
    {
      label: '客户全称',
      fieldName: 'custName',
      inputRender: (outerForm: FormInstance) => {
        return <Input disabled value={outerForm.getFieldValue('custName')} />;
      },
    },
    {
      label: '现销售',
      fieldName: 'currentSales',
      rules: [{ required: true, message: '请输入现销售' }],
      inputRender: (outerForm: FormInstance) => {
        return (
          <CurrentSalesSelector
            disabled={!currentCustId || scene === 'add'}
            onConfirm={onCurrentSalesChange}
            // params={{ custId: currentCustId, branchId: userBranchId }}
            params={{ custId: currentCustId, deptId: userBranchId }}
            // paramsData={{ id: currentCustId }}
            skipEmptyParam
          />
        );
      },
    },
    {
      label: '客户联系人',
      fieldName: 'hrContract',
      inputRender: 'string',
    },
    {
      label: '邮件地址',
      fieldName: 'email',
      inputRender: 'string',
      rules: [{ validator: validateEmail }],
    },
    { label: '联系电话', fieldName: 'contactTel', inputRender: 'string' },
    {
      label: '开始日期',
      fieldName: '',
      inputRender: () => (
        <DateRange
          fields={[
            {
              title: '开始日期',
              dataIndex: 'contractStartDate',
              rules: [{ required: true, message: '请输入开始日期' }],
            },
            {
              title: '结束日期',
              dataIndex: 'contractStopDate',
              rules: [{ required: true, message: '请输入结束日期' }],
            },
          ]}
          // colConf={column3}
          format={stdDateFormat}
        />
      ),
    },
    {
      label: '签约方公司抬头',
      fieldName: 'signBranchTitleId',
      rules: [{ required: true, message: '请输入签约方公司抬头' }],
      inputRender: () => {
        if (getSubTypeId === '501' || getSubTypeId === '502') {
          //福利签约方抬头
          return <GetSignFlagManualSelect params={{ type: '9128' }} />;
        } else if (getSubTypeId === '504') {
          //健康管理
          return <GetSignFlagManualSelect params={{ type: '9129' }} />;
        } else if (getSubTypeId === '505' || getSubTypeId === '506') {
          //雇主险 补医保
          return <GetSignFlagManualSelect params={{ type: '9130' }} />;
        } else if (getSubTypeId === '503') {
          //健康体检
          return <GetSignFlagManualSelect params={{ type: '9131' }} />;
        } else {
          return <BranchTitleSpeSelector params={{ departmentId: branchTitleDepartId }} />;
        }
      },
      inputProps: {
        // disabled: scene === contractFormScene.update
      },
    },
    {
      label: '合同类别',
      fieldName: 'contractCategery',
      rules: [{ required: true, message: '请输选择同类别' }],
      inputRender: () => <ContractCategorySelector onChange={onContractCategoryChange} />,
      inputProps: { disabled: !contractFormConfig.isUpdateContractCategery },
    },
    {
      label: '合同版本',
      fieldName: 'contractVersion',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    {
      label: '现销售城市',
      fieldName: 'cityName',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    {
      label: '服务区域类型',
      fieldName: 'areaType',
      rules: [{ required: true, message: '请输入服务区域类型' }],
      inputRender: () => mapToSelectors(contractAreaTypeMap, { onChange: onAreaTypeChange }),
    },
    {
      label: '缴费类型',
      fieldName: 'payType',
      rules: [{ required: true, message: '请输入缴费类型' }],
      inputRender: () => <PayTypeSelector />,
    },
    {
      label: '现销售大区',
      fieldName: 'formerGoverningAreaName',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    {
      label: '范本修改版合同备注',
      fieldName: 'modelModifyVersionRemark',
      inputRender: 'text',
      rules: [
        {
          required: contractCategory === '2',
          message: '【范本修改版合同备注】不能为空',
        },
      ],
    },
    { label: '服务地区', fieldName: 'svcRegion', inputRender: 'text' },
    { label: '备注', fieldName: 'memo', inputRender: 'text' },
    {
      label: '是否抢单',
      fieldName: 'isRob',
      inputRender: () => <BooleanSelector order="asc" />,
      inputProps: {
        hidden:
          contractFormConfig.isHealthOnther ||
          contractFormConfig.isWelfareGather ||
          contractFormConfig.isHealthPlatform ||
          contractFormConfig.isHealthCheckup,
      },
    },
    {
      label: '竞争对手',
      fieldName: 'competitor',
      br: true,
      inputRender: () => <CompetitorSelector />,
      inputProps: {
        hidden:
          contractFormConfig.isHealthOnther ||
          contractFormConfig.isWelfareGather ||
          contractFormConfig.isHealthPlatform ||
          contractFormConfig.isHealthCheckup,
      },
    },
    {
      label: '是否为已有客户所推荐',
      fieldName: 'isCustRecommend',
      rules: [{ required: true, message: '请输入是否为已有客户所推荐' }],
      inputRender: () => <BooleanSelector order="asc" />,
      inputProps: {
        hidden:
          contractFormConfig.isHealthOnther ||
          contractFormConfig.isWelfareGather ||
          contractFormConfig.isHealthPlatform ||
          contractFormConfig.isHealthCheckup,
      },
    },
    {
      label: '是否代发薪资',
      fieldName: 'isIssuingSalary',
      rules: [{ required: true, message: '请选择是否代发薪资' }],
      inputRender: () => <BooleanSelector order="asc" onChange={onisIssuingSalaryChange} />,
      inputProps: {
        hidden:
          contractFormConfig.isHealthOnther ||
          contractFormConfig.isWelfareGather ||
          contractFormConfig.isHealthPlatform ||
          contractFormConfig.isHealthCheckup,
      },
    },
    {
      label: '是否集中一地投保',
      fieldName: 'isSameInsur',
      rules: [{ required: true, message: '请选择是否集中一地投保' }],
      inputRender: () => <BooleanSelector order="asc" />,
      inputProps: {
        hidden:
          contractFormConfig.isHealthOnther ||
          contractFormConfig.isWelfareGather ||
          contractFormConfig.isHealthPlatform ||
          contractFormConfig.isHealthCheckup,
      },
    },
    {
      label: '是否增强型代理',
      fieldName: 'enhancedAgent',
      // rules: [{ required: true, message: '请选择是否增强型代理' }],
      inputRender: () => <Switchs defaultValue="0" />,
      inputProps: {
        hidden:
          contractFormConfig.isHealthOnther ||
          contractFormConfig.isWelfareGather ||
          contractFormConfig.isHealthPlatform ||
          contractFormConfig.isHealthCheckup,
      },
    },
    {
      label: '是否客服二次开发',
      fieldName: 'isSecondaryDev',
      noBorder: true,
      inputRender: () => <Switchs />,
      inputProps: {
        hidden:
          contractFormConfig.isHealthOnther ||
          contractFormConfig.isWelfareGather ||
          contractFormConfig.isHealthPlatform ||
          contractFormConfig.isHealthCheckup,
      },
    },
    {
      label: '是否降价、垫付、账期延期',
      fieldName: 'isDefer',
      noBorder: true,
      inputRender: () => <Switchs />,
      inputProps: {
        hidden:
          contractFormConfig.isHealthOnther ||
          contractFormConfig.isWelfareGather ||
          contractFormConfig.isHealthPlatform ||
          contractFormConfig.isHealthCheckup,
      },
    },
    {
      label: '是否包含退休业务',
      fieldName: 'isRetirementBusiness',
      noBorder: true,
      inputProps: {
        hidden: !contractFormConfig.isRetireTransact,
        disabled: contractFormConfig.isRetirementBusiness,
        onChange: (e: any) => isRetirementBusinessChange(e),
      },
      inputRender: () => <Switchs />,
    },
    {
      label: '是否有赠送退休额度',
      fieldName: 'isRetQuotaGranted',
      noBorder: true,
      inputProps: {
        hidden: !contractFormConfig.isRetireTransact,
        disabled: !contractFormConfig.isRetirementBusinessList,
        onChange: (e: any) => isRetQuotaGrantedOnChange(e),
      },
      inputRender: () => <Switchs />,
    },
    {
      label: '赠送退休数量',
      fieldName: 'retirementGiftCount',
      noBorder: true,
      inputProps: {
        hidden: !contractFormConfig.isRetireTransact,
        disabled: !contractFormConfig.isRetQuotaGrantedRatio,
      },
      rules: [
        { validator: validateNaturalPositive('格式错误必须为正整数') },
        { required: contractFormConfig.isRetQuotaGrantedRatio, message: '请填写赠送退休数量' },
      ],
      inputRender: 'number',
    },
    {
      label: '含差旅服务',
      fieldName: 'isTravelServices',
      noBorder: true,
      inputProps: {
        hidden: !contractFormConfig.isTravelServices,
        onChange: (e: any) => travelServicesOnChange(e),
      },
      inputRender: () => <Switchs />,
    },
    {
      label: '差旅服务费比例%',
      fieldName: 'travelServicesRatio',
      inputRender: 'number',
      inputProps: {
        hidden: !contractFormConfig.isTravelServices,
        disabled: !contractFormConfig.isEditeTravelServicesRatio,
      },
      rules: [
        {
          required: contractFormConfig.isEditeTravelServicesRatio,
          message: '【差旅服务费比例】不能为空',
        },
        {
          pattern: /^(100|([1-9][0-9]?)|(0|[1-9][0-9]?)(\.[\d]{1,2}))$/,
          message: '请输入0~100的两位小数',
        },
      ],
    },
    // { label: '合同最终结束日期类型', fieldName: 'contractEndDateType', inputRender: 'string' },
    // { label: '最终结束日期', fieldName: 'contractEndDate', inputRender: 'date' },
    // {
    //   label: '是否有补充附件',
    //   fieldName: 'isAddedAttachment',
    //   inputRender: () => <BooleanSelector onChange={isAddedAttachmentOnChange} />,
    //   inputProps: { disabled: true },
    // },
    {
      label: '体检税率%',
      fieldName: 'whPeRate',
      inputRender: () => (
        <GetSignFlagManualSelect
          params={{
            type: '9133',
          }}
        />
      ),
      inputProps: { hidden: !contractFormConfig.isHealthCheckup },
      rules: [
        {
          required: contractFormConfig.isHealthCheckup,
          message: '【体检税率%】不能为空',
        },
      ],
    },
    {
      label: '提成销售',
      fieldName: 'whCommissionSale',
      inputRender: () => {
        return (
          <EmployeePop
            rowValue="whCommissionSale-whCommissionSaleName"
            keyMap={{
              whCommissionSale: 'EMPID',
              whCommissionSaleName: 'REALNAME',
            }}
          />
        );
      },
      inputProps: {
        hidden:
          !contractFormConfig.isHealthCheckup &&
          !contractFormConfig.isWelfareGather &&
          !contractFormConfig.isHealthOnther &&
          !contractFormConfig.isHealthPlatform,
      },
      // rules: [
      //   {
      //     required:
      //       contractFormConfig.isHealthCheckup ||
      //       contractFormConfig.isWelfareGather ||
      //       contractFormConfig.isHealthOnther,
      //     message: '提成销售不能为空',
      //   },
      // ],
    },
    // {
    //   label: '合同审核相关的附件',
    //   fieldName: 'approveRelatedAttachment',
    //   inputRender: 'upload',
    //   inputProps: {
    //     maxSize: FileSize._20MB, // 最大只能传_20MB
    //     fileName: 'approveRelatedAttachmentName',
    //     // 这里必传，当代办页使用此页面是，funcId不存在，导致上传出错。
    //     bizType: '10702000',
    //     // disabled: !contractFormConfig.isUploadApproveRelatedAttachment,//说是不让使用了 先行注释掉
    //     disabled: true,
    //   },
    //   rules: [
    //     {
    //       required: contractFormConfig.isUploadApproveRelatedAttachment,
    //       message: '【合同审核相关的附件】不能为空',
    //     },
    //   ],
    // },
    // 收入
    {
      label: '毛利',
      fieldName: 'grossProfit',
      inputRender: 'number',
      inputProps: {
        hidden: !contractFormConfig.isCalculateGrossProfit,
        disabled: true,
      },
    },
    {
      label: '职场健康付款方式',
      fieldName: 'paymentMode',
      inputRender: () => mapToSelectors(paymentModeMap, { onChange: paymentModeOnChange }),
      inputProps: {
        hidden: !contractFormConfig.isHealthCheckup,
      },
      rules: [
        {
          required: contractFormConfig.isHealthCheckup,
          message: '职场健康付款方式',
        },
      ],
    },
    {
      label: '职场健康预付款比例%',
      fieldName: 'advancePaymentRatio',
      inputRender: 'number',
      inputProps: {
        hidden: !contractFormConfig.isHealthCheckup,
        disabled: !contractFormConfig.isEditeAdvancePaymentRatio,
      },
      rules: [
        {
          required: contractFormConfig.isEditeAdvancePaymentRatio,
          message: '【职场健康预付款比例】不能为空',
        },
      ],
    },
    {
      label: '职场健康毛利率%',
      fieldName: 'whMargin',
      inputRender: 'number',
      inputProps: {
        hidden:
          !contractFormConfig.isHealthCheckup &&
          !contractFormConfig.isWelfareGather &&
          !contractFormConfig.isHealthOnther &&
          !contractFormConfig.isHealthPlatform,
        disabled: true,
      },
      rules: [{ pattern: /^(0|[1-9]\d*)(?:\.\d{1,3})?$/, message: '最多保留三位小数' }],
    },
    {
      label: '毛利率%',
      fieldName: 'peGrossProfit',
      inputRender: 'number',
      inputProps: { hidden: !contractFormConfig.isPhysicalExamination },
      rules: [
        {
          required: contractFormConfig.isPhysicalExamination,
          message: '【毛利率%】不能为空',
        },
      ],
    },
    {
      label: '收入',
      fieldName: 'income',
      rules: [
        {
          required: contractFormConfig.isCalculateGrossProfit,
          message: '【收入】不能为空',
        },
        { validator: validateCurrency('收入格式错误') },
      ],
      inputRender: () => <InputNumber onChange={onGrossProfitChange} />,
      inputProps: { hidden: !contractFormConfig.isCalculateGrossProfit },
    },
    {
      label: '税费',
      fieldName: 'tax',
      rules: [
        {
          required: contractFormConfig.isCalculateGrossProfit,
          message: '【税费】不能为空',
        },
      ],
      inputRender: () => <InputNumber onChange={onGrossProfitChange} />,
      inputProps: { hidden: !contractFormConfig.isCalculateGrossProfit },
    },
    {
      label: '执行成本',
      fieldName: 'executionCost',
      rules: [
        {
          required: contractFormConfig.isCalculateGrossProfit,
          message: '【执行成本】不能为空',
        },
        { validator: validateCurrency('执行成本格式错误') },
      ],
      inputRender: () => <InputNumber onChange={onGrossProfitChange} />,
      inputProps: { hidden: !contractFormConfig.isCalculateGrossProfit },
    },
    {
      label: '代收代付',
      fieldName: 'agentBusiness',
      rules: [
        {
          required: contractFormConfig.isCalculateGrossProfit,
          message: '【代收代付】不能为空',
        },
        { validator: validateCurrency('代收代付格式错误') },
      ],
      inputRender: () => <InputNumber onChange={onGrossProfitChange} />,
      inputProps: { hidden: !contractFormConfig.isCalculateGrossProfit },
    },
    {
      label: '收入',
      fieldName: 'peIncome',
      rules: [
        {
          required: contractFormConfig.isPhysicalExamination,
          message: '【收入】不能为空',
        },
        { validator: validateCurrency('收入格式错误') },
      ],
      inputRender: 'number',
      inputProps: { hidden: !contractFormConfig.isPhysicalExamination },
    },
    {
      label: '预估成本',
      fieldName: 'peExecutionCost',
      rules: [
        {
          required: contractFormConfig.isPhysicalExamination,
          message: '请填写预估成本',
        },
        { validator: validateCurrency('预估成本格式错误') },
      ],
      inputRender: 'number',
      inputProps: { hidden: !contractFormConfig.isPhysicalExamination },
    },
    {
      label: '付款方式',
      fieldName: 'paymentMode',
      inputRender: () => mapToSelectors(paymentModeMap, { onChange: paymentModeOnChange }),
      inputProps: {
        hidden: !contractFormConfig.isPhysicalExamination,
      },
      rules: [
        {
          required: contractFormConfig.isPhysicalExamination,
          message: '【付款方式】不能为空',
        },
      ],
    },
    {
      label: '预付款比例%',
      fieldName: 'advancePaymentRatio',
      inputRender: 'number',
      inputProps: {
        hidden: !contractFormConfig.isPhysicalExamination,
        disabled: !contractFormConfig.isEditeAdvancePaymentRatio,
      },
      rules: [
        {
          required: contractFormConfig.isEditeAdvancePaymentRatio,
          message: '【预付款比例】不能为空',
        },
      ],
    },
    {
      label: '开票顺序',
      fieldName: 'whInvoiceOrder',
      inputRender: () => (
        <GetSignFlagManualSelect
          style={{ color: whInvoiceOrderId === '1' ? 'red' : '#000000' }}
          params={{
            type: '9127',
          }}
        />
      ),
      inputProps: {
        hidden:
          !contractFormConfig.isHealthCheckup &&
          !contractFormConfig.isWelfareGather &&
          !contractFormConfig.isHealthOnther &&
          !contractFormConfig.isHealthPlatform,
        onChange: handleWhInvoiceOrder,
      },
      rules: [
        {
          required:
            contractFormConfig.isHealthCheckup ||
            contractFormConfig.isWelfareGather ||
            contractFormConfig.isHealthOnther ||
            contractFormConfig.isHealthPlatform,
          message: '【开票顺序】不能为空',
        },
      ],
    },
    {
      label: '垫付额度',
      fieldName: 'prepayAmt',
      inputRender: 'number',
      inputProps: {
        hidden:
          !contractFormConfig.isHealthCheckup &&
          !contractFormConfig.isWelfareGather &&
          !contractFormConfig.isHealthOnther &&
          !contractFormConfig.isHealthPlatform,
        onChange: handleMainPrepayAmt,
      },
      rules: [validDateCost],
    },
    {
      label: '预计垫付时长（天）',
      fieldName: 'whExpectedPrepayDay',
      inputRender: 'number',
      inputProps: {
        hidden:
          !contractFormConfig.isHealthCheckup &&
          !contractFormConfig.isWelfareGather &&
          !contractFormConfig.isHealthOnther &&
          !contractFormConfig.isHealthPlatform,
      },
      rules: [
        {
          required: mainPrepayAmt,
          message: '【预计垫付时长（天）】不能为空',
        },
      ],
    },
    {
      label: '项目编号',
      fieldName: 'whItemCode',
      inputRender: 'string',
      inputProps: {
        hidden: !contractFormConfig.isHealthCheckup,
      },
      rules: [
        {
          required: true,
          message: '【项目编号】不能为空',
        },
      ],
    },
    {
      label: '销售发票类型',
      fieldName: 'whSaleInvoiceType',
      inputRender: () => (
        <GetSignFlagManualSelect
          params={{
            type: '9134',
          }}
        />
      ),
      inputProps: {
        hidden:
          !contractFormConfig.isHealthCheckup &&
          !contractFormConfig.isWelfareGather &&
          !contractFormConfig.isHealthOnther &&
          !contractFormConfig.isHealthPlatform,
      },
      rules: [
        {
          required:
            contractFormConfig.isHealthCheckup ||
            contractFormConfig.isWelfareGather ||
            contractFormConfig.isHealthOnther ||
            contractFormConfig.isHealthPlatform,
          message: '【销售发票类型】不能为空',
        },
      ],
    },
    {
      label: '返佣收入',
      fieldName: 'whRebateIncome',
      inputRender: 'number',
      inputProps: {
        hidden: !contractFormConfig.isHealthPlatform,
      },
      rules: [validDateCost],
    },
    {
      label: '返佣税费',
      fieldName: 'whRebateTax',
      inputRender: 'number',
      inputProps: {
        hidden: !contractFormConfig.isHealthPlatform,
      },
      rules: [validDateCost],
    },
    {
      label: '合同寄送地址',
      fieldName: 'whContractSendAddress',
      inputRender: 'string',
      inputProps: { hidden: !contractFormConfig.isWelfareGather },
      rules: [
        {
          required: contractFormConfig.isWelfareGather,
          message: '【合同寄送地址】不能为空',
        },
      ],
      colNumber: 1,
    },
    {
      label: '采购发票类型',
      fieldName: 'whPurchaseInvoiceType',
      inputRender: () => (
        <GetSignFlagManualSelect
          params={{
            type: '9137',
          }}
        />
      ),
      inputProps: {
        hidden: !contractFormConfig.isWelfareGather && !contractFormConfig.isHealthPlatform,
      },
    },
    {
      label: '垫付备注',
      fieldName: 'whPrepayRemark',
      inputRender: 'text',
      inputProps: {
        hidden:
          !contractFormConfig.isHealthCheckup &&
          !contractFormConfig.isWelfareGather &&
          !contractFormConfig.isHealthOnther &&
          !contractFormConfig.isHealthPlatform,
      },
      // rules: [
      //   {
      //     required: contractFormConfig.isHealthCheckup || contractFormConfig.isWelfareGather,
      //     message: '【垫付备注】不能为空',
      //   },
      // ],
    },
    {
      label: '销售发票内容',
      fieldName: 'whSaleInvoiceContent',
      inputRender: 'text',
      inputProps: {
        hidden:
          !contractFormConfig.isHealthCheckup &&
          !contractFormConfig.isWelfareGather &&
          !contractFormConfig.isHealthOnther &&
          !contractFormConfig.isHealthPlatform,
      },
    },
    {
      label: '采购发票内容',
      fieldName: 'whPurchaseInvoiceContent',
      inputRender: 'text',
      inputProps: {
        hidden: !contractFormConfig.isWelfareGather && !contractFormConfig.isHealthPlatform,
      },
    },
    {
      label: '预付款时间',
      fieldName: 'whAdvancePaymentDt',
      inputRender: 'date',
      inputProps: {
        hidden: !contractFormConfig.isWelfareGather && !contractFormConfig.isHealthPlatform,
      },
    },
    {
      label: '尾款时间',
      fieldName: 'whFinalPaymentDt',
      inputRender: 'date',
      inputProps: {
        hidden: !contractFormConfig.isWelfareGather && !contractFormConfig.isHealthPlatform,
      },
    },
    {
      label: '支付供货商货款时间',
      fieldName: 'whSupplierPaymentDt',
      inputRender: 'date',
      inputProps: {
        hidden: !contractFormConfig.isWelfareGather && !contractFormConfig.isHealthPlatform,
      },
    },
    {
      label: '预付款金额',
      fieldName: 'whAdvancePaymentAmt',
      inputRender: 'string',
      inputProps: {
        hidden: !contractFormConfig.isWelfareGather && !contractFormConfig.isHealthPlatform,
      },
      rules: [validDateCost],
    },
    {
      label: '尾款金额',
      fieldName: 'whFinalPaymentAmt',
      inputRender: 'number',
      inputProps: {
        hidden: !contractFormConfig.isWelfareGather && !contractFormConfig.isHealthPlatform,
      },
      rules: [validDateCost],
    },
    {
      label: '风险分担比例%',
      fieldName: 'riskSharingRatio',
      inputRender: 'number',
      inputProps: {
        hidden: !contractFormConfig.isRiskRatio,
      },
      rules: [
        {
          required: contractFormConfig.isRiskRatio,
          message: '【风险分担比例】不能为空',
        },
        {
          pattern: /(^(\d|[1-9]\d)(\.\d{1,2})?$)|(^100$)/,
          message: '请输入0~100的两位小数',
        },
      ],
    },
    {
      label: '风险金比例%',
      fieldName: 'riskPremiumRatio',
      inputRender: 'number',
      inputProps: {
        hidden: !contractFormConfig.isRiskRatio,
      },
      rules: [
        {
          required: contractFormConfig.isRiskRatio,
          message: '【风险金比例】不能为空',
        },
        {
          pattern: /(^(\d|[1-9]\d)(\.\d{1,2})?$)|(^100$)/,
          message: '请输入0~100的两位小数',
        },
      ],
    },
    {
      label: '是否内支',
      fieldName: 'isInternalPayment',
      inputRender: () => <Switchs onChange={isInternalPaymentOnChange} />,
      inputProps: {
        hidden: !contractFormConfig.isPhysicalExamination,
      },
    },
    {
      label: '内支金额',
      fieldName: 'internalMoney',
      rules: [
        {
          required: contractFormConfig.internalMoneyNeeded,
          message: '【内支金额】不能为空',
        },
        { validator: validateCurrency('内支金额格式错误') },
      ],
      inputRender: 'number',
      inputProps: {
        hidden: !contractFormConfig.isPhysicalExamination,
        disabled: !contractFormConfig.internalMoneyNeeded,
      },
    },
    // {
    //   label: '非标合同审批单',
    //   fieldName: 'nonStaCoctApprId',
    //   inputProps: { disabled: !userBranchId || !currentCustId || !currentSales },
    //   inputRender: () => {
    //     // console.log('currentCustId in nonStaCoctApprId:', currentCustId)
    //     return (
    //       <NonStaCoctApprPop
    //         rowValue="nonStaCoctApprId-applyCode"
    //         addonAfter={addonNonStaCoctApprId(!userBranchId)}
    //         fixedValues={{ custId: currentCustId, currentSales }}
    //       />
    //     );
    //   },
    // },
    // {
    //   label: '本次续签是否需要调整合同条款？',
    //   fieldName: 'isAdjustRenewContract',
    //   inputRender: () => <BooleanSelector />,
    //   rules: [
    //     {
    //       required: scene === 'renew',
    //       message: '[本次续签是否需要调整合同条款？]不能为空',
    //     },
    //   ],
    //   inputProps: {
    //     hidden: scene !== 'renew',
    //   },
    // },
  ];

  const salesFormColumns: EditeFormProps[] = [
    {
      label: '签约人均价格',
      fieldName: 'contractAvgAmt',
      rules: [
        { required: true, message: '请输入签约人均价格' },
        { validator: validateCurrency('人均价格格式不正确') },
      ],
      inputRender: 'number',
      inputProps: { onChange: contractAvgAmtonChange },
    },
    {
      label: '预计12个月内可达到人数',
      fieldName: 'estimatedHeadcount',
      rules: [
        { validator: validateNaturalPositive('人数格式错误必须为正整数') },
        { required: true, message: '请输入预计12个月内可达到人数' },
      ],
      inputRender: 'number',
    },
    {
      label: '预估首次账单日期',
      fieldName: 'estimateFirstBillDate',
      rules: [{ required: true, message: '请输入预估首次账单日期' }],
      inputRender: 'date',
      inputProps: {
        getPopupContainer: undefined,
      },
    },
    { label: '未来商机', fieldName: 'furtureOpportunity', inputRender: 'text' },
  ];

  const prepayFormColumns: EditeFormProps[] = [
    {
      label: '账单日期(天)',
      fieldName: 'billDt',
      rules: [{ validator: validateNaturalDay() }],
      inputRender: 'number',
    },
    {
      label: '约定到款月',
      fieldName: 'agereedAmtReceiveMon',
      inputRender: () => mapToSelectors(agereedAmtReceiveMonMap),
    },
    {
      label: '约定到款日(天)',
      fieldName: 'agreedWageArriveDay',
      rules: [
        // { min: 1, message: '日期应当不得小于1' },
        // { min: 31, message: '日期应当不得大于31' },
        { validator: validateNaturalDay() },
        { required: true, message: '请输入约定到款日(天)' },
      ],
      inputRender: () => {
        return <InputNumber onChange={onAgreedWageArriveDayChange} />;
      },
    },
    {
      label: '账期（天）',
      fieldName: 'creditPeriod',
      inputRender: 'number',
      rules: [{ validator: validateNaturalDay() }],
    },
    {
      label: '垫付额度',
      fieldName: 'prepayAmt',
      rules: [{ validator: validateCurrency('垫付额度格式错误') }],
      inputRender: 'number',
    },
    {
      label: '薪资发放月',
      fieldName: 'payMonth',
      inputRender: () => mapToSelectors(payMonthMapMap),
      rules: [{ required: contractFormConfig.isIssuingSalary }],
    },
    {
      label: '薪资发放日',
      fieldName: 'agreedPayDt',
      inputRender: 'number',
      rules: [
        { validator: validateNaturalDay() },
        { required: contractFormConfig.isIssuingSalary },
      ],
    },
  ];

  const approveOpinionFormColumns: EditeFormProps[] = [
    {
      label: '提交意见',
      fieldName: 'approveOpinion',
      inputRender: 'text',
    },
  ];

  const productLineColumns: WritableColumnProps<any>[] = [
    {
      title: '产品线名称',
      dataIndex: 'productlineId',
      inputRender: () => {
        // const params = contractId
        //   ? { contractId }
        //   : { custId: currentCustId, saleId: currentSales };
        const params = { custId: currentCustId, saleId: currentSales };
        return (
          // <ProdcutLineSelector
          //   params={{
          //     ...params,
          //     statementName: 'contractManage.custProductLineDropDownListByMap',
          //   }}
          // />
          <CustProductLineSelector
            params={{
              ...params,
            }}
          />
        );
      },
      rules: [{ required: true, message: '请输入产品线名称' }],
    },
    {
      title: '签约人数/数量',
      dataIndex: 'compactNumber',
      inputRender: 'number',
      rules: [
        { required: true, message: '请输入签约人数' },
        { validator: validateNaturalPositive('签约人数格式错误') },
      ],
    },
    {
      title: '人均销售收入',
      dataIndex: 'averageMoney',
      inputRender: 'number',
      rules: [{ required: true, message: '请输入人均销售收入' }],
    },
  ];
  const productLineHealthColumns: WritableColumnProps<any>[] = [
    {
      title: '产品线名称',
      dataIndex: 'productlineId',
      inputRender: () => {
        // const params = contractId
        //   ? { contractId }
        //   : { custId: currentCustId, saleId: currentSales };
        const params = { custId: currentCustId, saleId: currentSales };
        return (
          // <ProdcutLineSelector
          //   params={{
          //     ...params,
          //     statementName: 'contractManage.custProductLineDropDownListByMap',
          //   }}
          // />
          <CustProductLineSelector
            params={{
              ...params,
            }}
          />
        );
      },
      rules: [{ required: true, message: '请输入产品线名称' }],
    },
    {
      title: '签约人数/数量',
      dataIndex: 'compactNumber',
      inputRender: 'number',
      rules: [
        { required: true, message: '请输入签约人数' },
        { validator: validateNaturalPositive('签约人数格式错误') },
      ],
    },
    {
      title: '金额',
      dataIndex: 'averageMoney',
      inputRender: 'number',
      rules: [{ required: true, message: '请输入金额' }],
    },
  ];
  const csFormColumns: EditeFormProps[] = [
    {
      label: '指定交接客服',
      fieldName: 'liabilityCs',
      inputRender: () => {
        return (
          <InnerUserPop
            rowValue="liabilityCs-liabilityCsDepartmentId-liabilityCsName"
            disabled={!salesId}
            fixedValues={{
              roleCode: '20940',
              areaId: currentContract.areaId,
              branchId: currentContract.departmentId,
              branchName: currentContract.departmentName,
              isExcludeDefault: 1,
            }}
            keyMap={{
              liabilityCs: 'EMPID',
              liabilityCsDepartmentId: 'BRANCHID',
              liabilityCsName: 'REALNAME',
            }}
          />
        );
      },
      // inputProps: { disabled: !contractFormConfig.isSelectCsBySale },
      rules: [{ required: true, message: '请选择指定交接客服。' }],
    },
    {
      label: '指定交接事项',
      fieldName: 'transferRemark',
      inputRender: 'text',
    },
  ];

  const quotationColumns: WritableColumnProps<any>[] = [
    {
      title: '报价单编号',
      dataIndex: 'quotationCode',
    },
    {
      title: '报价单名称',
      dataIndex: 'quotationName',
    },
    { title: '预计签约人数', dataIndex: 'suppltMedInsurHeadCount' },
    { title: '备注', dataIndex: 'remark' },
    { title: '薪税社保业务售价', dataIndex: 'quotationTotalSalPrice' },
    { title: '职场健康总售价', dataIndex: 'quotationTotalOhPrice' },
    {
      title: '报价单状态',
      dataIndex: 'status',
      render: (outerForm?: FormInstance, record?: Store) => QuostatusMap.get(record?.status),
    },
    {
      title: '查看明细',
      dataIndex: 'view',
      render: (text: string, record: any) => (
        <Link onClick={() => viewQuotation(record.quotationId)}>查看</Link>
      ),
    },
  ];

  const onImportFile = (info: UploadChangeParam<UploadFile<any>>) => {
    // console.log(onImportFile)
    if (info.file.status === 'done') {
      legalForm.setFieldsValue({ contractFileUploadDt: today() });
    } else {
      legalForm.setFieldsValue({ contractFileUploadDt: null });
    }
  };

  const legalFormColumns: EditeFormProps[] = [
    {
      label: '首个法务',
      fieldName: 'firstLegalApproveId',
      inputRender: () => (
        <InnerUserPop
          rowValue="firstLegalApproveId-firstLegalApproveName"
          keyMap={{ firstLegalApproveId: 'EMPID', firstLegalApproveName: 'REALNAME' }}
          fixedValues={{ roleCode: 60095 }}
        />
      ),
      rules: [
        {
          required: contractFormConfig.isSelectFirstLegalId,
          message: '【合同审核相关的附件】不能为空',
        },
      ],
      inputProps: { disabled: !contractFormConfig.isSelectFirstLegalId },
    },
    // {
    //   label: '合同附件',
    //   fieldName: 'importFileId',
    //   inputRender: 'upload',
    //   inputProps: {
    //     maxSize: FileSize._20MB, // 最大只能传_20MB
    //     fileName: 'importFileName',
    //     bizType: '10702000',
    //     onChange: onImportFile,
    //   },
    //   rules: [{ required: true, message: '请上传合同附件' }],
    // },
    // {
    //   label: '附件上传时间',
    //   fieldName: 'contractFileUploadDt',
    //   inputRender: 'string',
    //   inputProps: { disabled: true },
    // },
    { label: '合同附件备注', fieldName: 'contractFileRemark', inputRender: 'text' },
  ];

  const custPayerColumns: WritableColumnProps<any>[] = [
    {
      title: '付款方编号',
      dataIndex: 'custPayerId',
    },
    {
      title: '付款方名称',
      dataIndex: 'payerName',
    },
    {
      title: '发票抬头',
      dataIndex: 'checkTitle',
    },
  ];

  const quotationDel = async () => {
    const quotationAll = writableQuotation.getList().visible;
    const deleted = writableQuotation.selectedSingleRow;
    const quotationList = quotationAll.filter((item: any) => {
      return item.quotationCode !== deleted.quotationCode;
    });
    const fullQ = quotationList.map((q: any) => ({
      ...q,
      quotationTotalOhPrice: q?.quotationTotalOhPrice ? Number(q?.quotationTotalOhPrice) : '',
    }));
    let isProd = 0;
    for (const item of fullQ) {
      if (item?.isRetireeProduct !== '0') {
        isProd += 1;
        setIsRetireeProduct(true);
      } else {
        setIsRetireeProduct(false);
      }
    }
    if (mainForm.getFieldValue('contractType') === '5') {
      const totalPrice = Calculator.subtract(
        mainForm.getFieldValue('totalPrice'),
        deleted?.quotationTotalOhPrice || 0,
      );
      mainForm.setFieldsValue({ totalPrice: totalPrice });
    }
    if (isProd > 0) {
      mainForm.setFieldsValue({
        isRetirementBusiness: '1',
      });
      setContractFormConfig({
        isRetirementBusinessList: true,
        isRetirementBusiness: true,
      });
    } else {
      setContractFormConfig({
        isRetirementBusiness: false,
      });
    }
    if (deleted[addedInLocal] !== addedInLocal) {
      await API.crm.contractManage.delQuotationInContract.requests({ ...deleted, contractId });
      msgOk('删除报价单成功。');
    }
    await writableQuotation.deleteRows(writableQuotation.selectedSingleRow, { hard: true });
  };

  const productLineAdd = () => {
    if (!currentCustId) {
      return msgErr('未选择客户,不可新增产品线');
    }
    writableProductLine.addRows();
  };

  const productLineDel = async () => {
    const deleted = writableProductLine.selectedSingleRow;
    if (deleted[addedInLocal] !== addedInLocal) {
      await API.crm.contractManage.delProductLineInContract.requests({ ...deleted, contractId });
      msgOk('删除产品线成功。');
    }
    writableProductLine.deleteRows(writableProductLine.selectedSingleRow, { hard: true });
  };

  const handdleMultiConfirmCustPayer = (custPayers?: defs.crm.CustomerPayerDTO[]) => {
    writableCustPayer.setNewData(custPayers);
  };

  const deletecustPayer = async () => {
    const deleted = writableCustPayer.selectedSingleRow;
    if (deleted[addedInLocal] !== addedInLocal) {
      await API.crm.contractManage.delPayerInContract.requests({ ...deleted, contractId });
      msgOk('删除付款方列表成功。');
    }
    writableCustPayer.deleteRows(writableCustPayer.selectedSingleRow, { hard: true });
  };

  const onOk = async () => {
    // try {
    //   values = await Promise.all([
    //     mainForm.validateFields(),
    //     salesForm.validateFields(),
    //     csForm.validateFields(),
    //     legalForm.validateFields(),
    //     prepayForm.validateFields(),
    //     writableQuotation.validateFields(),
    //     writableProductLine.validateFields(),
    //     writableCustPayer.validateFields(),
    //   ]);
    // } catch (e) {
    //   console.log(e)
    //   throw e
    // }

    // const [
    //   mainData,
    //   salesData,
    //   csFormData,
    //   legalFormData,
    //   prepayFormData,
    //   quotations,
    //   productLines,
    //   custPayers,
    // ] = values;

    let mainData: Store;
    let salesData: Store;
    let csFormData: Store;
    let legalFormData: Store;
    let prepayFormData: Store;
    let quotations: Store;
    let productLines: Store;
    let importData: Store;
    let retireData: Store;
    const tabErrs = { ...tabErrors };

    try {
      mainData = await mainForm.validateFields();
      tabErrs.mainForm = false;
    } catch (e) {
      setTabErrors({ ...tabErrs, mainForm: true });
      throw e;
    }

    try {
      salesData = await salesForm.validateFields();
      tabErrs.salesForm = false;
    } catch (e) {
      setTabErrors({ ...tabErrs, salesForm: true });
      throw e;
    }
    try {
      csFormData = await csForm.validateFields();
      tabErrs.csForm = false;
    } catch (e) {
      setTabErrors({ ...tabErrs, csForm: true });
      throw e;
    }
    try {
      legalFormData = await legalForm.validateFields();
      tabErrs.legalForm = false;
    } catch (e) {
      setTabErrors({ ...tabErrs, legalForm: true });
      throw e;
    }

    try {
      prepayFormData = await prepayForm.validateFields();
      tabErrs.prepayForm = false;
    } catch (e) {
      setTabErrors({ ...tabErrs, prepayForm: true });
      throw e;
    }
    try {
      quotations = await writableQuotation.validateFields();
      tabErrs.writableQuotation = false;
    } catch (e) {
      setTabErrors({ ...tabErrs, writableQuotation: true });
      throw e;
    }
    try {
      productLines = await writableProductLine.validateFields();
      tabErrs.writableProductLine = false;
    } catch (e) {
      setTabErrors({ ...tabErrs, writableProductLine: true });
      throw e;
    }
    try {
      importData = await writableUpload.validateFields();
      tabErrs.writableUpload = false;
    } catch (e) {
      setTabErrors({ ...tabErrs, writableUpload: true });
      throw e;
    }
    try {
      retireData = await writableRetire.validateFields();
      tabErrs.writableRetire = false;
    } catch (e) {
      setTabErrors({ ...tabErrs, writableRetire: true });
      throw e;
    }
    const values: any = await Promise.all([writableCustPayer.validateFields()]);
    const [custPayers] = values;

    if (quotations.visibleCount === 0 || productLines.visibleCount === 0) {
      return msgErr('报价单产品线都至少需要一条');
    }
    if (mainData.contractSubType !== '503') {
      if (importData.visibleCount === 0) {
        return msgErr('常规合同至少需要合同文件一个附件');
      }
    } else {
      if (mainData.contractCategery === '2') {
        if (importData.visibleCount === 0) {
          msgErr('合同小类为体检且合同类别是范本修改版合同，合同文件必传');
          return Promise.reject();
        }
      }
    }

    infoLoading[1](true);
    let payerIds: string | undefined = undefined;
    if (custPayers.visibleCount > 0) {
      payerIds = custPayers.visible.map((cust) => cust.custPayerId).join(',');
    }
    const quoIds = quotations.visible.map((quot) => quot.quotationId).join(',');
    // setCurrentContarct({ ...currentContract, quotationIds: quotationIds.join(',') })
    const contractProductLineIds: string[] = [];
    const productLineIds: string[] = [];
    const compactNumbers: string[] = [];
    const averageMoneys: string[] = [];
    const productLineIdSet: Set<string> = new Set();
    productLines.visible.forEach((line) => {
      contractProductLineIds.push(line.smcontractProductlineId);
      productLineIds.push(line.productlineId);
      productLineIdSet.add(line.productlineId);
      compactNumbers.push(line.compactNumber);
      averageMoneys.push(line.averageMoney);
    });

    if (productLineIds.length !== productLineIdSet.size) {
      setTabErrors({ ...tabErrs, writableQuotation: true });
      infoLoading[1](false);
      return msgErr('报价单模块下，产品线有重复记录');
    }
    const formData = {
      ...currentContract,
      ...mainData,
      ...salesData,
      ...csFormData,
      ...legalFormData,
      ...prepayFormData,
      ...(mainData?.travelServicesRatio && {
        travelServicesRatio: mainData?.travelServicesRatio / 100,
      }),
      quoIds,
      contractProductLineIds: contractProductLineIds.join(','),
      productLineIds: productLineIds.join(','),
      compactNumbers: compactNumbers.join(','),
      averageMoneys: averageMoneys.join(','),
      contractFileList: importData.visible,
      contractRetireeList: retireData.visible,
    };
    let fileListNum3 = 0; //补充协议
    let fileListNum1 = 0;
    let fileListNum9 = 0;
    for (const item of formData?.contractFileList) {
      if (item?.fileType === '3') {
        fileListNum3 += 1;
      }
      if (item?.fileType === '1') {
        fileListNum1 += 1;
      }
      if (item?.fileType === '9') {
        fileListNum9 += 1;
      }
    }
    if (mainData.contractSubType !== '503') {
      if (fileListNum1 < 1 && fileListNum3 < 1) {
        infoLoading[1](false);
        return msgErr('合同小类非体检时，合同文件或者补充协议必填一个。');
      }
    }
    if (formData.isRetirementBusiness === '1' && contractFormConfig.isRetireTransact) {
      if (retireData.visible?.length < 1) {
        infoLoading[1](false);
        return msgErr('是否包含退休业务为是时退休人员列表至少有一条数据。');
      }
    }

    if (payerIds) formData.payerIds = payerIds;
    let msg: string;

    if (scene === 'add') {
      msg = '新增合同成功';
    } else if (scene === 'update') {
      msg = '更新合同成功';
    } else if (scene === 'examine') {
      formData.isCommitApprove = '1';
      const approveOpinion = approveOpinionForm.getFieldValue('approveOpinion');
      if (approveOpinion) {
        // formData.remark = approveOpinion;
        formData.approveOpinion = approveOpinion;
      }

      msg = '提交审核成功';
    } else if (scene === 'renew') {
      msg = '续签合同成功';
    } else {
      msg = '请求成功';
    }

    if (scene === 'renew') {
      API.crm.contractManage.setRenewContract
        .requests(formData)
        .then((data) => {
          msgOk(msg);
          infoLoading[1](false);
          setVisible(false);
          onConfirm();
        })
        .finally(() => infoLoading[1](false));
    } else {
      API.crm.contractManage.save
        .requests(formData)
        .then((data) => {
          msgOk(msg);
          infoLoading[1](false);
          setVisible(false);
          onConfirm();
        })
        .finally(() => infoLoading[1](false));
    }
  };
  const onImportBtn = (type: string) => {
    setImportType(type);
    setOnShowImport(true);
  };
  const importHandle = async (data: POJO) => {
    if (fileData?.length < 1) return msgErr('请选择文件');
    if (importType === '1') {
      const fileList: any[] = [];
      fileData.forEach((item) => {
        fileList.push({ ...item, ...data, ...retireFile, activityNameEn: 1 });
      });
      await writableUpload.addRows(fileList);
      setFile(undefined);
    }
    setOnShowImport(false);
  };
  const onDelImport = (type: string) => {
    // setImportType(type);
    const list = writableUpload.getList().visible;
    writableUpload.deleteRows(writableUpload.selectedSingleRow);
    if (
      writableUpload.selectedSingleRow?.contractFileId &&
      scene === 'update' &&
      !_contract.isCopyContract
    ) {
      API.crm.contractManage.delFileInContract.requests({ ...writableUpload.selectedSingleRow });
    }
  };
  const addFileRenderButtons = () => {
    return (
      <RowElement>
        <ColElementButton
          style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}
        >
          <AsyncButton type="primary" onClick={importHandle}>
            上传1
          </AsyncButton>
          <Button type="primary" onClick={() => setOnShowImport(false)}>
            取消
          </Button>
        </ColElementButton>
      </RowElement>
    );
  };
  const onBatchDownload = async () => {
    const rows = writableUpload.selectedRows;
    const minioPaths = rows.map((item) => {
      return { filePath: item.filePath, fileName: item.fileName };
    });
    if (minioPaths.length < 1) return msgErr('请选择文件后再进行操作');
    const res = await API.minio.minIo.downloadFilesAsZipStream.request(
      { minioPaths },
      { params: { minioPaths }, responseType: 'blob' },
    );
    if (!res) {
      msgErr('导出数据失败');
      return;
    }
    if (minioPaths.length === 1) {
      const data = minioPaths[0];
      downloadFileWithAlert(res, `${data.fileName}.zip`);
    } else {
      downloadFileWithAlert(res, '相关附件.zip');
    }
  };
  return (
    <Codal
      title="大合同"
      visible={visible}
      loading={infoLoading[0]}
      onCancel={() => setVisible(false)}
      onOk={onOk}
      okText={contractFormConfig.isCommitApprove ? '提交审核' : '保存'}
      width={1200}
    >
      <Tabs type="card">
        <TabPane
          forceRender
          tab={<AlertTab onError={tabErrors.mainForm}>合同基本信息</AlertTab>}
          key={'1'}
        >
          <FormElement3 form={mainForm}>
            <EnumerateFields outerForm={mainForm} colNumber={3} formColumns={formColumns} />
          </FormElement3>
        </TabPane>
        {contractType !== '5' ? (
          <TabPane
            forceRender
            tab={
              <AlertTab
                onError={
                  tabErrors.salesForm ||
                  tabErrors.csForm ||
                  tabErrors.legalForm ||
                  tabErrors.prepayForm
                }
              >
                业务部门
              </AlertTab>
            }
            key={'2'}
          >
            <Group title="销售相关">
              <FormElement3 form={salesForm} initialValues={currentContract}>
                <EnumerateFields
                  outerForm={salesForm}
                  colNumber={3}
                  formColumns={salesFormColumns}
                />
              </FormElement3>
            </Group>
            <Group title="客服相关">
              <FormElement3 form={csForm} initialValues={currentContract}>
                <EnumerateFields outerForm={csForm} colNumber={3} formColumns={csFormColumns} />
              </FormElement3>
            </Group>
            <Group title="法务相关">
              <FormElement3 form={legalForm} initialValues={{ ...currentContract, ...importFile }}>
                <EnumerateFields
                  outerForm={legalForm}
                  colNumber={3}
                  formColumns={legalFormColumns}
                />
              </FormElement3>
            </Group>
            <Group title="垫付相关">
              <FormElement3 form={prepayForm} initialValues={currentContract}>
                <EnumerateFields
                  outerForm={prepayForm}
                  colNumber={3}
                  formColumns={prepayFormColumns}
                />
              </FormElement3>
            </Group>
          </TabPane>
        ) : null}
        <TabPane
          forceRender
          tab={<AlertTab onError={tabErrors.writableProductLine}>报价单</AlertTab>}
          key={'3'}
        >
          <RowElementButton>
            {/* <Button onClick={quotationAdd}>新增</Button> */}
            <QuotationFullPop
              buttonMode
              rowValue="quotationCode-quotationName"
              disabled={!currentCustId}
              fixedValues={{ custId: currentCustId, userId: currentUser.userId }}
              multiple
              handdleMultiConfirm={handdleMultiConfirmQoutaions}
            >
              <Button>新增</Button>
            </QuotationFullPop>

            {/* <Button onClick={quotationDel}>删除</Button> */}
            <PopconfirmButton
              disabled={isEmpty(writableProductLine.selectedSingleRow)}
              title={`选的数据，将进行删除操作，是否继续？`}
              onConfirm={quotationDel}
            >
              删除
            </PopconfirmButton>
          </RowElementButton>
          <Writable
            wriTable={writableQuotation}
            service={serviceQuotation}
            columns={quotationColumns}
            editable={false}
            notShowPagination
            notShowRowSelection
            scrollVertical
          />
          <RowElementButton>
            <Button onClick={productLineAdd}>新增</Button>
            <PopconfirmButton
              disabled={isEmpty(writableProductLine.selectedSingleRow)}
              title={`选中的数据，将进行删除操作，是否继续？`}
              onConfirm={productLineDel}
            >
              删除
            </PopconfirmButton>
          </RowElementButton>
          <Writable
            wriTable={writableProductLine}
            service={serviceProductLine}
            columns={
              mainForm.getFieldValue('contractType') === '5'
                ? productLineHealthColumns
                : productLineColumns
            }
            notShowPagination
            notShowRowSelection
            scrollVertical
          />
        </TabPane>
        <TabPane
          forceRender
          //  tab="销售相关"
          tab={<AlertTab onError={tabErrors.writableCustPayer}>付款方列表</AlertTab>}
          key={'4'}
        >
          <RowElementButton>
            <CustPayerPop
              buttonMode
              rowValue="custPayerId-payerName"
              disabled={!currentCustId}
              fixedValues={{ custId: currentCustId }}
              multiple
              handdleMultiConfirm={handdleMultiConfirmCustPayer}
            >
              <Button>新增</Button>
            </CustPayerPop>
            <PopconfirmButton
              disabled={isEmpty(writableCustPayer.selectedSingleRow)}
              title={`选中的数据，将进行删除操作，是否继续？`}
              onConfirm={deletecustPayer}
            >
              删除
            </PopconfirmButton>
          </RowElementButton>
          <Writable
            wriTable={writableCustPayer}
            service={serviceCustPayer}
            columns={custPayerColumns}
            notShowPagination
            notShowRowSelection
            scrollVertical
          />
        </TabPane>
        <TabPane
          forceRender
          tab={<AlertTab onError={tabErrors.writableUpload}>相关附件</AlertTab>}
          key={'5'}
        >
          <RowElementButton>
            <Button onClick={() => onImportBtn('1')}>新增</Button>
            <Button onClick={() => onDelImport('1')}>删除</Button>
            <Button onClick={onBatchDownload}>批量下载</Button>
          </RowElementButton>
          <Writable
            wriTable={writableUpload}
            service={serviceProductLine}
            columns={ImportColumns}
            notShowPagination
            scrollVertical
          />
        </TabPane>
        {contractFormConfig.isRetirementBusinessList ? (
          <TabPane
            forceRender
            tab={<AlertTab onError={tabErrors.writableRetire}>退休人员列表</AlertTab>}
            key={'6'}
          >
            <RowElementButton>
              <Button onClick={() => writableRetire.addRows({})}>新增</Button>
              <Button onClick={() => writableRetire.deleteRows(writableRetire.selectedSingleRow)}>
                删除
              </Button>
              {/* <Button onClick={() => onImportBtn('2')}>导入</Button> */}
              <ImportForm
                btnName="导入"
                downBtnName="下载模板"
                fileSuffix=".xls"
                ruleId="10702030"
                bizType="11"
                serviceName="slContractRetireeService"
                btnEnable={true}
                showImpHisBtn={false}
                showResultBtn={true}
                afterUpload={(res) => {
                  API.crm.contractManage.getSlContractRetireeList
                    .requests({ batchId: res })
                    .then((data: any) => {
                      const list = data?.list?.map((res) => {
                        return {
                          ...res,
                          idCardNum: res?.IDCARDNUM,
                          empName: res?.EMPNAME,
                          bz: res?.BZ,
                        };
                      });
                      if (data?.list.length > 0) writableRetire.setNewData(list);
                    });
                }}
                // afterUpload={afterUpload}
              />
            </RowElementButton>
            <Writable
              wriTable={writableRetire}
              service={serviceProductLine}
              columns={retireColumns}
              notShowPagination
              editable
              notShowRowSelection
              scrollVertical
            />
          </TabPane>
        ) : null}
      </Tabs>

      {contractFormConfig.isCommitApprove ? (
        <FormElement1 form={approveOpinionForm} initialValues={currentContract}>
          <EnumerateFields
            outerForm={approveOpinionForm}
            colNumber={1}
            formColumns={approveOpinionFormColumns}
          />
        </FormElement1>
      ) : null}
      {/* <QuotationUpdateForm
        title={'查看报价单'}
        width={1200}
        initialValues={quotationDetail[0]}
        visible={quotationViewModal[0]}
        onCancel={() => quotationViewModal[1](false)}
      /> */}
      <QuotationAddForm
        width={1200}
        modal={quotationViewModal}
        // listOptions={optionFunc}
        initialValues={quotationDetail[0]}
      />
      <AddForm
        title="附件上传"
        visible={onShowImport}
        hideHandle={() => setOnShowImport(false)}
        submitHandle={importHandle}
        formColumns={importType === '1' ? ImportFormColumns : ImportFormColumns1}
        okText="上传"
      ></AddForm>
      <PreviewCodal
        fileUrl={previewUrl}
        modal={previewModal}
        fileType={previewFileType}
        file={previewFile}
      />
    </Codal>
  );
};

export { ContractForm };
