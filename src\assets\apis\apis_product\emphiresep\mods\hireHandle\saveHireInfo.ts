import * as defs from '../../baseClass';
import {
  fetch,
  RequestConfig,
  beforeFetch,
  afterFetch,
  checkResStatus,
  removeSpace,
} from '@/utils/request';

/**
     * @url /rhro-service-1.0/hireHandle/saveHireInfo
     * @desc 没有用的接口
没有用的接口
     * hasForm: false
     * hasBody: true
     */

export class Params {
  /** absPath */
  absPath?: string;
  /** accountId */
  accountId?: string;
  /** accountInfoId */
  accountInfoId?: string;
  /** add */
  add?: boolean;
  /** applyAvgIncome */
  applyAvgIncome?: string;
  /** associationStatus */
  associationStatus?: string;
  /** 银行账号 */
  bankAcct?: string;
  /** 批次号,用于备份 */
  batchId?: string;
  /** beginWorkDate */
  beginWorkDate?: string;
  /** 账单表别名,控制客户权限用 */
  billAlias?: string;
  /** birthday */
  birthday?: string;
  /** 财务大类 */
  bizCategory?: string;
  /** 业务类型,控制小合同权限用 */
  bizmanType?: string;
  /** bjResidenceNum */
  bjResidenceNum?: string;
  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias?: string;
  /** buyItems */
  buyItems?: string;
  /** city */
  city?: string;
  /** cityId */
  cityId?: string;
  /** 低代码对应表单数据ID */
  clcDataId?: string;
  /** 低代码对应模板ID */
  clcTemplateId?: string;
  /** 低代码对应指定版本模板ID */
  clcTemplateJsonId?: string;
  /** clientOperation */
  clientOperation?: number;
  /** flex是否行编号 */
  clientRowSeq?: number;
  /** flex是否选择 */
  clientSelected?: boolean;
  /** 合同表别名,控制合同权限用 */
  contractAlias?: string;
  /** 创建人 */
  createBy?: string;
  /** createBy2 */
  createBy2?: string;
  /** 创建日期 */
  createDt?: string;
  /** currentSalary */
  currentSalary?: string;
  /** custCode */
  custCode?: string;
  /** custId */
  custId?: string;
  /** custName */
  custName?: string;
  /** 客户表别名,控制客户权限用 */
  customerAlias?: string;
  /** del */
  del?: boolean;
  /** disabilityCardNum */
  disabilityCardNum?: string;
  /** 档案所在地 */
  documentLocation?: string;
  /** educationLevel */
  educationLevel?: string;
  /** educationLevelStr */
  educationLevelStr?: string;
  /** effectiveDate */
  effectiveDate?: string;
  /** email */
  email?: string;
  /** 紧急联系人 */
  emergencyContact?: string;
  /** 紧急联系人电话 */
  emergencyPhone?: string;
  /** empCode */
  empCode?: string;
  /** empHireSepId */
  empHireSepId?: string;
  /** empHiresepAddressId */
  empHiresepAddressId?: string;
  /** 主键 */
  empHiresepContactId?: string;
  /** empHiresepMainId */
  empHiresepMainId?: string;
  /** empHiresepShBankId */
  empHiresepShBankId?: string;
  /** empHiresepSocialId */
  empHiresepSocialId?: string;
  /** empId */
  empId?: string;
  /** empName */
  empName?: string;
  /** 人员类别 */
  empType?: string;
  /** 人员类别名称 */
  empTypeName?: string;
  /** empolymentCertCode */
  empolymentCertCode?: string;
  /** ethnic */
  ethnic?: string;
  /** ethnicStr */
  ethnicStr?: string;
  /** eventCode */
  eventCode?: string;
  /** 导入类型,扩充使用 */
  expType?: string;
  /** filterByAuthNum */
  filterByAuthNum?: string;
  /** 提供查询是做为排除条件使用 */
  filterId?: string;
  /** 拼接好的地址 */
  fullAddress?: string;
  /** funBtnActiveStr */
  funBtnActiveStr?: string;
  /** 公积金情况 1:新开户 2: 账号在封存办 3:账号在原单位 */
  fundInfo?: string;
  /** gender */
  gender?: string;
  /** genderStr */
  genderStr?: string;
  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch?: string;
  /** hasEmpolymentCert */
  hasEmpolymentCert?: string;
  /** hasFinanceSs */
  hasFinanceSs?: string;
  /** hasNanjingHealthIns */
  hasNanjingHealthIns?: string;
  /** hasPayLargeAmt */
  hasPayLargeAmt?: string;
  /** hasResidence */
  hasResidence?: string;
  /** hasRetireBook */
  hasRetireBook?: string;
  /** hasSpecialSick */
  hasSpecialSick?: string;
  /** hasSsCard */
  hasSsCard?: string;
  /** hasUnemployBenifit */
  hasUnemployBenifit?: string;
  /** healthStatus */
  healthStatus?: string;
  /** healthStatusStr */
  healthStatusStr?: string;
  /** hospital1Id */
  hospital1Id?: string;
  /** hospital2Id */
  hospital2Id?: string;
  /** hospital3Id */
  hospital3Id?: string;
  /** hospital4Id */
  hospital4Id?: string;
  /** hospital5Id */
  hospital5Id?: string;
  /** hospitalList */
  hospitalList?: Array<HiresepHospital>;
  /** 户口地址 */
  hukouAddress?: string;
  /** 户口所在市id */
  hukouCityId?: string;
  /** 户口所在市名 */
  hukouCityName?: string;
  /** 户口所在省id */
  hukouProvinceId?: string;
  /** 户口所在省名 */
  hukouProvinceName?: string;
  /** 户口性质 */
  hukouType?: string;
  /** 户口性质中文 */
  hukouTypeName?: string;
  /** hukouTypeStr */
  hukouTypeStr?: string;
  /** 户口邮编 */
  hukouZipCode?: string;
  /** idCardNum */
  idCardNum?: string;
  /** 暂时是身份证 */
  idCardType?: string;
  /** idCardValidEnd */
  idCardValidEnd?: string;
  /** idCardValidStart */
  idCardValidStart?: string;
  /** 图片,ImageBase64 */
  imageData?: string;
  /** imageName */
  imageName?: string;
  /** inId */
  inId?: string;
  /** isAgricultural */
  isAgricultural?: string;
  /** isAgriculturalMedical */
  isAgriculturalMedical?: string;
  /** isBankPaySs */
  isBankPaySs?: string;
  /** isBeijingPaid */
  isBeijingPaid?: string;
  /** 是否账单查询 */
  isBillQuery?: string;
  /** isChangchunBabyHealth */
  isChangchunBabyHealth?: string;
  /** isChangchunHealth */
  isChangchunHealth?: string;
  /** isChangchunStopSs */
  isChangchunStopSs?: string;
  /** flex是否变化 */
  isChanged?: boolean;
  /** 删除标记 */
  isDeleted?: string;
  /** isFirstSecurity */
  isFirstSecurity?: string;
  /** isFundLoans */
  isFundLoans?: string;
  /** isGraduatedPeriod */
  isGraduatedPeriod?: string;
  /** 是否为工伤期、医疗期、孕期、产期、哺乳期，0：否 1：是 */
  isInjuryPeriod?: string;
  /** isLegalPerson */
  isLegalPerson?: string;
  /** isMakeupStopSs */
  isMakeupStopSs?: string;
  /** 是否医疗期 */
  isMedicalPeriod?: string;
  /** isMovetoShenzhen */
  isMovetoShenzhen?: string;
  /** 近期是否有公积金贷款需求 */
  isNeedFundLoan?: string;
  /** isPaid */
  isPaid?: string;
  /** 是否孕期、产期、哺乳期 */
  isPregnancyPeriod?: string;
  /** isTaiyuanProvidentFund */
  isTaiyuanProvidentFund?: string;
  /** isTaiyuanSocialSecurity */
  isTaiyuanSocialSecurity?: string;
  /** isTransfered30 */
  isTransfered30?: string;
  /** 是否薪资查询 */
  isWageQuery?: string;
  /** 是否工伤期 */
  isWorkInjuryPeriod?: string;
  /** jobStatus */
  jobStatus?: string;
  /** jsonStr */
  jsonStr?: string;
  /** longtermWorkplace */
  longtermWorkplace?: string;
  /** marriageStatus */
  marriageStatus?: string;
  /** 婚姻状况中文 */
  marriageStatusName?: string;
  /** medicalInsuranceType */
  medicalInsuranceType?: string;
  /** 模拟人 */
  mimicBy?: string;
  /** mobilePhoneNum */
  mobilePhoneNum?: string;
  /** needTransferCtg */
  needTransferCtg?: string;
  /** nickName */
  nickName?: string;
  /** noChange */
  noChange?: boolean;
  /** 开户行名称 */
  openBankName?: string;
  /** oriDocPlace */
  oriDocPlace?: string;
  /** paidItems */
  paidItems?: string;
  /** parttimeJobName */
  parttimeJobName?: string;
  /** 个人账号 */
  personalAccount?: string;
  /** pfAccount */
  pfAccount?: string;
  /** pfNo12 */
  pfNo12?: string;
  /** pfSituation */
  pfSituation?: string;
  /** phoneNum */
  phoneNum?: string;
  /** politicalStatus */
  politicalStatus?: string;
  /** politicalStatusStr */
  politicalStatusStr?: string;
  /** 流程审批角色名字 */
  processAprRoleName?: string;
  /** 供应商集团权限添加 */
  providerIdAlias?: string;
  /** 代理人 */
  proxyBy?: string;
  /** prvdGroupIdAlias */
  prvdGroupIdAlias?: string;
  /** rejectReason */
  rejectReason?: string;
  /** relPath */
  relPath?: string;
  /** residentAddress */
  residentAddress?: string;
  /** residentCityId */
  residentCityId?: string;
  /** residentProvinceId */
  residentProvinceId?: string;
  /** residentZipCode */
  residentZipCode?: string;
  /** 卡纯代发人员,默认过滤 */
  restrictPure?: string;
  /** 卡权限 */
  restrictType?: string;
  /** retireBookExpiration */
  retireBookExpiration?: string;
  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth?: string;
  /** 获取前台勾选key的字符串 */
  selectKeyStr?: string;
  /** 是否单立户 */
  sfDlh?: string;
  /** sickName */
  sickName?: string;
  /** 社保情况 1:新开 2：转移 */
  socailStatus?: string;
  /** 社保类型 1:城保 2:三险 3:征地工非全险 */
  socailType?: string;
  /** specialIdentity */
  specialIdentity?: string;
  /** 数据来源 0：微信 1：HRO */
  src?: number;
  /** 数据来源中文 0：微信 1：HRO */
  srcStr?: string;
  /** ssNumber */
  ssNumber?: string;
  /** ssSituation */
  ssSituation?: string;
  /** ssStatus */
  ssStatus?: string;
  /** status */
  status?: string;
  /** statusStr */
  statusStr?: string;
  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias?: string;
  /** 小合同编号 */
  subcontractId?: string;
  /** 小合同名称 */
  subcontractName?: string;
  /** 修改人 */
  updateBy?: string;
  /** updateByName */
  updateByName?: string;
  /** 修改日期 */
  updateDt?: string;
  /** upt */
  upt?: boolean;
  /** 用户id,控制小合同权限用 */
  userId?: string;
  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias?: string;
  /** workExperience */
  workExperience?: string;
}

export const init = new defs.emphiresep.CommonResponse();
export const url = '/rhro-service-1.0/hireHandle/saveHireInfo:POST';
export const initialUrl = '/rhro-service-1.0/hireHandle/saveHireInfo';
export const cacheKey = '_hireHandle_saveHireInfo_POST';
export async function request(data: Params, options?: RequestConfig) {
  const reqUrl = `/rhro-service-1.0/hireHandle/saveHireInfo`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          reolve(res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
export async function requests(data: Params, options?: RequestConfig) {
  const reqUrl = `/rhro-service-1.0/hireHandle/saveHireInfo`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          if (checkResStatus(res)) return reolve(res ? res.data : res);
          reject(res ? res.message : res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
