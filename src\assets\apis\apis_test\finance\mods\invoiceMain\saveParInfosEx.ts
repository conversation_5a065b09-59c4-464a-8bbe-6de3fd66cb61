import * as defs from '../../baseClass';
import {
  fetch,
  RequestConfig,
  beforeFetch,
  afterFetch,
  checkResStatus,
  removeSpace,
} from '@/utils/request';

/**
     * @url /rhro-service-1.0/invoiceMain/saveParInfosEx
     * @desc 审批时保存发票摘要
审批时保存发票摘要
     * hasForm: false
     * hasBody: true
     */

export class Params {}

export const init = new defs.finance.CommonResponse();
export const url = '/rhro-service-1.0/invoiceMain/saveParInfosEx:POST';
export const initialUrl = '/rhro-service-1.0/invoiceMain/saveParInfosEx';
export const cacheKey = '_invoiceMain_saveParInfosEx_POST';
export async function request(
  data: defs.finance.ParInfoDTO,
  options?: RequestConfig,
) {
  const reqUrl = `/rhro-service-1.0/invoiceMain/saveParInfosEx`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          reolve(res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
export async function requests(
  data: defs.finance.ParInfoDTO,
  options?: RequestConfig,
) {
  const reqUrl = `/rhro-service-1.0/invoiceMain/saveParInfosEx`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          if (checkResStatus(res)) return reolve(res ? res.data : res);
          reject(res ? res.message : res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
