import * as defs from '../../baseClass';
import {
  fetch,
  RequestConfig,
  beforeFetch,
  afterFetch,
  checkResStatus,
  removeSpace,
} from '@/utils/request';

/**
     * @url /rhro-service-1.0/bigReduce/fsReduceEmp
     * @desc 手动提交HRO减员
手动提交HRO减员
     * hasForm: false
     * hasBody: true
     */

export class Params {}

export const init = [];
export const url = '/rhro-service-1.0/bigReduce/fsReduceEmp:POST';
export const initialUrl = '/rhro-service-1.0/bigReduce/fsReduceEmp';
export const cacheKey = '_bigReduce_fsReduceEmp_POST';
export async function request(data: Array<number>, options?: RequestConfig) {
  const reqUrl = `/rhro-service-1.0/bigReduce/fsReduceEmp`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          reolve(res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
export async function requests(data: Array<number>, options?: RequestConfig) {
  const reqUrl = `/rhro-service-1.0/bigReduce/fsReduceEmp`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          if (checkResStatus(res)) return reolve(res ? res.data : res);
          reject(res ? res.message : res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
