import React, { useEffect, useState } from 'react';
import { Form, Button, message, InputNumber } from 'antd';
import Codal from '@/components/Codal';
import { FormElement3 } from '@/components/Forms/FormLayouts';
import { EnumerateFields } from '@/components/CachedPage/EnumerateFields';
import { EditeFormProps } from '@/components/EditeForm';
import { msgErr, resError } from '@/utils/methods/message';
import { mapToSelectors } from '@/components/Selectors';
import { yesNoDataMap } from '@/utils/settings/basedata/comboManage';
import { validMap } from '@/utils/settings/empwelfare/businessProcessing';
import { GetBusinessNodeSelector } from '@/components/Selectors/BaseDataSelectors';
import TextArea from 'antd/lib/input/TextArea';

interface BusinessNodeFormProps {
  modal: [boolean, CallableFunction];
  listOptions: any;
  initialInfo?: any;
}
const service = API.welfaremanage.ebmBusinessCityConfig.insertOrUpdateBusinessCityNodeConfig;
const BusinessNodeForm: React.FC<BusinessNodeFormProps> = ({ modal, listOptions, initialInfo }) => {
  const [visible, setVisible] = modal;
  if (!visible) return null;

  const [form] = Form.useForm();
  const [isEdit, setIsEdit] = useState(false);
  const [isGovernFee, setIsGovernFee] = useState<string | undefined>(undefined);

  useEffect(() => {
    if (!visible) return;

    setIsEdit(false);
    form.resetFields();

    if (initialInfo && Object.keys(initialInfo).length > 0 && initialInfo.type !== 'add') {
      setIsEdit(true);
      form.setFieldsValue(initialInfo);
    }
  }, [visible, initialInfo, form]);

  const onSave = async () => {
    const values = await form.validateFields();

    const submitData = {
      ...initialInfo,
      ...values,
    };

    const res = await service.request(submitData);
    if (resError(res)) {
      msgErr(res?.message);
      return;
    }

    message.success(isEdit ? '修改成功' : '新增成功');

    setVisible(false);
    form.resetFields();
    // 刷新列表
    listOptions.request(listOptions.queries);
  };

  const renderButtons = () => [
    <Button key="cancel" onClick={() => setVisible(false)}>
      取消
    </Button>,
    <Button key="submit" type="primary" onClick={onSave}>
      确认
    </Button>,
  ];

  const formColumns: EditeFormProps[] = [
    {
      label: '业务顺序',
      fieldName: 'busCityNodeNum',
      inputRender: 'number',
    },
    {
      label: '业务节点',
      fieldName: 'busNodeConfigId',
      inputRender: () => (
        <GetBusinessNodeSelector
          params={{ busConfigId: initialInfo?.busConfigId }}
          placeholder="请选择业务节点"
          allowClear
        />
      ),
      rules: [{ required: true, message: '请选择业务节点' }],
    },
    {
      label: '是否收取资料',
      fieldName: 'isCollectMaterial',
      inputRender: () => mapToSelectors(yesNoDataMap),
      rules: [{ required: true, message: '请选择是否收取资料' }],
    },
    {
      label: '是否支付津贴待遇',
      fieldName: 'isAllowance',
      inputRender: () => mapToSelectors(yesNoDataMap),
      rules: [{ required: true, message: '请选择是否支付津贴待遇' }],
    },
    {
      label: '办理周期',
      fieldName: 'transactPeriod',
      inputRender: () => (
        <InputNumber
          min={1}
          step={1}
          precision={0}
          formatter={(value) => `${value}`.replace(/[^0-9]/g, '')}
          parser={(value: any) => value?.replace(/[^0-9]/g, '')}
          onChange={(value: any) => {
            if (value && !/^[1-9]\d*$/.test(value)) {
              return;
            }
          }}
        />
      ),
      rules: [{ required: true, message: '请输入办理周期' }],
    },
    {
      label: '是否有政府性收费',
      fieldName: 'isGovernFee',
      inputRender: () =>
        mapToSelectors(yesNoDataMap, { onChange: (value: any) => setIsGovernFee(value) }),
      rules: [{ required: true, message: '请选择是否有政府性收费' }],
    },
    {
      label: '政府性收费金额',
      fieldName: 'governFee',
      inputRender: () => <TextArea rows={3} maxLength={200} />,
      rules: isGovernFee === '1' ? [{ required: true, message: '请输入政府性收费金额' }] : [],
    },
    {
      label: '业务节点状态',
      fieldName: 'isValid',
      inputRender: () => mapToSelectors(validMap),
      rules: [{ required: true, message: '请选择业务节点状态' }],
    },
    {
      label: '是否被引用',
      fieldName: 'refStr',
      inputRender: 'string',
      inputProps: {
        disabled: true,
      },
    },
  ];

  return (
    <Codal
      title={isEdit ? '修改业务节点' : '新增业务节点'}
      visible={visible}
      checkEdit={false}
      footer={renderButtons()}
      onCancel={() => {
        setVisible(false);
        form.setFieldsValue({});
        form.resetFields();
      }}
      width="60%"
    >
      <FormElement3 form={form}>
        <EnumerateFields formColumns={formColumns} outerForm={form} colNumber={2} />
      </FormElement3>
    </Codal>
  );
};

export default BusinessNodeForm;
