import React, { useEffect, useState } from 'react';
import { CachedPage } from '@/components/CachedPage';
import { EditeFormProps } from '@/components/EditeForm';
import { Button, Form, Typography } from 'antd';
import { CustomerPop } from '@/components/StandardPop/CustomerPop';
import { WritableColumnProps } from '@/utils/writable/types';
import { WritableInstance } from '@/components/Writable';
import { AsyncButton } from '@/components/Forms/Confirm';
import { stdMonthFormatMoDash } from '@/utils/methods/times';
import { DateRange } from '@/components/DateRange4';
import { mapToSelectors, mapToSelectView } from '@/components/Selectors/FuncSelectors';
import { invoiceStatusType, verifyStatusType } from '@/utils/settings/finance/queryExBill';
import { BillDetailModal } from './BillDetailModal';
import NumericInput from '@/pages/Sales/Quotation/QuotationTempManage/NumericInput';

const CommercialInsuranceAR = () => {
  const [form] = Form.useForm();
  const [visible, setVisible] = useState(false);
  const [userListMap, setUserListMap] = useState<Map<number, string>>(new Map());
  const [currentRow, setCurrentRow] = useState<Record<any, any>>({});

  // 处理应收金额点击事件
  const handleReceivableAmountClick = (record: any) => {
    setCurrentRow(record);
    setVisible(true);
  };

  useEffect(() => {
    const getUserList = async () => {
      const params = {
        statementName: 'cc.getUserList',
      };
      const data = await API.basedata.baseDataCls.getDorpDownList.requests(params, { params });
      const map = new Map<number, string>();
      data.list.forEach((e: any) => map.set(+e.key, e.shortName));
      setUserListMap(map);
    };
    getUserList();
  }, []);

  // 表单配置
  const formColumns: EditeFormProps[] = [
    {
      label: '客户',
      fieldName: 'custId',
      inputRender: () => (
        <CustomerPop
          rowValue="custId-custName"
          keyMap={{ custId: 'custId', custName: 'custName' }}
        />
      ),
    },
    {
      label: '账单年月',
      fieldName: '',
      inputRender: () => (
        <DateRange
          fields={[
            { title: '账单年月起始月', dataIndex: 'billYmSt' },
            { title: '账单年月截止月', dataIndex: 'billYmEd' },
          ]}
          format={stdMonthFormatMoDash}
        />
      ),
    },
    {
      label: '财务应收年月',
      fieldName: '',
      inputRender: () => (
        <DateRange
          fields={[
            { title: '财务应收起始年月', dataIndex: 'finReceivableYmSt' },
            { title: '财务应收截止年月', dataIndex: 'finReceivableYmEd' },
          ]}
          format={stdMonthFormatMoDash}
        />
      ),
    },
    {
      label: '应收金额>=',
      fieldName: 'receivableAmtSt',
      inputRender: () => (
        <NumericInput
          maxDecimalPlaces={2}
          allowNegative={false}
          min={0}
          placeholder="请输入应收金额"
          style={{ width: '100%' }}
        />
      ),
      rules: [
        {
          validator: (_, value) => {
            if (!value) return Promise.resolve();
            const numValue = Number(value);
            if (isNaN(numValue) || numValue < 0) {
              return Promise.reject(new Error('请输入有效的应收金额数字'));
            }
            return Promise.resolve();
          },
        },
      ],
    },
    {
      label: '应收金额<=',
      fieldName: 'receivableAmtEd',
      inputRender: () => (
        <NumericInput
          maxDecimalPlaces={2}
          allowNegative={false}
          min={0}
          placeholder="请输入应收金额"
          style={{ width: '100%' }}
        />
      ),
      rules: [
        {
          validator: (_, value) => {
            if (!value) return Promise.resolve();
            const numValue = Number(value);
            if (isNaN(numValue) || numValue < 0) {
              return Promise.reject(new Error('请输入有效的应收金额数字'));
            }
            return Promise.resolve();
          },
        },
      ],
    },
    {
      label: '生成人',
      fieldName: 'createByName',
      inputRender: 'string',
      //   inputRender: () => (
      //     <BillCreatorSubSelectPop
      //       rowValue="RN-EMPID-REALNAME"
      //       keyMap={{
      //         RN: 'RN',
      //         creater: 'EMPID',
      //         REALNAME: 'REALNAME',
      //       }}
      //     />
      //   ),
    },
    {
      label: '核销状态',
      fieldName: 'verifyStatus',
      inputRender: () => mapToSelectors(verifyStatusType, { allowClear: true }),
    },
    {
      label: '开票状态',
      fieldName: 'invoiceStatus',
      inputRender: () => mapToSelectors(invoiceStatusType, { allowClear: true }),
    },
    {
      label: '同步时间>=',
      fieldName: 'createDtS',
      inputRender: 'date',
    },
    {
      label: '同步时间<=',
      fieldName: 'createDtE',
      inputRender: 'date',
    },
  ];

  // 表格列配置
  const columns: WritableColumnProps<any>[] = [
    { title: '客户编号', dataIndex: 'customerCode' },
    { title: '客户名称', dataIndex: 'custName' },
    { title: '账单名称', dataIndex: 'receivableTempltName' },
    { title: '签单分公司抬头名称', dataIndex: 'signBranchTitleName' },
    { title: '账单年月', dataIndex: 'billYm' },
    { title: '财务应收年月', dataIndex: 'finReceivableYm' },
    {
      title: '应收金额',
      dataIndex: 'receivableAmt',
      render: (value: any, record: any) => {
        if (value === null || value === undefined || value === '') {
          return '-';
        }
        const numValue = Number(value);
        return (
          <Typography.Link onClick={() => handleReceivableAmountClick(record)}>
            {numValue}
          </Typography.Link>
        );
      },
    },
    {
      title: '生成人',
      dataIndex: 'createByName',
      render: (value) => mapToSelectView(userListMap, value),
    },
    { title: '同步时间', dataIndex: 'createDt' },
    { title: '核销状态', dataIndex: 'verifyStatus' },
    {
      title: '开票状态',
      dataIndex: 'invoiceStatus',
      render: (value) => mapToSelectView(invoiceStatusType, value),
    },
  ];

  const renderButtons = (options: WritableInstance) => (
    <>
      <Button type="primary" htmlType="submit">
        查询
      </Button>
      <AsyncButton
        onClick={() => {
          options.handleExport(
            { service: API.emphiresep.ciReceivable.exportFile },
            {
              columns,
              condition: { ...options.queries },
              fileName: '商保应收查询.xlsx',
            },
          );
        }}
      >
        导出
      </AsyncButton>
    </>
  );

  return (
    <>
      <CachedPage
        service={API.emphiresep.ciReceivable.queryCiReceivablePage}
        formColumns={formColumns}
        columns={columns}
        renderButtons={renderButtons}
        form={form}
        handleQueries={(queries) => {
          queries.ciType = '2';
          queries.customerId = queries.custId;
          return queries;
        }}
        notShowRowSelection
      />
      <BillDetailModal modal={[visible, setVisible]} currentRow={currentRow} />
    </>
  );
};

export default CommercialInsuranceAR;
