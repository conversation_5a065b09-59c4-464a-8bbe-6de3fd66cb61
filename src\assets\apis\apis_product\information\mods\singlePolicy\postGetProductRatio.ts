import * as defs from '../../baseClass';
import {
  fetch,
  RequestConfig,
  beforeFetch,
  afterFetch,
  checkResStatus,
  removeSpace,
} from '@/utils/request';

/**
     * @url /rhro-service-1.0/singlePolicy/getProductRatio
     * @desc 公积金，补充公积金比例id
公积金，补充公积金比例id
     * hasForm: false
     * hasBody: true
     */

export class Params {
  /** comboId */
  comboId: number;
  /** productTypeId */
  productTypeId: number;
  /** trialMonth */
  trialMonth: string;
}

export const init = new defs.information.Result();
export const url = '/rhro-service-1.0/singlePolicy/getProductRatio:POST';
export const initialUrl = '/rhro-service-1.0/singlePolicy/getProductRatio';
export const cacheKey = '_singlePolicy_getProductRatio_POST';
export async function request(data: Params, options?: RequestConfig) {
  const reqUrl = `/rhro-service-1.0/singlePolicy/getProductRatio`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          reolve(res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
export async function requests(data: Params, options?: RequestConfig) {
  const reqUrl = `/rhro-service-1.0/singlePolicy/getProductRatio`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          if (checkResStatus(res)) return reolve(res ? res.data : res);
          reject(res ? res.message : res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
