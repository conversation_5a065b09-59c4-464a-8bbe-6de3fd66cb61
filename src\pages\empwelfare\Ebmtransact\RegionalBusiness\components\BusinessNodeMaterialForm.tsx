/*
 * @Author: tjj
 * @Email: <EMAIL>
 * @Date: 2025-09-04 11:09:50
 * @LastAuthor: tjj
 * @message:
 */
import React, { useEffect, useState } from 'react';
import { Form, Button, message } from 'antd';
import Codal from '@/components/Codal';
import { FormElement3 } from '@/components/Forms/FormLayouts';
import { EnumerateFields } from '@/components/CachedPage/EnumerateFields';
import { EditeFormProps } from '@/components/EditeForm';
import { msgErr, resError } from '@/utils/methods/message';
import { mapToSelectors } from '@/components/Selectors';
import { yesNoDataMap } from '@/utils/settings/basedata/comboManage';
import { signatoryPartyMap } from '@/utils/settings/empwelfare/businessProcessing';
import { GetCityNodeMaterialSelector } from '@/components/Selectors/BaseDataSelectors';
import { UploadFile } from 'antd/lib/upload/interface';

interface BusinessNodeMaterialFormProps {
  modal: [boolean, CallableFunction];
  listOptions: any;
  initialInfo?: any;
}

const service = API.welfaremanage.ebmBusinessCityConfig.insertOrUpdateBusinessCnMaterial;
const BusinessNodeMaterialForm: React.FC<BusinessNodeMaterialFormProps> = ({
  modal,
  listOptions,
  initialInfo,
}) => {
  const [visible, setVisible] = modal;
  if (!visible) return null;

  const [form] = Form.useForm();
  const [isEdit, setIsEdit] = useState(false);

  useEffect(() => {
    if (!visible) return;
    setIsEdit(false);
    form.resetFields();

    if (initialInfo && Object.keys(initialInfo).length > 0 && initialInfo.type !== 'add') {
      setIsEdit(true);
      form.setFieldsValue(initialInfo);
    }
  }, [visible, initialInfo, form]);

  const onSave = async () => {
    const values = await form.validateFields();
    // 确保包含业务内容ID
    const submitData = {
      ...initialInfo,
      ...values,
    };

    const res = await service.request(submitData);

    if (resError(res)) {
      msgErr(res?.message);
      return;
    }

    message.success(isEdit ? '修改成功' : '新增成功');

    setVisible(false);
    form.resetFields();
    // 刷新列表，传递当前的业务内容ID
    listOptions.request(listOptions.queries);
  };

  const renderButtons = () => [
    <Button key="cancel" onClick={() => setVisible(false)}>
      取消
    </Button>,
    <Button key="submit" type="primary" onClick={onSave}>
      确认
    </Button>,
  ];

  const formColumns: EditeFormProps[] = [
    {
      label: '材料名称',
      fieldName: 'materialsId',
      inputRender: () => <GetCityNodeMaterialSelector placeholder="请选择材料名称" allowClear />,
      rules: [{ required: true, message: '请输入材料名称' }],
    },
    {
      label: '是否原件',
      fieldName: 'isOriginal',
      inputRender: () => mapToSelectors(yesNoDataMap),
    },
    {
      label: '材料数量',
      fieldName: 'materialNum',
      inputRender: 'number',
      rules: [{ required: true, message: '请输入材料数量' }],
    },
    {
      label: '是否返还材料',
      fieldName: 'isReturnMaterial',
      inputRender: () => mapToSelectors(yesNoDataMap),
      rules: [{ required: true, message: '请选择是否返还材料' }],
    },
    {
      label: '用印签字方',
      fieldName: 'signatoryParty',
      inputRender: () => mapToSelectors(signatoryPartyMap),
      rules: [{ required: true, message: '请选择用印签字方' }],
    },
    {
      label: '材料模板',
      fieldName: 'materialFileId',
      inputProps: {
        maxCount: 1,
        fileName: 'materialFileName',
        bizType: '30506200',
        showUploadList: { showRemoveIcon: false },
      },
      inputRender: 'upload',
    },
  ];

  return (
    <Codal
      title={isEdit ? '修改节点材料' : '新增节点材料'}
      visible={visible}
      checkEdit={false}
      footer={renderButtons()}
      onCancel={() => {
        setVisible(false);
        form.setFieldsValue({});
        form.resetFields();
      }}
      width="60%"
    >
      <FormElement3 form={form}>
        <EnumerateFields formColumns={formColumns} outerForm={form} colNumber={2} />
      </FormElement3>
    </Codal>
  );
};

export default BusinessNodeMaterialForm;
