class AreaCodeVO {
  /** 地区码 */
  areaCode = undefined;

  /** 地区id */
  areaId = undefined;

  /** 地区名称 */
  areaName = '';

  /** 地区类型 */
  areaType = undefined;

  /** 版本 */
  version = undefined;
}

class BaseEntity {
  /** add */
  add = false;

  /** 年度社平工资 */
  averageWageSociety = undefined;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 残障金收取定值 */
  disabilityCollectFix = undefined;

  /** 残障金收取比例% */
  disabilityCollectRatio = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** 公积金上限 */
  housingFundToplimit = undefined;

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** 最低工资 */
  minimunWage = undefined;

  /** noChange */
  noChange = false;

  /** 工会经费收取定值 */
  outLayCollectFix = undefined;

  /** 工会经费收取比例% */
  outLayCollectRatio = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';

  /** 扣缴义务人ID */
  withholdAgentId = undefined;

  /** 扣缴义务人名称 */
  withholdAgentName = '';

  /** 扣缴义务人限额ID */
  withholdAgentNormId = undefined;

  /** 扣缴义务人类型 1：大户 2：外部供应商 3:单立户 */
  withholdAgentType = undefined;

  /** 扣缴义务人类型 1：大户 2：外部供应商 3:单立户（显示） */
  withholdAgentTypeName = '';
}

class BatchSaveOrUpdateAreaCodeVO {
  /** 新增地区码集合 */
  insertList = [];

  /** 更新地区码集合 */
  updateList = [];
}

class BdPolicyBusnameClass {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** 编号 */
  busnameClassCode = '';

  /** 主键 */
  busnameClassId = '';

  /** 业务项目名称 */
  busnameClassName = '';

  /** 所属类型 */
  categoryId = '';

  /** 所属类型名称 */
  categoryName = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class BdPolicyBusnameSubtype {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** 小类编号 */
  busnameSubtypeCode = '';

  /** 主键 */
  busnameSubtypeId = '';

  /** 小类名称 */
  busnameSubtypeName = '';

  /** 大类名称ID */
  busnameTypeId = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 屏蔽客户数 */
  exCustNum = '';

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 客户端是否显示0 不显示 1显示 */
  isClientShow = '';

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 手机端是否显示0 不显示 1显示 */
  isWechartShow = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class BdPolicyBusnameType {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** 项目名称ID */
  busnameClassId = '';

  /** 大类编号 */
  busnameTypeCode = '';

  /** 主键 */
  busnameTypeId = '';

  /** 大类名称 */
  busnameTypeName = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class BdTaxRuleQuery {
  /** 调基任务号 */
  adjTaskId = '';

  /** 基础数据编码 */
  baseDataCode = '';

  /** 批次id */
  batchId = '';

  /** 列名 */
  columns = '';

  /** 薪资批次id */
  currentRecordId = '';

  /** 客户id */
  custId = '';

  /** 结束时间 */
  endDt = '';

  /** endIndex */
  endIndex = undefined;

  /** 错误类型 */
  errorType = '';

  /** 导入结果 0：成功 1：失败 */
  impTag = '';

  /** 导入人姓名 */
  impUserName = '';

  /** 是否有效 */
  isEffective = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** 导入批次中做详细筛选的id值 */
  recordId = '';

  /** 规则id */
  ruleId = '';

  /** 规则名称 */
  ruleName = '';

  /** 前端传入的参数，判断不同的情况查询语句 */
  selByAuth = '';

  /** 开始时间 */
  startDt = '';

  /** startIndex */
  startIndex = undefined;

  /** 表名 */
  tableName = '';

  /** 类型id */
  typeId = '';

  /** 薪资类别ID */
  wageClassId = '';

  /** withholdAgentId */
  withholdAgentId = '';

  /** withholdAgentType */
  withholdAgentType = '';
}

class BusinessSubType {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** busSubtypeId */
  busSubtypeId = '';

  /** busSubtypeName */
  busSubtypeName = '';

  /** busTypeId */
  busTypeId = '';

  /** busTypeName */
  busTypeName = '';

  /** 项目名称id */
  busnameClassId = '';

  /** 小类名称id */
  busnameSubtypeId = '';

  /** 大类名称id */
  busnameTypeId = '';

  /** 所属类型 */
  category = '';

  /** 人员类型列表 */
  categoryNames = '';

  /** cityId */
  cityId = '';

  /** cityName */
  cityName = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 企业空表模板 */
  eBlankTemplatePath = '';

  /** 企业空表模板名 */
  eBlankTemplatePathName = '';

  /** 企业样本模板 */
  eSampleTemplatePath = '';

  /** 企业样本模板名 */
  eSampleTemplatePathName = '';

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** 是否可预约 */
  isBooked = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** isRef */
  isRef = '';

  /** 是否有效 */
  isValid = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 个人空表模板 */
  pBlankTemplatePath = '';

  /** 个人空表模板名 */
  pBlankTemplatePathName = '';

  /** 个人样本模板 */
  pSampleTemplatePath = '';

  /** 个人样本模板名 */
  pSampleTemplatePathName = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 人员类型列表id */
  personCategoryIds = '';

  /** 人员类型列表名称 */
  personCategoryNames = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** remark */
  remark = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';

  /** whetherPay */
  whetherPay = '';
}

class BusinessSubTypeQuery {
  /** busSubtypeId */
  busSubtypeId = '';

  /** busSubtypeName */
  busSubtypeName = '';

  /** busTypeId */
  busTypeId = '';

  /** busTypeName */
  busTypeName = '';

  /** 业务项目名称id */
  busnameClassId = '';

  /** 大类名称id */
  busnameTypeId = '';

  /** cityId */
  cityId = '';

  /** cityName */
  cityName = '';

  /** endIndex */
  endIndex = undefined;

  /** isRef */
  isRef = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** remark */
  remark = '';

  /** startIndex */
  startIndex = undefined;

  /** whetherPay */
  whetherPay = '';
}

class BusinessType {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** busTypeId */
  busTypeId = '';

  /** busTypeName */
  busTypeName = '';

  /** 业务项目名称id */
  busnameClassId = '';

  /** 业务项目名称 */
  busnameClassName = '';

  /** 业务大类名称id */
  busnameTypeId = '';

  /** categoryId */
  categoryId = '';

  /** categoryName */
  categoryName = '';

  /** cityId */
  cityId = '';

  /** cityName */
  cityName = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** doRequire */
  doRequire = '';

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否有效 */
  isValid = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** orderRequire */
  orderRequire = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** remark */
  remark = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** ssGroupId */
  ssGroupId = '';

  /** ssGroupName */
  ssGroupName = '';

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** type */
  type = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class BusinessTypeQuery {
  /** busTypeId */
  busTypeId = '';

  /** busTypeName */
  busTypeName = '';

  /** 业务项目名称id */
  busnameClassId = '';

  /** 业务大类名称id */
  busnameTypeId = '';

  /** categoryId */
  categoryId = '';

  /** categoryName */
  categoryName = '';

  /** cityId */
  cityId = '';

  /** cityName */
  cityName = '';

  /** doRequire */
  doRequire = '';

  /** endIndex */
  endIndex = undefined;

  /** orderRequire */
  orderRequire = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** remark */
  remark = '';

  /** ssGroupId */
  ssGroupId = '';

  /** ssGroupName */
  ssGroupName = '';

  /** startIndex */
  startIndex = undefined;

  /** type */
  type = '';
}

class BusinessTypeVO {
  /** businessType */
  businessType = new BusinessType();

  /** subList */
  subList = [];
}

class CommonQuery {
  /** 城市id */
  cityId = '';

  /** 客户id */
  custId = '';

  /** 部门级别 */
  departmentGrade = '';

  /** 部门内部编号 */
  departmentInternalCode = '';

  /** 部门名称 */
  departmentName = '';

  /** endIndex */
  endIndex = undefined;

  /** 城市id */
  fileProviderName = '';

  /** 大区id */
  governingArea = '';

  /** 大区id */
  governingAreaId = '';

  /** 分公司id */
  governingBranch = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** 供应商类型 */
  providerType = '';

  /** 供应商集团id */
  prvdGroupId = '';

  /** startIndex */
  startIndex = undefined;
}

class CommonResponse {
  /** bizCode */
  bizCode = undefined;

  /** code */
  code = undefined;

  /** data */
  data = new Page();

  /** message */
  message = '';

  /** t */
  t = new Page();
}

class DisableBenefitRatio {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** benefitRatio */
  benefitRatio = '';

  /** benefitRatioId */
  benefitRatioId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** cityId */
  cityId = '';

  /** cityName */
  cityName = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** 创建日期 */
  createDt = '';

  /** ceateTime */
  createTime = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** ids */
  ids = [];

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** realName */
  realName = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class DropdownList {
  /** 业务大类类型 */
  btType = '';

  /** chargeRate */
  chargeRate = '';

  /** cityId */
  cityId = '';

  /** cityIdForParty */
  cityIdForParty = '';

  /** cityName */
  cityName = '';

  /** contractAvgAmt */
  contractAvgAmt = '';

  /** contractHeadcount */
  contractHeadcount = '';

  /** contractName */
  contractName = '';

  /** contractSubType */
  contractSubType = '';

  /** contractSubTypeName */
  contractSubTypeName = '';

  /** contractType */
  contractType = '';

  /** contractTypeName */
  contractTypeName = '';

  /** currentSalesName */
  currentSalesName = '';

  /** departmentName */
  departmentName = '';

  /** englishTermName */
  englishTermName = '';

  /** exFeeMonth */
  exFeeMonth = '';

  /** 供应商收费模板 */
  exFeeTempltId = '';

  /** governingArea */
  governingArea = '';

  /** 所属大区 */
  governingAreaId = '';

  /** governingBranch */
  governingBranch = '';

  /** 所属分公司 */
  governingBranchId = '';

  /** groupType */
  groupType = '';

  /** 主键 */
  key = '';

  /** liabilityCsName */
  liabilityCsName = '';

  /** 全称 */
  name = '';

  /** 拼音码 */
  pinYinCode = '';

  /** productLineId */
  productLineId = '';

  /** 供应商类型1内部2外部 */
  providerType = '';

  /** 保留名字1 */
  reserveName1 = '';

  /** 保留名字2 */
  reserveName2 = '';

  /** 储备对象,用于查询下拉框的时候,可能还需要查询其他列的值 */
  reserveObj = '';

  /** 缩写名 */
  shortName = '';

  /** signBrachTitleId */
  signBrachTitleId = '';

  /** signBranchTitleName */
  signBranchTitleName = '';

  /** 社保组ID */
  ssGroupId = '';

  /** svcSubtypeName */
  svcSubtypeName = '';

  /** svcTypeName */
  svcTypeName = '';
}

class EmpMaintainPayEntity {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** companyCode */
  companyCode = '';

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** custCode */
  custCode = '';

  /** custId */
  custId = '';

  /** custName */
  custName = '';

  /** custPayEntityId */
  custPayEntityId = '';

  /** custPayEntityName */
  custPayEntityName = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** departmentName */
  departmentName = '';

  /** employerMaintainId */
  employerMaintainId = '';

  /** employerName */
  employerName = '';

  /** 用工方简称 */
  employerShortName = '';

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** hasStamp */
  hasStamp = undefined;

  /** hasStampStr */
  hasStampStr = '';

  /** ids */
  ids = [];

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class ExportQuery {
  /** 查询条件 */
  condition = undefined;

  /** 表头字段列表 */
  fieldArr = [];

  /** 表头字段中文拼接 */
  headStr = '';

  /** 表头字段类型列表 */
  typeArr = [];
}

class FileProviderQuery {
  /** 所属分公司ID */
  departmentId = undefined;

  /** endIndex */
  endIndex = undefined;

  /** 档案供应商名称 */
  fileProviderName = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** startIndex */
  startIndex = undefined;
}

class FilterEntity {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** categoryId */
  categoryId = '';

  /** categoryName */
  categoryName = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** companyName */
  companyName = '';

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** custName */
  custName = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** fileType */
  fileType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** groupName */
  groupName = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** materialType */
  materialType = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** sealDes */
  sealDes = '';

  /** sealType */
  sealType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';

  /** versionType */
  versionType = '';
}

class GlobalResult {
  /** code */
  code = '';

  /** data */
  data = [];

  /** message */
  message = '';
}

class ImMedicalDTO {
  /** batchId */
  batchId = '';

  /** cityId */
  cityId = '';

  /** cityName */
  cityName = '';

  /** 创建人 */
  createByName = '';

  /** effectiveDt */
  effectiveDt = '';

  /** effectiveStatus */
  effectiveStatus = '';

  /** fileId */
  fileId = '';

  /** fileName */
  fileName = '';

  /** importId */
  importId = '';

  /** importTaskName */
  importTaskName = '';

  /** remark */
  remark = '';
}

class ImMedicalQuery {
  /** batchId */
  batchId = '';

  /** cityId */
  cityId = '';

  /** cityName */
  cityName = '';

  /** 创建人 */
  createByName = '';

  /** effectiveDt */
  effectiveDt = '';

  /** effectiveStatus */
  effectiveStatus = '';

  /** endIndex */
  endIndex = undefined;

  /** fileId */
  fileId = '';

  /** fileName */
  fileName = '';

  /** importId */
  importId = '';

  /** importTaskName */
  importTaskName = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** remark */
  remark = '';

  /** startIndex */
  startIndex = undefined;
}

class JindieOrg {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** cfCompanyLevel */
  cfCompanyLevel = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 金蝶同步标记 0未同步 1已同步 */
  isSyncJindie = '';

  /** isValid */
  isValid = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** jindieOrgId */
  jindieOrgId = '';

  /** jindieOrgName */
  jindieOrgName = '';

  /** jindieOrgType */
  jindieOrgType = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 不含税单价 */
  priceNoTax = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** superOrgId */
  superOrgId = '';

  /** 纳税资质 1一般纳税人2简易计税3小规模纳税人 */
  taxAptitude = '';

  /** 纳税资质（显示） */
  taxAptitudeName = '';

  /** 税率 bd_base_data type=614 对应数据 */
  taxRate = '';

  /** 税率 （显示） */
  taxRateName = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class JindieOrgQuery {
  /** endIndex */
  endIndex = undefined;

  /** isValid */
  isValid = '';

  /** jindieOrgId */
  jindieOrgId = '';

  /** jindieOrgName */
  jindieOrgName = '';

  /** jindieOrgType */
  jindieOrgType = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** startIndex */
  startIndex = undefined;

  /** superOrgId */
  superOrgId = '';
}

class Map {}

class Materials {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** branchName */
  branchName = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** createName */
  createName = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** isOriginal */
  isOriginal = '';

  /** isRef */
  isRef = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** materialsId */
  materialsId = '';

  /** materialsName */
  materialsName = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** nremark */
  nremark = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** remark */
  remark = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class MaterialsPackage {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** busSubtypeId */
  busSubtypeId = '';

  /** busTypeId */
  busTypeId = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否是单位办理材料1 是 0否 */
  isEMaterial = '';

  /** isOriginal */
  isOriginal = '';

  /** 是否是个人办理材料1 是 0否 */
  isPMaterial = '';

  /** isReturn */
  isReturn = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** materialsAccount */
  materialsAccount = '';

  /** materialsId */
  materialsId = '';

  /** materialsName */
  materialsName = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** packageId */
  packageId = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class MaterialsPackageQuery {
  /** busSubtypeId */
  busSubtypeId = '';

  /** busTypeId */
  busTypeId = '';

  /** endIndex */
  endIndex = undefined;

  /** isOriginal */
  isOriginal = '';

  /** isReturn */
  isReturn = '';

  /** materialsAccount */
  materialsAccount = '';

  /** materialsId */
  materialsId = '';

  /** materialsName */
  materialsName = '';

  /** packageId */
  packageId = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** startIndex */
  startIndex = undefined;
}

class MaterialsQuery {
  /** branchName */
  branchName = '';

  /** createName */
  createName = '';

  /** endIndex */
  endIndex = undefined;

  /** isOriginal */
  isOriginal = '';

  /** isRef */
  isRef = '';

  /** materialsId */
  materialsId = '';

  /** materialsName */
  materialsName = '';

  /** nremark */
  nremark = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** remark */
  remark = '';

  /** startIndex */
  startIndex = undefined;
}

class MedicalIns {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** cityId */
  cityId = '';

  /** cityName */
  cityName = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** countyName */
  countyName = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** endIndex */
  endIndex = undefined;

  /** errorInfo */
  errorInfo = '';

  /** errorType */
  errorType = '';

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** hosCode */
  hosCode = '';

  /** hosId */
  hosId = '';

  /** hosLevel */
  hosLevel = '';

  /** hosName */
  hosName = '';

  /** hosType */
  hosType = '';

  /** impTag */
  impTag = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** lineNum */
  lineNum = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class MedicalInsDTO {
  /** cityId */
  cityId = '';

  /** cityName */
  cityName = '';

  /** countyName */
  countyName = '';

  /** errorInfo */
  errorInfo = '';

  /** errorType */
  errorType = '';

  /** hosCode */
  hosCode = '';

  /** hosId */
  hosId = '';

  /** hosLevel */
  hosLevel = '';

  /** hosName */
  hosName = '';

  /** hosType */
  hosType = '';

  /** impTag */
  impTag = '';

  /** lineNum */
  lineNum = '';
}

class MedicalInsVO {
  /** ids */
  ids = [];

  /** 医疗机构维护实体 */
  medicalInsDTO = new MedicalInsDTO();

  /** 医疗机构维护集合 */
  medicalInsDTOList = [];
}

class Page {
  /** currentPage */
  currentPage = undefined;

  /** currentPageNo */
  currentPageNo = undefined;

  /** data */
  data = [];

  /** pageSize */
  pageSize = undefined;

  /** result */
  result = [];

  /** start */
  start = undefined;

  /** totalCount */
  totalCount = undefined;

  /** totalPage */
  totalPage = undefined;

  /** totalPageCount */
  totalPageCount = undefined;
}

class PayAccountSyncQuery {
  /** 分行名 */
  bankBranch = '';

  /** 所属银行id */
  bankBranchName = '';

  /** 所属银行id */
  bankId = undefined;

  /** 开户名 */
  bankName = '';

  /** 是否启用,0 否 1是 */
  enabledState = undefined;

  /** endIndex */
  endIndex = undefined;

  /** 是否已同步,0否1 是 */
  isSync = undefined;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** 支付地id */
  payPlace = undefined;

  /** 支付地抬头 */
  payTitleStr = '';

  /** startIndex */
  startIndex = undefined;
}

class ServicePointCustDTO {
  /** 增员材料 */
  addEmpMaterial = '';

  /** 大区 */
  area = '';

  /** 接单方id */
  assigneeProviderId = '';

  /** 账单收费规则:1 预付;2每月付 */
  billFeeRule = '';

  /** 城市ID */
  cityId = '';

  /** cityName */
  cityName = '';

  /** 联系人(客服) */
  contactName = '';

  /** 联系电话 */
  contactTel = '';

  /** 客户编号 */
  custCode = '';

  /** 英文名称 */
  custEnglishName = '';

  /** 客户Id */
  custId = '';

  /** 客户名称 */
  custName = '';

  /** 客户简称 */
  custShortName = '';

  /** 离职补差规则 */
  dismissMakeupRule = '';

  /** 离职补差起始月 */
  dismissMakeupSatartMon = '';

  /** 是否有效 */
  isDeleted = '';

  /** 是否离职补差 */
  isDismissMakeup = '';

  /** 大户所在区 */
  largeAccountArea = '';

  /** 机构类别:1 自营分公司; 2 供应商 */
  organizationType = '';

  /** 补交材料 */
  payInbackMaterial = '';

  /** provinceName */
  provinceName = '';

  /** 减员材料 */
  reduceEmpMaterial = '';

  /** 备注 */
  remark = '';

  /** 网点服务名称(接单方) */
  serviceAssigneeName = '';

  /** 服务网点标识: 1 优选,2 备选,3 只减不增,4 仅操作指定客户,5 终止合作 */
  serviceBranchFlag = '';

  /** serviceCustId */
  serviceCustId = '';

  /** 网点服务名称(集团) */
  serviceGroupName = '';

  /** 主键 */
  servicePointId = '';

  /** 公积金申报频率 */
  sfApplicationFrequency = '';

  /** 公积基金中心当月可操作时间段 */
  sfOperateTime = '';

  /** 当地单立户可操作区县 */
  singleAccountArea = '';

  /** 社保申报频率 */
  ssApplicationFrequency = '';

  /** 社保当月可操作时间段 */
  ssOperateTime = '';
}

class ServicePointDTO {
  /** 增员材料 */
  addEmpMaterial = '';

  /** 大区 */
  area = '';

  /** 接单方id */
  assigneeProviderId = '';

  /** 账单收费规则:1 预付;2每月付 */
  billFeeRule = '';

  /** 城市ID */
  cityId = '';

  /** cityLevel */
  cityLevel = '';

  /** cityLevelCN */
  cityLevelCN = '';

  /** cityName */
  cityName = '';

  /** 联系人(客服) */
  contactName = '';

  /** 联系电话 */
  contactTel = '';

  /** 离职补差规则 */
  dismissMakeupRule = '';

  /** 离职补差起始月 */
  dismissMakeupSatartMon = '';

  /** 是否有效 */
  isDeleted = '';

  /** 是否离职补差 */
  isDismissMakeup = '';

  /** 大户所在区 */
  largeAccountArea = '';

  /** 机构类别:1 自营分公司; 2 供应商 */
  organizationType = '';

  /** 补交材料 */
  payInbackMaterial = '';

  /** provinceName */
  provinceName = '';

  /** 减员材料 */
  reduceEmpMaterial = '';

  /** 备注 */
  remark = '';

  /** 网点服务名称(接单方) */
  serviceAssigneeName = '';

  /** 服务网点标识: 1 优选,2 备选,3 只减不增,4 仅操作指定客户,5 终止合作 */
  serviceBranchFlag = '';

  /** 网点服务名称(集团) */
  serviceGroupName = '';

  /** 主键 */
  servicePointId = '';

  /** 公积金申报频率 */
  sfApplicationFrequency = '';

  /** 公积基金中心当月可操作时间段 */
  sfOperateTime = '';

  /** 当地单立户可操作区县 */
  singleAccountArea = '';

  /** 社保申报频率 */
  ssApplicationFrequency = '';

  /** 社保当月可操作时间段 */
  ssOperateTime = '';
}

class SpecialBranchTitle {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** branchId */
  branchId = undefined;

  /** branchName */
  branchName = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** isValid */
  isValid = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** signBranchInnerId */
  signBranchInnerId = '';

  /** signBranchName */
  signBranchName = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** titleId */
  titleId = undefined;

  /** titleName */
  titleName = '';

  /** titleSpecialId */
  titleSpecialId = undefined;

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class SpecialBranchTitleQuery {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** branchId */
  branchId = undefined;

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** isValid */
  isValid = undefined;

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** signBranchId */
  signBranchId = undefined;

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** titleName */
  titleName = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class SysBranchTitle {
  /** add */
  add = false;

  /** bankNameAcct */
  bankNameAcct = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** branchId */
  branchId = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** checkTitle */
  checkTitle = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** contactAddress */
  contactAddress = '';

  /** contactTel */
  contactTel = '';

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** departName */
  departName = '';

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** isValid */
  isValid = '';

  /** isValidStr */
  isValidStr = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** jindieOrgId */
  jindieOrgId = '';

  /** memo */
  memo = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** taxpayerIdentifie */
  taxpayerIdentifie = '';

  /** titleCode */
  titleCode = '';

  /** titleId */
  titleId = '';

  /** titleName */
  titleName = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class SysBranchTitleBilling {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 金税盘号 */
  billingGold = '';

  /** billingId */
  billingId = '';

  /** 开票机号 */
  billingNum = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** remark */
  remark = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** titleId */
  titleId = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class SysBranchTitleParItem {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 摘要名称 */
  itemName = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** titleId */
  titleId = '';

  /** titleParItemId */
  titleParItemId = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class SysBranchTitleQuery {
  /** add */
  add = false;

  /** areaId */
  areaId = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** branchId */
  branchId = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** departName */
  departName = '';

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** isValid */
  isValid = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** jindieOrgId */
  jindieOrgId = '';

  /** memo */
  memo = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** titleCode */
  titleCode = '';

  /** titleId */
  titleId = '';

  /** titleName */
  titleName = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class SysBranchTitleVO {
  /** addList */
  addList = [];

  /** addListPar */
  addListPar = [];

  /** uptList */
  uptList = [];

  /** uptListPar */
  uptListPar = [];
}

class TaxPayerBenefit {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 唯一号 */
  empCode = '';

  /** 姓名 */
  empName = '';

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** 证照号码 */
  idCardNum = '';

  /** 身份证件类型：1.身份证2.护照3.军官证4.香港身份证5.双派 */
  idCardType = undefined;

  /** 身份证件类型：1.身份证2.护照3.军官证4.香港身份证5.双派 */
  idCardTypeStr = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否有效 */
  isValid = undefined;

  /** 是否有效 1:是 0:否 */
  isValidStr = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 主键 */
  taxPayerBenefitId = undefined;

  /** 年度 */
  taxYear = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';

  /** 扣缴义务人编号 */
  withholdAgentId = undefined;

  /** 扣缴义务人名称 */
  withholdAgentName = '';

  /** 扣缴义务人类型 1：大户 2：外部供应商 3:单立户 */
  withholdAgentType = undefined;

  /** 扣缴义务人类型 1：大户 2：外部供应商 3:单立户 */
  withholdAgentTypeStr = '';
}

class TaxPayerBenefitQuery {
  /** endIndex */
  endIndex = undefined;

  /** 证照号码 */
  idCardNum = '';

  /** 是否有效:0无效，1有效 */
  isValid = undefined;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** startIndex */
  startIndex = undefined;

  /** 年度 */
  taxYear = '';

  /** 扣缴义务人ID */
  withholdAgentId = undefined;
}

class ThreeToOneRule {
  /** add */
  add = false;

  /** 适用的人员类别(1：代理，2：派遣，3：岗位外包，4：BPO) */
  applyEmpType = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** 分公司id */
  branchId = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** 签约方公司抬头要求(公司下所有有效的签单方抬头记录) */
  branchTitleId = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 是否默认缴纳主体(0否1是) */
  isDefaultSubject = '';

  /** 删除标记 */
  isDeleted = '';

  /** 签署劳动合同是否支持电子签(0否1是) */
  isLaborSign = '';

  /** 是否强制缴纳公积金(0否1是) */
  isPayFund = '';

  /** 签署离职材料是否支持电子签(0否1是) */
  isQuitMaterialsSign = '';

  /** 是否强制缴纳残障金(工资)(0否1是) */
  isSalaryDisabilityAllowance = '';

  /** 是否强制缴纳工会费(工资)(0否1是) */
  isSalaryUnionFee = '';

  /** 是否强制缴纳残障金(社保)(0否1是) */
  isSsDisabilityAllowance = '';

  /** 是否强制缴纳工会费(社保)(0否1是) */
  isSsUnionFee = '';

  /** 参保或劳动用工备案是否需要上传劳动合同扫描件(0否1是) */
  isUploadLaborFile = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 劳动合同主体上年度职工月平均工资(不定时工时-最新) */
  laborLastYearAvgSalary = '';

  /** 劳动合同主体月度最低工资标准(标准工时、综合工时-最新) */
  laborMonMinAvgSalary = '';

  /** 劳动合同签订主体 */
  laborSubjectId = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 外包执行【综合工时】是否有资质(1.没有资质/有资质，2.客户可以使用/有资质，3.客户不能使用) */
  outerCompIsQualifications = '';

  /** 外包执行【不定时工时】是否有资质(1.没有资质/有资质，2.客户可以使用/有资质，3.客户不能使用) */
  outerFlexIsQualifications = '';

  /** 外包其他说明 */
  outerOtherContent = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 工资扣缴义务人名称 */
  salaryWithholdingId = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 派造执行【综合工时】是否有资质(1.没有资质/有资质，2.客户可以使用/有资质，3.客户不能使用) */
  sendCompIsQualifications = '';

  /** 派遣执行【不定时工时】是否有资质(1.没有资质/有资质，2.客户可以使用/有资质，3.客户不能使用) */
  sendFlexIsQualifications = '';

  /** 派遣其他说明 */
  sendOtherContent = '';

  /**  社保公积金缴纳主体 有效签约方抬头 */
  ssSubjectId = '';

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 缴纳主体适用范围说明 */
  subjectContent = '';

  /** 主键 */
  threeToOneRuleId = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class ThreeToOneRuleQuery {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** 分公司id */
  branchId = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 是否默认缴纳主体(0否1是) */
  isDefaultSubject = '';

  /** 删除标记 */
  isDeleted = '';

  /** 签署劳动合同是否支持电子签(0否1是) */
  isLaborSign = '';

  /** 签署离职材料是否支持电子签(0否1是) */
  isQuitMaterialsSign = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 劳动合同签订主体 */
  laborSubjectId = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 工资扣缴义务人名称 */
  salaryWithholdingId = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 社保公积金缴纳主体 有效签约方抬头 */
  ssSubjectId = '';

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class ThreeToOneRuleVo {
  /** 是否强制缴纳残障金(工资)(0否1是) */
  isSalaryDisabilityAllowance = '';

  /** 是否强制缴纳工会费(工资)(0否1是) */
  isSalaryUnionFee = '';
}

class UsualTemplate {
  /** add */
  add = false;

  /** 适用合同类型 */
  applyContractType = '';

  /** 适用合同类型文本 */
  applyContractTypeName = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** 分公司id */
  branchId = '';

  /** 分公司名称 */
  branchName = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 导入类型,扩充使用 */
  expType = '';

  /** 文件ID */
  fileId = '';

  /** 文件名称 */
  fileName = '';

  /** 文件类型 */
  fileType = '';

  /** 文件类型文本 */
  fileTypeName = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 历史上传说明 */
  oldUploadIllustrate = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 模板ID */
  templateId = undefined;

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** 上传说明 */
  uploadIllustrate = '';

  /** 上传材料名称 */
  uploadMaterialName = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';

  /** 版本类型 */
  versionType = '';

  /** 版本类型文本 */
  versionTypeName = '';
}

class UsualTemplateQuery {
  /** add */
  add = false;

  /** 适用合同类型 */
  applyContractType = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** 分公司id */
  branchId = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** 文件类型 */
  fileType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** 上传材料名称 */
  uploadMaterialName = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';

  /** 版本类型 */
  versionType = '';
}

class areaCodeDTO {
  /** 地区编码 */
  areaCode = '';

  /** 地区id */
  areaId = undefined;

  /** 地区名称 */
  areaName = '';

  /** 版本 */
  version = undefined;
}

class areaCodeQuery {
  /** 地区码 */
  areaCode = undefined;

  /** 地区名称 */
  areaName = '';

  /** endIndex */
  endIndex = undefined;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** startIndex */
  startIndex = undefined;

  /** 版本 */
  version = undefined;
}

class baseData {
  /** 编码 */
  baseDataCode = '';

  /** 基础数据ID */
  baseDataId = '';

  /** 名称 */
  baseDataName = '';

  /** 英文名称 */
  englishTermName = '';

  /** 是否有效：0无效 1有效 */
  isValid = '';

  /** 拼音码 */
  pinYinCode = '';

  /** 说明/备注 */
  remark = '';

  /** 父类型编码 */
  type = '';
}

class baseDataInterDTO {
  /** 基础数据小类编码 */
  baseDataCode = '';

  /** 名称 */
  baseDataName = '';

  /** 创建人 */
  createBy = '';

  /** 创建时间 */
  createDt = '';

  /** 国际化ID */
  id = '';

  /** 语种 */
  languageType = '';

  /** 基础数据大类编码 */
  typeId = '';
}

class baseDataInterQuery {
  /** 基础数据小类编码 */
  baseDataCode = '';

  /** 基础数据大类编码 */
  typeId = '';
}

class baseDataInterVO {
  /** 基础数据小类编码 */
  baseDataCode = '';

  /** 名称 */
  baseDataName = '';

  /** 创建人 */
  createBy = '';

  /** 语种 */
  languageType = '';

  /** 基础数据大类编码 */
  typeId = '';
}

class baseDataQuery {
  /** 类型名称 */
  baseDataName = '';

  /** endIndex */
  endIndex = undefined;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** startIndex */
  startIndex = undefined;

  /** 类型编码 */
  type = '';
}

class baseDataVO {
  /** 编码 */
  baseDataCode = '';

  /** 基础数据ID */
  baseDataId = '';

  /** 名称 */
  baseDataName = '';

  /** 英文名称 */
  englishTermName = '';

  /** 是否有效：0无效 1有效 */
  isValid = '';

  /** 修改前编码，修改时不能为空 */
  oldBaseDataCode = '';

  /** 拼音码 */
  pinYinCode = '';

  /** 说明/备注 */
  remark = '';

  /** 父类型编码 */
  type = '';
}

class batchSaveOrUpdateCityVO {
  /** 新增城市集合 */
  insertList = [];

  /** 更新城市集合 */
  updateList = [];
}

class batchSaveOrUpdateCompetitorVO {
  /** 新增竞争对手集合 */
  insertList = [];

  /** 更新竞争对手集合 */
  updateList = [];
}

class batchSaveOrUpdateCountryVO {
  /** 新增国家集合 */
  insertList = [];

  /** 更新国家集合 */
  updateList = [];
}

class batchSaveOrUpdateEmpMaintainAcctVO {
  /** 新增list */
  insertList = [];

  /** 更新list */
  updateList = [];
}

class batchSaveOrUpdateProductSubVO {
  /** 新增福利产品小类集合 */
  insertList = [];

  /** 更新福利产品小类集合 */
  updateList = [];
}

class batchSaveOrUpdateProductSuperVO {
  /** 新增福利产品大类集合 */
  insertList = [];

  /** 更新福利产品大类集合 */
  updateList = [];
}

class batchSaveOrUpdateProvinceVO {
  /** 新增省份集合 */
  insertList = [];

  /** 更新省份集合 */
  updateList = [];
}

class batchSaveOrUpdateServiceSubTypeVO {
  /** 新增合同小类集合 */
  insertList = [];

  /** 更新合同小类集合 */
  updateList = [];
}

class batchSaveOrUpdateServiceTypeVO {
  /** 新增合同大类集合 */
  insertList = [];

  /** 更新合同大类集合 */
  updateList = [];
}

class branchInvoiceDTO {
  /** 分公司id */
  branchId = '';

  /** 主键id */
  branchInvoiceId = '';

  /** 分公司名称 */
  branchName = '';

  /** 普票开票方式:1电子发票、2纸制发票 */
  geneInvoiceType = '';

  /** 普票开票方式名称 */
  geneInvoiceTypeName = '';

  /** 专票开票方式:1电子发票、2纸制发票 */
  specInvoiceType = '';

  /** 专票开票方式名称 */
  specInvoiceTypeName = '';
}

class branchInvoiceQuery {
  /** 分公司id */
  branchId = '';

  /** endIndex */
  endIndex = undefined;

  /** 普票开票方式:1电子发票、2纸制发票 */
  geneInvoiceType = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** 专票开票方式:1电子发票、2纸制发票 */
  specInvoiceType = '';

  /** startIndex */
  startIndex = undefined;
}

class branchInvoiceVO {
  /** 分公司id */
  branchId = '';

  /** 主键id */
  branchInvoiceId = '';

  /** 普票开票方式:1电子发票、2纸制发票 */
  geneInvoiceType = '';

  /** 专票开票方式:1电子发票、2纸制发票 */
  specInvoiceType = '';
}

class cityDTO {
  /** 代理城市打折系数 */
  agentDiscountFactor = '';

  /** 代理级次 */
  agentLevel = '';

  /** 所属大区 */
  area = '';

  /** 城市编码 */
  cityCode = '';

  /** 城市英文名 */
  cityEnglishName = '';

  /** 城市系数 */
  cityFactor = '';

  /** 城市ID */
  cityId = '';

  /** 城市抵扣级别 */
  cityLevel = undefined;

  /** 城市名称 */
  cityName = '';

  /** 所属上级城市 */
  governingCity = '';

  /** 是否被使用 */
  isUsed = '';

  /** 拼音码 */
  pinyinCode = '';

  /** 省ID */
  provinceId = '';

  /** 省名 */
  provinceName = '';
}

class cityQuery {
  /** 大区 */
  area = undefined;

  /** 城市区号 */
  cityCode = '';

  /** 城市名称 */
  cityName = '';

  /** endIndex */
  endIndex = undefined;

  /** 是否被使用 0:否，1:是 */
  isUsed = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** 省份ID */
  provinceId = '';

  /** startIndex */
  startIndex = undefined;
}

class cityVO {
  /** 代理城市打折系数 */
  agentDiscountFactor = '';

  /** 代理级次 */
  agentLevel = '';

  /** 所属大区 */
  area = '';

  /** 城市编码 */
  cityCode = '';

  /** 城市英文名 */
  cityEnglishName = '';

  /** 城市系数 */
  cityFactor = '';

  /** 城市ID */
  cityId = '';

  /** 城市抵扣级别 */
  cityLevel = undefined;

  /** 城市名称 */
  cityName = '';

  /** 所属上级城市 */
  governingCity = '';

  /** 是否被使用 */
  isUsed = '';

  /** 拼音码 */
  pinyinCode = '';

  /** 省ID */
  provinceId = '';

  /** 省名 */
  provinceName = '';
}

class competitorDTO {
  /** 公司名称 */
  companyName = '';

  /** 竞争对手ID */
  competitorId = undefined;

  /** 创建人 */
  createBy = '';

  /** 创建时间 */
  createDt = '';

  /** 逻辑删除标识 0：正常 1：已删除，默认值为0 */
  isDeleted = '';

  /** 模拟人 */
  mimicBy = '';

  /** 创建人 */
  realName = '';

  /** 备注 */
  remark = '';

  /** 是否有效 */
  status = undefined;

  /** 更新人 */
  updateBy = '';

  /** 更新时间 */
  updateDt = '';
}

class competitorQuery {
  /** 公司名称 */
  companyName = '';

  /** endIndex */
  endIndex = undefined;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** startIndex */
  startIndex = undefined;
}

class competitorVO {
  /** 公司名称 */
  companyName = '';

  /** 竞争对手ID */
  competitorId = undefined;

  /** 创建人 */
  realName = '';

  /** 备注 */
  remark = '';

  /** 是否有效 */
  status = undefined;
}

class countryDTO {
  /** 国家区位码 */
  countryAreaCode = '';

  /** 国家英文名称 */
  countryEnglishName = '';

  /** 国家ID */
  countryId = '';

  /** 国家名称 */
  countryName = '';

  /** 国家简称 */
  countryShortName = '';

  /** 创建人 */
  createBy = '';

  /** 创建时间 */
  createDt = '';

  /** 逻辑删除标识 0：正常 1：已删除，默认值为0 */
  isDeleted = '';

  /** 模拟人 */
  mimicBy = '';

  /** 更新人 */
  updateBy = '';

  /** 更新时间 */
  updateDt = '';
}

class countryQuery {
  /** 国家区位码 */
  countryAreaCode = '';

  /** 国家名称 */
  countryName = '';

  /** 国家简称 */
  countryShortName = '';

  /** endIndex */
  endIndex = undefined;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** startIndex */
  startIndex = undefined;
}

class countryVO {
  /** 国家区位码 */
  countryAreaCode = '';

  /** 国家英文名称 */
  countryEnglishName = '';

  /** 国家ID */
  countryId = '';

  /** 国家名称 */
  countryName = '';

  /** 国家简称 */
  countryShortName = '';

  /** 创建人 */
  createBy = '';

  /** 创建时间 */
  createDt = '';

  /** 逻辑删除标识 0：正常 1：已删除，默认值为0 */
  isDeleted = '';

  /** 模拟人 */
  mimicBy = '';

  /** 更新人 */
  updateBy = '';

  /** 更新时间 */
  updateDt = '';
}

class dropdownListDTO {
  /** 业务大类类型 */
  btType = '';

  /** chargeRate */
  chargeRate = '';

  /** cityId */
  cityId = '';

  /** cityIdForParty */
  cityIdForParty = '';

  /** contractAvgAmt */
  contractAvgAmt = '';

  /** contractHeadcount */
  contractHeadcount = '';

  /** contractName */
  contractName = '';

  /** currentSalesName */
  currentSalesName = '';

  /** departmentName */
  departmentName = '';

  /** exFeeMonth */
  exFeeMonth = '';

  /** 供应商收费模板 */
  exFeeTempltId = '';

  /** governingArea */
  governingArea = '';

  /** 所属大区 */
  governingAreaId = '';

  /** governingBranch */
  governingBranch = '';

  /** 所属分公司 */
  governingBranchId = '';

  /** groupType */
  groupType = '';

  /** 主键 */
  key = '';

  /** liabilityCsName */
  liabilityCsName = '';

  /** 全称 */
  name = '';

  /** 拼音码 */
  pinYinCode = '';

  /** productLineId */
  productLineId = '';

  /** 供应商类型1内部2外部 */
  providerType = '';

  /** 保留名字1 */
  reserveName1 = '';

  /** 保留名字2 */
  reserveName2 = '';

  /** 储备对象,用于查询下拉框的时候,可能还需要查询其他列的值 */
  reserveObj = '';

  /** 缩写名 */
  shortName = '';

  /** 社保组ID */
  ssGroupId = '';

  /** svcSubtypeName */
  svcSubtypeName = '';

  /** svcTypeName */
  svcTypeName = '';
}

class empMaintainAcctDTO {
  /** 开户行名称 */
  acctBankName = '';

  /** 主键 */
  acctId = '';

  /** 开户名 */
  acctName = '';

  /** 开户账号 */
  acctNum = '';

  /** 是否默认(默认0) */
  isDefault = '';

  /** 金蝶编号 */
  jindieId = '';

  /** 用工方id */
  maintainId = '';

  /** 社保组id */
  ssGroupId = '';
}

class empMaintainAcctVO {
  /** companyCode */
  companyCode = '';

  /** custCode */
  custCode = '';

  /** custId */
  custId = '';

  /** custPayEntityId */
  custPayEntityId = '';

  /** custPayEntityName */
  custPayEntityName = '';

  /** employerMaintainId */
  employerMaintainId = '';

  /** endIndex */
  endIndex = undefined;

  /** ids */
  ids = [];

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** startIndex */
  startIndex = undefined;
}

class empMaintainDTO {
  /** 城市id */
  cityId = '';

  /** companyCode */
  companyCode = '';

  /** 分公司 */
  departmentId = '';

  /** 用工方维护ID */
  employerMaintainId = '';

  /** 用工方 */
  employerName = '';

  /** 用工方简称 */
  employerShortName = '';

  /** 是否有电子章，1：是，0：否 */
  hasStamp = undefined;

  /** hasStampName */
  hasStampName = '';

  /** 是否独立户，1：是，0：否 */
  isIndependent = '';

  /** 金碟组织编号 */
  jindieOrgId = '';

  /** 支付分公司 */
  payProviderName = '';
}

class empMaintainQuery {
  /** 分公司 */
  departmentId = '';

  /** 用工方 */
  employerName = '';

  /** endIndex */
  endIndex = undefined;

  /** 是否有电子章，1：是，0：否 */
  hasStamp = undefined;

  /** 单立户 */
  isIndependent = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** startIndex */
  startIndex = undefined;
}

class empMaintainVO {
  /** companyCode */
  companyCode = '';

  /** 分公司 */
  departmentId = '';

  /** 用工方维护ID */
  employerMaintainId = '';

  /** 用工方 */
  employerName = '';

  /** 用工方简称 */
  employerShortName = '';

  /** 是否有电子章，1：是，0：否 */
  hasStamp = undefined;

  /** 是否独立户，1：是，0：否 */
  isIndependent = '';

  /** 金碟组织编号 */
  jindieOrgId = '';
}

class fileProviderDTO {
  /** 收费频率  1:月缴,12:年缴 */
  chargeRate = undefined;

  /** 所属城市ID */
  cityId = undefined;

  /** 成本 */
  cost = undefined;

  /** 创建人 */
  createBy = '';

  /** 创建时间 */
  createDt = '';

  /** 所属分公司ID */
  departmentId = undefined;

  /** 档案供应商编码 */
  fileProviderCode = '';

  /** 档案供应商ID */
  fileProviderId = undefined;

  /** 档案供应商名称 */
  fileProviderName = '';

  /** 逻辑删除标识 0：正常 1：已删除，默认值为0 */
  isDeleted = '';

  /** 模拟人 */
  mimicBy = '';

  /** 价格 */
  price = undefined;

  /** 更新人 */
  updateBy = '';

  /** 更新时间 */
  updateDt = '';
}

class fileProviderVO {
  /** 收费频率  1:月缴,12:年缴 */
  chargeRate = undefined;

  /** 所属城市ID */
  cityId = undefined;

  /** 成本 */
  cost = undefined;

  /** 所属分公司ID */
  departmentId = undefined;

  /** 档案供应商编码 */
  fileProviderCode = '';

  /** 档案供应商ID */
  fileProviderId = undefined;

  /** 档案供应商名称 */
  fileProviderName = '';

  /** 价格 */
  price = undefined;
}

class medicalInsQuery {
  /** cityId */
  cityId = '';

  /** cityName */
  cityName = '';

  /** countyName */
  countyName = '';

  /** endIndex */
  endIndex = undefined;

  /** errorInfo */
  errorInfo = '';

  /** errorType */
  errorType = '';

  /** hosCode */
  hosCode = '';

  /** hosId */
  hosId = '';

  /** hosLevel */
  hosLevel = '';

  /** hosName */
  hosName = '';

  /** hosType */
  hosType = '';

  /** impTag */
  impTag = '';

  /** lineNum */
  lineNum = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** startIndex */
  startIndex = undefined;
}

class personCategoryDTO {
  /** 人员分类名称 */
  categoryName = '';

  /** 城市id */
  cityId = '';

  /** 城市名称 */
  cityName = '';

  /** 部门Id */
  departmentId = '';

  /** 是否删除 */
  isDeleted = '';

  /** 人员分类Id */
  personCategoryId = '';

  /** 产品Id */
  productId = '';

  /** 产品名称 */
  productName = '';

  /** 社保组名称 */
  ssGroupName = '';

  /** 社保组类型 */
  ssGroupType = '';
}

class personCategoryQuery {
  /** 人员分类名称 */
  categoryName = '';

  /** 城市Id */
  cityId = '';

  /** endIndex */
  endIndex = undefined;

  /** isDeleted */
  isDeleted = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** 人员分类编号 */
  personCategoryId = '';

  /** startIndex */
  startIndex = undefined;
}

class productSubDTO {
  /** 创建人 */
  createBy = '';

  /** 创建时间 */
  createDt = '';

  /** 逻辑删除标识 0：正常 1：已删除，默认值为0 */
  isDeleted = '';

  /** 模拟人 */
  mimicBy = '';

  /** 福利产品大类编号 */
  productSubtypeCode = '';

  /** 福利产品小类ID */
  productSubtypeId = undefined;

  /** 福利产品大类名称 */
  productSubtypeName = '';

  /** 福利产品大类ID */
  productSuperTypeId = undefined;

  /** 备注 */
  remark = '';

  /** 更新人 */
  updateBy = '';

  /** 更新时间 */
  updateDt = '';
}

class productSubQuery {
  /** endIndex */
  endIndex = undefined;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** 福利产品大类ID */
  productSuperTypeId = undefined;

  /** startIndex */
  startIndex = undefined;
}

class productSubVO {
  /** 福利产品小类编号 */
  productSubtypeCode = '';

  /** 福利产品小类ID */
  productSubtypeId = undefined;

  /** 福利产品小类名称 */
  productSubtypeName = '';

  /** 福利产品大类ID */
  productSuperTypeId = undefined;

  /** 备注 */
  remark = '';
}

class productSuperDTO {
  /** 创建人 */
  createBy = '';

  /** 创建时间 */
  createDt = '';

  /** 逻辑删除标识 0：正常 1：已删除，默认值为0 */
  isDeleted = '';

  /** 模拟人 */
  mimicBy = '';

  /** 福利产品大类编号 */
  productSuperTypeCode = '';

  /** 福利产品大类ID */
  productSuperTypeId = undefined;

  /** 福利产品大类名称 */
  productSuperTypeName = '';

  /** 备注 */
  remark = '';

  /** 更新人 */
  updateBy = '';

  /** 更新时间 */
  updateDt = '';
}

class productSuperQuery {
  /** endIndex */
  endIndex = undefined;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** 福利产品大类名称 */
  productSuperTypeName = '';

  /** startIndex */
  startIndex = undefined;
}

class productSuperVO {
  /** 福利产品大类编号 */
  productSuperTypeCode = '';

  /** 福利产品大类ID */
  productSuperTypeId = undefined;

  /** 福利产品大类名称 */
  productSuperTypeName = '';

  /** 备注 */
  remark = '';
}

class provinceDTO {
  /** 国别 */
  countryId = '';

  /** 创建人 */
  createBy = '';

  /** 创建时间 */
  createDt = '';

  /** 逻辑删除标识 0：正常 1：已删除，默认值为0 */
  isDeleted = '';

  /** 模拟人 */
  mimicBy = '';

  /** 拼音码 */
  pinyinCode = '';

  /** 省份英文名 */
  provinceEnglishName = '';

  /** 省份ID */
  provinceId = '';

  /** 省份名称 */
  provinceName = '';

  /** 省名简称 */
  provinceShortName = '';

  /** provincialCityId */
  provincialCityId = '';

  /** provincialCityName */
  provincialCityName = '';

  /** 更新人 */
  updateBy = '';

  /** 更新时间 */
  updateDt = '';
}

class provinceQuery {
  /** 国家 */
  countryId = '';

  /** endIndex */
  endIndex = undefined;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** 省份英文名称 */
  provinceEnglishName = '';

  /** 省份名称 */
  provinceName = '';

  /** 省份简称 */
  provinceShortName = '';

  /** startIndex */
  startIndex = undefined;
}

class provinceVO {
  /** 国别 */
  countryId = '';

  /** 创建人 */
  createBy = '';

  /** 创建时间 */
  createDt = '';

  /** 逻辑删除标识 0：正常 1：已删除，默认值为0 */
  isDeleted = '';

  /** 模拟人 */
  mimicBy = '';

  /** 拼音码 */
  pinyinCode = '';

  /** 省份英文名 */
  provinceEnglishName = '';

  /** 省份ID */
  provinceId = '';

  /** 省份名称 */
  provinceName = '';

  /** 省名简称 */
  provinceShortName = '';

  /** 省份城市 */
  provincialCityId = '';

  /** 更新人 */
  updateBy = '';

  /** 更新时间 */
  updateDt = '';
}

class servicePointCustQuery {
  /** 大区 */
  area = '';

  /** 城市 */
  cityId = '';

  /** 城市名称 */
  cityName = '';

  /** 联系人(客服) */
  contactName = '';

  /** 联系电话 */
  contactTel = '';

  /** 客户编号 */
  custCode = '';

  /** 英文名称 */
  custEnglishName = '';

  /** 客户Id */
  custId = '';

  /** 客户名称 */
  custName = '';

  /** 客户简称 */
  custShortName = '';

  /** endIndex */
  endIndex = undefined;

  /** 是否有效 */
  isDeleted = '';

  /** 机构类别:1 自营分公司; 2 供应商 */
  organizationType = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** 省份 */
  provinceName = '';

  /** 网点服务名称(接单方) */
  serviceAssigneeName = '';

  /** 服务网点标识: 1 优选,2 备选,3 只减不增,4 仅操作指定客户,5 终止合作 */
  serviceBranchFlag = '';

  /** serviceCustId */
  serviceCustId = '';

  /** 网点服务名称(集团) */
  serviceGroupName = '';

  /** 主键 */
  servicePointId = '';

  /** startIndex */
  startIndex = undefined;
}

class servicePointQuery {
  /** 大区 */
  area = '';

  /** 接单方id */
  assigneeProviderId = '';

  /** 城市 */
  cityId = '';

  /** cityLevel */
  cityLevel = '';

  /** cityLevelCN */
  cityLevelCN = '';

  /** 城市名称 */
  cityName = '';

  /** 联系人(客服) */
  contactName = '';

  /** 联系电话 */
  contactTel = '';

  /** 网点分公司id */
  departmentId = '';

  /** 网点分公司id */
  departmentName = '';

  /** endIndex */
  endIndex = undefined;

  /** 是否有效 */
  isDeleted = '';

  /** 机构类别:1 自营分公司; 2 供应商 */
  organizationType = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** 省份 */
  provinceName = '';

  /** 网点服务名称(接单方) */
  serviceAssigneeName = '';

  /** 服务网点标识: 1 优选,2 备选,3 只减不增,4 仅操作指定客户,5 终止合作 */
  serviceBranchFlag = '';

  /** 网点服务名称(集团) */
  serviceGroupName = '';

  /** startIndex */
  startIndex = undefined;
}

class serviceSubTypeDTO {
  /** 创建人 */
  createBy = '';

  /** 创建时间 */
  createDt = '';

  /** 逻辑删除标识 0：正常 1：已删除，默认值为0 */
  isDeleted = '';

  /** 说明 */
  memo = '';

  /** 模拟人 */
  mimicBy = '';

  /** 拼音码 */
  pinYinCode = '';

  /** 合同小类ID */
  svcSubtypeId = undefined;

  /** 合同小类名称 */
  svcSubtypeName = '';

  /** 合同大类ID */
  svcTypeId = undefined;

  /** 合同小类编码 */
  svcTypeSubCode = '';

  /** 更新人 */
  updateBy = '';

  /** 更新时间 */
  updateDt = '';
}

class serviceSubTypeQuery {
  /** endIndex */
  endIndex = undefined;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** startIndex */
  startIndex = undefined;

  /** 合同大类ID */
  svcTypeId = undefined;
}

class serviceTypeDTO {
  /** 创建人 */
  createBy = '';

  /** 创建时间 */
  createDt = '';

  /** 逻辑删除标识 0：正常 1：已删除，默认值为0 */
  isDeleted = '';

  /** 说明 */
  memo = '';

  /** 模拟人 */
  mimicBy = '';

  /** 合同大类编码 */
  svcTypeCode = '';

  /** 合同大类ID */
  svcTypeId = undefined;

  /** 合同大类名称 */
  svcTypeName = '';

  /** 更新人 */
  updateBy = '';

  /** 更新时间 */
  updateDt = '';
}

class serviceTypeQuery {
  /** endIndex */
  endIndex = undefined;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** startIndex */
  startIndex = undefined;

  /** 合同大类名称 */
  svcTypeName = '';
}

class taxRateDTO {
  /** deductAmount */
  deductAmount = undefined;

  /** fixRate */
  fixRate = undefined;

  /** remark */
  remark = '';

  /** wageTaxbaseId */
  wageTaxbaseId = undefined;

  /** wageTaxbaseName */
  wageTaxbaseName = '';
}

class taxRateDetailDTO {
  /** maxAmount */
  maxAmount = undefined;

  /** minAmount */
  minAmount = undefined;

  /** quickDeduct */
  quickDeduct = undefined;

  /** taxLevel */
  taxLevel = undefined;

  /** taxRate */
  taxRate = undefined;

  /** wageDetailId */
  wageDetailId = undefined;

  /** wageTaxbaseId */
  wageTaxbaseId = undefined;
}

class taxRateDetailVO {
  /** maxAmount */
  maxAmount = undefined;

  /** minAmount */
  minAmount = undefined;

  /** quickDeduct */
  quickDeduct = undefined;

  /** taxLevel */
  taxLevel = undefined;

  /** taxRate */
  taxRate = undefined;
}

class taxRateQuery {
  /** endIndex */
  endIndex = undefined;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** startIndex */
  startIndex = undefined;

  /** wageTaxbaseName */
  wageTaxbaseName = '';
}

class taxRateVO {
  /** 起征点 */
  deductAmount = undefined;

  /** 固定税率 */
  fixRate = undefined;

  /** 说明 */
  remark = '';

  /** 薪资税率明细 */
  taxRateDetailVOList = [];

  /** 薪资税率表id */
  wageTaxbaseId = undefined;

  /** 薪资税率表名称 */
  wageTaxbaseName = '';
}

class withholdAgentNormQuery {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';

  /** 扣缴义务人ID */
  withholdAgentId = '';
}

export const basedata = {
  AreaCodeVO,
  BaseEntity,
  BatchSaveOrUpdateAreaCodeVO,
  BdPolicyBusnameClass,
  BdPolicyBusnameSubtype,
  BdPolicyBusnameType,
  BdTaxRuleQuery,
  BusinessSubType,
  BusinessSubTypeQuery,
  BusinessType,
  BusinessTypeQuery,
  BusinessTypeVO,
  CommonQuery,
  CommonResponse,
  DisableBenefitRatio,
  DropdownList,
  EmpMaintainPayEntity,
  ExportQuery,
  FileProviderQuery,
  FilterEntity,
  GlobalResult,
  ImMedicalDTO,
  ImMedicalQuery,
  JindieOrg,
  JindieOrgQuery,
  Map,
  Materials,
  MaterialsPackage,
  MaterialsPackageQuery,
  MaterialsQuery,
  MedicalIns,
  MedicalInsDTO,
  MedicalInsVO,
  Page,
  PayAccountSyncQuery,
  ServicePointCustDTO,
  ServicePointDTO,
  SpecialBranchTitle,
  SpecialBranchTitleQuery,
  SysBranchTitle,
  SysBranchTitleBilling,
  SysBranchTitleParItem,
  SysBranchTitleQuery,
  SysBranchTitleVO,
  TaxPayerBenefit,
  TaxPayerBenefitQuery,
  ThreeToOneRule,
  ThreeToOneRuleQuery,
  ThreeToOneRuleVo,
  UsualTemplate,
  UsualTemplateQuery,
  areaCodeDTO,
  areaCodeQuery,
  baseData,
  baseDataInterDTO,
  baseDataInterQuery,
  baseDataInterVO,
  baseDataQuery,
  baseDataVO,
  batchSaveOrUpdateCityVO,
  batchSaveOrUpdateCompetitorVO,
  batchSaveOrUpdateCountryVO,
  batchSaveOrUpdateEmpMaintainAcctVO,
  batchSaveOrUpdateProductSubVO,
  batchSaveOrUpdateProductSuperVO,
  batchSaveOrUpdateProvinceVO,
  batchSaveOrUpdateServiceSubTypeVO,
  batchSaveOrUpdateServiceTypeVO,
  branchInvoiceDTO,
  branchInvoiceQuery,
  branchInvoiceVO,
  cityDTO,
  cityQuery,
  cityVO,
  competitorDTO,
  competitorQuery,
  competitorVO,
  countryDTO,
  countryQuery,
  countryVO,
  dropdownListDTO,
  empMaintainAcctDTO,
  empMaintainAcctVO,
  empMaintainDTO,
  empMaintainQuery,
  empMaintainVO,
  fileProviderDTO,
  fileProviderVO,
  medicalInsQuery,
  personCategoryDTO,
  personCategoryQuery,
  productSubDTO,
  productSubQuery,
  productSubVO,
  productSuperDTO,
  productSuperQuery,
  productSuperVO,
  provinceDTO,
  provinceQuery,
  provinceVO,
  servicePointCustQuery,
  servicePointQuery,
  serviceSubTypeDTO,
  serviceSubTypeQuery,
  serviceTypeDTO,
  serviceTypeQuery,
  taxRateDTO,
  taxRateDetailDTO,
  taxRateDetailVO,
  taxRateQuery,
  taxRateVO,
  withholdAgentNormQuery,
};
