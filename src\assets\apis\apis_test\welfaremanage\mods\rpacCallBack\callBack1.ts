import * as defs from '../../baseClass';
import {
  fetch,
  RequestConfig,
  beforeFetch,
  afterFetch,
  checkResStatus,
  removeSpace,
} from '@/utils/request';

/**
     * @url /rhro-service-1.0/api/rpac/call/back1
     * @desc ceshi1
查询组织下拉
     * hasForm: false
     * hasBody: true
     */

export class Params {}

export const init = new defs.welfaremanage.CommonResponse();
export const url = '/rhro-service-1.0/api/rpac/call/back1:POST';
export const initialUrl = '/rhro-service-1.0/api/rpac/call/back1';
export const cacheKey = '_api_rpac_call_back1_POST';
export async function request(
  data: defs.welfaremanage.RpaBatchInfoDTO,
  options?: RequestConfig,
) {
  const reqUrl = `/rhro-service-1.0/api/rpac/call/back1`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          reolve(res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
export async function requests(
  data: defs.welfaremanage.RpaBatchInfoDTO,
  options?: RequestConfig,
) {
  const reqUrl = `/rhro-service-1.0/api/rpac/call/back1`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          if (checkResStatus(res)) return reolve(res ? res.data : res);
          reject(res ? res.message : res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
