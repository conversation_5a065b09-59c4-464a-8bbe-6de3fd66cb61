import React, { useState } from 'react';
import { Button, Form, Modal, Upload } from 'antd';
import { WritableColumnProps } from '@/utils/writable/types';
import { CachedPage } from '@/components/CachedPage';
import { EditeFormProps, FormInstance } from '@/components/EditeForm';
import { WritableInstance } from '@/components/Writable';
import {
  CommonBaseDataSelector,
  QueryOrgDownSelector,
  WalfareProcessorSelector,
} from '@/components/Selectors/BaseDataSelectors';
import { DateRange } from '@/components/DateRange4';
import { mapToSelectors } from '@/components/Selectors';
import { AsyncButton } from '@/components/Forms/Confirm';
import { getCurrentUserCityId, getUserId } from '@/utils/model';
import { SocialGroupPop } from '@/components/StandardPop';
import { msgErr, msgOk, msgWarn } from '@/utils/methods/message';
import { AuthButtons } from '@/components/Forms/RenderButtons';
import NumberCodal from './compontents/NumberCodal';
import SendCodal from './compontents/SendCodal';
import CompleteCompanyInfo from '@/components/CompleteCompanyInfo';
import Link from 'antd/lib/typography/Link';
import ListProductCodal from './compontents/ListProductCodal';
import { isEmpty } from '@/utils/methods/checker';
import { InnerUserPop } from '@/components/StandardPop/InnerUserPop';
import { BizUtil } from '@/utils/settings/bizUtil';

let options: WritableInstance;

interface QuerySsAndFundPayDetailProps {
  [props: string]: any;
}
const sendStatusMap = new Map<number, string>([
  [0, '待比对'],
  [1, '比对中'],
  [2, '比对成功'],
  [3, '已发送'],
  [4, '报税成功'],
]);

const taxDetailMap = new Map<number, string>([
  [1, '处理中'],
  [2, '成功'],
  [3, '失败'],
  [6, '校验失败'],
]);

const service = API.welfaremanage.rpaTaxation.list;

const QuerySsAndFundPayDetail: React.FC<QuerySsAndFundPayDetailProps> = () => {
  const cityId = getCurrentUserCityId();
  const userId = getUserId();

  const [form] = Form.useForm();
  const [numModal, setNumModal] = useState<boolean>(false);
  const [sendModal, setSendModal] = useState<number>(0);
  const [queryOrgDownText, setQueryOrgDownText] = useState<string>('');
  // const [reviewDis, setReviewDis] = useState<boolean>(true);
  const [companyInfoOpen, setCompanyInfoOpen] = useState(false);
  const [isSending, setIsSending] = useState(false);
  const [unmaintainedData, setUnmaintainedData] = useState<any>({});
  const [productModal, setProductModal] = useState<boolean>(false);
  const [taxId, setTaxId] = useState<number | undefined>();

  const exportColumns = [
    { title: '客户编号', dataIndex: 'custCode' },
    { title: '客户名称', dataIndex: 'custName' },

    { title: '缴费企业名称', dataIndex: 'companyName' },
    { title: '企业编号', dataIndex: 'companyCode' },
    { title: '统一信用代码', dataIndex: 'orgCreditCode' },
    { title: '雇员姓名', dataIndex: 'empName' },
    { title: '唯一号', dataIndex: 'empId' },
    { title: '证件类型', dataIndex: 'idcardType' },
    { title: '证件号码', dataIndex: 'idcardNum' },
    { title: '申报工资', dataIndex: 'decSalary' },
    { title: '接单客服', dataIndex: 'assigneeCsName' },
    { title: '福利办理月', dataIndex: 'welfareProcessMon' },
  ];

  const columns: WritableColumnProps<any>[] = [
    { title: '客户编号', dataIndex: 'custCode' },
    { title: '客户名称', dataIndex: 'custName' },
    { title: '缴费企业名称', dataIndex: 'companyName' },
    { title: '企业编号', dataIndex: 'companyCode' },
    { title: '统一信用代码', dataIndex: 'orgCreditCode' },
    { title: '雇员姓名', dataIndex: 'empName' },
    { title: '唯一号', dataIndex: 'empId' },
    { title: '证件类型', dataIndex: 'idcardType' },
    { title: '证件号码', dataIndex: 'idcardNum' },
    { title: '原缴费基数', dataIndex: 'taxDecSalary' },
    { title: '比对时间', dataIndex: 'compareDt' },
    { title: '申报工资', dataIndex: 'decSalary', inputRender: 'number' },
    { title: '申报发起时间', dataIndex: 'batchStartDt' },
    { title: '申报反馈时间', dataIndex: 'batchEndDt' },
    {
      title: '税务反馈结果',
      dataIndex: 'taxResult',
      hidden: queryOrgDownText === '社保' || queryOrgDownText === '医疗',
    },
    {
      title: '社保反查',
      dataIndex: 'ssResult',
      hidden: queryOrgDownText === '税务' || queryOrgDownText === '医疗',
    },
    {
      title: '医保反查',
      dataIndex: 'medicalResult',
      hidden: queryOrgDownText === '税务' || queryOrgDownText === '社保',
    },
    { title: '办理人', dataIndex: 'createByName' },
    { title: '入表时间', dataIndex: 'createDt' },
    { title: '明细状态', dataIndex: 'taxDetail' },
    { title: '税务报送状态', dataIndex: 'sendStatus' },
    {
      title: '产品申报工资',
      dataIndex: 'product',
      render: (text, record) => <Link onClick={() => onProductDetail(record)}>详细</Link>,
    },
    { title: '接单客服', dataIndex: 'assigneeCsName' },
    { title: '福利办理月', dataIndex: 'welfareProcessMon' },
  ];

  const onProductDetail = (record: POJO) => {
    setTaxId(record.taxId); // 322
    setProductModal(true);
  };

  const formColumns: EditeFormProps[] = [
    {
      label: `社保组`,
      fieldName: 'ssGroupId',
      inputRender: (outerForm: FormInstance) => {
        return (
          <SocialGroupPop
            rowValue="ssGroupId-cityId-ssGroupName"
            keyMap={{
              ssGroupId: 'SSGROUPID',
              cityId: 'CITYID',
              ssGroupName: 'INSURANCENAME',
            }}
            fixedValues={{ ssGroupType: '1', cityId }}
            cityId={cityId}
            handdleConfirm={() => {
              // setReviewDis(true);
              form.setFieldsValue({ orgCode: undefined, welfareProcessor: undefined });
            }}
            handleClear={() => {
              // setReviewDis(true);
              form.setFieldsValue({ orgCode: undefined, welfareProcessor: undefined });
            }}
          />
        );
      },
      rules: [{ required: true, message: '请选择社保组' }],
    },
    // {
    //   label: '对应组织',
    //   fieldName: 'orgCode',
    //   shouldUpdate: (prevValues, curValues) => {
    //     return prevValues.cityId !== curValues.cityId;
    //   },
    //   inputRender: (outerForm) => {
    //     const { cityId } = outerForm.getFieldsValue();
    //     return (
    //       <QueryOrgDownSelector
    //         params={{ cityId }}
    //         skipEmptyParam
    //         onChange={(key, record: any) => setQueryOrgDownText(record?.title || '')}
    //         onLoaded={(list: any) => {
    //           if (list.length) {
    //             setReviewDis(false);
    //           }
    //         }}
    //       />
    //     );
    //   },
    // },
    {
      label: '雇员姓名',
      fieldName: 'empName',
      inputRender: 'string',
    },
    {
      label: '唯一号',
      fieldName: 'empId',
      inputRender: 'string',
    },
    {
      label: '福利办理方',
      fieldName: 'welfareProcessor',
      shouldUpdate: (prevValues, curValues) => {
        return prevValues.cityId !== curValues.cityId;
      },
      inputRender: (outerForm) => {
        const { cityId } = outerForm.getFieldsValue();
        return <WalfareProcessorSelector skipEmptyParam params={{ cityId }} />;
      },
      rules: [{ required: true, message: '请输入福利办理方' }],
    },
    {
      label: '证件类型',
      fieldName: 'idCardType',
      inputRender: () => <CommonBaseDataSelector params={{ type: '045' }} />,
    },
    {
      label: '证件号码',
      fieldName: 'idcardNum',
      inputRender: 'string',
    },
    {
      label: '客户编号',
      fieldName: 'custCode',
      inputRender: 'string',
    },
    {
      label: '客户名称',
      fieldName: 'custName',
      inputRender: 'string',
    },
    {
      label: '办理时间',
      fieldName: 'ssSDate',
      inputRender: () => (
        <DateRange
          fields={[
            {
              title: '办理时间>=',
              dataIndex: 'ssStartDate',
            },
            {
              title: '办理时间<=',
              dataIndex: 'ssEndDate',
            },
          ]}
        />
      ),
    },
    {
      label: '明细状态',
      fieldName: 'taxDetail',
      inputRender: () => mapToSelectors(taxDetailMap, { allowClear: true }),
    },
    {
      label: '税务报送状态',
      fieldName: 'sendStatus',
      inputRender: () => mapToSelectors(sendStatusMap, { allowClear: true }),
    },
    {
      label: '接单客服',
      fieldName: 'assigneeCs',
      inputRender: () => (
        <InnerUserPop
          rowValue="assigneeCs-assigneeCsName"
          keyMap={{ assigneeCs: 'EMPID', assigneeCsName: 'REALNAME' }}
          fixedValues={{ roleCode: BizUtil.ROLE_CS, isDefault: 1 }}
        />
      ),
    },
    {
      label: '福利办理月',
      fieldName: 'welfareProcessMon',
      inputRender: 'month',
    },
    {
      label: '缴费实体名称',
      fieldName: 'companyName',
      inputRender: 'string',
    },
  ];

  const exportData = (table: WritableInstance) => {
    table.handleExport(
      {
        service: API.welfaremanage.rpaTaxation.exportFile,
        data: (value) => value,
      },
      { columns: exportColumns, fileName: '税务自动报送.xlsx' },
    );
  };

  const customRequest = (option: any) => {
    const { file, onError, onSuccess } = option;

    API.welfaremanage.rpaTaxation.uploadFile
      .request({ file })
      .then((res) => {
        onSuccess(res, file);
      })
      .catch(onError);
  };

  const onBatchSave = async () => {
    const { updated } = options.getList();
    if (!updated.length) {
      return msgWarn('没有修改的数据');
    }
    const data = updated.map((item) => {
      return { taxId: item.taxId, decSalary: item.decSalary };
    });
    await API.welfaremanage.rpaTaxation.batchUpdate.requests(data);
    msgOk('修改成功');
    options.request();
  };

  const onBatchDel = () => {
    const selectedRows = options.selectedRows;
    if (!selectedRows.length) {
      return msgWarn('请先勾选要删除的数据');
    }
    Modal.confirm({
      content: `删除${selectedRows.length}条报送数据`,
      onOk: async () => {
        const data = selectedRows.map((item) => {
          return { taxId: item.taxId, isDeleted: 1 };
        });
        await API.welfaremanage.rpaTaxation.batchUpdate.request(data);
        msgOk('操作成功');
        options.request(options.queries);
      },
    });
  };

  const applyProcess_clickHandler = () => {
    setNumModal(true);
  };

  // const onReview = async () => {
  //   const values = options.queries;
  //   await API.welfaremanage.rpaTaxation.send.requests({ ...values, empType: 3 });
  //   msgOk('操作成功');
  //   options.request(options.queries);
  // };

  const onSalaryDeclaration = (row) => {
    if (row.sendStatus !== '比对成功') {
      return msgErr('税务报送状态为比对成功才可进行此操作');
    }
    setSendModal(2);
  };

  const onSyncName = async (rows) => {
    const data = rows.map((item) => {
      return { taxId: item.taxId, isIndependent: item.isIndependent, companyId: item.companyId };
    });
    await API.welfaremanage.rpaTaxation.syncName.requests(data);
    msgOk('操作成功');
  };

  const onSyncSalary = async (rows) => {
    const data = rows.map((item) => {
      return { taxId: item.taxId, isIndependent: item.isIndependent, companyId: item.companyId };
    });

    await API.welfaremanage.rpaTaxation.syncSalary.requests(data);
    msgOk('操作成功');
  };
  const renderButtons = (_options: WritableInstance) => {
    options = _options;
    return (
      <>
        <Button type="primary" htmlType="submit">
          查询
        </Button>
        <AsyncButton onClick={() => setSendModal(1)} disabled={isEmpty(options.selectedSingleRow)}>
          税务比对
        </AsyncButton>
        <AsyncButton
          onClick={() => onSalaryDeclaration(options.selectedSingleRow)}
          disabled={isEmpty(options.selectedSingleRow)}
        >
          工资申报
        </AsyncButton>
        {/* <AsyncButton onClick={() => onReview()} disabled={reviewDis}>
          反查
        </AsyncButton> */}

        <Button onClick={() => exportData(_options)}>导出</Button>
        <Upload
          customRequest={(options) => customRequest(options)}
          maxCount={1}
          showUploadList={false}
        >
          <Button>导入</Button>
        </Upload>
        <AsyncButton onClick={onBatchSave}>保存</AsyncButton>
        <AsyncButton onClick={onBatchDel}>删除</AsyncButton>
        <AuthButtons hidden={userId !== '1'}>
          <Button onClick={applyProcess_clickHandler}>批次数量</Button>
        </AuthButtons>
        <AsyncButton
          onClick={() => onSyncName(options.selectedRows)}
          disabled={isEmpty(options.selectedRows)}
        >
          更新缴费企业名称
        </AsyncButton>
        <AsyncButton
          onClick={() => onSyncSalary(options.selectedRows)}
          disabled={isEmpty(options.selectedRows)}
        >
          更新申报工资
        </AsyncButton>
        {/* <Button onClick={() => onProductDetail({})}>详情</Button> */}
      </>
    );
  };

  return (
    <>
      <CachedPage
        service={service}
        formColumns={formColumns}
        columns={columns}
        renderButtons={renderButtons}
        form={form}
        editable
      >
        <NumberCodal
          visible={numModal}
          title="设置批次数量"
          hideHandle={() => {
            setNumModal(false);
          }}
          submit={async (values: any, values1: any) => {
            await API.welfaremanage.socialBatch.setBatchCount
              .requests({ ...values, empType: 3 })
              .then((res) => {
                msgOk('批次数量保存成功');
                setNumModal(false);
              });
            await API.welfaremanage.socialBatch.setBatchCount
              .requests({ ...values1, empType: 4 })
              .then((res) => {
                msgOk('批次数量保存成功');
                setNumModal(false);
              });
          }}
        />
        <SendCodal
          visible={sendModal}
          hideHandle={() => {
            setSendModal(0);
          }}
          submit={async (
            operateYear: string,
            declareWagesType: number,
            isCompareEmptySalary?: string,
          ) => {
            const values = await form.validateFields();
            const rows = options.selectedRows;
            for (const key in values) {
              if (values[key] === '') {
                delete values[key];
              }
            }
            if (sendModal === 1) {
              const resData = await API.welfaremanage.rpaTaxation.send.requests({
                operateYear,
                ...values,
                empType: 15,
                declareWagesType,
                isCompareEmptySalary,
              });
              msgOk(resData.msg || '比对成功');
              setSendModal(0);
              options.request();
              return;
            }

            const taxIdList = rows.map((item) => item.taxId);
            const resData = await API.welfaremanage.rpaTaxation.send.requests({
              operateYear,
              ...values,
              empType: 4,
              declareWagesType,
              // empId: row.empId,
              taxIdList,
            });

            if (resData.code == 500 || resData.code == 800) {
              // 已筛选出来的，没有key的公司，需要扫码的公司名单
              setUnmaintainedData({ ...resData.data, ssBatchId: resData.ssBatchId });
              setCompanyInfoOpen(true);
            } else if (resData.code == 200) {
              msgOk(resData.msg || '申报成功');
              setSendModal(0);
              options.request();
            } else {
              msgErr(resData.msg || '请联系管理员');
            }
          }}
        />
        <ListProductCodal
          visible={productModal}
          title="产品申报工资"
          hideHandle={() => {
            setProductModal(false);
          }}
          taxId={taxId}
        />
      </CachedPage>
      {/* 没有key的公司，需要扫码的公司 & 未维护的公司 */}
      <CompleteCompanyInfo
        modal={[companyInfoOpen, setCompanyInfoOpen]}
        modalType="1"
        data={unmaintainedData}
        loadingInfo={[isSending, setIsSending]}
        cityId={cityId}
        organizationType="3"
      />
    </>
  );
};

export default QuerySsAndFundPayDetail;
