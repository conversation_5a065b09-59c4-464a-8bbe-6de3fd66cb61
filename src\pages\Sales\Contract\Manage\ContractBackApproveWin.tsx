import React, { useEffect, useState } from 'react';
import { Button, Tabs, Form, Input, InputNumber, Popconfirm, Tooltip } from 'antd';
import { WritableColumnProps } from '@/utils/writable/types';
import { CachedPage } from '@/components/CachedPage';
import { EditeFormProps, FormInstance } from '@/components/EditeForm';
import Codal from '@/components/Codal';
import { WritableInstance, Writable, useWritable } from '@/components/Writable';
import DetailForm from '@/components/EditeForm/DetailForm';
import { FormElement1, FormElement3, RowElementButton } from '@/components/Forms/FormLayouts';
import { EnumerateFields } from '@/components/CachedPage/EnumerateFields';
import {
  ProdcutLineSelector,
  BooleanSelector,
  SignFlagManualSelector,
  CustProductLineSelector,
} from '@/components/Selectors/BaseDropDown';
import { CustomerPop } from '@/components/StandardPop/CustomerPop';
import { CurrentSalesSelector } from '@/pages/Sales/Contract/Manage/Seletors';
import { ContractTypeSelector, mapToSelectors } from '@/components/Selectors';
import { useSelector } from 'umi';
import { ConnectState } from '@/models/connect';
import {
  ContractSubTypeSelector,
  BranchTitleSelector,
  QuotationCodeSelector,
  ContractSubTypeAllSelector,
  BranchTitleSpeSelector,
  GetSignFlagManualSelect,
  ContractgetSubTypeSelector,
} from '@/components/Selectors/BaseDataSelectors';
import {
  naturalNumberRegx,
  naturalPositiveRegx,
  validateCurrency,
  validateEmail,
  validateNaturalDay,
  validateNaturalNumber,
  validateNaturalPositive,
  validatePositive,
  validatePositiveFloat,
} from '@/utils/forms/validate';
import { column3 } from '@/utils/forms/typography';
import { stdDateFormat, today } from '@/utils/methods/times';
import { DateRange } from '@/components/DateRange4';
import {
  agereedAmtReceiveMonMap,
  contractAreaTypeMap,
  paymentModeMap,
  payMonthMapMap,
  contractEndDateTypeMap,
} from '@/utils/settings/sales/contract';
import { CompetitorSelector, PayTypeSelector, ContractCategorySelector } from './Seletors';
import { CODE_SUCCESS, msgErr, msgOk, resError } from '@/utils/methods/message';
import { render } from 'react-dom';
import { isEmpty } from 'lodash';
import { Calculator } from '@/utils/methods/calculator';
import { stdBoolTrue, stdBoolTrueSrting } from '@/utils/settings';
import { Switchs } from '@/components/Selectors/Switch';
import { CsPop, InnerUserPop } from '@/components/StandardPop/InnerUserPop';
import { addedInLocal, tableIndexKey } from '@/utils/settings/forms';
import QuerySelectedQutationWin from './QuerySelectedQutationWin';
import { QuotationFullPop } from '@/components/StandardPop/QuotationFullPop';
import QuotationUpdateForm from '@/pages/Sales/Quotation/QuotationManage/Forms/QuotationDetailForm';
import { Typography } from 'antd';
import { getCurrentMenu, getCurrentUser } from '@/utils/model';
import { PopconfirmButton } from '@/components/Forms/PopconfirmButton';
import { NonStaCoctApprPop } from '@/components/StandardPop/NonStaCoctApprPop';
import Upload, { UploadChangeParam } from 'antd/lib/upload';
import { UploadFile } from 'antd/lib/upload/interface';
import { CustPayerPop } from '@/components/StandardPop/CustPayerPop';
import { AsyncButton } from '@/components/Forms/Confirm';
import { shallowEqual } from 'react-redux';
import { Store } from 'antd/lib/form/interface';
import { AlertTab } from '@/components/AlertTabPane/AlertTab';
import { GeneralInputRenderOption } from '@/components/Writable/libs/GeneralInput';
import { FileSize } from '@/components/UploadForm';
import {
  colorRed,
  QuostatusMap,
  isValidMap,
} from '@/pages/emphiresep/sendorder/CustomerSubcontract/ContractView';
import Group from '@/components/Group';
import ImportForm from '@/components/UploadForm/ImportForm';
import AddForm from '@/components/EditeForm/AddForm';
import { validDateCost, getFileExtension } from './ContractForm';
import { EmployeePop } from '@/components/StandardPop/EmployeePop';
import { downloadFileWithAlert } from '@/utils/methods/file';
import { DeleteOutlined, UploadOutlined } from '@ant-design/icons';
import QuotationAddForm from '../../Quotation/QuotationManage/Forms/QuotationAddForm';
import { multipleNum } from '../../Quotation/QuotationManage';
import { PreviewCodal } from './PreviewFile';

const { Link } = Typography;

const { TabPane } = Tabs;

const serviceSave = API.sale.contract.save;
const serviceQuotation: IServiceType = {
  ...API.sale.contract.save,
  cacheKey: API.sale.contract.save + '_quotation',
};
const serviceProductLine: IServiceType = {
  ...API.sale.contract.save,
  cacheKey: API.sale.contract.save + '_productLine',
};
const serviceCustPayer: IServiceType = {
  ...API.sale.contract.save,
  cacheKey: API.sale.contract.save + '_custPayer',
};

export enum contractFormScene {
  add,
  update,
  renew,
}

enum tabScene {
  sales = '1',
  cs = '2',
  quotation = '3',
  legal = '4',
  prepay = '5',
  custPayer = '6',
  reject = '7',
}

interface QueryContractDetailInfoWinProps {
  [props: string]: any;
  modal: [boolean, CallableFunction];
  contract?: Partial<defs.sale.ContractDTO>;
  additional?: Partial<defs.sale.ContractDTO>;
  onConfirm: () => void;
  scene: 'approve' | 'back' | undefined;
}

const service = API.sale.customer.sel;
const apis = [
  'contractManageService,getContractAttachmentByContractId',
  'contractManageService,getContractApproveRelatedAttachmentByContractId',
  'contractManageService,queryContractById',
  'baseServiceCLS,getDorpDownList',
  'contractManageService,getAreaIdByCurrentSales',
  'contractManageService,getDepartmentIdByCurrentSales',
  'baseServiceCLS,getDorpDownList',
  'contractManageService,getPayerByContractId',
  'contractManageService,getQuotationByContractId',
  'contractManageService,getProductLineByContractId',
  'baseServiceCLS,getDorpDownList',
  'contractManageService,getAreaIdByCurrentSales',
  'contractManageService,getDepartmentIdByCurrentSales',
  'baseServiceCLS,getDorpDownList',
  'contractManageService,initData',
  'baseServiceCLS,getDorpDownList',
  'contractManageService,save',
  'serviceTypeService,getSub',
  'quotationService,getQuotation',
  'contractManageService,getAreaIdByCurrentSales',
  'contractManageService,getDepartmentIdByCurrentSales',
  'baseServiceCLS,getDorpDownList',
  'baseServiceCLS,getDorpDownList',
  'contractManageService,delQuotationInContract',
  'contractManageService,delProductLineInContract',
];

const codalNames = ['QuerySelectedQutationWin'];

type TContractDTO = defs.sale.ContractDTO;

const dafaultContractFormConfig = {
  winIsRenew: false,
  winIsRenewOperate: false,
  isPaymentQAResultUpdate: false,
  isBeijingDepartment: false,
  isCommitApprove: false,
  isSelectFirstLegalId: false,
  isUploadApproveRelatedAttachment: false,
  isSelectCsBySale: false,
  isCalculateGrossProfit: false,
  isPhysicalExamination: false,
  isTravelServices: false,
  isEditeAdvancePaymentRatio: false,
  isEditeTravelServicesRatio: false,
  isUpdateContractCategery: true,
  isExecuteStatus: false,
  internalMoneyNeeded: false,
  isIssuingSalary: false,
  isRiskRatio: false,
  isRetireTransact: false,
  isHealthCheckup: false,
  isWelfareGather: false,
  isHealthOnther: false,
  isHealthPlatform: false,
  isRetQuotaGrantedRatio: false,
  isRetirementBusiness: false,
  isRetirementBusinessList: false,
};
const contractFormConfig = { ...dafaultContractFormConfig };
const serviceApproveReject: IServiceType = {
  ...API.crm.slDisaReason.queryByPage,
  cacheKey: API.crm.slDisaReason.queryByPage + '_approveReject',
};

const ContractBackApproveWin: React.FC<QueryContractDetailInfoWinProps> = (props) => {
  const { scene, title, modal, contract: _contract, additional, onConfirm } = props;
  const [visible, setVisible] = modal;
  if (!visible) {
    Object.keys(dafaultContractFormConfig).forEach((key) => {
      contractFormConfig[key] = dafaultContractFormConfig[key];
    });
    return null;
  }
  const contract = { ..._contract, ...additional };
  const { contractId, activityStatus, parentContractId } = contract;
  const currentUser = useSelector((state: ConnectState) => state.user.currentUser, shallowEqual);
  const userBranchId = currentUser.profile?.governingBranch;
  const querySelectedQutationWin = useState(false);
  const quotationViewModal = useState(false);
  const quotationDetail = useState({});
  // console.log('contract in ContractApproveWin:', contract)
  // const propContractType = naturalNumberRegx.test(String(contract.contractType)) ? contract.contractType : undefined
  const [contractType, setContractType] = useState<string>();
  const [contractCategory, setContractCategory] = useState(contract.contractCategery);
  const [contractSubTypeNameMap, setcontractSubTypeNameMap] = useState<POVO>({});
  const [contractTypeNameMap, setcontractTypeNameMap] = useState<POVO>({});
  const [currentContract, setCurrentContarct] = useState<TContractDTO>({});
  const [importFile, setimportFile] = useState<Partial<TContractDTO>>({});
  const [branchTitleDepartId, setBranchTitleDepartId] = useState('');
  const [currentCustId, setcurrentCustId] = useState<string | undefined>(contract.custId);
  const [contractFormConfigCount, setContractFormConfigCount] = useState(0);
  const [signFlagManual, setsignFlagManual] = useState<string>();
  const [isRetireeProduct, setIsRetireeProduct] = useState(false);
  const isContractRedList = useState<string[]>([]);
  const producttColorList = useState<string[]>([]);
  const quotationColorList = useState<string[]>([]);
  const [getSubTypeId, setSubTypeId] = useState<string>();
  const [file, setFile] = useState<any>({});
  const [previewUrl, setPreviewUrl] = useState<string>('');
  const [previewFile, setPreviewFile] = useState<string>('');
  const [previewFileType, setPreviewFileType] = useState<string>('');
  const [isUpload, setIsUpload] = useState<boolean>(false);
  const previewModal = useState(false);

  const setContractFormConfig = (data: POJO<boolean>) => {
    Object.keys(data).forEach((key) => {
      contractFormConfig[key] = data[key];
    });
    setContractFormConfigCount(contractFormConfigCount + 1);
  };

  const [currentSales, setcurrentSales] = useState<string | undefined>(
    contract.currentSales || currentUser.userId,
  );
  const [loadingData, setLoadingData] = useState(false);

  const [mainForm] = Form.useForm();
  const [salesForm] = Form.useForm();
  const [csForm] = Form.useForm();
  const [legalForm] = Form.useForm();
  const [prepayForm] = Form.useForm();
  const [approvalMemoForm] = Form.useForm();
  const [uploadForm] = Form.useForm();
  const [onShowImport, setOnShowImport] = useState(false);
  const [importType, setImportType] = useState<string>('');
  const [retireFile, setRetireFileFile] = useState<any>({});
  const [fileData, setFileData] = useState<any>([]);
  const [whInvoiceOrderId, setWhInvoiceOrderId] = useState<string>();
  const [mainPrepayAmt, setMainPrepayAmt] = useState<boolean>(false);

  const writableQuotation = useWritable({ service: serviceQuotation });
  const writableProductLine = useWritable({ service: serviceProductLine });
  const writableCustPayer = useWritable({ service: serviceCustPayer });
  const writableApproveReject = useWritable({ service: serviceApproveReject });
  const writableUpload = useWritable({ service: serviceCustPayer });
  const writableRetire = useWritable({
    service: { ...serviceCustPayer, cachekey: serviceCustPayer + '1' },
  });

  const [tabErrors, setTabErrors] = useState({
    mainForm: false,
    salesForm: false,
    csForm: false,
    legalForm: false,
    prepayForm: false,
    approveOpinionForm: false,
    writableQuotation: false,
    writableProductLine: false,
    writableCustPayer: false,
    writableUpload: false,
    writableRetire: false,
  });

  const winIsView = contract.activityNameCn === '数据中心核对';

  // const privatePopObject = getCurrentUser();
  // console.log('privatePopObject in ContractApproveWin:', privatePopObject)
  const getDepartInfo = async (departmentId: string | undefined) => {
    // 天大地大，找不到一个获取部门信息的接口。
    if (!departmentId) return {};
    const branchLi = await API.admin.department.queryBranchList.requests({ departmentId });
    if (!branchLi) return {};
    if (!branchLi.list || branchLi.list.length === 0) return {};
    return branchLi.list[0];
  };

  useEffect(() => {
    (async () => {
      if (parentContractId) {
        const list = await API.crm.contractManage.getNotSameParentRecordByContractId.requests({
          contractId: contractId,
        });
        const productList =
          await API.crm.contractManage.getNotSameParentProductLineByContractId.requests({
            contractId: contractId,
            parentContractId: parentContractId,
          });
        const quotationList =
          await API.crm.contractManage.getNotSameParentQuotsByContractId.requests({
            contractId: contractId,
            parentContractId: parentContractId,
          });
        producttColorList[1](productList.list || []);
        quotationColorList[1](quotationList.list || []);
        isContractRedList[1](list.split(',') || []);
      }
    })();
  }, []);

  useEffect(() => {
    if (!contractId) return setLoadingData(false);
    setLoadingData(true);
    (async () => {
      // *********, 	20110614-G-0731-008, available
      let winIsRenew = false;
      let isPaymentQAResultUpdate = false;
      let isBeijingDepartment = false;
      let isSelectCsBySale = false;
      let isSelectFirstLegalId = false;
      let isCalculateGrossProfit = false;
      let isUpdateContractCategery = false;
      let isPhysicalExamination = false;
      let isTravelServices = false;
      let isEditeAdvancePaymentRatio = false;
      let isEditeTravelServicesRatio = false;
      let isRiskRatio = false;
      let isRetireTransact = false; //退休办理
      let isHealthCheckup = false; //健康体检
      let isWelfareGather = false; //福利采集
      let isHealthOnther = false; //福利其他三项
      let isHealthPlatform = false; //福利平台业务
      let isRetirementBusiness = false; //是否有退休业务
      let isRetirementBusinessList = false; //是否有退休业务
      let isRetQuotaGrantedRatio = false;
      let isProduct = false;
      const isUploadApproveRelatedAttachment = false;

      const contr: TContractDTO = await API.crm.contractManage.getContractById.requests({
        contractId,
      });
      contr?.contractSubType && setSubTypeId(contr?.contractSubType);
      const contrAddition = {} as Partial<TContractDTO>;
      const {
        areaType,
        contractHeadcount,
        contractAvgAmt,
        agreedWageArriveDay: _agreedWageArriveDay,
        currentSales,
      } = contract;
      const {
        contractSvcState,
        isAdjustRenewContract,
        isAddedAttachment,
        liabilityCs,
        liabilityCsName,
        contractType,
        contractSubType,
        contractVersion,
        isTravelServices: _isTravelServices,
        departmentId,
        governingArea,
        governingBranchName,
        paymentMode,
      } = contr;

      const isIssuingSalary = contr.isIssuingSalary === stdBoolTrueSrting;
      winIsRenew = contractSvcState === '1';
      const agreedWageArriveDay = Number(_agreedWageArriveDay);
      if (contractHeadcount && !contr?.totalPrice) {
        contrAddition.totalPrice = Calculator.multiply(
          contractHeadcount,
          contractAvgAmt!,
        ).toString();
      }
      if (
        (areaType === '3' && agreedWageArriveDay > 15) ||
        (areaType === '1' && agreedWageArriveDay > 20)
      ) {
        isPaymentQAResultUpdate = true;
      }
      if (contr?.isRetQuotaGranted === '1') {
        isRetQuotaGrantedRatio = true;
      }
      if (currentSales) {
        isSelectCsBySale = true;
        contrAddition.areaId = governingArea;
        contrAddition.departmentId = departmentId;
        contrAddition.departmentName = governingBranchName;
        // contrAddition.areaId = await API.crm.contractManage.getAreaIdByCurrentSales.requests({
        //   saleId: currentSales,
        // });
        // const departmentId = await API.crm.contractManage.getDepartmentIdByCurrentSales.requests({
        //   saleId: currentSales,
        // });
        // contrAddition.departmentId = departmentId;
        if (departmentId) {
          // const departmentInfo = await getDepartInfo(departmentId);
          // contrAddition.departmentName = departmentInfo.departmentName;
          // contrAddition.areaId = departmentInfo.governingAreaId;
          setBranchTitleDepartId(departmentId);
          if (departmentId === '11504') {
            isBeijingDepartment = true;
          }
        }
      } else {
        isSelectCsBySale = false;
        isSelectFirstLegalId = false;
        contrAddition.firstLegalApproveId = undefined;
        contrAddition.firstLegalApproveName = undefined;
      }
      setContractCategory(contr.contractCategery);
      if (liabilityCs) {
        // 不用怀疑，这里就是为了查个名字
        const empInfo = await API.admin.employee.queryEmployee.requests({ empId: liabilityCs });
        contrAddition.liabilityCsName = empInfo?.employeeName;
      }
      const quotations = await API.crm.contractManage.getQuotationByContractId.requests({
        contractId,
      });
      for (const item of quotations?.list) {
        if (item?.isRetireeProduct !== '0') {
          setIsRetireeProduct(true);
          isProduct = true;
        }
      }
      setContractFormConfig({
        isRetirementBusiness: true,
        isRetirementBusinessList: true,
      });
      writableQuotation.setNewData(quotations);
      if (contractType) {
        isUpdateContractCategery = true;
      }
      if (contr?.prepayAmt) {
        if (Number(contr?.prepayAmt) > 0) {
          setMainPrepayAmt(true);
        }
      }
      if (contractSubType) {
        if (['8', '9'].includes(contractSubType)) {
          isCalculateGrossProfit = true;
        }
        if (contractSubType === '10') {
          isPhysicalExamination = true;
        }

        if (contractSubType === '*********') {
          isTravelServices = true;
        }
        if (contractSubType === '406') {
          isRiskRatio = true;
        }
        if (
          contractSubType === '210' ||
          contractSubType === '15' ||
          contractSubType === '14' ||
          contractSubType === '11' ||
          contractSubType === '12' ||
          contractSubType === '13' ||
          contractSubType === '*********'
        ) {
          //退休
          isRetireTransact = true;
        }
        if (contractSubType === '501') {
          //福利集采
          isWelfareGather = true;
        }
        if (contractSubType === '502') {
          //平台业务-福利
          isHealthPlatform = true;
        }
        if (contractSubType === '503') {
          //健康体检
          isHealthCheckup = true;
        }
        if (contractSubType === '504' || contractSubType === '505' || contractSubType === '506') {
          //合同小类为健康管理、雇主险、补医保
          isHealthOnther = true;
        }
        if (contractSubType === '210') {
          //合同小类为退休捆绑小类
          isRetirementBusiness = true;
          isRetirementBusinessList = true;
        }
        if (contr.isRetirementBusiness === '1') {
          isRetirementBusinessList = true;
        } else {
          isRetirementBusinessList = false;
        }
        if (isProduct && isRetireTransact) {
          mainForm.setFieldsValue({
            isRetirementBusiness: '1',
          });
          isRetirementBusinessList = true;
          isRetirementBusiness = true;
        }
      }

      // if (!contractVersion) {
      //   if (!contractSubType || !contractType) {
      //     contrAddition.contractVersion = '未匹配';
      //   } else {
      //     const contractVersionType = contractType === '4' ? '2' : '1';
      //     const contractVersionRes = await API.sale.contract.getContractVersion.request({
      //       contractVersionType: contractVersionType,
      //       contractType: contractType,
      //       contractSubType: contractSubType,
      //     });
      //     if (resError(contractVersionRes)) {
      //       contrAddition.contractVersion = '未匹配';
      //     } else {
      //       contrAddition.contractVersion = contractVersionRes.data;
      //     }
      //   }
      // }
      if (paymentMode === '3') {
        isEditeAdvancePaymentRatio = true;
      }
      if (_isTravelServices == '1') {
        isEditeTravelServicesRatio = true;
      }

      let importFileInfo = {};

      const productLines = await API.crm.contractManage.getProductLineByContractId.requests({
        contractId,
      });
      writableProductLine.setNewData(productLines);
      const payers = await API.crm.contractManage.getPayerByContractId.requests({ contractId });
      writableCustPayer.setNewData(payers);
      if (contractType !== '5') {
        const attachments = await API.crm.contractManage.getContractAttachmentByContractId.request({
          contractId,
        });
        if (!resError(attachments)) {
          const { data } = attachments;
          // if (data.fileId) {
          importFileInfo = {
            importFileId: data.fileId,
            importFileName: data.attName,
            contractFileUploadDt: data.createDt,
            contractFileRemark: data.remark,
          };
          setimportFile(importFileInfo);
          // }
          legalForm.setFieldsValue({ ...importFileInfo, contractFileRemark: data.remark });
        }
      }
      writableApproveReject.request({
        contractId: contractId,
        processInstanceId: contract.processInstanceId,
      });

      const relatedAttachment =
        await API.crm.contractManage.getContractApproveRelatedAttachmentByContractId.request({
          contractId,
        });
      if (!resError(relatedAttachment)) {
        // console.log('relatedAttachment in getContractApproveRelatedAttachmentByContractId:', relatedAttachment)
        if (relatedAttachment.data) {
          contrAddition.approveRelatedAttachment = relatedAttachment.data.fileId;
          contrAddition.approveRelatedAttachmentName = relatedAttachment.data.attName;
          // isUploadApproveRelatedAttachment = true;
        }
      }

      setContractFormConfig({
        ...contractFormConfig,
        winIsRenew,
        isPaymentQAResultUpdate,
        isBeijingDepartment,
        isSelectCsBySale,
        isSelectFirstLegalId,
        isCalculateGrossProfit,
        isUpdateContractCategery,
        isPhysicalExamination,
        isTravelServices,
        isEditeAdvancePaymentRatio,
        isEditeTravelServicesRatio,
        isIssuingSalary,
        isRiskRatio,
        isRetireTransact,
        isHealthCheckup, //健康体检
        isWelfareGather, //福利采集
        isHealthOnther, //福利其他三项
        isHealthPlatform, //福利平台业务
        isRetirementBusiness, //是否有退休业务
        isRetirementBusinessList, //是否有退休业务
        isRetQuotaGrantedRatio,
      });
      // 依次是，getContractById接口返回的值
      // 外部父级组件传入的contract对象
      // 本次回调中，其他接口获取到的额外的值
      const fullContract = {
        ...contract,
        ...contr,
        ...contrAddition,
        ...importFileInfo,
        ...(contr?.travelServicesRatio && {
          travelServicesRatio: contr?.travelServicesRatio * 100,
        }),
      };
      setCurrentContarct(fullContract);
      // console.log('fullContract in Effect:', fullContract)
      if (naturalNumberRegx.test(String(fullContract.contractType))) {
        setContractType(fullContract.contractType);
      }
      mainForm.setFieldsValue(fullContract);
      salesForm.setFieldsValue(fullContract);
      csForm.setFieldsValue(fullContract);
      legalForm.setFieldsValue(fullContract);
      prepayForm.setFieldsValue(fullContract);
      approvalMemoForm.setFieldsValue(fullContract);
      writableUpload.setNewData(fullContract?.contractFileList);
      writableRetire.setNewData(fullContract?.contractRetireeList);
      // getContractApproveRelatedAttachmentByContractId若获得数据，则下一句不需要
      if (!isUploadApproveRelatedAttachment) isAddedAttachmentOnChange(isAddedAttachment);
      uploadForm.setFieldsValue({
        isDeleted: '0',
      });
      setLoadingData(false);
    })();
  }, [contractId]);

  // const onCurrentSalesChange = (sales: defs.sale.DropdownList) => {
  //   if (!sales || isEmpty(sales)) {
  //     const typeList = null;
  //     setContractFormConfig({
  //       ...contractFormConfig,
  //       isSelectFirstLegalId: false,
  //       isSelectCsBySale: false,
  //     });
  //     legalForm.setFieldsValue({
  //       firstLegalApproveId: null,
  //       firstLegalApproveName: null,
  //     });
  //     return;
  //   }
  //   const { key } = sales;
  //   setcurrentSales(key);
  //   API.crm.contractManage.getAreaIdByCurrentSales.requests({ saleId: key! }).then((data) => {
  //     setCurrentContarct({ ...currentContract, areaId: data });
  //   });

  //   API.crm.contractManage.getDepartmentIdByCurrentSales.requests({ saleId: key! }).then((data) => {
  //     setCurrentContarct({ ...currentContract, departmentId: data });
  //     if (data) {
  //       setBranchTitleDepartId(data);
  //       if (data === '11504') {
  //         setContractFormConfig({
  //           ...contractFormConfig,
  //           isBeijingDepartment: true,
  //         });
  //       }
  //     }
  //   });
  //   setContractFormConfig({
  //     ...contractFormConfig,
  //     isSelectFirstLegalId: true,
  //   });
  // };
  const productLineHealthColumns: WritableColumnProps<any>[] = [
    {
      title: '产品线名称',
      dataIndex: 'productlineId',
      inputRender: ({ record }) => {
        const params = { custId: currentCustId, saleId: currentSales };
        const id = record.smcontractProductlineId;
        const data: any = producttColorList[0].find((item) => {
          return id === item?.smcontractProductlineId;
        });
        return (
          <CustProductLineSelector
            className={data?.returnType && data?.returnType === '1' ? 'contractSelectRed' : ''}
            dropdownStyle={{ color: 'red' }}
            params={{
              ...params,
            }}
          />
        );
      },
      onGridChange: (value: Partial<any>, options: GeneralInputRenderOption<any>) => {
        const list = writableProductLine.getList();
        const visible = list.visible.filter((row: any) => row[tableIndexKey] !== options.serial);
        const proIds = visible.map((row: any) => row.productlineId);
        if (proIds.includes(value.productlineId)) {
          msgErr('请勿重复选择同一个产品线!');
          writableProductLine.updateRows({ productlineId: null, [tableIndexKey]: options.serial });
        }
      },
      rules: [{ required: true, message: '请输入产品线名称' }],
    },
    {
      title: '签约人数',
      dataIndex: 'compactNumber',
      inputRender: ({ record }) => {
        const id = record.smcontractProductlineId;
        const data: any = producttColorList[0].find((item: any) => {
          return id === item?.smcontractProductlineId;
        });
        if (data?.returnType && (data?.returnType === '2' || data?.returnType === '4')) {
          return <InputNumber style={{ width: '100%', color: 'red' }} />;
        } else {
          return <InputNumber style={{ width: '100%' }} />;
        }
      },
      rules: [
        { required: true, message: '请输入签约人数' },
        { validator: validateNaturalPositive(), message: '签约人数格式错误' },
      ],
    },
    {
      title: '金额',
      dataIndex: 'averageMoney',
      inputRender: ({ record }) => {
        const id = record.smcontractProductlineId;
        const data: any = producttColorList[0].find((item: any) => {
          return id === item?.smcontractProductlineId;
        });
        if (data?.returnType && (data?.returnType === '3' || data?.returnType === '4')) {
          return <InputNumber style={{ width: '100%', color: 'red' }} />;
        } else {
          return <InputNumber style={{ width: '100%' }} />;
        }
      },
      rules: [{ required: true, message: '请输入人均销售收入' }],
    },
  ];
  const onContractHeadcountChange = (value: string | number | undefined) => {
    if (!value) {
      setCurrentContarct({ ...currentContract, totalPrice: '0' });
      mainForm.setFieldsValue({ totalPrice: '0' });
      return;
    }
    const contractHeadcount = String(value);
    const contractAvgAmt = salesForm.getFieldValue('contractAvgAmt');
    if (contractAvgAmt) {
      const totalPrice = String(Calculator.multiply(contractHeadcount, contractAvgAmt));
      mainForm.setFieldsValue({ totalPrice });
      setCurrentContarct({ ...currentContract, totalPrice });
    }
    setCurrentContarct({ ...currentContract, isQuarterlyPaymentLess20: undefined });
  };

  const onAreaTypeChange = (value: string) => {
    const agreedWageArriveDay = Number(prepayForm.getFieldValue('agreedWageArriveDay'));
    if (
      (value === '3' && agreedWageArriveDay > 15) ||
      (value === '1' && agreedWageArriveDay > 20)
    ) {
      setContractFormConfig({ ...contractFormConfig, isPaymentQAResultUpdate: true });
    } else {
      setContractFormConfig({ ...contractFormConfig, isPaymentQAResultUpdate: false });
    }
    mainForm.setFieldsValue({ isPaymentQAResult: '' });
  };

  const onAgreedWageArriveDayChange = (value: string) => {
    const agreedWageArriveDay = Number(value);
    const areaType = mainForm.getFieldValue('areaType');
    if (
      (areaType === '3' && agreedWageArriveDay > 15) ||
      (areaType === '1' && agreedWageArriveDay > 20)
    ) {
      setContractFormConfig({ ...contractFormConfig, isPaymentQAResultUpdate: true });
    } else {
      setContractFormConfig({ ...contractFormConfig, isPaymentQAResultUpdate: false });
    }
    mainForm.setFieldsValue({ isPaymentQAResult: '' });
  };

  const onContractCategoryChange = (contractCategory: string) => {
    setContractCategory(contractCategory);
    const { contractSubType } = mainForm.getFieldsValue(['contractType', 'contractSubType']);
    if (!contractSubType) {
      mainForm.setFieldsValue({ contractVersion: '未匹配' });
      return;
    }
    getContractVersion();
  };

  const isAddedAttachmentOnChange = (value?: string) => {
    if (value === stdBoolTrueSrting) {
      return setContractFormConfig({
        ...contractFormConfig,
        isUploadApproveRelatedAttachment: true,
      });
    }
    setContractFormConfig({ ...contractFormConfig, isUploadApproveRelatedAttachment: false });
    mainForm.setFieldsValue({
      approveRelatedAttachment: undefined,
      approveRelatedAttachmentName: undefined,
    });
  };

  const onGrossProfitChange = (value: string | number | undefined) => {
    // 如果当前输入为空，则计算结果不会产生变化，可以直接返回。
    if (!value) return;
    // 下面的getFieldsValue能否得到最新的值，尚待验证。
    const { income, tax, executionCost, agentBusiness } = mainForm.getFieldsValue([
      'income',
      'tax',
      'executionCost',
      'agentBusiness',
    ]);
    const grossProfit = new Calculator(0);
    grossProfit.plus(income).minus(tax).minus(executionCost).minus(agentBusiness);
    mainForm.setFieldsValue({ grossProfit: grossProfit.toNumber() });
  };

  const paymentModeOnChange = (value: string | undefined) => {
    if (value === '3') {
      return setContractFormConfig({ ...contractFormConfig, isEditeAdvancePaymentRatio: true });
    }
    setContractFormConfig({ ...contractFormConfig, isEditeAdvancePaymentRatio: false });
    mainForm.setFieldsValue({ advancePaymentRatio: undefined });
  };

  const travelServicesOnChange = (value: string | undefined) => {
    if (value === '1') {
      return setContractFormConfig({ isEditeTravelServicesRatio: true });
    }
    setContractFormConfig({ isEditeTravelServicesRatio: false });
    mainForm.setFieldsValue({ travelServicesRatio: undefined });
  };

  const isInternalPaymentOnChange = (value: string | undefined) => {
    if (value === stdBoolTrueSrting) {
      return setContractFormConfig({ ...contractFormConfig, internalMoneyNeeded: true });
    }
    setContractFormConfig({ ...contractFormConfig, internalMoneyNeeded: false });
    mainForm.setFieldsValue({ internalMoney: undefined });
  };

  const handdleCustConfirm = (value?: defs.sale.Customer) => {
    const custInfo = {
      custId: undefined,
      custName: undefined,
      custCode: undefined,
      hrContract: undefined,
      contactTel: undefined,
      email: undefined,
    };
    if (!value) {
      mainForm.setFieldsValue(custInfo);
      return;
    }
    custInfo.custId = value.custId;
    custInfo.custName = value.custName;
    custInfo.custCode = value.custCode;
    custInfo.hrContract = value.hrContract;
    custInfo.contactTel = value.contactTel;
    custInfo.email = value.email;
    mainForm.setFieldsValue(custInfo);
    setcurrentCustId(value.custId);
    writableQuotation.setData({ list: [], pagination: {} });
    writableCustPayer.setData({ list: [], pagination: {} });
    writableProductLine.setData({ list: [], pagination: {} });
  };

  const onValueFoundContractType = (value: string | undefined, text: string | undefined) => {
    // console.log('value in onValueFoundContractType:', value)
    if (!value) return;
    setContractType(value);
    mainForm.setFieldsValue({ contractType: value, ontractTypeName: text });
  };

  const onContractSubTypeSelectorLoaded = (data: POJO[]) => {
    if (!data || !data[0]) return;
    // const contractSubType = mainForm.getFieldValue('contractSubType');
    // setSubTypeId(contractSubType);
    // mainForm.setFieldsValue({ contractSubType });
    // // onContractSubTypeFlush(contractSubType);
    // getContractVersion();
  };

  const onValueFoundContractSubType = (value: string | undefined, text: string | undefined) => {
    if (!value) return;
    mainForm.setFieldsValue({ contractSubType: value, contractSubTypeName: text });
    // onContractSubTypeChange(value)
    // onContractSubTypeFlush(value);
    getContractVersion();
  };

  const onContractSubTypeFlush = (subTypeId: string | undefined) => {
    if (!subTypeId) return;

    let isUpdateContractCategery: boolean;
    let isCalculateGrossProfit: boolean;
    let isPhysicalExamination: boolean;
    let isTravelServices: boolean;
    let isRiskRatio: boolean;
    let isRetireTransact: boolean; //退休办理
    let isHealthCheckup: boolean; //健康体检
    let isWelfareGather: boolean; //福利采集
    let isHealthOnther: boolean; //福利其他三项
    let isHealthPlatform: boolean; //福利平台业务
    let isRetirementBusiness: boolean; //是否有退休业务
    let isRetirementBusinessList: boolean; //是否有退休业务

    const contractType = mainForm.getFieldValue('contractType');
    if (subTypeId !== '7' && contractType === '4') {
      mainForm.setFieldsValue({ contractCategery: '2' });
      isUpdateContractCategery = false;
    } else {
      isUpdateContractCategery = true;
    }
    if (['8', '9'].includes(subTypeId)) {
      isCalculateGrossProfit = true;
    } else {
      isCalculateGrossProfit = false;
      mainForm.setFieldsValue({
        income: null,
        tax: null,
        executionCost: null,
        agentBusiness: null,
        grossProfit: null,
      });
    }
    if (subTypeId === '10') {
      isPhysicalExamination = true;
    } else {
      isPhysicalExamination = false;
      mainForm.setFieldsValue({
        peIncome: null,
        peExecutionCost: null,
        peGrossProfit: null,
        // paymentMode: null,
        isInternalPayment: null,
        internalMoney: null,
        // advancePaymentRatio: null,
      });
    }
    if (subTypeId === '*********') {
      isTravelServices = true;
    } else {
      isTravelServices = false;
      mainForm.setFieldsValue({
        isTravelServices: null,
        travelServicesRatio: null,
      });
    }
    if (subTypeId === '406') {
      isRiskRatio = true;
    } else {
      isRiskRatio = false;
      mainForm.setFieldsValue({
        riskSharingRatio: null,
        riskPremiumRatio: null,
      });
    }
    if (
      subTypeId === '210' ||
      subTypeId === '15' ||
      subTypeId === '14' ||
      subTypeId === '11' ||
      subTypeId === '12' ||
      subTypeId === '13' ||
      subTypeId === '*********'
    ) {
      //退休
      isRetireTransact = true;
      isRetirementBusinessList = true;
    } else {
      isRetireTransact = false;
      isRetirementBusinessList = false;
    }
    if (subTypeId === '501') {
      //福利集采
      isWelfareGather = true;
    } else {
      isWelfareGather = false;
    }
    if (subTypeId === '502') {
      //平台业务-福利
      isHealthPlatform = true;
    } else {
      isHealthPlatform = false;
    }
    if (subTypeId === '503') {
      //健康体检
      isHealthCheckup = true;
    } else {
      isHealthCheckup = false;
    }
    if (subTypeId === '504' || subTypeId === '505' || subTypeId === '506') {
      //合同小类为健康管理、雇主险、补医保
      isHealthOnther = true;
    } else {
      isHealthOnther = false;
    }
    if (subTypeId === '210') {
      //合同小类为退休捆绑小类
      isRetirementBusiness = true;
      isRetirementBusinessList = true;
      mainForm.setFieldsValue({
        isRetirementBusiness: '1',
      });
    } else {
      // isRetirementBusiness = false;
      // isRetirementBusinessList = false;
      // mainForm.setFieldsValue({
      //   // isRetirementBusiness: '',
      //   isRetQuotaGranted: '0',
      //   retirementGiftCount: '',
      // });
      // isRetirementBusinessList = false;
      // mainForm.setFieldsValue({
      //   isRetirementBusiness: '',
      // });
    }
    if (isRetireeProduct) {
      mainForm.setFieldsValue({
        isRetirementBusiness: '1',
      });
      isRetirementBusinessList = true;
      isRetirementBusiness = true;
    }
    setContractFormConfig({
      isUpdateContractCategery,
      isCalculateGrossProfit,
      isPhysicalExamination,
      isTravelServices,
      isRiskRatio,
      isRetireTransact,
      isHealthCheckup,
      isWelfareGather,
      isHealthPlatform,
      isHealthOnther,
      isRetirementBusiness,
      isRetirementBusinessList,
    });
  };

  const onContractSubTypeChange = (subTypeId: string | undefined) => {
    onContractSubTypeFlush(subTypeId);
    if (subTypeId) {
      setSubTypeId(subTypeId);
      getContractVersion({ contractSubType: subTypeId });
      mainForm.setFieldsValue({
        whInvoiceOrder: '2',
        whPeRate: '1',
        signBranchTitleId: '',
      });
      setWhInvoiceOrderId('2');
    } else {
      mainForm.setFieldsValue({
        contractVersion: '未匹配',
        peIncome: null,
        peExecutionCost: null,
        peGrossProfit: null,
        paymentMode: null,
        isInternalPayment: null,
        internalMoney: null,
        advancePaymentRatio: null,
        travelServicesRatio: null,
        isTravelServices: null,
        signBranchTitleId: null,
      });
      setContractFormConfig({
        isUpdateContractCategery: false,
        isCalculateGrossProfit: false,
        isPhysicalExamination: false,
        isTravelServices: false,
      });
    }
  };

  const onContractTypeChange = (svcTypeId: string) => {
    mainForm.setFieldsValue({ contractSubType: null, signBranchTitleId: '' });
    onContractSubTypeChange(undefined);
    if (!svcTypeId) return;
    if (svcTypeId == '5') {
      //健康大类时总售价重新计算
      const list = writableQuotation.getList().all;
      const quotationList = list.map((q) => ({
        ...list,
        quotationTotalOhPrice: q?.quotationTotalOhPrice ? Number(q?.quotationTotalOhPrice) : '',
      }));
      if (quotationList.length > 0) {
        mainForm.setFieldsValue({
          totalPrice: sumArray(quotationList, 'quotationTotalOhPrice').toFixed(2),
        });
      }
    } else {
      const contractHeadcount = mainForm.getFieldValue('contractHeadcount');
      const contractAvgAmt = salesForm.getFieldValue('contractAvgAmt');
      if (contractAvgAmt) {
        const totalPrice = String(Calculator.multiply(contractHeadcount, contractAvgAmt));
        mainForm.setFieldsValue({ totalPrice });
        setCurrentContarct({ ...currentContract, totalPrice });
      }
    }
    setContractType(svcTypeId);
    mainForm.setFieldsValue({ contractSubType: null, contractSubTypeName: null });
  };

  const contractAvgAmtonChange = (value: string | undefined) => {
    if (!value) {
      setCurrentContarct({ ...currentContract, totalPrice: '0' });
      mainForm.setFieldsValue({ totalPrice: '0' });
      return;
    }

    const contractAvgAmt = String(value);
    const contractHeadcount = mainForm.getFieldValue('contractHeadcount');
    if (contractHeadcount) {
      const totalPrice = String(Calculator.multiply(contractHeadcount, contractAvgAmt));
      setCurrentContarct({ ...currentContract, totalPrice });
      mainForm.setFieldsValue({ totalPrice });
    }
  };

  const getContractVersion = (params?: POJO) => {
    const { contractType, contractSubType, contractCategery } = mainForm.getFieldsValue([
      'contractType',
      'contractSubType',
      'contractCategery',
    ]);
    if (!contractSubType || !contractType) return;
    API.sale.contract.getContractVersion
      .request({
        contractVersionType: contractCategery,
        contractType: contractType,
        contractSubType: contractSubType,
        ...params,
      })
      .then((res: StdRes<string>) => {
        if (resError(res)) {
          return mainForm.setFieldsValue({ contractVersion: '未匹配' });
        }
        mainForm.setFieldsValue({ contractVersion: res.data || '未匹配' });
      });
  };

  const handdleMultiConfirmQoutaions = (quotations?: defs.emphiresep.Quotation[]) => {
    if (!quotations) return;
    const fullQ = quotations.map((q) => ({
      ...q,
      suppltMedInsurHeadCount: q.suppltMedInsurHeadcount,
      status: String(q.status),
      quotationTotalOhPrice: q?.quotationTotalOhPrice ? Number(q?.quotationTotalOhPrice) : '',
    }));
    let isProd = 0;
    for (const item of fullQ) {
      if (item?.isRetireeProduct !== '0') {
        isProd += 1;
      }
    }
    if (contractType === '5') {
      mainForm.setFieldsValue({ totalPrice: sumArray(fullQ, 'quotationTotalOhPrice').toFixed(2) });
    }
    if (isProd > 0 && contractFormConfig?.isRetireTransact) {
      setIsRetireeProduct(true);
      mainForm.setFieldsValue({
        isRetirementBusiness: '1',
      });
      setContractFormConfig({
        isRetirementBusinessList: true,
        isRetirementBusiness: true,
      });
    } else {
      setIsRetireeProduct(false);
      setContractFormConfig({
        isRetirementBusiness: false,
      });
    }
    writableQuotation.setNewData(fullQ);
  };
  const sumArray = (arr: any[], prop: string) => {
    return arr.reduce((sum, obj) => {
      const value = typeof obj[prop] === 'number' ? obj[prop] : 0;
      return sum + value;
    }, 0);
  };
  const viewQuotation = async (quotationId: string) => {
    const data = await API.sale.quotation.getQuotationData.requests(
      { quotationId: quotationId },
      { params: { quotationId: quotationId } },
    );
    let initData: POJO = {};
    if (data.quotationList.length > 0) {
      initData = { ...initData, ...data, ...data.quotationList[0], ...data.saleList[0] };
    }
    if (data.saleList.length > 0) {
      initData = { ...initData, atr: multipleNum(data.saleList[0].atr) };
    }

    if (data.businessDetailList.length > 0) {
      initData = {
        ...initData,
        wvatr: data.businessDetailList[0].vatr,
        watr: parseFloat(data.businessDetailList[0].atr) * 100,
      };
    }
    if (data.healthDetailList.length > 0) {
      initData = {
        ...initData,
        wvatr: data.healthDetailList[0].vatr,
        watr: parseFloat(data.healthDetailList[0].atr) * 100,
      };
    }

    if (data.EmployerDetailList.length > 0) {
      initData = {
        ...initData,
        wvatr: data.EmployerDetailList[0].vatr,
        watr: parseFloat(data.EmployerDetailList[0].atr) * 100,
      };
    }
    quotationDetail[1]({ ...initData, type: 'VIEW', text: '', title: '查看报价单' });
    quotationViewModal[1](true);
  };
  const activityNameEn712 =
    Boolean(currentContract.activityNameEn) &&
    ['7', '12'].includes(currentContract.activityNameEn!);

  const SignFlagManualSelectoronChange = (signFlagM: string) => {
    setsignFlagManual(signFlagM);
  };

  const viewNonStaCoctApprId = (nonStaCoctApprId: string) => {
    // console.log('nonStaCoctApprId in viewNonStaCoctApprId:', nonStaCoctApprId)
  };

  const addonNonStaCoctApprId = (disabled: boolean) => {
    // console.log('currentContract in addonNonStaCoctApprId:', currentContract)
    const { nonStaCoctApprId } = currentContract;
    return (
      <Typography.Link
        disabled={disabled || !nonStaCoctApprId}
        onClick={() => viewNonStaCoctApprId}
      >
        查看
      </Typography.Link>
    );
  };

  const onImportFile = (info: UploadChangeParam<UploadFile<any>>) => {
    if (info.file.status === 'done') {
      legalForm.setFieldsValue({ contractFileUploadDt: today() });
    } else {
      legalForm.setFieldsValue({ contractFileUploadDt: null });
    }
  };

  const handdleMultiConfirmCustPayer = (custPayers?: defs.crm.CustomerPayerDTO[]) => {
    writableCustPayer.setNewData(custPayers);
  };

  const onisIssuingSalaryChange = (isIssuingSalary: string) => {
    setContractFormConfig({ isIssuingSalary: isIssuingSalary === stdBoolTrueSrting });
  };
  const isRetirementBusinessChange = (value: string | undefined) => {
    if (value === '1') {
      return setContractFormConfig({ isRetirementBusinessList: true });
    }
    writableRetire.resetFields();
    writableRetire.setNewData([]);
    setContractFormConfig({ isRetirementBusinessList: false, isRetQuotaGrantedRatio: false });
    mainForm.setFieldsValue({ retirementGiftCount: undefined, isRetQuotaGranted: '0' });
  };
  const isRetQuotaGrantedOnChange = (value: string | undefined) => {
    if (value === '1') {
      return setContractFormConfig({ isRetQuotaGrantedRatio: true });
    }
    setContractFormConfig({ isRetQuotaGrantedRatio: false });
    mainForm.setFieldsValue({ retirementGiftCount: undefined });
  };
  const handleWhInvoiceOrder = (value: string) => {
    setWhInvoiceOrderId(value);
  };
  const handleMainPrepayAmt = (value) => {
    if (value > 0) {
      setMainPrepayAmt(true);
    } else {
      setMainPrepayAmt(false);
    }
  };
  const searchCustTianyan = (disabled: boolean) => {
    if (disabled) {
      return (
        <Typography.Link
          // disabled={disabled || !nonStaCoctApprId}
          onClick={() => window.open('https://www.tianyancha.com/search?key=' + disabled)}
        >
          核查客户
        </Typography.Link>
      );
    } else {
      return null;
    }
  };
  const uploadFormColumns: EditeFormProps[] = [
    {
      label: '附件类型',
      fieldName: 'fileType',
      inputRender: () => (
        <GetSignFlagManualSelect
          params={{
            type: '9132',
          }}
          onChange={(value) => {
            if (!value && !uploadForm.getFieldValue('isDeleted')) {
              handleUploadQuery();
            }
          }}
        />
      ),
    },
    {
      label: '是否有效',
      fieldName: 'isDeleted',
      inputRender: () =>
        mapToSelectors(isValidMap, {
          onChange: (value) => {
            if (!value && !uploadForm.getFieldValue('fileType')) {
              handleUploadQuery();
            }
          },
        }),
    },
  ];
  const formColumns: EditeFormProps[] = [
    {
      label: '合同编号',
      fieldName: 'contractCode',
      inputRender: 'string',
      inputProps: {
        disabled: true,
      },
    },
    {
      label: '合同名称',
      fieldName: 'contractName',
      rules: [{ required: true, message: '请输入合同名称' }],
      inputRender: 'string',
    },
    {
      label: colorRed(
        isContractRedList[0].includes('3') || isContractRedList[0].includes('4'),
        '总售价',
      ),
      fieldName: 'totalPrice',
      rules: [{ required: true, message: '请输入总售价' }],
      inputRender: 'number',
      inputProps: {
        disabled: true,
      },
    },
    {
      label: colorRed(isContractRedList[0].includes('1'), '合同大类名称'),
      fieldName: 'contractType',
      rules: [{ required: true, message: '请输入合同大类名称' }],
      inputRender: () => (
        <ContractTypeSelector
          onValueFound={onValueFoundContractType}
          onChange={onContractTypeChange}
          disabled
        />
      ),
    },
    {
      label: colorRed(isContractRedList[0].includes('2'), '合同小类名称'),
      fieldName: 'contractSubType',
      rules: [{ required: true, message: '请输入合同小类名称' }],
      inputRender: () => (
        <ContractgetSubTypeSelector
          skipEmptyParam
          params={{ svcTypeId: contractType }}
          onValueFound={onValueFoundContractSubType}
          onLoaded={onContractSubTypeSelectorLoaded}
          onChange={onContractSubTypeChange}
          disabled
        />
      ),
    },
    {
      label: colorRed(isContractRedList[0].includes('3'), '预计签约人数'),
      fieldName: 'contractHeadcount',
      rules: [
        { validator: validateNaturalPositive(), message: '预计签约人数格式错误' },
        { required: true, message: '请输入预计签约人数' },
      ],
      inputRender: () => <InputNumber onChange={onContractHeadcountChange} />,
    },
    {
      label: '客户编号',
      fieldName: 'custCode',
      rules: [{ required: true, message: '请输入客户编号' }],
      inputRender: (outerForm: FormInstance) => (
        <CustomerPop
          rowValue="custCode-custId-custName-custCode"
          handdleConfirm={handdleCustConfirm}
          keyMap={{
            custId: 'custId',
            custCode: 'custCode',
            hrContract: 'hrContract',
            contactTel: 'contactTel',
            email: 'email',
          }}
          disabled
          fixedValues={{
            userRoleType: '1',
          }}
          addonAfter={searchCustTianyan(outerForm.getFieldValue('custName'))}
        />
      ),
    },
    {
      label: '客户全称',
      fieldName: 'custName',
      inputRender: (outerForm: FormInstance) => {
        return <Input disabled value={outerForm.getFieldValue('custName')} />;
      },
    },
    // {
    //   label: '现销售',
    //   fieldName: 'currentSales',
    //   rules: [{ required: true, message: '请输入现销售' }],
    //   inputRender: (outerForm: FormInstance) => {
    //     return (
    //       <CurrentSalesSelector
    //         disabled={!currentCustId}
    //         onConfirm={onCurrentSalesChange}
    //         params={{ custId: currentCustId, deptId: userBranchId }}
    //         skipEmptyParam
    //       />
    //     );
    //   },
    // },
    {
      label: '现销售',
      fieldName: 'salesName',
      rules: [{ required: true, message: '请输入现销售' }],
      inputRender: 'string',
    },
    {
      label: '客户联系人',
      fieldName: 'hrContract',
      inputRender: 'string',
    },
    {
      label: '邮件地址',
      fieldName: 'email',
      inputRender: 'string',
      rules: [{ validator: validateEmail }],
    },

    {
      label: '联系电话',
      fieldName: 'contactTel',
      inputRender: 'string',
      // editable="{(((privatePopObject.processDefCode == 'ContractCs')
    },
    {
      label: '开始日期',
      fieldName: '',
      inputRender: () => (
        <DateRange
          fields={[
            {
              title: '开始日期',
              dataIndex: 'contractStartDate',
              rules: [{ required: true, message: '请输入开始日期' }],
            },
            {
              title: '结束日期',
              dataIndex: 'contractStopDate',
              rules: [{ required: true, message: '请输入结束日期' }],
            },
          ]}
          // colConf={column3}
          format={stdDateFormat}
        />
      ),
    },
    {
      label: colorRed(isContractRedList[0].includes('9'), '签约方公司抬头'),
      fieldName: 'signBranchTitleId',
      rules: [{ required: true, message: '请输入签约方公司抬头' }],
      inputRender: () => {
        if (getSubTypeId === '501' || getSubTypeId === '502') {
          //福利签约方抬头
          return <GetSignFlagManualSelect params={{ type: '9128' }} />;
        } else if (getSubTypeId === '504') {
          //健康管理
          return <GetSignFlagManualSelect params={{ type: '9129' }} />;
        } else if (getSubTypeId === '505' || getSubTypeId === '506') {
          //雇主险 补医保
          return <GetSignFlagManualSelect params={{ type: '9130' }} />;
        } else if (getSubTypeId === '503') {
          //健康体检
          return <GetSignFlagManualSelect params={{ type: '9131' }} />;
        } else {
          return (
            <BranchTitleSpeSelector skipEmptyParam params={{ departmentId: branchTitleDepartId }} />
          );
        }
      },
    },
    {
      label: '合同类别',
      fieldName: 'contractCategery',
      rules: [{ required: true, message: '请输入合同类别' }],
      inputRender: () => <ContractCategorySelector onChange={onContractCategoryChange} />,
      inputProps: {
        disabled: !contractFormConfig.isUpdateContractCategery,
      },
    },
    {
      label: '合同版本',
      fieldName: 'contractVersion',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    {
      label: '现销售城市',
      fieldName: 'cityName',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    {
      label: colorRed(isContractRedList[0].includes('5'), '服务区域类型'),
      fieldName: 'areaType',
      rules: [{ required: true, message: '请输入服务区域类型' }],
      inputRender: () => mapToSelectors(contractAreaTypeMap, { onChange: onAreaTypeChange }),
    },
    {
      label: colorRed(isContractRedList[0].includes('10'), '缴费类型'),
      fieldName: 'payType',
      rules: [{ required: true, message: '请输入缴费类型' }],
      inputRender: () => <PayTypeSelector />,
    },
    {
      label: '现销售大区',
      fieldName: 'formerGoverningAreaName',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    {
      label: '范本修改版合同备注',
      fieldName: 'modelModifyVersionRemark',
      inputRender: 'text',
      rules: [
        {
          required: contractCategory === '2',
          message: '【范本修改版合同备注】不能为空',
        },
      ],
    },

    {
      label: '服务地区',
      fieldName: 'svcRegion',
      inputRender: 'text',
    },

    {
      label: '备注',
      fieldName: 'memo',
      inputRender: 'text',
    },
    {
      label: '是否抢单',
      fieldName: 'isRob',
      inputRender: () => <BooleanSelector />,
      hidden:
        contractFormConfig.isHealthOnther ||
        contractFormConfig.isWelfareGather ||
        contractFormConfig.isHealthPlatform ||
        contractFormConfig.isHealthCheckup,
    },
    {
      label: '竞争对手',
      fieldName: 'competitor',
      br: true,
      inputRender: () => <CompetitorSelector />,
      hidden:
        contractFormConfig.isHealthOnther ||
        contractFormConfig.isWelfareGather ||
        contractFormConfig.isHealthPlatform ||
        contractFormConfig.isHealthCheckup,
    },
    {
      label: colorRed(isContractRedList[0].includes('23'), '是否为已有客户所推荐'),
      fieldName: 'isCustRecommend',
      rules: [{ required: true, message: '请输入是否为已有客户所推荐' }],
      inputRender: () => <BooleanSelector />,
      hidden:
        contractFormConfig.isHealthOnther ||
        contractFormConfig.isWelfareGather ||
        contractFormConfig.isHealthPlatform ||
        contractFormConfig.isHealthCheckup,
    },
    {
      label: colorRed(isContractRedList[0].includes('8'), '是否代发薪资'),
      fieldName: 'isIssuingSalary',
      rules: [{ required: true, message: '请选择是否代发薪资' }],
      inputRender: () => <BooleanSelector order="asc" onChange={onisIssuingSalaryChange} />,
      hidden:
        contractFormConfig.isHealthOnther ||
        contractFormConfig.isWelfareGather ||
        contractFormConfig.isHealthPlatform ||
        contractFormConfig.isHealthCheckup,
    },
    {
      label: colorRed(isContractRedList[0].includes('6'), '是否集中一地投保'),
      fieldName: 'isSameInsur',
      rules: [{ required: true, message: '请选择是否集中一地投保' }],
      inputRender: () => <BooleanSelector order="asc" />,
      hidden:
        contractFormConfig.isHealthOnther ||
        contractFormConfig.isWelfareGather ||
        contractFormConfig.isHealthPlatform ||
        contractFormConfig.isHealthCheckup,
    },
    {
      label: colorRed(isContractRedList[0].includes('7'), '是否增强型代理'),
      fieldName: 'enhancedAgent',
      // rules: [{ required: true, message: '请选择是否增强型代理' }],
      inputRender: () => <Switchs defaultValue="0" />,
      hidden:
        contractFormConfig.isHealthOnther ||
        contractFormConfig.isWelfareGather ||
        contractFormConfig.isHealthPlatform ||
        contractFormConfig.isHealthCheckup,
    },
    {
      label: '是否客服二次开发',
      fieldName: 'isSecondaryDev',
      inputRender: () => <Switchs />,
      hidden:
        contractFormConfig.isHealthOnther ||
        contractFormConfig.isWelfareGather ||
        contractFormConfig.isHealthPlatform ||
        contractFormConfig.isHealthCheckup,
    },
    {
      label: '是否降价、垫付、账期延期',
      fieldName: 'isDefer',
      inputRender: () => <Switchs />,
      hidden:
        contractFormConfig.isHealthOnther ||
        contractFormConfig.isWelfareGather ||
        contractFormConfig.isHealthPlatform ||
        contractFormConfig.isHealthCheckup,
    },
    {
      label: '是否包含退休业务',
      fieldName: 'isRetirementBusiness',
      noBorder: true,
      inputProps: {
        hidden: !contractFormConfig.isRetireTransact,
        disabled: contractFormConfig.isRetirementBusiness,
        onChange: (e: any) => isRetirementBusinessChange(e),
      },
      inputRender: () => <Switchs />,
    },
    {
      label: '是否有赠送退休额度',
      fieldName: 'isRetQuotaGranted',
      noBorder: true,
      inputProps: {
        hidden: !contractFormConfig.isRetireTransact,
        disabled: !contractFormConfig.isRetirementBusinessList,
        onChange: (e: any) => isRetQuotaGrantedOnChange(e),
      },
      inputRender: () => <Switchs />,
    },
    {
      label: '赠送退休数量',
      fieldName: 'retirementGiftCount',
      noBorder: true,
      inputProps: {
        hidden: !contractFormConfig.isRetireTransact,
        disabled: !contractFormConfig.isRetQuotaGrantedRatio,
      },
      rules: [
        { validator: validateNaturalPositive('格式错误必须为正整数') },
        { required: contractFormConfig.isRetQuotaGrantedRatio, message: '请填写赠送退休数量' },
      ],
      inputRender: 'number',
    },

    {
      label: colorRed(isContractRedList[0].includes('15'), '含差旅服务'),
      fieldName: 'isTravelServices',
      noBorder: true,
      inputProps: {
        hidden: !contractFormConfig.isTravelServices,
        onChange: (e: any) => travelServicesOnChange(e),
      },
      inputRender: () => <Switchs />,
    },
    {
      label: colorRed(isContractRedList[0].includes('16'), '差旅服务费比例%'),
      fieldName: 'travelServicesRatio',
      inputRender: 'number',
      inputProps: {
        hidden: !contractFormConfig.isTravelServices,
        disabled: !contractFormConfig.isEditeTravelServicesRatio,
      },
      rules: [
        {
          required: contractFormConfig.isEditeTravelServicesRatio,
          message: '请输入0~100的两位小数',
        },
        {
          pattern: /^(100|([1-9][0-9]?)|(0|[1-9][0-9]?)(\.[\d]{1,2}))$/,
          message: '请输入0~100的两位小数',
        },
      ],
    },
    {
      label: '体检税率%',
      fieldName: 'whPeRate',
      inputRender: () => (
        <GetSignFlagManualSelect
          params={{
            type: '9133',
          }}
        />
      ),
      inputProps: { hidden: !contractFormConfig.isHealthCheckup },
      rules: [
        {
          required: contractFormConfig.isHealthCheckup,
          message: '体检税率%不能为空',
        },
      ],
    },
    {
      label: '提成销售',
      fieldName: 'whCommissionSale',
      inputRender: () => {
        return (
          <EmployeePop
            rowValue="whCommissionSale-whCommissionSaleName"
            keyMap={{
              whCommissionSale: 'EMPID',
              whCommissionSaleName: 'REALNAME',
            }}
          />
        );
      },
      inputProps: {
        hidden:
          !contractFormConfig.isHealthCheckup &&
          !contractFormConfig.isWelfareGather &&
          !contractFormConfig.isHealthOnther &&
          !contractFormConfig.isHealthPlatform,
      },
      // rules: [
      //   {
      //     required:
      //       contractFormConfig.isHealthCheckup ||
      //       contractFormConfig.isWelfareGather ||
      //       contractFormConfig.isHealthOnther,
      //     message: '提成销售不能为空',
      //   },
      // ],
    },
    // {
    //   label: '合同审核相关的附件',
    //   fieldName: 'approveRelatedAttachment',
    //   inputRender: 'upload',
    //   inputProps: {
    //     maxSize: FileSize._20MB, // 最大只能传_20MB
    //     fileName: 'approveRelatedAttachmentName',
    //     // 这里必传，当代办页使用此页面是，funcId不存在，导致上传出错。
    //     bizType: '10702000',
    //     // disabled: !contractFormConfig.isUploadApproveRelatedAttachment,//说是不让使用了 先行注释掉
    //     disabled: true,
    //   },
    //   rules: [
    //     {
    //       required: contractFormConfig.isUploadApproveRelatedAttachment,
    //       message: '【合同审核相关的附件】不能为空',
    //     },
    //   ],
    // },
    // 收入
    {
      label: colorRed(
        isContractRedList[0].includes('11') ||
          isContractRedList[0].includes('12') ||
          isContractRedList[0].includes('13') ||
          isContractRedList[0].includes('14'),
        '毛利',
      ),
      fieldName: 'grossProfit',
      rules: [
        {
          required: contractFormConfig.isCalculateGrossProfit,
          message: '【毛利】不能为空',
        },
        {
          pattern: /(^-?(?:\d+|\d{1,3}(?:,\d{3})+)(?:\.\d{1,2})?$)/,
          message: '必须为数字，且只能保留两位小数',
        },
        // { validator: validateCurrency('收入格式错误') },
      ],
      inputRender: () => <InputNumber onChange={onGrossProfitChange} />,
      inputProps: { hidden: !contractFormConfig.isCalculateGrossProfit },
    },
    {
      label: '职场健康付款方式',
      fieldName: 'paymentMode',
      inputRender: () => mapToSelectors(paymentModeMap, { onChange: paymentModeOnChange }),
      inputProps: {
        hidden: !contractFormConfig.isHealthCheckup,
      },
      rules: [
        {
          required: contractFormConfig.isHealthCheckup,
          message: '职场健康付款方式',
        },
      ],
    },
    {
      label: '职场健康预付款比例%',
      fieldName: 'advancePaymentRatio',
      inputRender: 'number',
      inputProps: {
        hidden: !contractFormConfig.isHealthCheckup,
        disabled: !contractFormConfig.isEditeAdvancePaymentRatio,
      },
      rules: [
        {
          required: contractFormConfig.isEditeAdvancePaymentRatio,
          message: '【职场健康预付款比例】不能为空',
        },
      ],
    },
    {
      label: '职场健康毛利率%',
      fieldName: 'whMargin',
      inputRender: 'number',
      inputProps: {
        hidden:
          !contractFormConfig.isHealthCheckup &&
          !contractFormConfig.isWelfareGather &&
          !contractFormConfig.isHealthOnther &&
          !contractFormConfig.isHealthPlatform,
        disabled: true,
      },
      rules: [{ pattern: /^(0|[1-9]\d*)(?:\.\d{1,3})?$/, message: '最多保留三位小数' }],
    },
    {
      label: '毛利率%',
      fieldName: 'peGrossProfit',
      inputRender: 'number',
      inputProps: { hidden: !contractFormConfig.isPhysicalExamination },
      rules: [
        {
          required: contractFormConfig.isPhysicalExamination,
          message: '【毛利率%】不能为空',
        },
      ],
    },
    {
      label: '收入',
      fieldName: 'income',
      rules: [
        {
          required: contractFormConfig.isCalculateGrossProfit,
          message: '【收入】不能为空',
        },
        { validator: validateCurrency('收入格式错误') },
      ],
      inputRender: () => <InputNumber onChange={onGrossProfitChange} />,
      inputProps: { hidden: !contractFormConfig.isCalculateGrossProfit },
    },
    {
      label: colorRed(isContractRedList[0].includes('12'), '税费'),
      fieldName: 'tax',
      rules: [
        {
          required: contractFormConfig.isCalculateGrossProfit,
          message: '【税费】不能为空',
        },
        { validator: validatePositive('税费格式错误') },
      ],
      inputRender: () => <InputNumber onChange={onGrossProfitChange} />,
      inputProps: {
        hidden: !contractFormConfig.isCalculateGrossProfit,
      },
    },
    {
      label: colorRed(isContractRedList[0].includes('13'), '执行成本'),
      fieldName: 'executionCost',
      rules: [
        {
          required: contractFormConfig.isCalculateGrossProfit,
          message: '【执行成本】不能为空',
        },
        {
          validator: validateCurrency('代收代付格式错误。'),
        },
      ],
      inputRender: () => <InputNumber onChange={onGrossProfitChange} />,
      inputProps: {
        hidden: !contractFormConfig.isCalculateGrossProfit,
      },
    },
    {
      label: colorRed(isContractRedList[0].includes('14'), '代收代付'),
      fieldName: 'agentBusiness',
      rules: [
        {
          required: contractFormConfig.isCalculateGrossProfit,
          message: '【合同审核相关的附件】不能为空',
        },
        {
          validator: validateCurrency('代收代付格式错误。'),
        },
      ],
      inputRender: () => <InputNumber onChange={onGrossProfitChange} />,
      inputProps: {
        hidden: !contractFormConfig.isCalculateGrossProfit,
      },
    },
    {
      label: colorRed(isContractRedList[0].includes('11'), '收入'),
      fieldName: 'peIncome',
      rules: [
        {
          required: contractFormConfig.isPhysicalExamination,
          message: '【收入】不能为空',
        },
        {
          validator: validateCurrency('收入格式错误。'),
        },
      ],
      inputRender: 'number',
      inputProps: {
        hidden: !contractFormConfig.isPhysicalExamination,
      },
    },
    {
      label: '预估成本',
      fieldName: 'peExecutionCost',
      rules: [
        {
          required: contractFormConfig.isPhysicalExamination,
          message: '【合同审核相关的附件】不能为空',
        },
        {
          validator: validateCurrency('预估成本格式错误。'),
        },
      ],
      inputRender: 'number',
      inputProps: { hidden: !contractFormConfig.isPhysicalExamination },
    },
    {
      label: '付款方式',
      fieldName: 'paymentMode',
      inputRender: () => mapToSelectors(paymentModeMap, { onChange: paymentModeOnChange }),
      inputProps: {
        hidden: !contractFormConfig.isPhysicalExamination,
      },
      rules: [
        {
          required: contractFormConfig.isPhysicalExamination,
          message: '【付款方式】不能为空',
        },
      ],
    },
    {
      label: '预付款比例%',
      fieldName: 'advancePaymentRatio',
      inputRender: 'number',
      inputProps: {
        hidden: !contractFormConfig.isPhysicalExamination,
        disabled: !contractFormConfig.isEditeAdvancePaymentRatio,
      },
      rules: [
        {
          required: contractFormConfig.isEditeAdvancePaymentRatio,
          message: '【预付款比例】不能为空',
        },
      ],
    },
    {
      label: '开票顺序',
      fieldName: 'whInvoiceOrder',
      inputRender: () => (
        <GetSignFlagManualSelect
          style={{ color: whInvoiceOrderId === '1' ? 'red' : '#000000' }}
          params={{
            type: '9127',
          }}
        />
      ),
      inputProps: {
        hidden:
          !contractFormConfig.isHealthCheckup &&
          !contractFormConfig.isWelfareGather &&
          !contractFormConfig.isHealthOnther &&
          !contractFormConfig.isHealthPlatform,
        onChange: handleWhInvoiceOrder,
      },
      rules: [
        {
          required:
            contractFormConfig.isHealthCheckup ||
            contractFormConfig.isWelfareGather ||
            contractFormConfig.isHealthOnther ||
            contractFormConfig.isHealthPlatform,
          message: '【开票顺序】不能为空',
        },
      ],
    },
    {
      label: '垫付额度',
      fieldName: 'prepayAmt',
      inputRender: 'number',
      inputProps: {
        hidden:
          !contractFormConfig.isHealthCheckup &&
          !contractFormConfig.isWelfareGather &&
          !contractFormConfig.isHealthOnther &&
          !contractFormConfig.isHealthPlatform,
        onChange: handleMainPrepayAmt,
      },
      rules: [validDateCost],
    },
    {
      label: '预计垫付时长（天）',
      fieldName: 'whExpectedPrepayDay',
      inputRender: 'number',
      inputProps: {
        hidden:
          !contractFormConfig.isHealthCheckup &&
          !contractFormConfig.isWelfareGather &&
          !contractFormConfig.isHealthOnther &&
          !contractFormConfig.isHealthPlatform,
      },
      rules: [
        {
          required: mainPrepayAmt,
          message: '【预计垫付时长（天）】不能为空',
        },
      ],
    },
    {
      label: '项目编号',
      fieldName: 'whItemCode',
      inputRender: 'string',
      inputProps: {
        hidden: !contractFormConfig.isHealthCheckup,
      },
      rules: [
        {
          required: true,
          message: '【项目编号】不能为空',
        },
      ],
    },
    {
      label: '销售发票类型',
      fieldName: 'whSaleInvoiceType',
      inputRender: () => (
        <GetSignFlagManualSelect
          params={{
            type: '9134',
          }}
        />
      ),
      inputProps: {
        hidden:
          !contractFormConfig.isHealthCheckup &&
          !contractFormConfig.isWelfareGather &&
          !contractFormConfig.isHealthOnther &&
          !contractFormConfig.isHealthPlatform,
      },
      rules: [
        {
          required:
            contractFormConfig.isHealthCheckup ||
            contractFormConfig.isWelfareGather ||
            contractFormConfig.isHealthOnther ||
            contractFormConfig.isHealthPlatform,
          message: '【销售发票类型】不能为空',
        },
      ],
    },
    {
      label: '返佣收入',
      fieldName: 'whRebateIncome',
      inputRender: 'number',
      inputProps: {
        hidden: !contractFormConfig.isHealthPlatform,
      },
      rules: [validDateCost],
    },
    {
      label: '返佣税费',
      fieldName: 'whRebateTax',
      inputRender: 'number',
      inputProps: {
        hidden: !contractFormConfig.isHealthPlatform,
      },
      rules: [validDateCost],
    },
    {
      label: '合同寄送地址',
      fieldName: 'whContractSendAddress',
      inputRender: 'string',
      inputProps: { hidden: !contractFormConfig.isWelfareGather },
      rules: [
        {
          required: contractFormConfig.isWelfareGather,
          message: '【合同寄送地址】不能为空',
        },
      ],
      colNumber: 1,
    },
    {
      label: '采购发票类型',
      fieldName: 'whPurchaseInvoiceType',
      inputRender: () => (
        <GetSignFlagManualSelect
          params={{
            type: '9137',
          }}
        />
      ),
      inputProps: {
        hidden: !contractFormConfig.isWelfareGather && !contractFormConfig.isHealthPlatform,
      },
    },
    {
      label: '垫付备注',
      fieldName: 'whPrepayRemark',
      inputRender: 'text',
      inputProps: {
        hidden:
          !contractFormConfig.isHealthCheckup &&
          !contractFormConfig.isWelfareGather &&
          !contractFormConfig.isHealthOnther &&
          !contractFormConfig.isHealthPlatform,
      },
      // rules: [
      //   {
      //     required: contractFormConfig.isHealthCheckup || contractFormConfig.isWelfareGather,
      //     message: '【垫付备注】不能为空',
      //   },
      // ],
    },
    {
      label: '销售发票内容',
      fieldName: 'whSaleInvoiceContent',
      inputRender: 'text',
      inputProps: {
        hidden:
          !contractFormConfig.isHealthCheckup &&
          !contractFormConfig.isWelfareGather &&
          !contractFormConfig.isHealthOnther &&
          !contractFormConfig.isHealthPlatform,
      },
    },
    {
      label: '采购发票内容',
      fieldName: 'whPurchaseInvoiceContent',
      inputRender: 'text',
      inputProps: {
        hidden: !contractFormConfig.isWelfareGather && !contractFormConfig.isHealthPlatform,
      },
    },
    {
      label: '预付款时间',
      fieldName: 'whAdvancePaymentDt',
      inputRender: 'date',
      inputProps: {
        hidden: !contractFormConfig.isWelfareGather && !contractFormConfig.isHealthPlatform,
      },
    },
    {
      label: '尾款时间',
      fieldName: 'whFinalPaymentDt',
      inputRender: 'date',
      inputProps: {
        hidden: !contractFormConfig.isWelfareGather && !contractFormConfig.isHealthPlatform,
      },
    },
    {
      label: '支付供货商货款时间',
      fieldName: 'whSupplierPaymentDt',
      inputRender: 'date',
      inputProps: {
        hidden: !contractFormConfig.isWelfareGather && !contractFormConfig.isHealthPlatform,
      },
    },
    {
      label: '预付款金额',
      fieldName: 'whAdvancePaymentAmt',
      inputRender: 'string',
      inputProps: {
        hidden: !contractFormConfig.isWelfareGather && !contractFormConfig.isHealthPlatform,
      },
      rules: [validDateCost],
    },
    {
      label: '尾款金额',
      fieldName: 'whFinalPaymentAmt',
      inputRender: 'number',
      inputProps: {
        hidden: !contractFormConfig.isWelfareGather && !contractFormConfig.isHealthPlatform,
      },
      rules: [validDateCost],
    },
    {
      label: '风险分担比例%',
      fieldName: 'riskSharingRatio',
      inputRender: 'number',
      inputProps: {
        hidden: !contractFormConfig.isRiskRatio,
      },
      rules: [
        {
          required: contractFormConfig.isRiskRatio,
          message: '【风险分担比例】不能为空',
        },
        {
          pattern: /(^(\d|[1-9]\d)(\.\d{1,2})?$)|(^100$)/,
          message: '请输入0~100的两位小数',
        },
      ],
    },
    {
      label: '风险金比例%',
      fieldName: 'riskPremiumRatio',
      inputRender: 'number',
      inputProps: {
        hidden: !contractFormConfig.isRiskRatio,
      },
      rules: [
        {
          required: contractFormConfig.isRiskRatio,
          message: '【风险金比例】不能为空',
        },
        {
          pattern: /(^(\d|[1-9]\d)(\.\d{1,2})?$)|(^100$)/,
          message: '请输入0~100的两位小数',
        },
      ],
    },
    {
      label: '是否内支',
      fieldName: 'isInternalPayment',
      inputRender: () => <Switchs onChange={isInternalPaymentOnChange} />,
      inputProps: {
        hidden: !contractFormConfig.isPhysicalExamination,
      },
    },
    {
      label: '内支金额',
      fieldName: 'internalMoney',
      rules: [
        {
          required: contractFormConfig.internalMoneyNeeded,
          message: '【预付款比例】不能为空',
        },
        {
          validator: validateCurrency('预估成本格式错误。'),
        },
      ],
      inputRender: 'number',
      inputProps: {
        hidden: !contractFormConfig.isPhysicalExamination,
        disabled: !contractFormConfig.internalMoneyNeeded,
      },
    },
    // {
    //   label: '非标合同审批单',
    //   fieldName: 'nonStaCoctApprId',
    //   inputProps: { disabled: !userBranchId || !currentCustId || !currentSales },
    //   inputRender: () => {
    //     // console.log('currentCustId in nonStaCoctApprId:', currentCustId)
    //     return (
    //       <NonStaCoctApprPop
    //         rowValue="nonStaCoctApprId-applyCode"
    //         addonAfter={addonNonStaCoctApprId(!userBranchId)}
    //         fixedValues={{ custId: currentCustId, currentSales }}
    //       />
    //     );
    //   },
    // },
    // {
    //   label: '用章审核意见',
    //   fieldName: 'sealOpinion',
    //   inputRender: 'string',
    //   inputProps: { disabled: true },
    // },
    // {
    //   label: 'QA审核意见',
    //   fieldName: 'qaApprove',
    //   inputRender: 'string',
    //   inputProps: { disabled: true },
    // },

    // {
    //   label: '是否有补充附件',
    //   fieldName: 'isAddedAttachment',
    //   rules: [{ required: true, message: '请输入是否有补充附件' }],
    //   inputRender: () => <BooleanSelector onChange={isAddedAttachmentOnChange} />,
    // },
    // {
    //   label: '合同审核相关的附件',
    //   fieldName: 'approveRelatedAttachment',
    //   inputRender: 'upload',
    //   inputProps: {
    //     maxSize: FileSize._20MB,
    //     fileName: 'approveRelatedAttachmentName',
    //     bizType: '10702000',
    //     disabled: !contractFormConfig.isUploadApproveRelatedAttachment,
    //   },
    //   rules: [
    //     {
    //       required: contractFormConfig.isUploadApproveRelatedAttachment,
    //       message: '【合同审核相关的附件】不能为空',
    //     },
    //   ],
    // },
    // 收入
    // {
    //   label: '本次续签是否需要调整合同条款？',
    //   fieldName: 'isAdjustRenewContract',
    //   inputRender: () => <BooleanSelector />,
    //   rules: [
    //     {
    //       required: contractFormConfig.winIsRenew,
    //       message: '[本次续签是否需要调整合同条款？]不能为空',
    //     },
    //   ],
    //   inputProps: {
    //     hidden: !contractFormConfig.winIsRenew,
    //   },
    // },
  ];

  const salesFormColumns: EditeFormProps[] = [
    {
      label: colorRed(isContractRedList[0].includes('4'), '签约人均价格'),
      fieldName: 'contractAvgAmt',
      rules: [
        { required: true, message: '请输入签约人均价格' },
        { validator: validateCurrency('人均价格格式不正确') },
      ],
      inputRender: () => <InputNumber onChange={contractAvgAmtonChange} />,
    },
    {
      label: '预计12个月内可达到人数',
      fieldName: 'estimatedHeadcount',
      rules: [
        { validator: validateNaturalPositive('人数格式错误必须为正整数') },
        { required: true, message: '请输入预计12个月内可达到人数' },
      ],
      inputRender: 'number',
    },
    {
      label: '预估首次账单日期',
      fieldName: 'estimateFirstBillDate',
      rules: [{ required: true, message: '请输入预估首次账单日期' }],
      inputRender: 'date',
    },
    {
      label: '未来商机',
      fieldName: 'furtureOpportunity',
      inputRender: 'text',
    },
    // {
    //   label: '销售审核意见',
    //   fieldName: 'salesApprove',
    //   inputRender: 'text',
    //   inputProps: { disabled: true },
    //   br: true,
    //   colNumber: 1,
    // },
  ];

  const prepayFormColumns: EditeFormProps[] = [
    {
      label: colorRed(isContractRedList[0].includes('17'), '账单日期(天)'),
      fieldName: 'billDt',
      inputRender: 'number',
      rules: [{ validator: validateNaturalDay() }],
    },
    {
      label: colorRed(isContractRedList[0].includes('18'), '约定到款月'),
      fieldName: 'agereedAmtReceiveMon',
      inputRender: () => mapToSelectors(agereedAmtReceiveMonMap),
    },
    {
      label: colorRed(isContractRedList[0].includes('19'), '约定到款日(天)'),
      fieldName: 'agreedWageArriveDay',
      rules: [
        // { min: 1, message: '日期应当不得小于1' },
        // { min: 31, message: '日期应当不得大于31' },
        { validator: validateNaturalDay() },
        { required: true, message: '请输入约定到款日(天)' },
      ],
      inputRender: () => {
        return <InputNumber onChange={onAgreedWageArriveDayChange} />;
      },
    },
    {
      label: '账期（天）',
      fieldName: 'creditPeriod',
      inputRender: 'number',
      rules: [{ validator: validateNaturalDay() }],
    },
    {
      // 这里很神奇，我在 ContractBackApproveWin.mxml 里没有找到这个字段，但是老系统界面上却有。
      // 也许是后来加上的。
      label: colorRed(isContractRedList[0].includes('21'), '薪资发放月'),
      fieldName: 'payMonth',
      inputRender: () => mapToSelectors(payMonthMapMap),
      rules: [
        {
          required: contractFormConfig.isIssuingSalary,
          message: '当[代发薪资]为是时，薪资发放月必填',
        },
      ],
    },
    {
      label: colorRed(isContractRedList[0].includes('22'), '薪资发放日'),
      fieldName: 'agreedPayDt',
      inputRender: 'number',
      rules: [{ validator: validateNaturalDay() }],
    },
    {
      label: colorRed(isContractRedList[0].includes('20'), '垫款额度'),
      fieldName: 'prepayAmt',
      rules: [{ validator: validateCurrency('垫款额度格式不正确') }],
      inputRender: 'string',
    },
    // {
    //   label: '垫付审核意见',
    //   fieldName: 'prepayApproval',
    //   inputRender: 'string',
    //   inputProps: { disabled: true },
    // },
  ];

  const productLineColumns: WritableColumnProps<any>[] = [
    {
      title: '产品线名称',
      dataIndex: 'productlineId',
      inputRender: ({ record }) => {
        const params = { custId: currentCustId, saleId: currentSales };
        const id = record.smcontractProductlineId;
        const data: any = producttColorList[0].find((item) => {
          return id === item?.smcontractProductlineId;
        });
        return (
          <CustProductLineSelector
            className={data?.returnType && data?.returnType === '1' ? 'contractSelectRed' : ''}
            dropdownStyle={{ color: 'red' }}
            params={{
              ...params,
            }}
          />
        );
      },
      onGridChange: (value: Partial<any>, options: GeneralInputRenderOption<any>) => {
        const list = writableProductLine.getList();
        const visible = list.visible.filter((row: any) => row[tableIndexKey] !== options.serial);
        const proIds = visible.map((row: any) => row.productlineId);
        if (proIds.includes(value.productlineId)) {
          msgErr('请勿重复选择同一个产品线!');
          writableProductLine.updateRows({ productlineId: null, [tableIndexKey]: options.serial });
        }
      },
      rules: [{ required: true, message: '请输入产品线名称' }],
    },
    {
      title: '签约人数',
      dataIndex: 'compactNumber',
      inputRender: ({ record }) => {
        const id = record.smcontractProductlineId;
        const data: any = producttColorList[0].find((item: any) => {
          return id === item?.smcontractProductlineId;
        });
        if (data?.returnType && (data?.returnType === '2' || data?.returnType === '4')) {
          return <InputNumber style={{ width: '100%', color: 'red' }} />;
        } else {
          return <InputNumber style={{ width: '100%' }} />;
        }
      },
      rules: [
        { required: true, message: '请输入签约人数' },
        { validator: validateNaturalPositive(), message: '签约人数格式错误' },
      ],
    },
    {
      title: '人均销售收入',
      dataIndex: 'averageMoney',
      inputRender: ({ record }) => {
        const id = record.smcontractProductlineId;
        const data: any = producttColorList[0].find((item: any) => {
          return id === item?.smcontractProductlineId;
        });
        if (data?.returnType && (data?.returnType === '3' || data?.returnType === '4')) {
          return <InputNumber style={{ width: '100%', color: 'red' }} />;
        } else {
          return <InputNumber style={{ width: '100%' }} />;
        }
      },
      rules: [{ required: true, message: '请输入人均销售收入' }],
    },
  ];

  const csFormColumns: EditeFormProps[] = [
    {
      label: '指定交接客服',
      fieldName: 'liabilityCs',
      inputRender: () => (
        <InnerUserPop
          rowValue="liabilityCs-liabilityCsDepartmentId-liabilityCsName"
          fixedValues={{
            roleCode: '20940',
            //branchId: currentContract.departmentId,
            isExcludeDefault: 1,
          }}
          keyMap={{
            liabilityCs: 'EMPID',
            liabilityCsName: 'REALNAME',
            liabilityCsDepartmentId: 'BRANCHID',
          }}
        />
      ),
      rules: [
        {
          required: true,
          message: '请选择指定交接客服。',
        },
      ],
    },
    {
      label: '指定交接事项',
      fieldName: 'transferRemark',
      inputRender: 'text',
    },
    // {
    //   label: '客服审核意见',
    //   fieldName: 'csApproval',
    //   inputRender: 'string',
    //   inputProps: { disabled: true },
    // },
  ];

  const quotationColumns: WritableColumnProps<any>[] = [
    // TODO:报价单去重
    {
      title: '报价单编号',
      dataIndex: 'quotationCode',
      render: (value, record) => {
        const id = record.quotationCode;
        const data: any = quotationColorList[0].find((item) => {
          return id === item?.QUOTATION_CODE;
        });
        if (!isEmpty(data)) {
          if (data?.returnType && data?.returnType === '1') {
            return colorRed(true, value);
          }
        } else {
          return value;
        }
      },
    },
    {
      title: '报价单名称',
      dataIndex: 'quotationName',
    },
    { title: '预计签约人数', dataIndex: 'suppltMedInsurHeadCount' },
    { title: '备注', dataIndex: 'remark' },
    { title: '薪税社保业务售价', dataIndex: 'quotationTotalSalPrice' },
    { title: '职场健康总售价', dataIndex: 'quotationTotalOhPrice' },
    {
      title: '报价单状态',
      dataIndex: 'status',
      render: (outerForm?: FormInstance, record?: Store) => QuostatusMap.get(record?.status),
    },
    {
      title: '查看明细',
      dataIndex: 'view',
      render: (text: string, record: any) => (
        <Link onClick={() => viewQuotation(record.quotationId)}>查看</Link>
      ),
    },
  ];

  const legalFormColumns: EditeFormProps[] = [
    {
      label: '首个法务',
      fieldName: 'firstLegalApproveId',
      inputRender: () => (
        <InnerUserPop
          rowValue="firstLegalApproveId-firstLegalApproveName"
          keyMap={{ firstLegalApproveId: 'EMPID', firstLegalApproveName: 'REALNAME' }}
          fixedValues={{ roleCode: 60095 }}
        />
      ),
      inputProps: { disabled: !contractFormConfig.isSelectFirstLegalId },
    },
    // {
    //   label: '合同附件',
    //   fieldName: 'importFileId',
    //   inputRender: 'upload',
    //   inputProps: {
    //     maxSize: FileSize._20MB, // 最大只能传_20MB
    //     fileName: 'importFileName',
    //     bizType: '10702000',
    //     onChange: onImportFile,
    //   },
    // },
    // {
    //   label: '附件上传时间',
    //   fieldName: 'contractFileUploadDt',
    //   inputRender: 'string',
    //   inputProps: { disabled: true },
    // },
    {
      label: '合同附件备注',
      fieldName: 'contractFileRemark',
      inputRender: 'text',
    },
    // {
    //   label: '法务审核意见',
    //   fieldName: 'legalApproval',
    //   inputRender: 'text',
    //   inputProps: { disabled: true },
    // },
  ];

  const custPayerColumns: WritableColumnProps<any>[] = [
    { title: '付款方编号', dataIndex: 'custPayerId' },
    { title: '付款方名称', dataIndex: 'payerName' },
    {
      title: '发票抬头',
      dataIndex: 'checkTitle',
    },
  ];

  const approvalMemoFormColumns: EditeFormProps[] = [
    {
      label: '历史审批意见',
      fieldName: 'salesApprove',
      inputRender: 'text',
      inputProps: { disabled: true },
      br: true,
      colNumber: 1,
    },
    {
      label: '审批意见',
      fieldName: 'approveOpinion',
      inputRender: 'text',
      rules: [{ required: true, message: '请填写审批意见' }],
    },
  ];

  const quotationDel = async () => {
    const quotationAll = writableQuotation.getList().visible;
    const deleted = writableQuotation.selectedSingleRow;
    const quotationList = quotationAll.filter((item: any) => {
      return item.quotationCode !== deleted.quotationCode;
    });
    const fullQ = quotationList.map((q: any) => ({
      ...q,
      quotationTotalOhPrice: q?.quotationTotalOhPrice ? Number(q?.quotationTotalOhPrice) : '',
    }));
    let isProd = 0;
    for (const item of fullQ) {
      if (item?.isRetireeProduct !== '0') {
        isProd += 1;
        setIsRetireeProduct(true);
      }
    }
    if (contractType === '5') {
      mainForm.setFieldsValue({ totalPrice: sumArray(fullQ, 'quotationTotalOhPrice').toFixed(2) });
    }
    if (isProd > 0) {
      mainForm.setFieldsValue({
        isRetirementBusiness: '1',
      });
      setContractFormConfig({
        isRetirementBusinessList: true,
        isRetirementBusiness: true,
      });
    } else {
      setContractFormConfig({
        isRetirementBusiness: false,
      });
    }
    if (deleted[addedInLocal] !== addedInLocal) {
      await API.crm.contractManage.delQuotationInContract.requests({ ...deleted, contractId });
      msgOk('删除报价单成功。');
    }
    writableQuotation.deleteRows(writableQuotation.selectedSingleRow, { hard: true });
  };

  const productLineAdd = () => {
    writableProductLine.addRows();
  };

  const productLineDel = async () => {
    writableProductLine.deleteRows(writableProductLine.selectedRows);
    const deleted = writableProductLine.selectedSingleRow;
    if (deleted[addedInLocal] !== addedInLocal) {
      await API.crm.contractManage.delProductLineInContract.requests({ ...deleted, contractId });
      msgOk('删除产品线成功。');
    }
    writableProductLine.deleteRows(writableProductLine.selectedSingleRow, { hard: true });
  };

  const deletecustPayer = async () => {
    const deleted = writableCustPayer.selectedSingleRow;
    if (deleted[addedInLocal] !== addedInLocal) {
      await API.crm.contractManage.delPayerInContract.requests({ ...deleted, contractId });
      msgOk('删除付款方列表成功。');
    }
    writableCustPayer.deleteRows(writableCustPayer.selectedSingleRow, { hard: true });
  };
  // console.log('currentContract in QueryContractDetailInfoWin:', currentContract)
  // console.log('importFile in QueryContractDetailInfoWin:', importFile)

  const collecteFullContract = async (action: 'approve' | 'reject' | 'terminate') => {
    let mainData: Store;
    let salesData: Store;
    let csFormData: Store;
    let legalFormData: Store;
    let prepayFormData: Store;
    let quotations: POJO;
    let productLines: POJO;
    let custPayers: POJO;
    let approvalMemoFormData: Store;
    let importData: Store;
    let retireData: Store;
    const tabErrs = { ...tabErrors };
    if (action !== 'approve') {
      mainData = mainForm.getFieldsValue();
      salesData = salesForm.getFieldsValue();
      csForm.getFieldsValue();
      csFormData = legalFormData = legalForm.getFieldsValue();
      prepayFormData = prepayForm.getFieldsValue();

      quotations = writableQuotation.getList();
      productLines = writableProductLine.getList();
      custPayers = writableCustPayer.getList();
      approvalMemoFormData = approvalMemoForm.getFieldsValue();
    } else {
      try {
        mainData = await mainForm.validateFields();
        tabErrs.mainForm = false;
      } catch (e) {
        setTabErrors({ ...tabErrs, mainForm: true });
        throw e;
      }

      try {
        salesData = await salesForm.validateFields();
        tabErrs.salesForm = false;
      } catch (e) {
        setTabErrors({ ...tabErrs, salesForm: true });
        throw e;
      }
      try {
        csFormData = await csForm.validateFields();
        tabErrs.csForm = false;
      } catch (e) {
        setTabErrors({ ...tabErrs, csForm: true });
        throw e;
      }
      try {
        legalFormData = await legalForm.validateFields();
        tabErrs.legalForm = false;
      } catch (e) {
        setTabErrors({ ...tabErrs, legalForm: true });
        throw e;
      }

      try {
        prepayFormData = await prepayForm.validateFields();
        tabErrs.prepayForm = false;
      } catch (e) {
        setTabErrors({ ...tabErrs, prepayForm: true });
        throw e;
      }

      quotations = await writableQuotation.validateFields();
      productLines = await writableProductLine.validateFields();
      custPayers = await writableCustPayer.validateFields();
      approvalMemoFormData = await approvalMemoForm.validateFields();
    }
    try {
      if (isUpload) {
        await API.crm.contractManage.getContractFileListByContractId
          .requests({
            contractId: contract.contractId,
            isDeleted: '0',
          })
          .then((data: any) => {
            importData = data.list;
          });
      } else {
        importData = await (await writableUpload.validateFields()).visible;
      }

      tabErrs.writableUpload = false;
    } catch (e) {
      setTabErrors({ ...tabErrs, writableUpload: true });
      throw e;
    }
    try {
      retireData = await writableRetire.validateFields();
      tabErrs.writableRetire = false;
    } catch (e) {
      setTabErrors({ ...tabErrs, writableRetire: true });
      throw e;
    }
    if (mainData?.contractSubType !== '503') {
      if (importData.length === 0) {
        msgErr('常规合同至少需要合同文件一个附件');
        return Promise.reject();
      }
    } else {
      if (mainData.contractCategery === '2') {
        if (importData.length === 0) {
          msgErr('合同小类为体检且合同类别是范本修改版合同，合同文件必传');
          return Promise.reject();
        }
      }
    }
    let fileListNum9 = 0;
    for (const item of importData) {
      if (item?.fileType === '9') {
        fileListNum9 += 1;
      }
    }
    if (mainData.isRetirementBusiness === '1') {
      if (retireData.visible?.length < 1) {
        msgErr('是否包含退休业务为是时退休人员列表至少有一条数据。');
        return Promise.reject();
      }
    }
    if (quotations.visibleCount === 0 || productLines.visibleCount === 0) {
      msgErr('报价单产品线都至少需要一条');
      return Promise.reject();
    }

    let payerIds: string | undefined = undefined;
    if (custPayers.allCount > 0) {
      // TODO: 这里原码是Array
      payerIds = custPayers.visible.map((cust) => cust.custPayerId).join(',');
    }
    const quoIds = quotations.visible.map((quot) => quot.quotationId).join(',');
    // setCurrentContarct({ ...currentContract, quotationIds: quotationIds.join(',') })
    const contractProductLineIds: string[] = [];
    const productLineIds: string[] = [];
    const compactNumbers: string[] = [];
    const averageMoneys: string[] = [];
    const productLineIdSet: Set<string> = new Set();
    productLines.visible.forEach((line) => {
      contractProductLineIds.push(line.smcontractProductlineId);
      productLineIds.push(line.productlineId);
      productLineIdSet.add(line.productlineId);
      compactNumbers.push(line.compactNumber);
      averageMoneys.push(line.averageMoney);
    });
    if (productLineIds.length !== productLineIdSet.size) {
      setTabErrors({ ...tabErrs, writableQuotation: true });
      msgErr('报价单模块下，产品线有重复记录');
      return Promise.reject();
    }
    const formData = {
      ...currentContract,
      ...mainData,
      ...salesData,
      ...csFormData,
      ...legalFormData,
      ...prepayFormData,
      ...approvalMemoFormData,
      ...(mainData?.travelServicesRatio && {
        travelServicesRatio: mainData?.travelServicesRatio / 100,
      }),
      quoIds,
      contractProductLineIds: contractProductLineIds.join(','),
      productLineIds: productLineIds.join(','),
      compactNumbers: compactNumbers.join(','),
      averageMoneys: averageMoneys.join(','),
      contractFileList: importData,
      contractRetireeList: retireData.visible,
    };
    if (payerIds) formData.payerIds = payerIds;
    if (contractTypeNameMap.hasOwnProperty(formData.contractType)) {
      const contractTypeCode = contractTypeNameMap[formData.contractType];
      formData.contractTypeName = formData.contractType;
      formData.contractType = contractTypeCode;
    }
    if (contractSubTypeNameMap.hasOwnProperty(formData.contractSubType)) {
      const contractSubTypeCode = contractSubTypeNameMap[formData.contractSubType];
      formData.contractSubTypeName = formData.contractSubType;
      formData.contractSubType = contractSubTypeCode;
    }
    return formData;
  };

  const approve = async () => {
    const formData = await collecteFullContract('approve');
    setLoadingData(true);
    try {
      await API.crm.contractManage.approve.requests(formData);
    } catch (e) {
      setLoadingData(false);
      throw e;
    }
    msgOk('通过此合同的请求成功');
    setLoadingData(false);
    setVisible(false);
    onConfirm();
  };

  const reject = async () => {
    const formData = await collecteFullContract('reject');
    setLoadingData(true);
    try {
      await API.crm.contractManage.back.requests(formData);
    } catch (e) {
      setLoadingData(false);
      throw e;
    }
    msgOk('驳回此合同的请求成功');
    setLoadingData(false);
    setVisible(false);
    onConfirm();
  };

  const terminate = async () => {
    const formData = await collecteFullContract('terminate');
    setLoadingData(true);
    try {
      await API.crm.contractManage.terminal.requests(formData);
    } catch (e) {
      setLoadingData(false);
      throw e;
    }
    msgOk('终止此合同的请求成功');
    setLoadingData(false);
    setVisible(false);
    onConfirm();
  };
  const approveRejectColumns: WritableColumnProps<any>[] = [
    { title: '步骤', dataIndex: 'activityNameCn' },
    {
      title: '驳回原因',
      dataIndex: 'reasonStr',
    },
    {
      title: '驳回备注',
      dataIndex: 'reasonBz',
      render: (value) => {
        return (
          <Tooltip placement="left" title={value}>
            <span>{value}</span>
          </Tooltip>
        );
      },
    },
    { title: '操作人', dataIndex: 'createByStr' },
    { title: '创建时间', dataIndex: 'createDt' },
    { title: '驳回批次', dataIndex: 'disaBatchId' },
  ];
  const ImportColumns: WritableColumnProps<any>[] = [
    { title: '附件类型', dataIndex: 'fileTypeName' },
    // { title: '附件名称', dataIndex: 'fileTypeName' },
    { title: '备注', dataIndex: 'remark' },
    // { title: '上传步骤', dataIndex: 'fileTypeName' },
    { title: '上传人', dataIndex: 'createBy' },
    { title: '上传时间', dataIndex: 'createDt' },
    { title: '是否有效', dataIndex: 'isDeleted', render: (r) => isValidMap.get(r) },
    { title: '删除人', dataIndex: 'deleteBy' },
    { title: '删除时间', dataIndex: 'deleteDt' },
    {
      title: '下载',
      dataIndex: 'filePath',
      render: (text, record) => {
        return (
          <Link onClick={() => exportImportFile(record.filePath, record.fileName)}>
            {record.fileName ? record.fileName : ''}
          </Link>
        );
      },
    },
    {
      title: '预览',
      dataIndex: '',
      render: (text, record) => {
        return (
          <Link onClick={() => filePreview(record.filePath, record.fileName)}>
            {record.fileName ? record.fileName : ''}
          </Link>
        );
      },
    },
  ];
  const filePreview = (fileId: string, fileName: string) => {
    if (!fileId || !fileName) {
      return msgErr('无文件预览');
    }
    API.minio.minIo.downloadFile
      .request({ minioPath: fileId, fileName }, { responseType: 'blob' })
      .then((res) => {
        if (res) {
          setPreviewFileType(getFileExtension(fileId));
          const url = URL.createObjectURL(res);
          setPreviewUrl(url);
          setPreviewFile(res);
          previewModal[1](true);
        }
      });
  };
  const exportImportFile = (fileId: string, fileName: string) => {
    if (!fileId || !fileName) {
      return msgErr('无文件下载');
    }
    API.minio.minIo.downloadFile
      .request({ minioPath: fileId, fileName }, { responseType: 'blob' })
      .then((res) => {
        if (res) {
          downloadFileWithAlert(res, fileName);
        }
      });
  };
  const retireColumns: WritableColumnProps<any>[] = [
    { title: '证件号码', dataIndex: 'idCardNum', inputRender: 'string' },
    {
      title: '姓名',
      dataIndex: 'empName',
      inputRender: 'string',
    },
    {
      title: '备注',
      dataIndex: 'bz',
      inputRender: 'string',
    },
  ];
  const onImportBtn = (type: string) => {
    setImportType(type);
    setOnShowImport(true);
  };
  const importHandle = async (data: POJO) => {
    if (fileData?.length < 1) return msgErr('请选择文件');
    if (importType === '1') {
      const fileList: any[] = [];
      fileData.forEach((item) => {
        fileList.push({ ...item, ...data, ...retireFile, activityNameEn: '0' });
      });
      await writableUpload.addRows(fileList);
      setFile(undefined);
    }
    setOnShowImport(false);
  };
  const onDelImport = (type: string) => {
    // setImportType(type);
    const itemData = writableUpload.selectedSingleRow;
    if (isEmpty(itemData)) {
      return msgErr('请选择要删除的单行数据');
    }
    if (contract.activityNameEn === itemData?.activityNameEn || itemData?.activityNameEn === '1') {
      writableUpload.deleteRows(writableUpload.selectedSingleRow);
    } else {
      return msgErr('仅能删除当前步骤上传的文件,或者销售提交的文件');
    }
  };
  const onImport = (option: any) => {
    const { file } = option;
    setRetireFileFile({
      ...retireFile,
      fileName: file?.name,
    });
  };
  const handleFileType = (v, o) => {
    setRetireFileFile({ ...retireFile, fileTypeName: o.title });
  };
  let filelist: any = [];
  const customRequest = async (option: any) => {
    const { file, onError, onSuccess } = option;
    await API.minio.minIo.uploadFile
      .request({
        file,
        functionId: '10702000',
      })
      .then((res) => {
        filelist = filelist.concat({
          fileName: file.name,
          filePath: res?.message,
        });
        onSuccess(res, file);
        setFileData(filelist);
      })
      .catch(onError);
  };
  const showUploadList = {
    showRemoveIcon: true,
    removeIcon: <DeleteOutlined />,
  };
  const onRemove = (file) => {
    const list = fileData.filter((item) => item.fileName !== file.name);
    setFileData(list);
  };
  const ImportFormColumns: EditeFormProps[] = [
    {
      label: '附件类型',
      fieldName: 'fileType',
      inputRender: () => (
        <GetSignFlagManualSelect
          params={{
            type: '9132',
          }}
          onChange={(value, option) => handleFileType(value, option)}
        />
      ),
      rules: [{ required: true, message: '请选择附件类型' }],
      colNumber: 3,
    },
    {
      label: '备注',
      fieldName: 'remark',
      inputRender: 'text',
    },
    {
      label: '上传文件',
      fieldName: 'upload',
      rules: [{ required: true, message: '请选择文件' }],
      inputRender: () => (
        <div style={{ display: 'inline-block' }}>
          <Upload
            customRequest={customRequest}
            maxCount={10}
            multiple
            showUploadList={showUploadList}
            // onPreview={() => onPreview(type)}
            // fileList={fileList}
            onRemove={onRemove}
          >
            <Button>
              <UploadOutlined /> 附件{' '}
            </Button>
          </Upload>
        </div>
      ),
    },
  ];
  const ImportFormColumns1: EditeFormProps[] = [
    {
      label: '上传文件',
      fieldName: 'upload',
      rules: [{ required: true, message: '请选择文件' }],
      inputRender: 'upload',
      inputProps: {
        maxSize: FileSize._20MB, // 最大只能传_20MB
        bizType: '10702000',
        onChange: onImport,
      },
    },
  ];
  const onBatchDownload = async () => {
    const rows = writableUpload.selectedRows;
    const minioPaths = rows.map((item) => {
      return { filePath: item.filePath, fileName: item.fileName };
    });
    if (minioPaths.length < 1) return msgErr('请选择文件后再进行操作');
    const res = await API.minio.minIo.downloadFilesAsZipStream.request(
      { minioPaths },
      { params: { minioPaths }, responseType: 'blob' },
    );
    if (!res) {
      msgErr('导出数据失败');
      return;
    }
    if (minioPaths.length === 1) {
      const data = minioPaths[0];
      downloadFileWithAlert(res, `${data.fileName}.zip`);
    } else {
      downloadFileWithAlert(res, '相关附件.zip');
    }
  };
  const handleUploadQuery = async () => {
    const formValues = await uploadForm.validateFields();
    if (formValues.fileType || formValues.isDeleted !== '0') {
      setIsUpload(true);
    } else {
      setIsUpload(false);
    }
    await API.crm.contractManage.getContractFileListByContractId
      .requests({
        contractId: contract.contractId,
        ...formValues,
      })
      .then((data: any) => {
        writableUpload.setNewData(data?.list);
      });
  };
  return (
    <Codal
      title={title}
      visible={visible}
      onCancel={() => setVisible(false)}
      width="80%"
      loading={loadingData}
      footer={
        <RowElementButton>
          {/* 产品说不要二次警告了，但是老系统是有警告的，所以有备无患 */}
          {/* <PopconfirmButton title="确认要通过此合同？" onConfirm={approve}>
            重新申请
          </PopconfirmButton> */}
          <AsyncButton type="primary" onClick={approve}>
            重新申请
          </AsyncButton>
          {/* <PopconfirmButton
            hidden={additional?.back === false}
            title="确认要驳回此合同？"
            onConfirm={reject}
          >
            驳回
          </PopconfirmButton> */}
          <AsyncButton hidden={additional?.back === false} onClick={reject}>
            驳回
          </AsyncButton>

          {/* <PopconfirmButton
            hidden={additional?.terminal === false}
            title="确认要终止此流程？"
            onConfirm={terminate}
          >
            终止
          </PopconfirmButton> */}
          <AsyncButton hidden={additional?.terminal === false} onClick={terminate}>
            终止
          </AsyncButton>
          <Button onClick={() => setVisible(false)}>取消</Button>
        </RowElementButton>
      }
    >
      <Tabs type="card">
        <TabPane
          forceRender
          tab={<AlertTab onError={tabErrors.mainForm}>合同基本信息</AlertTab>}
          key={'1'}
        >
          <FormElement3 form={mainForm}>
            <EnumerateFields outerForm={mainForm} colNumber={3} formColumns={formColumns} />
          </FormElement3>
        </TabPane>
        {contractType !== '5' ? (
          <TabPane
            forceRender
            tab={
              <AlertTab
                onError={
                  tabErrors.salesForm ||
                  tabErrors.csForm ||
                  tabErrors.legalForm ||
                  tabErrors.prepayForm
                }
              >
                业务部门
              </AlertTab>
            }
            key={'2'}
          >
            <Group title="销售相关">
              <FormElement3 form={salesForm} initialValues={currentContract}>
                <EnumerateFields
                  outerForm={salesForm}
                  colNumber={3}
                  formColumns={salesFormColumns}
                />
              </FormElement3>
            </Group>
            <Group title="客服相关">
              <FormElement3 form={csForm} initialValues={currentContract}>
                <EnumerateFields outerForm={csForm} colNumber={3} formColumns={csFormColumns} />
              </FormElement3>
            </Group>
            <Group title="法务相关">
              <FormElement3 form={legalForm} initialValues={{ ...currentContract, ...importFile }}>
                <EnumerateFields
                  outerForm={legalForm}
                  colNumber={3}
                  formColumns={legalFormColumns}
                />
              </FormElement3>
            </Group>
            <Group title="垫付相关">
              <FormElement3 form={prepayForm} initialValues={currentContract}>
                <EnumerateFields
                  outerForm={prepayForm}
                  colNumber={3}
                  formColumns={prepayFormColumns}
                />
              </FormElement3>
            </Group>
          </TabPane>
        ) : null}
        <TabPane
          forceRender
          tab={<AlertTab onError={tabErrors.writableProductLine}>报价单</AlertTab>}
          key={'3'}
        >
          <RowElementButton>
            {/* <Button onClick={quotationAdd}>新增</Button> */}
            <QuotationFullPop
              buttonMode
              rowValue="quotationCode-quotationName"
              disabled={!currentCustId}
              fixedValues={{ custId: currentCustId, userId: currentUser.userId }}
              multiple
              handdleMultiConfirm={handdleMultiConfirmQoutaions}
            >
              <Button>新增</Button>
            </QuotationFullPop>

            {/* <Button onClick={quotationDel}>删除</Button> */}
            <PopconfirmButton
              disabled={isEmpty(writableProductLine.selectedSingleRow)}
              title={`选的数据，将进行删除操作，是否继续？`}
              onConfirm={quotationDel}
            >
              删除
            </PopconfirmButton>
          </RowElementButton>
          <Writable
            wriTable={writableQuotation}
            service={serviceQuotation}
            columns={quotationColumns}
            editable={false}
            notShowPagination
            notShowRowSelection
            scrollVertical
          />
          <RowElementButton>
            <Button onClick={productLineAdd}>新增</Button>
            <PopconfirmButton
              disabled={isEmpty(writableProductLine.selectedSingleRow)}
              title={`选中的数据，将进行删除操作，是否继续？`}
              onConfirm={productLineDel}
            >
              删除
            </PopconfirmButton>
          </RowElementButton>
          <Writable
            wriTable={writableProductLine}
            service={serviceProductLine}
            columns={contractType === '5' ? productLineHealthColumns : productLineColumns}
            notShowPagination
            notShowRowSelection
            scrollVertical
          />
        </TabPane>
        <TabPane
          forceRender
          //  tab="销售相关"
          tab={<AlertTab onError={tabErrors.writableCustPayer}>付款方列表</AlertTab>}
          key={'4'}
        >
          <RowElementButton>
            <CustPayerPop
              buttonMode
              rowValue="custPayerId-payerName"
              disabled={!currentCustId}
              fixedValues={{ custId: currentCustId }}
              multiple
              handdleMultiConfirm={handdleMultiConfirmCustPayer}
            >
              <Button>新增</Button>
            </CustPayerPop>
            <PopconfirmButton
              disabled={isEmpty(writableCustPayer.selectedSingleRow)}
              title={`选中的数据，将进行删除操作，是否继续？`}
              onConfirm={deletecustPayer}
            >
              删除
            </PopconfirmButton>
          </RowElementButton>
          <Writable
            wriTable={writableCustPayer}
            service={serviceCustPayer}
            columns={custPayerColumns}
            notShowPagination
            notShowRowSelection
            scrollVertical
          />
        </TabPane>
        <TabPane
          forceRender
          tab={<AlertTab onError={tabErrors.writableUpload}>相关附件</AlertTab>}
          key={'5'}
        >
          <FormElement3 form={uploadForm}>
            <EnumerateFields outerForm={uploadForm} colNumber={3} formColumns={uploadFormColumns} />
          </FormElement3>
          <RowElementButton>
            <Button onClick={() => onImportBtn('1')} disabled={isUpload}>
              新增
            </Button>
            <Button onClick={() => onDelImport('1')} disabled={isUpload}>
              删除
            </Button>
            <Button onClick={onBatchDownload}>批量下载</Button>
            <Button onClick={handleUploadQuery}>查询</Button>
          </RowElementButton>
          <Writable
            wriTable={writableUpload}
            service={serviceProductLine}
            columns={ImportColumns}
            notShowPagination
            scrollVertical
          />
        </TabPane>
        {contractFormConfig.isRetirementBusinessList ? (
          <TabPane
            forceRender
            tab={<AlertTab onError={tabErrors.writableRetire}>退休人员列表</AlertTab>}
            key={'6'}
          >
            <RowElementButton>
              <Button onClick={() => writableRetire.addRows()}>新增</Button>
              <Button onClick={() => writableRetire.deleteRows(writableRetire.selectedSingleRow)}>
                删除
              </Button>
              {/* <Button onClick={() => onImportBtn('2')}>导入</Button> */}
              <ImportForm
                btnName="导入"
                downBtnName="下载模板"
                fileSuffix=".xls"
                ruleId="10702030"
                bizType="11"
                serviceName="slContractRetireeService"
                btnEnable={true}
                showImpHisBtn={false}
                showResultBtn={true}
                afterUpload={(res) => {
                  API.crm.contractManage.getSlContractRetireeList
                    .requests({ batchId: res })
                    .then((data: any) => {
                      if (data?.list.length > 0) writableRetire.setNewData(data?.list);
                    });
                }}
                // afterUpload={afterUpload}
              />
            </RowElementButton>
            <Writable
              wriTable={writableRetire}
              service={serviceProductLine}
              columns={retireColumns}
              notShowPagination
              editable
              notShowRowSelection
              scrollVertical
            />
          </TabPane>
        ) : null}
        <TabPane tab="驳回原因" key={tabScene.reject}>
          <Writable
            wriTable={writableApproveReject}
            service={serviceApproveReject}
            columns={approveRejectColumns}
            readOnly
          />
        </TabPane>
      </Tabs>
      <FormElement1 form={approvalMemoForm} initialValues={currentContract}>
        <EnumerateFields
          outerForm={prepayForm}
          colNumber={1}
          formColumns={approvalMemoFormColumns}
        />
      </FormElement1>
      {/* <QuotationUpdateForm
        title={'查看报价单'}
        width={1200}
        initialValues={quotationDetail[0]}
        visible={quotationViewModal[0]}
        onCancel={() => quotationViewModal[1](false)}
      /> */}
      <QuotationAddForm
        width={1200}
        modal={quotationViewModal}
        // listOptions={optionFunc}
        initialValues={quotationDetail[0]}
      />
      <AddForm
        title="附件上传"
        visible={onShowImport}
        hideHandle={() => setOnShowImport(false)}
        submitHandle={importHandle}
        formColumns={importType === '1' ? ImportFormColumns : ImportFormColumns1}
        okText="上传"
      ></AddForm>
      <PreviewCodal
        fileUrl={previewUrl}
        modal={previewModal}
        fileType={previewFileType}
        file={previewFile}
      />
    </Codal>
  );
};

export { ContractBackApproveWin };
