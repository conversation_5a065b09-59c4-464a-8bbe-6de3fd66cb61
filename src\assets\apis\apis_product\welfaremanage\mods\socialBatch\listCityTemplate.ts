import * as defs from '../../baseClass';
import {
  fetch,
  RequestConfig,
  beforeFetch,
  afterFetch,
  checkResStatus,
  removeSpace,
} from '@/utils/request';

/**
     * @url /rhro-service-1.0/socialBatch/listCityTemplate
     * @desc 社保增减员传ssId（社保组或合并组id） empType 增员或减员
社保增减员传ssId（社保组或合并组id） empType 增员或减员
     * hasForm: false
     * hasBody: true
     */

export class Params {
  /** empType */
  empType: number;
  /** ssId */
  ssId: string;
}

export const init = undefined;
export const url = '/rhro-service-1.0/socialBatch/listCityTemplate:GET';
export const initialUrl = '/rhro-service-1.0/socialBatch/listCityTemplate';
export const cacheKey = '_socialBatch_listCityTemplate_GET';
export async function request(params: Params, options?: RequestConfig) {
  const reqUrl = `/rhro-service-1.0/socialBatch/listCityTemplate`;
  const fetchOption = {
    reqUrl,
    requestType: 'form',
    params: removeSpace(params),

    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .get(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          reolve(res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
export async function requests(params: Params, options?: RequestConfig) {
  const reqUrl = `/rhro-service-1.0/socialBatch/listCityTemplate`;
  const fetchOption = {
    reqUrl,
    requestType: 'form',
    params: removeSpace(params),

    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .get(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          if (checkResStatus(res)) return reolve(res ? res.data : res);
          reject(res ? res.message : res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
