/**
 * 历史福利产品
 */
import { useWritable } from '@/components/Writable';
import { ColElementButton, FormElement3, RowElement } from '@/components/Forms/FormLayouts';
import { Writable } from '@/components/Writable';
import { Button, Checkbox, Form } from 'antd';
import React, { useEffect } from 'react';
import { WritableColumnProps } from '@/utils/writable/types';
import EllipsisGrid from '@/components/StandardTable/Grid/EllipsisGrid';
import { QuotationTempExPop } from '@/components/StandardPop/QuotationTempExPop';
import { RadioItem } from '@/components/EditeForm/Item/CheckboxItem';
import { EditeFormProps, EnumerateFields } from '@/components/CachedPage/EnumerateFields';
import { formatPercentTruncated } from '../../QuotationTempManage/calculation';

interface OldWefareProductProps {
  [props: string]: any;
  onDataChange?: (data: any) => void;
}
const service = API.sale.quotation.createQuotation;
const OldWefareProduct: React.FC<OldWefareProductProps> = ({
  initialValues,
  form,
  onDataChange,
}) => {
  const wriTable2 = useWritable({ service: { ...service, cacheKey: service.cacheKey + 2 } });
  const wriTable3 = useWritable({ service: { ...service, cacheKey: service.cacheKey + 3 } });
  const wriTable4 = useWritable({ service: { ...service, cacheKey: service.cacheKey + 4 } });
  const businessProdColumn: WritableColumnProps<any>[] = [
    {
      title: '产品方案名称',
      dataIndex: 'quotationTempltName',
      inputRender: () => {
        return (
          <QuotationTempExPop
            rowValue="quotationTempltId-quotationTempltPrice-isCityDiscount-remark-quotationTempltType-effectiveDt-invalidDt-quotationTempltCost-productId-quotationTempltName"
            keyMap={{
              quotationTempltId: 'quotationTempltId',
              quotationTempltName: 'quotationTempltName',
              quotationTempltCost: 'quotationTempltCost',
              quotationTempltPrice: 'quotationTempltPrice',
              isCityDiscount: 'isCityDiscount',
              remark: 'remark',
              quotationTempltType: 'quotationTempltType',
              effectiveDt: 'effectiveDt',
              invalidDt: 'invalidDt',
              productId: 'productId',
            }}
            fixedValues={{
              quotationTempltType: '1700',
              suppltMedInsurHeadcount: form.getFieldValue('suppltMedInsurHeadcount'),
              newSaleId: form.getFieldValue('newSaleId'),
              custId: form.getFieldValue('custId'),
            }}
            disabled={true}
          />
        );
      },
    },
    { title: '价格', dataIndex: 'productPrice' },
    {
      title: '销售单价（不含税）',
      dataIndex: 'salesPriceNoTax',
    },
    {
      title: '增值税率%',
      dataIndex: 'vatr',
      render: (v) => formatPercentTruncated(v, 2, true),
    },
    {
      title: '附加税率%',
      dataIndex: 'atr',
      render: (v) => formatPercentTruncated(v, 2, true),
    },
    { title: ' 销售单价（含税）', dataIndex: 'salesPrice' },
    { title: ' 增值税', dataIndex: 'vat' },
    { title: ' 是否年缴', dataIndex: 'isYearsToPay', inputRender: () => <RadioItem /> },
    { title: ' 补医保项目起始日期 ', dataIndex: 'suplmtMedStartDt', inputRender: 'date' },
    { title: ' 补医保项目终止日期 ', dataIndex: 'suplmtMedEndDt', inputRender: 'date' },
    {
      title: ' 描述 ',
      dataIndex: 'productDesc',
      render: (value: string) => <EllipsisGrid text={value} />,
    },
    // { title: ' 方案明细 ',dataIndex: 'salesPriceNoTax'},
  ];
  const businessProdColumn1: WritableColumnProps<any>[] = [
    {
      title: '产品方案名称',
      dataIndex: 'quotationTempltName',
      inputRender: () => (
        <QuotationTempExPop
          rowValue="quotationTempltId-quotationTempltPrice-isCityDiscount-remark-quotationTempltType-effectiveDt-invalidDt-quotationTempltCost-productId-quotationTempltName"
          keyMap={{
            quotationTempltId: 'quotationTempltId',
            quotationTempltName: 'quotationTempltName',
            quotationTempltCost: 'quotationTempltCost',
            quotationTempltPrice: 'quotationTempltPrice',
            isCityDiscount: 'isCityDiscount',
            remark: 'remark',
            quotationTempltType: 'quotationTempltType',
            effectiveDt: 'effectiveDt',
            invalidDt: 'invalidDt',
            productId: 'productId',
          }}
          fixedValues={{
            quotationTempltType: '1001',
            suppltMedInsurHeadcount: form.getFieldValue('suppltMedInsurHeadcount'),
            newSaleId: form.getFieldValue('newSaleId'),
            custId: form.getFieldValue('custId'),
          }}
          disabled={true}
        />
      ),
    },
    { title: '价格', dataIndex: 'productPrice' },
    {
      title: '销售单价（不含税）',
      dataIndex: 'salesPriceNoTax',
    },
    {
      title: '增值税率%',
      dataIndex: 'vatr',
      render: (v) => formatPercentTruncated(v, 2, true),
    },
    {
      title: '附加税率%',
      dataIndex: 'atr',
      render: (v) => formatPercentTruncated(v, 2, true),
    },
    { title: ' 销售单价（含税）', dataIndex: 'salesPrice' },
    { title: ' 增值税', dataIndex: 'vat' },
    { title: ' 是否年缴', dataIndex: 'isYearsToPay', inputRender: () => <RadioItem /> },
    { title: ' 补医保项目起始日期 ', dataIndex: 'effectiveDt', inputRender: 'date' },
    { title: ' 补医保项目终止日期 ', dataIndex: 'invalidDt', inputRender: 'date' },
    {
      title: ' 描述 ',
      dataIndex: 'productDesc',
      render: (value: string) => <EllipsisGrid text={value} />,
    },
    // { title: ' 方案明细 ', dataIndex: ''},
  ];
  const businessProdColumn2: WritableColumnProps<any>[] = [
    {
      title: '产品方案名称',
      dataIndex: 'quotationTempltName',
      inputRender: () => (
        <QuotationTempExPop
          rowValue="quotationTempltId-quotationTempltPrice-isCityDiscount-remark-quotationTempltType-effectiveDt-invalidDt-quotationTempltCost-productId-quotationTempltName"
          keyMap={{
            quotationTempltId: 'quotationTempltId',
            quotationTempltName: 'quotationTempltName',
            quotationTempltCost: 'quotationTempltCost',
            quotationTempltPrice: 'quotationTempltPrice',
            isCityDiscount: 'isCityDiscount',
            remark: 'remark',
            quotationTempltType: 'quotationTempltType',
            effectiveDt: 'effectiveDt',
            invalidDt: 'invalidDt',
            productId: 'productId',
          }}
          fixedValues={{
            quotationTempltType: '1002',
            suppltMedInsurHeadcount: form.getFieldValue('suppltMedInsurHeadcount'),
            newSaleId: form.getFieldValue('newSaleId'),
            custId: form.getFieldValue('custId'),
          }}
          disabled={true}
        />
      ),
    },
    { title: '价格', dataIndex: 'productPrice' },
    {
      title: '销售单价（不含税）',
      dataIndex: 'salesPriceNoTax',
    },
    {
      title: '增值税率%',
      dataIndex: 'vatr',
      render: (v) => formatPercentTruncated(v, 2, true),
    },
    {
      title: '附加税率%',
      dataIndex: 'atr',
      render: (v) => formatPercentTruncated(v, 2, true),
    },
    { title: ' 销售单价（含税）', dataIndex: 'salesPrice' },
    { title: ' 增值税', dataIndex: 'vat' },
    { title: ' 是否年缴', dataIndex: 'isYearsToPay', inputRender: () => <RadioItem /> },
    { title: ' 补医保项目起始日期 ', dataIndex: 'suplmtMedStartDt', inputRender: 'date' },
    { title: ' 补医保项目终止日期 ', dataIndex: 'suplmtMedEndDt', inputRender: 'date' },
    {
      title: ' 描述 ',
      dataIndex: 'productDesc',
      render: (value: string) => <EllipsisGrid text={value} />,
    },
  ];
  useEffect(() => {
    if (onDataChange && wriTable2.data.list) {
      onDataChange({ quotationSpecialPropertyList: wriTable2.data.list });
    }
  }, [wriTable2.data.list, wriTable3.data.list, wriTable4.data.list, onDataChange]);
  useEffect(() => {
    if (initialValues.EmployerDetailList && initialValues.EmployerDetailList.length > 0) {
      wriTable4.setNewData(initialValues.EmployerDetailList);
    } else {
      wriTable4.setNewData([]);
    }
    if (initialValues.healthDetailList && initialValues.healthDetailList.length > 0) {
      wriTable3.setNewData(initialValues.healthDetailList);
    } else {
      wriTable3.setNewData([]);
    }
    if (initialValues.businessDetailList && initialValues.businessDetailList.length > 0) {
      wriTable2.setNewData(initialValues.businessDetailList);
    } else {
      wriTable2.setNewData([]);
    }
  }, [
    initialValues.EmployerDetailList,
    initialValues.healthDetailList,
    initialValues.businessDetailList,
  ]);
  const welfareColumns: EditeFormProps[] = [
    {
      label: '增值税率',
      fieldName: 'wvatr',
    },
    {
      label: '附加税率%',
      fieldName: 'watr',
    },
  ];
  return (
    <>
      <EnumerateFields outerForm={form} formColumns={welfareColumns} colNumber={3} />
      <RowElement>
        <ColElementButton>
          <Button key="addWefare1" type="primary" disabled>
            添加
          </Button>
          <Button key="cancelWefare1" disabled>
            删除
          </Button>
        </ColElementButton>
      </RowElement>
      <Form.Item name={'businessType'}>
        <Checkbox disabled>在账单中仅显示商业保险产品总额，不显示明细</Checkbox>
      </Form.Item>
      <Writable
        service={{ ...service, cacheKey: service.cacheKey + 2 }}
        columns={businessProdColumn}
        notShowPagination
        noDeleteButton
        dragable
        noAddButton
        wriTable={wriTable2}
      />
      <RowElement>
        <ColElementButton>
          <Button key="addWefare2" type="primary" disabled>
            添加
          </Button>
          <Button key="cancelWefare2" disabled>
            删除
          </Button>
        </ColElementButton>
      </RowElement>
      <Form.Item name={'healthType'}>
        <Checkbox disabled> 在账单中仅显示健康管理产品总额，不显示明细</Checkbox>
      </Form.Item>

      <Writable
        service={{ ...service, cacheKey: service.cacheKey + 3 }}
        columns={businessProdColumn1}
        notShowPagination
        noDeleteButton
        dragable
        noAddButton
        wriTable={wriTable3}
      />
      <RowElement>
        <ColElementButton>
          <Button key="add" type="primary" disabled>
            添加
          </Button>
          <Button key="cancel" disabled>
            删除
          </Button>
        </ColElementButton>
      </RowElement>
      <Form.Item name={'employerType'}>
        <Checkbox disabled> 在账单中仅显示员工关爱产品总额，不显示明细</Checkbox>
      </Form.Item>

      <Writable
        service={{ ...service, cacheKey: service.cacheKey + 4 }}
        columns={businessProdColumn2}
        notShowPagination
        noDeleteButton
        noAddButton
        wriTable={wriTable4}
      />
    </>
  );
};

export default OldWefareProduct;
