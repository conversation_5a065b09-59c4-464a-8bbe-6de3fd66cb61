import * as defs from '../../baseClass';
import {
  fetch,
  RequestConfig,
  beforeFetch,
  afterFetch,
  checkResStatus,
  removeSpace,
} from '@/utils/request';

/**
     * @url /rhro-service-1.0/quotation/selectQuotationItemDetailEx
     * @desc 获取退休报价单产品
获取退休报价单产品
     * hasForm: false
     * hasBody: true
     */

export class Params {
  /** quotationId */
  quotationId: number;
  /** templtType */
  templtType: string;
}

export const init = new defs.sale.CommonResponse();
export const url =
  '/rhro-service-1.0/quotation/selectQuotationItemDetailEx:GET';
export const initialUrl =
  '/rhro-service-1.0/quotation/selectQuotationItemDetailEx';
export const cacheKey = '_quotation_selectQuotationItemDetailEx_GET';
export async function request(params: Params, options?: RequestConfig) {
  const reqUrl = `/rhro-service-1.0/quotation/selectQuotationItemDetailEx`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',
    params: removeSpace(params),

    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .get(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          reolve(res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
export async function requests(params: Params, options?: RequestConfig) {
  const reqUrl = `/rhro-service-1.0/quotation/selectQuotationItemDetailEx`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',
    params: removeSpace(params),

    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .get(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          if (checkResStatus(res)) return reolve(res ? res.data : res);
          reject(res ? res.message : res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
