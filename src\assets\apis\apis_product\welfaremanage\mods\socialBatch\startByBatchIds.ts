import * as defs from '../../baseClass';
import {
  fetch,
  RequestConfig,
  beforeFetch,
  afterFetch,
  checkResStatus,
  removeSpace,
} from '@/utils/request';

/**
     * @url /rhro-service-1.0/socialBatch/startByBatchIds
     * @desc 根据批次号重新发送增减员
根据批次号重新发送增减员
     * hasForm: false
     * hasBody: true
     */

export class Params {
  /** batchIds */
  batchIds: Array<string>;
}

export const init = new defs.welfaremanage.CommonResponse();
export const url = '/rhro-service-1.0/socialBatch/startByBatchIds:GET';
export const initialUrl = '/rhro-service-1.0/socialBatch/startByBatchIds';
export const cacheKey = '_socialBatch_startByBatchIds_GET';
export async function request(params: Params, options?: RequestConfig) {
  const reqUrl = `/rhro-service-1.0/socialBatch/startByBatchIds`;
  const fetchOption = {
    reqUrl,
    requestType: 'form',
    params: removeSpace(params),

    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .get(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          reolve(res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
export async function requests(params: Params, options?: RequestConfig) {
  const reqUrl = `/rhro-service-1.0/socialBatch/startByBatchIds`;
  const fetchOption = {
    reqUrl,
    requestType: 'form',
    params: removeSpace(params),

    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .get(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          if (checkResStatus(res)) return reolve(res ? res.data : res);
          reject(res ? res.message : res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
