/*
 * @Author: “liuxiamei” “<EMAIL>”
 * @Date: 2025-09-04 16:30:22
 * @LastEditors: “liuxiamei” “<EMAIL>”
 * @LastEditTime: 2025-09-05 10:17:43
 * @FilePath: \rhro_web2\src\components\StandardPop\EbmtransactGetEmpInfoPop.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import React from 'react';
import { DerivedPopProps, StandardPop } from '@/components/StandardPop/libs/StdPop';
import { CustomerPop } from './CustomerPop';
import { WritableColumnProps } from '@/utils/writable/types';

const columns: WritableColumnProps<any>[] = [
  { title: '唯一号', dataIndex: 'empCode' },
  { title: '姓名', dataIndex: 'empName' },
  { title: '证件号码', dataIndex: 'idCardNum' },
  { title: '城市', dataIndex: 'cityName' },
  { title: '户口性质', dataIndex: 'subTypeName' },
  { title: '客户名称', dataIndex: 'custName' },
  { title: '小合同名称', dataIndex: 'subContractName' },
  { title: '入离职状态', dataIndex: 'sepStatus' },
];

const formColumns = [
  {
    label: '客户名称',
    fieldName: 'custId',
    inputRender: () => (
      <CustomerPop
        rowValue="custId-custName"
        keyMap={{
          custId: 'custId',
          custName: 'custName',
        }}
      />
    ),
  },
  {
    label: '员工姓名',
    fieldName: 'empName',
    inputRender: 'string',
  },
  {
    label: '证件号码',
    fieldName: 'idCardNum',
    inputRender: 'string',
  },
  {
    label: '唯一号',
    fieldName: 'empCode',
    inputRender: 'string',
  },
];

const service = API.welfaremanage.ebmBusiness.getEmpInfo;

const EbmtransactGetEmpInfoPop: React.FC<DerivedPopProps<any>> = (props) => {
  return (
    <StandardPop
      columns={columns}
      service={service}
      formColumns={formColumns}
      rowValue={props.rowValue}
      modalwidth={'80%'}
      {...(props as any)}
    />
  );
};

export default EbmtransactGetEmpInfoPop;
