import * as defs from '../../baseClass';
import {
  fetch,
  RequestConfig,
  beforeFetch,
  afterFetch,
  checkResStatus,
  removeSpace,
} from '@/utils/request';

/**
     * @url /rhro-service-1.0/v1/rpa/startRpac/${data.jobId}
     * @desc 启动自动按户获取凭证
启动自动按户获取凭证
     * hasForm: false
     * hasBody: true
     */

export class Params {
  /** jobId */
  jobId: number;
}

export const init = '';
export const url = '/rhro-service-1.0/v1/rpa/startRpac/data.jobId:POST';
export const initialUrl = '/rhro-service-1.0/v1/rpa/startRpac';
export const cacheKey = '_v1_rpa_startRpac_data.jobId_POST';
export async function request(data: Params, options?: RequestConfig) {
  const reqUrl = `/rhro-service-1.0/v1/rpa/startRpac/${data.jobId}`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          reolve(res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
export async function requests(data: Params, options?: RequestConfig) {
  const reqUrl = `/rhro-service-1.0/v1/rpa/startRpac/${data.jobId}`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          if (checkResStatus(res)) return reolve(res ? res.data : res);
          reject(res ? res.message : res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
