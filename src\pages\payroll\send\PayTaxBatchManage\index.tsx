import React, { useEffect, useState } from 'react';
import { Button, Form, Modal, Typography } from 'antd';
import { WritableColumnProps } from '@/utils/writable/types';
import { CachedPage } from '@/components/CachedPage';
import { EditeFormProps } from '@/components/EditeForm';
import { useWritable, WritableInstance } from '@/components/Writable';
import { DateRange } from '@/components/DateRange4';
import { FormInstance, RuleObject } from 'antd/es/form';
import { CustomerPop } from '@/components/StandardPop/CustomerPop';
import { BooleanSelector } from '@/components/Selectors/BaseDropDown';
import {
  DeptmentSelector,
  GetApplyTitleByPayAddress,
  mapToSelectors,
} from '@/components/Selectors';
import QuerySocialPayDetailWin from '@/pages/finance/Pay/Query/components/QuerySocialPayDetailWin';
import { GeneralInputRenderOption } from '@/components/Writable/libs/GeneralInput';
import SendBatchDetailWin from '../../XZFFPCXN/XJFFPCXN/components/SendBatchDetailWin';
import { QueryThisTimeTaxDetailWin } from './QueryThisTimeTaxDetailWin';
import { msgErr, msgOk, resError } from '@/utils/methods/message';
import { isEmpty } from '@/utils/methods/checker';
import {
  base64ToFile,
  downloadFile,
  downloadFileWithAlert,
  exportFilebyColumns,
} from '@/utils/methods/file';
import { AsyncButton } from '@/components/Forms/Confirm';
import { getCurrentMenu } from '@/utils/model';
import { TablePage } from '@/utils/methods/pagenation';
import { lineStatusMark, lineStatusRed } from '@/utils/settings/forms';
import { UpdatePayTaxBatchWin } from './UpdatePayTaxBatchWin';
import { mapToRender } from '@/utils/methods/tables';
import { SetConfirmTaxFinishWin } from './SetConfirmTaxFinishWin';
import Search from 'antd/lib/input/Search';
const getPayTaxBatchMaps = require('./getPayTaxBatchMap0').data;

type TPayTaxBatch = defs.payroll.PayTaxBatch;

interface PayTaxBatchManageProps {
  [props: string]: any;
}

const service = API.payroll.payTaxBatch.getPayTaxBatchMap;

const statusMap = new Map([
  ['0', '初始'],
  ['1', '报税完成'],
]);

const PayTaxBatchManage: React.FC<PayTaxBatchManageProps> = () => {
  const QuerySocialPayDetailWinModal = useState(false);
  const SendBatchDetailWinModal = useState(false);
  const QueryThisTimeTaxDetailWinModal = useState(false);
  const UpdatePayTaxBatchWinModal = useState(false);
  const SetConfirmTaxFinishWinModal = useState(false);
  const [singleRow, setsingleRow] = useState<Partial<TPayTaxBatch>>({});
  const thisTimeTaxDetailWinisUpdate = useState(false);
  const payTaxBatchIds = useState<string>();
  const [form] = Form.useForm();

  // const afterRequest = (data: TablePage<any>) => {
  //   // 第一种方式，行高亮，在需要命中多行时，效果更好
  //   for (const row of data.list) {
  //     if (row.taxAmt === '0') {
  //       row[lineStatusMark] = lineStatusRed;
  //     }
  //   }
  //   return data;
  // };
  const writable = useWritable({ service });

  useEffect(() => {
    // writable.setNewData(getPayTaxBatchMaps);
  }, []);

  const formColumns: EditeFormProps[] = [
    { label: '报税批次号', fieldName: 'payTaxBatchId', inputRender: 'string' },
    { label: '报税批次名称', fieldName: 'payTaxBatchName', inputRender: 'string' },
    {
      label: '支付审核通过日期',
      fieldName: 'createDt',
      inputRender: () => (
        <DateRange
          fields={[
            {
              title: '支付审核通过日期从',
              dataIndex: 'createDtStart',
              rules: [{ required: true, message: '请选择支付审核通过日期从' }],
            },
            {
              title: '支付审核通过日期到',
              dataIndex: 'createDtEnd',
              rules: [
                { required: true, message: '请选择支付审核通过日期到' },
                {
                  validator: (rule: RuleObject, value: string) => {
                    if (!value) return Promise.resolve();
                    const createDtStart: string = form.getFieldValue('createDtStart');
                    if (createDtStart && createDtStart.substr(0, 7) !== value.substr(0, 7)) {
                      return Promise.reject(
                        '只能选择一个月内的[支付审核通过日期从]和[支付审核通过日期到]',
                      );
                    }
                    return Promise.resolve();
                  },
                },
              ],
            },
          ]}
        />
      ),
    },
    {
      label: '客户',
      fieldName: 'txtCustId',
      inputRender: () => (
        <CustomerPop rowValue="custId-custName" fixedValues={{ isWageQuery: '1' }} />
      ),
      colNumber: 2,
    },
    {
      label: '是否删除',
      fieldName: 'isDeleted',
      inputRender: () => <BooleanSelector order="asc" />,
    },
    { label: '生成人', fieldName: 'createByName', inputRender: 'string' },
    {
      label: '支付地',
      fieldName: 'payAddress',
      inputRender: (outerForm: FormInstance<any>, options: any) => (
        <DeptmentSelector
          onChange={() => {
            outerForm.setFieldsValue({ deptTitleId: null });
          }}
        />
      ),
    },
    { label: '状态', fieldName: 'status', inputRender: () => mapToSelectors(statusMap) },
    { label: '报税金额从', fieldName: 'taxAmtFrom', inputRender: 'string' },
    { label: '报税金额到', fieldName: 'taxAmtTo', inputRender: 'string' },
    {
      label: '支付地抬头',
      fieldName: 'deptTitleId',
      shouldUpdate: (prev, curr) => {
        return prev.payAddress !== curr.payAddress;
      },
      colNumber: 2,
      inputRender: (outerForm: FormInstance<any>, options: any) => (
        <GetApplyTitleByPayAddress
          skipEmptyParam
          params={{ payAddress: outerForm.getFieldValue('payAddress') }}
        />
      ),
    },
    { label: '发放批次号', fieldName: 'payBatchId', inputRender: 'string' },
    { label: '报税月', fieldName: 'taxMonth', inputRender: 'month' },
  ];

  const viewQuerySocialPayDetailWin = (row: TPayTaxBatch) => {
    setsingleRow(row);
    QuerySocialPayDetailWinModal[1](true);
  };

  const viewDetail = (row: TPayTaxBatch) => {
    setsingleRow(row);
    SendBatchDetailWinModal[1](true);
  };

  const viewThisTimeDetail = (row: TPayTaxBatch) => {
    setsingleRow(row);
    QueryThisTimeTaxDetailWinModal[1](true);
    thisTimeTaxDetailWinisUpdate[1](false);
  };

  const columns: WritableColumnProps<TPayTaxBatch>[] = [
    { title: '实际报税日期', dataIndex: 'realTaxDt' },
    { title: '报税批次号', dataIndex: 'payTaxBatchId' },
    { title: '发放批次号', dataIndex: 'payBatchId' },
    { title: '报税批次名称', dataIndex: 'payTaxBatchName' },
    {
      title: '申请金额',
      dataIndex: 'applyAmt',
      render: (value: any, record: TPayTaxBatch) => (
        <Typography.Link onClick={() => viewQuerySocialPayDetailWin(record)}>
          {value}
        </Typography.Link>
      ),
    },
    { title: '实发金额', dataIndex: 'realAmt' },
    {
      title: '报税金额',
      dataIndex: 'taxAmt',
    },
    {
      title: '支付地',
      dataIndex: 'payAddress',
      render: (value: any, record: TPayTaxBatch) => <DeptmentSelector code={record?.payAddress} />,
    },
    { title: '支付地抬头', dataIndex: 'deptTitleName' },
    { title: '生成人', dataIndex: 'createByName' },
    { title: '创建时间', dataIndex: 'createDt' },
    { title: '报税人数', dataIndex: 'taxNum' },
    { title: '状态', dataIndex: 'status', render: mapToRender(statusMap) },
    {
      title: '是否首次报税',
      dataIndex: 'isFirst',
      render: (value: any, record: TPayTaxBatch) => <BooleanSelector code={record?.isFirst} />,
    },
    { title: '预计报税日期', dataIndex: 'preTaxDt' },
    { title: '备注', dataIndex: 'memo' },
    {
      title: '详细',
      dataIndex: 'detail',
      render: (value: any, record: TPayTaxBatch) => (
        <Typography.Link onClick={() => viewDetail(record)}>查看</Typography.Link>
      ),
    },
    {
      title: '查看本次明细',
      dataIndex: 'thiDetail',
      render: (value: any, record: TPayTaxBatch) => (
        <Typography.Link onClick={() => viewThisTimeDetail(record)}>查看</Typography.Link>
      ),
    },
  ];

  const wagedataUpdate = () => {
    setsingleRow(writable.selectedSingleRow);
    UpdatePayTaxBatchWinModal[1](true);
  };

  const onConfirmUpdatePayTaxBatchWin = () => {
    writable.request(writable.queries);
  };

  const onConfirmSetConfirmTaxFinishWin = () => {
    writable.request(writable.queries);
  };

  const taxEnterForward = (selectedRows: any[]) => {
    const strs: string[] = [];
    for (const tempRecord of selectedRows) {
      if (tempRecord.status === '0') {
        strs.push(tempRecord.payTaxBatchId);
      }
    }
    payTaxBatchIds[1](strs.join(','));
    SetConfirmTaxFinishWinModal[1](true);
  };

  const taxEnter = async () => {
    const strs: string[] = [];
    for (const tempRecord of writable.selectedRows) {
      if (tempRecord.status === '0' && tempRecord.isDeleted === '0') {
        strs.push(tempRecord.payTaxBatchId);
      }
    }
    if (strs.length === 0) {
      return msgErr('请选择需要操作的记录。');
    }
    const payTaxBatchIds = strs.join(',');
    await API.payroll.payTaxBatch.checkALLIdCardInTax.requests({ payTaxBatchIds });
    taxEnterForward(writable.selectedRows);
  };

  const anewTax = async () => {
    const row = writable.selectedSingleRow;
    if (row.status === '1' && row.isFirst === '1' && row.isDeleted === '0') {
      const { payTaxBatchId } = row;
      const res = await API.payroll.payTaxBatch.checkContainFailureInTax.request({ payTaxBatchId });
      if (resError(res)) {
        return msgErr('该报税批次明细中包含有失败状态的记录才能重新报税');
      }
      // 这个接口没有数据测，但应该没大问题。
      await API.payroll.payTaxBatch.setAnewTax.requests({ payTaxBatchId });
      msgOk('操作成功');
      await writable.request(writable.queries);
    } else {
      return msgErr('只有报税完成，并且是首次报税的批次才能重新报税');
    }
  };

  const setTaxResult = async () => {
    const row = writable.selectedSingleRow;
    if (row.isDeleted !== '0') {
      return msgErr('不能对已删除的条目执行此操作');
    }
    setsingleRow(row);
    QueryThisTimeTaxDetailWinModal[1](true);
    thisTimeTaxDetailWinisUpdate[1](row.status !== '1');
  };

  const taxFileExport = async () => {
    const strs: string[] = [];
    for (const tempRecord of writable.selectedRows) {
      strs.push(tempRecord.payTaxBatchId);
    }

    const result = await API.payroll.payTaxBatch.setExportTaxFile.requests({
      payTaxBatchIds: strs.join(','),
    });
    const fileData = result.fileData;
    const fileName = result.fileName;
    Modal.confirm({
      content: '数据查询完毕是否导出？',
      onOk: () => {
        base64ToFile(fileData, fileName);
      },
    });
  };

  const staffFileExport = async () => {
    const strs: string[] = [];
    for (const tempRecord of writable.selectedRows) {
      strs.push(tempRecord.payTaxBatchId);
    }
    const result = await API.payroll.payTaxBatch.setExportTaxEmpInfo.requests({
      payTaxBatchIds: strs.join(','),
    });
    const fileData = result.fileData;
    const fileName = result.fileName;
    Modal.confirm({
      content: '数据查询完毕是否导出？',
      onOk: () => {
        base64ToFile(fileData, fileName);
      },
    });
  };

  const allExport = async () => {
    const fieldArr = [
      'groupId',
      'groupName',
      'custCode',
      'custName',
      'itemCsName',
      'realTaxDt',
      'payTaxBatchId',
      'payBatchId',
      'payTaxBatchName',
      'applyAmt',
      'realAmt',
      'taxAmt',
      'payAddress',
      'deptTitleName',
      'createByName',
      'createDt',
      'taxNum',
      'status',
      'isFirst',
      'preTaxDt',
      'memo',
    ];
    const headStr =
      '集团编号,集团名称,客户编号,客户名称,项目客服,实际报税日期,报税批次号,发放批次号,报税批次名称,申请金额,实发金额,报税金额,支付地,支付地抬头,生成人,创建时间,报税人数,状态,是否首次报税,预计报税日期,备注';
    const typeArr = [
      '1',
      '1',
      '1',
      '1',
      '1',
      '1',
      '1',
      '1',
      '1',
      '2',
      '2',
      '2',
      '1',
      '1',
      '1',
      '1',
      '2',
      '1',
      '1',
      '1',
      '1',
    ];
    const condition = {
      ...writable.queries,
      expType: '1',
      exportDataType: 'PayTaxBatchMainExport',
    };
    const params = {
      condition,
      fieldArr,
      headStr,
      typeArr,
    };
    const res = await API.payroll.payTaxBatch.toDownLoad.request(params, { responseType: 'blob' });
    if (res) {
      downloadFileWithAlert(res, 'taxBatch.xlsx');
    }
  };

  const functionId = getCurrentMenu().funcId;
  const updateEmpBaseInfoGo = () => {
    // FIXME: 这里有问题，不知道该传啥。
    API.payroll.payTaxBatch.updateEmpBaseInfo.requests({
      payTaxBatchId: writable.selectedSingleRow.payTaxBatchId,
      btnName: '更新员工基本信息',
      functionId,
      btnType: '1',
    });
  };

  const updateEmpBaseInfo = () => {
    const row = writable.selectedSingleRow;
    if (row.status === '1') {
      return msgErr('此记录报税批次已经完成了,不能更新员工基本信息');
    }
    Modal.confirm({
      content: '是否需要更新？',
      onOk: () => {
        updateEmpBaseInfoGo();
      },
    });
  };

  const onConfirmQueryThisTimeTaxDetailWin = () => {
    writable.request(writable.queries);
  };

  const onSearch = (value: string) => {
    writable.searchRows(value, ['payBatchId', 'payTaxBatchName', 'txtCustId', 'payTaxBatchId']);
  };

  const renderButtons = (options: WritableInstance) => {
    const noSelected = writable.selectedRows.length === 0;
    const notClicked = isEmpty(writable.selectedSingleRow);
    return (
      <>
        <Button type="primary" htmlType="submit">
          查询
        </Button>
        {/* <Search
          placeholder="雇员唯一号/姓名/证件号码:"
          onSearch={onSearch}
          style={{ width: 200 }}
        /> */}

        <AsyncButton disabled={notClicked} onClick={wagedataUpdate}>
          薪资数据修改
        </AsyncButton>
        <AsyncButton disabled={noSelected} onClick={taxEnter}>
          确认报税完成
        </AsyncButton>
        <AsyncButton disabled={notClicked} onClick={anewTax}>
          重新报税
        </AsyncButton>
        <AsyncButton disabled={notClicked} onClick={setTaxResult}>
          设置/查看报税结果
        </AsyncButton>
        <AsyncButton disabled={noSelected} onClick={taxFileExport}>
          导出报税文件
        </AsyncButton>
        <Button disabled={noSelected} onClick={staffFileExport}>
          导出人员信息
        </Button>
        <Button disabled={noSelected} onClick={updateEmpBaseInfo}>
          更新员工基本信息
        </Button>
        <AsyncButton disabled={writable.size === 0} onClick={allExport}>
          查询结果导出
        </AsyncButton>
      </>
    );
  };
  // console.log('下面有 initailValues, isDeleted 要保留')
  return (
    <CachedPage
      service={service}
      form={form}
      formColumns={formColumns}
      wriTable={writable}
      columns={columns}
      renderButtons={renderButtons}
      scroll={{ x: 2000 }}
      initialValues={{
        isDeleted: '0',
        // payTaxBatchId:"303487",
        // createDtEnd: '2020-08-01',
        // createDtStart: '2020-08-31',
      }}
    >
      <QuerySocialPayDetailWin
        visible={QuerySocialPayDetailWinModal[0]}
        currentState="noSecond"
        data={{
          payAuditId: singleRow.payBatchId || singleRow.payAuditId,
          approveStatus: '2',
          payType: '4',
          paySends: singleRow.paySends,
        }}
        hideHandle={() => QuerySocialPayDetailWinModal[1](false)}
        title="申请明细界面"
        // cb={(v) => setsingleRow(v)}
      />
      <SendBatchDetailWin
        visible={SendBatchDetailWinModal[0]}
        hideHandle={() => SendBatchDetailWinModal[1](false)}
        data={singleRow}
      />
      <QueryThisTimeTaxDetailWin
        modal={QueryThisTimeTaxDetailWinModal}
        payTaxBatchData={singleRow}
        isUpdate={thisTimeTaxDetailWinisUpdate[0]}
        onConfirm={onConfirmQueryThisTimeTaxDetailWin}
      />
      <UpdatePayTaxBatchWin
        modal={UpdatePayTaxBatchWinModal}
        payTaxBatchData={singleRow}
        onConfirm={onConfirmUpdatePayTaxBatchWin}
      />
      <SetConfirmTaxFinishWin
        modal={SetConfirmTaxFinishWinModal}
        payTaxBatchIds={payTaxBatchIds[0]}
        onConfirm={onConfirmSetConfirmTaxFinishWin}
      />
    </CachedPage>
  );
};

export default PayTaxBatchManage;
