import * as defs from '../../baseClass';
import {
  fetch,
  RequestConfig,
  beforeFetch,
  afterFetch,
  checkResStatus,
  removeSpace,
} from '@/utils/request';

/**
     * @url /rhro-service-1.0/sb/getNotSameNameFiveNumByPayBatchId
     * @desc 判断当前批次中存在超过5个开户名与雇员姓名不一致的情况
判断当前批次中存在超过5个开户名与雇员姓名不一致的情况
     * hasForm: false
     * hasBody: true
     */

export class Params {
  /** payBatchId */
  payBatchId: string;
}

export const init = new defs.payroll.CommonResponse();
export const url = '/rhro-service-1.0/sb/getNotSameNameFiveNumByPayBatchId:GET';
export const initialUrl =
  '/rhro-service-1.0/sb/getNotSameNameFiveNumByPayBatchId';
export const cacheKey = '_sb_getNotSameNameFiveNumByPayBatchId_GET';
export async function request(params: Params, options?: RequestConfig) {
  const reqUrl = `/rhro-service-1.0/sb/getNotSameNameFiveNumByPayBatchId`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',
    params: removeSpace(params),

    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .get(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          reolve(res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
export async function requests(params: Params, options?: RequestConfig) {
  const reqUrl = `/rhro-service-1.0/sb/getNotSameNameFiveNumByPayBatchId`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',
    params: removeSpace(params),

    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .get(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          if (checkResStatus(res)) return reolve(res ? res.data : res);
          reject(res ? res.message : res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
