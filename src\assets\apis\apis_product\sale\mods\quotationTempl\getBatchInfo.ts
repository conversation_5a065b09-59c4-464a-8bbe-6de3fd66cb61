import * as defs from '../../baseClass';
import {
  fetch,
  RequestConfig,
  beforeFetch,
  afterFetch,
  checkResStatus,
  removeSpace,
} from '@/utils/request';

/**
     * @url /rhro-service-1.0/quotationTempl/getBatchInfo
     * @desc 获取导入批次信息
获取导入批次信息
     * hasForm: false
     * hasBody: true
     */

export class Params {}

export const init = new defs.sale.Page();
export const url = '/rhro-service-1.0/quotationTempl/getBatchInfo:POST';
export const initialUrl = '/rhro-service-1.0/quotationTempl/getBatchInfo';
export const cacheKey = '_quotationTempl_getBatchInfo_POST';
export async function request(
  data: defs.sale.ImpRuleQuery,
  options?: RequestConfig,
) {
  const reqUrl = `/rhro-service-1.0/quotationTempl/getBatchInfo`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          reolve(res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
export async function requests(
  data: defs.sale.ImpRuleQuery,
  options?: RequestConfig,
) {
  const reqUrl = `/rhro-service-1.0/quotationTempl/getBatchInfo`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          if (checkResStatus(res)) return reolve(res ? res.data : res);
          reject(res ? res.message : res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
