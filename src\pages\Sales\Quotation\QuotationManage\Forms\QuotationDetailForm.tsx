import React, { useState, useEffect, ChangeEvent } from 'react';
import { Tabs, Card, Form, Button, Input, Checkbox, Typography } from 'antd';
import Codal from '@/components/Codal';
import { Writable, useWritable, WritableInstance } from '@/components/Writable';
import { EnumerateFields, EditeFormProps } from '@/components/CachedPage/EnumerateFields';
import { QuotationProductSelector, mapToSelectors } from '@/components/Selectors';
import { WritableColumnProps } from '@/utils/writable/types';
import { pageInit, removeEmpty } from '@/utils/methods/pagenation';
import { CustomerPop } from '@/components/StandardPop/CustomerPop';
import { QuotationTempPop } from '@/components/StandardPop/QuotationTempPop';
import { RowElement, ColElementButton, FormElement3 } from '@/components/Forms/FormLayouts';
import { RadioItem } from '@/components/EditeForm/Item/CheckboxItem';
import { ConnectState } from '@/models/connect';
import { useStore } from 'umi';
import { msgOk } from '@/utils/methods/message';
import TablePop from '../../QuotationTempManage/TablePop/index';
import { GeneralInputRenderOption } from '@/components/Writable/libs/GeneralInput';
import OrdinarySalesOrderDetail from '@/pages/Sales/Quotation/QuotationTempManage/components/orderDetail/OrdinarySalesOrderDetail';
import WelfareProgDetail from '@/pages/Sales/Quotation/QuotationTempManage/components/FormsDetail/WelfareProgDetail';
import RiskFundDetail from '@/pages/Sales/Quotation/QuotationTempManage/components/FormsDetail/RiskFundDetail';
import BusinessSalesDetail from '@/pages/Sales/Quotation/QuotationTempManage/components/FormsDetail/BusinessSalesDetail';
import moment from 'moment';
import { multipleNum } from './../index';
import EllipsisGrid from '@/components/StandardTable/Grid/EllipsisGrid';
import { isEmpty } from 'lodash';
const { Link } = Typography;
export const changeQuotationTempData = (data: any) => {
  const resultList: any = {};
  for (const obj of data) {
    resultList.approvePrice = obj.APPROVEPRICE;
    resultList.areaLimit = String(obj.AREALIMIT);
    resultList.createBy = obj.CREATEBY;
    resultList.createByName = obj.CREATEBYNAME;
    resultList.createDt = obj.CREATEDT;
    resultList.custCode = obj.CUSTCODE;
    resultList.custId = obj.CUSTID;
    resultList.custName = obj.CUSTNAME;
    resultList.effectiveDt = obj.EFFECTIVEDT;
    resultList.invalidDt = obj.INVALIDDT;
    resultList.isCityDiscount = String(obj.ISCITYDISCOUNT);
    resultList.isDeleted = obj.ISDELETED;
    resultList.isLimitedBySvcHeadcount = String(obj.ISLIMITEDBYSVCHEADCOUNT);
    resultList.isLimitedBySvcHeadcountName = obj.ISLIMITEDBYSVCHEADCOUNTNAME;
    resultList.isRelease = obj.ISRELEASE;
    resultList.minSvcHeadCount = obj.MINSVCHEADCOUNT;
    resultList.maxSvcHeadCount = obj.MAXSVCHEADCOUNT;
    resultList.productLineId = String(obj.PRODUCTLINEID);
    resultList.quotationTempltCode = obj.QUOTATIONTEMPLTCODE;
    resultList.quotationTempltCost = obj.QUOTATIONTEMPLTCOST;
    resultList.quotationTempltId = obj.QUOTATIONTEMPLTID;
    resultList.quotationTempltName = obj.QUOTATIONTEMPLTNAME;
    resultList.quotationTempltPrice = obj.QUOTATIONTEMPLTPRICE;
    resultList.quotationTempltScope = String(obj.QUOTATIONTEMPLTSCOPE);
    resultList.quotationTempltScopeName = obj.QUOTATIONTEMPLTSCOPENAME;
    resultList.quotationTempltType = obj.QUOTATIONTEMPLTTYPE;
    resultList.quotationTempltTypeName = obj.QUOTATIONTEMPLTTYPENAME;
    resultList.remark = obj.REMARK;
    resultList.validDtType = String(obj.VALIDDTTYPE);
    resultList.validDtTypeName = obj.VALIDDTTYPENAME;
  }
  return resultList;
};
const { TextArea } = Input;
/**
 * @function vatCalc 计算总额增值税
 * @param originPrice: 销售价格(不含税)
 * @param price: 销售价格(含税)
 * @param additionalTaxRate: 附加税
 * @param salesPrice: 总价格
 * @param addedTaxRate: 增值税率
 * @param valueAddedTax: 增值税
 *
 * @memberof EstablishFrom
 */
const calculationVat = (num1: any, num2: any, num3: any) => {
  const originPrice = parseFloat(num1);
  const addedTaxRate = parseFloat(num2);
  const additionalTaxRate = parseFloat(num3);
  const salesPrice = 0;
  const sum2: number = parseFloat(
    (
      Math.round(((originPrice * additionalTaxRate) / (1 + addedTaxRate)) * 100000) / 100000
    ).toFixed(5),
  );
  const result1: number = parseFloat(
    (Math.round((originPrice + sum2) * 100000) / 100000).toFixed(5),
  );
  const result2: number = parseFloat(
    (Math.round(result1 * addedTaxRate * 100000) / 100000).toFixed(5),
  );
  // return [salesPrice.toFixed(5), price.toFixed(5), valueAddedTax.toFixed(5)];
  return [salesPrice.toFixed(5), result1.toFixed(5), result2.toFixed(5)];
};
const quoteTyMap = new Map([
  ['1', '正常报价'],
  ['2', '阶梯总价'],
  ['3', '阶梯人均'],
]);
const qutationTypeMap = new Map([
  ['4', '风险金BPO方案'],
  ['3', '风险金方案'],
]);
interface OrdinarySalesProps {
  [props: string]: any;
}
const PopColumns = [
  {
    label: '方案名称',
    fieldName: 'quotationTempltName',
    inputRender: 'string',
  },
];

const prodTableColumns = [
  { title: '方案名称', dataIndex: 'quotationTempltName' },
  { title: '方案类型', dataIndex: 'quotationTempltTypeName' },
  { title: '报价单方案编号', dataIndex: 'quotationTempltCode' },
  { title: '产品线', dataIndex: 'productLineName' },
  {
    title: '是否存在有效期限',
    dataIndex: 'validDtTypeName',
  },
  { title: '生效日期', dataIndex: 'effectiveDt' },
  { title: '失效日期', dataIndex: 'invalidDt' },
  { title: '服务人数限制', dataIndex: 'isLimitedBySvcHeadcountName' },
  { title: '最小服务人数', dataIndex: 'minSvcHeadCount' },
  { title: '最大服务人数', dataIndex: 'maxSvcHeadCount' },
  { title: '创建时间', dataIndex: 'createDt' },
  { title: '创建者', dataIndex: 'createByName' },
  { title: '方案适用客户范围', dataIndex: 'quotationTempltScopeName' },
  { title: '设置方案区域', dataIndex: 'areaLimitName' },
];
// const vatrMap = new Map([
//   [0, '0%'],
//   [0.03, '3%'],
//   [0.05, '5%'],
//   [0.06, '6%'],
// ]);
let initialValue: any;
let quotationValue: any;
const QuotationUpdateForm: React.FC<OrdinarySalesProps> = (props) => {
  const { visible, onCancel, title, searchForms, statusForms, workItem, initialValues, ...rest } =
    props;
  const userId = useStore<ConnectState>().getState().user.currentUser.profile;
  initialValue = initialValues;
  quotationValue = initialValues;
  if (!visible) {
    quotationValue = {};
    return null;
  }
  const service = API.sale.quotation.createQuotation;
  const areaService = API.sale.quotationTempl.getQuotationTemplateAreas; //区域设置
  const infectService = API.sale.quotationTempl.getQuotationTemplateInfect; //人数折扣数据
  const productService = API.sale.quotationTempl.getQuotationTemplItemList; //产品数据
  const insuranceService = API.sale.quotationTempl.getQuotationTemplInsuranceTypeList; //险种
  const socProdService = API.sale.quotationTempl.getQuoTemplSsecrityListWithCustId; //福利产品
  const socProdSpoService = API.sale.quotationTempl.getQuoTemplSsecrityList; //福利产品
  const cityNameService = API.sale.quotation.selectUser;
  const [form] = Form.useForm();
  const salesDis = useState(false);
  const IndModal = useState(false);
  const [salesMap, setSalesMap] = useState<Map<number, string>>(new Map());
  const addIndModal = useState(false); //单项非正常报价表单
  const [selectedSingRow, setSelectedSingRow] = useState([]); //单项非正常报价表单
  const [templtType, setTempltType] = useState<string>(''); //单项非正常报价表单数据
  const [params, setParams] = useState({});
  const [vatrMap, setVatrMap] = useState<Map<number, string>>(new Map());
  const [titleName, setTitleName] = useState('');
  const ordinaryDetailModal = useState(false);
  const welfareDetailModal = useState(false);
  const riskFundDetailModal = useState(false);
  const businessDetailModal = useState(false);
  const businessDetailType = useState(true);
  const healthDetailType = useState(true);
  const employerDetailType = useState(true);
  const [quotationItemRiskDetail, setQuotationItemRiskDetail] = useState({});
  const wriTable = useWritable({ service });
  const wriTable1 = useWritable({ service: { ...service, cacheKey: service.cacheKey + 1 } }); //福利保险产品方案
  const wriTable2 = useWritable({ service: { ...service, cacheKey: service.cacheKey + 2 } });
  const wriTable3 = useWritable({ service: { ...service, cacheKey: service.cacheKey + 3 } });
  const wriTable4 = useWritable({ service: { ...service, cacheKey: service.cacheKey + 4 } });
  const wriTableInd = useWritable({ service: { ...service, cacheKey: service.cacheKey + 'Ind' } }); //单项产品
  const wriTableRisk = useWritable({
    service: { ...service, cacheKey: service.cacheKey + 'risk' },
  }); //风险金table
  const wriTableSpe = useWritable({ service: { ...service, cacheKey: service.cacheKey + 'spe' } }); //风险金table
  const wriTableInd1 = useWritable({
    service: { ...service, cacheKey: service.cacheKey + 'Ind1' },
  }); //单项非正常报价
  const prodVisible = useState(false);
  const [prodParams, setprodParams] = useState({}); //添加时所需条件
  if (quotationValue) {
    if (quotationValue.quotationList) {
      initialValue = {
        ...quotationValue,
        ...quotationValue.quotationList[0],
        ...quotationValue.saleList[0],
      };
    }
    if (quotationValue.riskDetailList.length !== 0) {
      initialValue = {
        ...initialValue,
        templateId: quotationValue.riskDetailList[0].templateId,
        templtType: quotationValue.riskDetailList[0].templtType,
        riskSalesPriceNoTax: quotationValue.riskDetailList[0].salesPriceNoTax,
        riskProductPrice: quotationValue.riskDetailList[0].productPrice,
        riskVatr: quotationValue.riskDetailList[0].vatr,
        riskAtr: quotationValue.riskDetailList[0].atr,
        riskPrice: quotationValue.riskDetailList[0].salesPrice,
        riskVat: quotationValue.riskDetailList[0].vat,
        wvatr: '',
        watr: '',
      };
    }
    if (quotationValue.businessDetailList.length !== 0) {
      initialValue.wvatr = quotationValue.businessDetailList[0].vatr;
      initialValue.watr = quotationValue.businessDetailList[0].atr;
    }
    if (quotationValue.healthDetailList.length !== 0) {
      initialValue.wvatr = quotationValue.healthDetailList[0].vatr;
      initialValue.watr = quotationValue.healthDetailList[0].atr;
    }
    if (quotationValue.EmployerDetailList.length !== 0) {
      initialValue.wvatr = quotationValue.EmployerDetailList[0].vatr;
      initialValue.watr = quotationValue.EmployerDetailList[0].atr;
    }
  } else {
    initialValue = {};
  }
  //获取增值税率
  const initVatrList = async () => {
    const params = {
      code: '57',
    };
    const data = await API.commons.common.getBaseDataInfoByCode.requests(params);
    const map = new Map<number, string>();
    data.list.forEach((e: any) => map.set(+e.key, e.shortName));
    setVatrMap(map);
  };
  useEffect(() => {
    initVatrList();
  }, []);
  useEffect(() => {
    if (initialValues.quotationList[0].createByName !== userId.realName) {
      form.setFieldsValue({
        creator: initialValues.quotationList[0].createByName,
      });
    } else {
      form.setFieldsValue({
        creator: userId.realName,
      });
    }
    cityNameService
      .requests({
        userId: statusForms !== 'add' ? quotationValue.quotationList[0].createBy : userId.userId,
      })
      .then((data) => {
        if (data.list.length > 0) {
          form.setFieldsValue({
            cityName: data.list[0].CITY_NAME,
          });
        } else {
          form.setFieldsValue({
            cityName: statusForms !== 'add' ? quotationValue.quotationList[0].cityName : '',
          });
        }
      });
    if (initialValue.businessDetailList.length !== 0) {
      businessDetailType[1](quotationValue.businessDetailList[0].detailType);
    }
    if (initialValue.healthDetailList.length !== 0) {
      healthDetailType[1](quotationValue.healthDetailList[0].detailType);
    }
    if (initialValue.EmployerDetailList.length !== 0) {
      employerDetailType[1](quotationValue.EmployerDetailList[0].detailType);
    }
    if (initialValue.quotationLadderList.length !== 0) {
      wriTable.setNewData(
        quotationValue.quotationLadderList.map((item: any) => {
          return { ...item, atr: parseFloat(item.atr) * 100 };
        }) || [],
      );
    }
    if (statusForms !== 'update') {
      handleFirstCust(initialValue);
      API.sale.quotationTempl.getQuotationTemplItemList
        .requests({ quotationTempltId: initialValue.templateId })
        .then((data) => {
          wriTableRisk.setNewData(data.list);
        });
      wriTableSpe.setNewData(quotationValue.specialDetailList || []); //特殊
      if (quotationValue.productDetailList.length > 0) {
        const list = quotationValue.productDetailList.map((item: any) => {
          const items = item;
          let subLadderList = [];
          if (items.quotationItemType !== '1') {
            subLadderList = items.subLadder.map((tempItem: any) => {
              return { ...tempItem, atr: multipleNum(tempItem.atr) };
            });
          } else {
            subLadderList = [];
          }
          return { ...item, subLadder: subLadderList, atr: multipleNum(items.atr) };
        });
        quotationValue.productDetailList = list;
      }
      wriTable1.setNewData(
        quotationValue.saleDetailList.map((item: any) => {
          return {
            ...item,
            atr: multipleNum(item.atr),
            quotationTempltCityFactor: item.quotationTempltCityFactor ? '1' : '0',
          };
        }) || [],
      );

      wriTable2.addRows(
        quotationValue.businessDetailList.map((item: any) => {
          return {
            ...item,
            isYearsToPay: String(item.isYearsToPay),
            atr: multipleNum(item.atr),
          };
        }) || [],
      );
      wriTable3.addRows(
        quotationValue.healthDetailList.map((item: any) => {
          return {
            ...item,
            isYearsToPay: String(item.isYearsToPay),
            atr: multipleNum(item.atr),
          };
        }) || [],
      );
      wriTable4.addRows(
        quotationValue.EmployerDetailList.map((item: any) => {
          return {
            ...item,
            isYearsToPay: String(item.isYearsToPay),
            atr: multipleNum(item.atr),
          };
        }) || [],
      );
      wriTableSpe.setNewData(
        quotationValue.specialPropertyList.map((item: any) => {
          return { ...item, isYearsToPay: String(item.isYearsToPay) };
        }) || [],
      ); //特殊
      wriTableInd.setNewData(quotationValue.productDetailList || []); //单项
      if (quotationValue.productDetailList.length > 0) {
        if (quotationValue.productDetailList[0].quotationItemType !== '1') {
          wriTableInd1.setNewData(quotationValue.productDetailList[0].subLadder);
          addIndModal[1](true);
          setSelectedSingRow(quotationValue.productDetailList[0]);
        }
      }
    }
    form.setFieldsValue({
      atr: multipleNum(initialValue.atr),
    });
  }, []);
  const formColumns: EditeFormProps[] = [
    //普通报价方式 form
    {
      label: '普通方案售价',
      fieldName: 'salesPriceNoTax',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    {
      label: '报价方式',
      fieldName: 'quotationItemType',
      inputRender: () => mapToSelectors(quoteTyMap),
      inputProps: { disabled: true },
    },
    {
      label: '增值税率%',
      fieldName: 'vatr',
      inputRender: () => mapToSelectors(vatrMap),
      inputProps: { disabled: true },
    },
    {
      label: '附加税率%',
      fieldName: 'atr',
      inputRender: 'string',
      rules: [{ pattern: /^\d+(\.\d{0,2})?$/, message: '仅保留两位小数' }],
      inputProps: { disabled: true },
    },
  ];
  //风险金form
  const formRiskColumns = [
    {
      label: '附加方案类型',
      fieldName: 'templtType',
      inputRender: (options: WritableInstance) =>
        mapToSelectors(qutationTypeMap, { onChange: handleTempltTypeChange, disabled: true }),
      inputProps: { disabled: true },
    },

    {
      label: '风险金/BPO方案',
      fieldName: 'templateId',
      inputRender: (options: WritableInstance) => {
        return (
          <QuotationTempPop
            rowValue="templateId-quotationTempltPrice-remark-quotationTempltName"
            keyMap={{
              templateId: 'quotationTempltId',
              quotationTempltName: 'quotationTempltName',
              quotationTempltPrice: 'quotationTempltPrice',
              remark: 'remark',
            }}
            fixedValues={{ quotationTempltType: templtType || '' }}
            disabled={true}
            handdleConfirm={handleRiskQuotationType}
          />
        );
      },
      inputProps: { disabled: true },
    },
    {
      label: '销售价格（不含税）',
      fieldName: 'riskSalesPriceNoTax', //onBlur={(e) => handleSalesPrice(options, e)}
      inputRender: (options: WritableInstance) => (
        <Input onBlur={(e) => handleRiskPrice(options, e)} disabled={true} />
      ),
    },
    {
      label: '标准价格',
      fieldName: 'riskProductPrice',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    {
      label: '增值税率%',
      fieldName: 'riskVatr',
      inputRender: () => mapToSelectors(vatrMap, { onChange: handleRiskVatr }),
      inputProps: { disabled: true },
    },
    {
      label: '附加税率%',
      fieldName: 'riskAtr',
      inputRender: 'string',
      inputProps: {
        onBlur: (e: ChangeEvent<HTMLInputElement>) => handleRiskAtr(e),
        disabled: true,
      },
    },
    {
      label: '销售价格（含税）',
      fieldName: 'riskPrice',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    {
      label: '增值税',
      fieldName: 'riskVat',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    {
      label: '备注',
      fieldName: 'riskRemark',
      inputRender: 'text',
      inputProps: { disabled: true },
    },
  ];
  const welfareColumns: EditeFormProps[] = [
    //福利报价方式
    {
      label: '增值税率%',
      fieldName: 'wvatr',
      inputRender: () => mapToSelectors(vatrMap),
      inputProps: { disabled: true },
    },
    {
      label: '附加税率%',
      fieldName: 'watr',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
  ];
  const ladderColumns: WritableColumnProps<any>[] = [
    //普通下面 人数form
    { title: '起始人数', dataIndex: 'beginNumber' },
    { title: '截止人数', dataIndex: 'endNumber' },
    {
      title: '增值税率%',
      dataIndex: 'vatr',
      inputRender: () => mapToSelectors(vatrMap, { disabled: true }),
    },
    {
      title: '附加税率%',
      dataIndex: 'atr',
      render: (text: any) => {
        if (!text && text !== 0) {
          return '';
        }
        return parseFloat(text) + '%';
      },
    },
    {
      title: '销售价格不含税',
      dataIndex: 'salesPriceNoTax',
    },
    { title: '销售价格含税', dataIndex: 'salesPrice' },
    { title: '增值税', dataIndex: 'vat' },
  ];
  const ladderIndColumns: WritableColumnProps<any>[] = [
    //普通下面 人数form
    { title: '起始人数', dataIndex: 'beginNumber' },
    { title: '截止人数', dataIndex: 'endNumber' },
    {
      title: '增值税率%',
      dataIndex: 'vatr',
      inputRender: () => mapToSelectors(vatrMap, { disabled: true }),
    },
    {
      title: '附加税率%',
      dataIndex: 'atr',
      render: (text: any) => {
        if (!text && text !== 0) {
          return '';
        }
        return parseFloat(text) + '%';
      },
    },
    {
      title: '销售价格不含税',
      dataIndex: 'salesPriceNoTax',
    },
    { title: '销售价格含税', dataIndex: 'price' },
    { title: '增值税', dataIndex: 'vat' },
  ];
  const normalColumns: WritableColumnProps<any>[] = [
    //普通下面正常报价 form
    { title: '普通销售方案', dataIndex: 'quotationTempltName' },
    { title: '标准价格', dataIndex: 'productPrice' },
    {
      title: '销售价格(不含税)',
      dataIndex: 'salesPriceNoTax',
      rules: [{ required: true, message: '请填写销售价格(不含税)' }],
    },
    {
      title: '增值税率%',
      dataIndex: 'vatr',
      inputRender: () => mapToSelectors(vatrMap, { disabled: true }),
      rules: [{ required: true, message: '请填写增值税率' }],
    },
    {
      title: '附加税率%',
      dataIndex: 'atr',
      rules: [{ required: true, message: '请填写附加税率%' }],
      render: (text: any) => {
        if (!text && text !== 0) {
          return '';
        }
        return parseFloat(text) + '%';
      },
    },
    { title: '销售价格（含税）', dataIndex: 'salesPrice' },
    { title: '增值税', dataIndex: 'vat' },
    {
      title: '是否支持城市系数',
      dataIndex: 'quotationTempltCityFactor',
      inputRender: (options: any) => (
        // <Checkbox disabled={Number(options.record.isCityDiscount)}/>
        <RadioItem disabled={true} />
      ),
    },
    {
      title: '描述',
      dataIndex: 'productDesc',
      render: (value: string) => <EllipsisGrid text={value} />,
    },
    {
      title: '方案明细',
      dataIndex: 'schemeDetail',
      inputRender: (options: any) => {
        const datalist = options.record;
        return <Link onClick={() => handleQuotationDetail(datalist)}>查看</Link>;
      },
    },
    { title: '备注', dataIndex: 'remark', inputRender: 'string' },
    {
      title: '是否外包给第三方供应商',
      dataIndex: 'ifForThirdpart',
      inputRender: (options: any) => <RadioItem disabled={true} />,
    },
    {
      title: '第三方供应商名称',
      dataIndex: 'thirdPartyProviderName',
      // inputRender: ({ record, serial }) => {
      //   return (
      //     <ThirdPartyProviderSelector
      //       params={{ quotationTemplateId: record.quotationTempltId || record.templateId }}
      //       keyMap={{
      //         cost: 'cost',
      //       }}
      //       onConfirm={(value) => {
      //         if (!value.cost) {
      //           return;
      //         }
      //         wriTable.updateRows({ serial, productCost: value.cost });
      //       }}
      //     />
      //   );
      // },
    },
  ];
  const formColumns1 = [
    // 第一步form
    {
      label: '报价单编号',
      fieldName: 'quotationCode',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    {
      label: '报价单名称',
      fieldName: 'quotationName',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    {
      label: '客户名称',
      fieldName: 'custName',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    {
      label: '预计签约人数',
      fieldName: 'suppltMedInsurHeadcount',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    {
      label: '服务地区',
      fieldName: 'svcArea',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    {
      label: '是否全国单',
      fieldName: 'ifNationwide',
      inputRender: () => <RadioItem />,
      inputProps: { disabled: true },
    },
    {
      label: '是否首次签约',
      fieldName: 'ifFirstAudit',
      inputRender: () => <RadioItem />,
      inputProps: { disabled: true },
    },
    {
      label: '是否上社保',
      fieldName: 'ifSs',
      inputRender: () => <RadioItem />,
      inputProps: { disabled: true },
    },
    {
      label: '是否上公积金',
      fieldName: 'ifAf',
      inputRender: () => <RadioItem />,
      inputProps: { disabled: true },
    },
    {
      label: '销售',
      fieldName: 'newSaleName',
      inputRender: 'string',
      // inputRender: () => mapToSelectors(salesMap),
      inputProps: { disabled: true },
    },
    {
      label: '所属城市',
      fieldName: 'cityName',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    {
      label: '创建者',
      fieldName: 'creator',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    {
      label: '报价单总售价',
      fieldName: 'quotationTotalSalPrice',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    {
      label: '备注',
      fieldName: 'remark',
      inputRender: 'text',
      inputProps: { disabled: true },
    },
  ];
  const indFormColumns = [
    //单项产品
    {
      label: '产品名称',
      fieldName: 'productName',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    {
      label: '产品类型',
      fieldName: 'productType',
      inputRender: () => <QuotationProductSelector disabled={true} />,
    },
  ];
  const riskColumns = [
    { title: '产品名称', dataIndex: 'productName' },
    { title: '标准价格', dataIndex: 'cost' },
    { title: '福利描述', dataIndex: 'remark', render: (text) => <EllipsisGrid text={text} /> },
  ];
  const individualColumns = [
    { title: '产品名称', dataIndex: 'productName' },
    { title: '产品类型', dataIndex: 'productTypeName' },
    { title: '产品大类', dataIndex: 'productCategoryName' },
    { title: '标准价格', dataIndex: 'standardPrice' },
    { title: '详情', dataIndex: 'detail' },
  ];
  const indEditColumns = [
    { title: '产品名称', dataIndex: 'productName' },
    { title: '标准价格', dataIndex: 'productPrice' },
    {
      title: '销售价格（不含税）',
      dataIndex: 'salesPriceNoTax',
    },
    {
      title: '增值税率%',
      dataIndex: 'vatr',
      inputRender: (options: WritableInstance) =>
        mapToSelectors(vatrMap, {
          onChange: (value) => handleIndVatrChange(value, options),
          disabled: true,
        }),
    },
    {
      title: '附加税率%',
      dataIndex: 'atr',
      inputRender: (options: WritableInstance) => (
        <Input onBlur={(e) => handleIndAtrChange(options, e)} disabled={true} />
      ),
      render: (text: any) => {
        if (!text && text !== 0) {
          return '';
        }
        return parseFloat(text) + '%';
      },
    },
    { title: '销售价格（含税）', dataIndex: 'salesPrice' },
    { title: '增值税', dataIndex: 'vat' },
    { title: '产品线', dataIndex: 'productLineName' },
    {
      title: '详情',
      dataIndex: 'remark',
      render: (value: string) => <EllipsisGrid text={value} />,
    },
  ];
  const SpeEditColumns = [
    { title: '名称', dataIndex: 'customizeType' },
    { title: '描述', dataIndex: 'remark' },
    { title: '金额', dataIndex: 'amt' },
  ];
  const businessProdColumn = [
    {
      title: '产品方案名称',
      dataIndex: 'quotationTempltName',
    },
    { title: '价格', dataIndex: 'productPrice' },
    { title: '销售价格（不含税）', dataIndex: 'salesPriceNoTax' },
    {
      title: '增值税率%',
      dataIndex: 'vatr',
      inputRender: () => mapToSelectors(vatrMap, { disabled: true }),
    },
    {
      title: '附加税率%',
      dataIndex: 'atr',
      render: (text: any) => {
        if (!text && text !== 0) {
          return '';
        }
        return parseFloat(text) + '%';
      },
    },
    { title: ' 销售价格（含税）', dataIndex: 'salesPrice' },
    { title: ' 增值税', dataIndex: 'vat' },
    {
      title: ' 是否年缴',
      dataIndex: 'isYearsToPay',
      inputRender: () => <RadioItem disabled={true} />,
    },
    { title: ' 起始日期 ', dataIndex: 'suplmtMedStartDt', inputRender: 'date' },
    { title: ' 截至日期 ', dataIndex: 'suplmtMedEndDt', inputRender: 'date' },
    {
      title: ' 描述 ',
      dataIndex: 'productDesc',
      render: (value: string) => <EllipsisGrid text={value} />,
    },
    {
      title: '方案明细',
      dataIndex: 'schemeDetail',
      inputRender: (options: any) => {
        const datalist = options.record;
        return <Link onClick={() => handleQuotationDetail(datalist)}>查看</Link>;
      },
    },
  ];
  const businessProdColumn1 = [
    {
      title: '产品方案名称',
      dataIndex: 'quotationTempltName',
    },
    { title: '价格', dataIndex: 'productPrice' },
    { title: '销售价格（不含税）', dataIndex: 'salesPriceNoTax' },
    {
      title: '增值税率%',
      dataIndex: 'vatr',
      inputRender: () => mapToSelectors(vatrMap, { disabled: true }),
    },
    {
      title: '附加税率%',
      dataIndex: 'atr',
      render: (text: any) => {
        if (!text && text !== 0) {
          return '';
        }
        return parseFloat(text) + '%';
      },
    },
    { title: ' 销售价格（含税）', dataIndex: 'salesPrice' },
    { title: ' 增值税', dataIndex: 'vat' },
    {
      title: ' 是否年缴',
      dataIndex: 'isYearsToPay',
      inputRender: () => <RadioItem disabled={true} />,
    },
    { title: '起始日期 ', dataIndex: 'effectiveDt', inputRender: 'date' },
    { title: '截至日期 ', dataIndex: 'invalidDt', inputRender: 'date' },
    {
      title: ' 描述 ',
      dataIndex: 'productDesc',
      render: (value: string) => <EllipsisGrid text={value} />,
    },
    {
      title: '方案明细',
      dataIndex: 'schemeDetail',
      inputRender: (options: any) => {
        const datalist = options.record;
        return <Link onClick={() => handleQuotationDetail(datalist)}>查看</Link>;
      },
    },
  ];
  const businessProdColumn2 = [
    {
      title: '产品方案名称',
      dataIndex: 'quotationTempltName',
    },
    { title: '价格', dataIndex: 'productPrice' },
    { title: '销售价格（不含税）', dataIndex: 'salesPriceNoTax' },
    {
      title: '增值税率%',
      dataIndex: 'vatr',
      inputRender: () => mapToSelectors(vatrMap, { disabled: true }),
    },
    {
      title: '附加税率%',
      dataIndex: 'atr',
      render: (text: any) => {
        if (!text && text !== 0) {
          return '';
        }
        return parseFloat(text) + '%';
      },
    },
    { title: ' 销售价格（含税）', dataIndex: 'salesPrice' },
    { title: ' 增值税', dataIndex: 'vat' },
    {
      title: ' 是否年缴',
      dataIndex: 'isYearsToPay',
      inputRender: () => <RadioItem disabled={true} />,
    },
    { title: ' 起始日期 ', dataIndex: 'suplmtMedStartDt', inputRender: 'date' },
    { title: ' 截至日期 ', dataIndex: 'suplmtMedEndDt', inputRender: 'date' },
    {
      title: ' 描述 ',
      dataIndex: 'productDesc',
      render: (value: string) => <EllipsisGrid text={value} />,
    },
    {
      title: '方案明细',
      dataIndex: 'schemeDetail',
      inputRender: (options: any) => {
        const datalist = options.record;
        return <Link onClick={() => handleQuotationDetail(datalist)}>查看</Link>;
      },
    },
  ];
  //客户选择回调
  const handleFirstCust = (value) => {
    API.sale.quotation.getCurrentSales.requests({ custId: value.custId }).then((data) => {
      const map = new Map<number, string>();
      data.list.forEach((e: any) => map.set(+e.key, e.shortName));
      setSalesMap(map);
    });
  };
  //风险金销售价格不含税
  const handleRiskPrice = (options: WritableInstance, e: any) => {
    const value = e.target.value;
    if (!value) return;
    const vatr = form.getFieldValue('riskVatr');
    const atr = form.getFieldValue('riskAtr');
    const obj = arrCalculation({ vatr, atr, salesPriceNoTax: value });
    form.setFieldsValue({
      riskPrice: obj.price,
      riskVat: obj.vat,
    });
  };
  //风险金增值税率
  const handleRiskVatr = (value: number) => {
    if (!value) return;
    const salesPriceNoTax = form.getFieldValue('riskSalesPriceNoTax');
    const atr = form.getFieldValue('riskAtr');
    const obj = arrCalculation({ vatr: value, atr, salesPriceNoTax });
    form.setFieldsValue({
      riskPrice: obj.price,
      riskVat: obj.vat,
    });
  };
  //风险金附加税率
  const handleRiskAtr = (e) => {
    const value = e.target.value;
    if (!value) return;
    const salesPriceNoTax = form.getFieldValue('riskSalesPriceNoTax');
    const vatr = form.getFieldValue('riskVatr');
    const obj = arrCalculation({ vatr, atr: value, salesPriceNoTax });
    form.setFieldsValue({
      riskPrice: obj.price,
      riskVat: obj.vat,
    });
  };
  const handleRiskQuotationType = (value: defs.sale.quotationTemplQuery) => {
    const { quotationTempltPrice, quotationTempltCost, templateId, remark } = value;
    form.setFieldsValue({
      riskProductPrice: quotationTempltPrice,
      riskRemark: remark,
    });
    setQuotationItemRiskDetail({
      productPrice: quotationTempltPrice,
      productCost: quotationTempltCost,
      remark: remark,
    });
    //TODO 根据风险金获取产品
    API.sale.quotationTempl.getQuotationTemplItemList
      .requests({ quotationTempltId: templateId })
      .then((data) => {
        if (data) {
          wriTableRisk.setNewData(data.list);
        } else {
          wriTableRisk.setNewData([]);
        }
      });
  };
  const handleTempltTypeChange = (value: any) => {
    form.setFieldsValue({
      quotationTempltId: '',
      quotationTempltName: '',
      riskProductPrice: '',
      riskRemark: '',
    });
    if (!value) {
      setTempltType('');
      return;
    }
    setTempltType(value);
  };
  const handleIndTypechange = (value: string) => {
    //单项产品行内报价方式
  };
  //汇总计算
  const arrCalculation = (item: defs.sale.quotationLadderDTO) => {
    if (item.vatr && item.atr && item.salesPriceNoTax) {
      const arrtax = calculationVat(item.salesPriceNoTax, item.vatr, item.atr);
      return { ...item, salesPrice: arrtax[1], price: arrtax[1], vat: arrtax[2] };
    } else {
      return { ...item };
    }
  };
  const handleIndVatrChange = (value: string, options: any) => {
    //单项表单增值税率事件
    if (!value) return;
    const obj = arrCalculation({ ...options.record, vatr: value });
    wriTableInd.setFieldsValue(options.serial, {
      ...options.record,
      ...obj,
    });
  };
  const handleIndAtrChange = (options: any, e) => {
    const value = e.target.value;
    if (!value) return;
    const obj = arrCalculation({ ...options.record, atr: value });
    wriTableInd.setFieldsValue(options.serial, {
      ...options.record,
      ...obj,
    });
  };
  const indSelectedRows = (rows: any) => {
    if (!rows) return;
    setSelectedSingRow(rows);
    const type: any = wriTableInd.getList().all[rows.aska - 1];
    if (type.quotationItemType !== '1') {
      addIndModal[1](true);
    } else {
      addIndModal[1](false);
    }
    wriTableInd1.setNewData(type.subLadder);
  };
  const handlePick = (rows: defs.sale.productDataQuery[]) => {
    form.validateFields().then((value) => {
      const list = rows.map((item: any) => {
        const obj = arrCalculation({
          ...item,
          vatr: value.vatr || '',
          atr: value.atr || '',
          salesPriceNoTax: value.salesPrice || '',
        });
        return {
          ...item,
          ...obj,
          salesPrice: obj.price,
          productCost: item.quotationTempltCost,
          templateId: item.quotationTempltId,
          productPrice: item.quotationTempltPrice,
        };
      });
      wriTable1.setNewData(list);
    });
  };
  const handleIndPick = (rows: any) => {
    //单项产品添加
    const list = rows.map((item: any) => {
      return { ...item, quotationItemType: '1', subLadder: [] };
    });
    wriTableInd.setNewData(list);
  };
  const renderFooter = () => {
    return (
      <RowElement>
        <ColElementButton
          style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}
        >
          {statusForms !== 'Adopt' ? (
            <Button key="onok" type="primary" onClick={() => onCancel()}>
              关闭
            </Button>
          ) : (
            renderAppFooter()
          )}
        </ColElementButton>
      </RowElement>
    );
  };
  const renderAppFooter = () => {
    return (
      <RowElement>
        <ColElementButton>
          <Button key="onok" type="primary" onClick={() => handleQuotationOk('Adopt')}>
            审批通过
          </Button>
          <Button key="onok" type="primary" onClick={() => handleQuotationOk('reject')}>
            驳回
          </Button>
          <Button
            key="onok"
            type="primary"
            onClick={() => {
              handleQuotationOk('stop');
            }}
          >
            终止
          </Button>
        </ColElementButton>
      </RowElement>
    );
  };
  const handleQuotationOk = async (appstatus: string) => {
    const statusApp = appstatus;
    form.validateFields().then(async (value) => {
      if (statusApp === 'Adopt') {
        // const values = {...initialValues,initialValues.}
        API.sale.quotation.doApprove
          // .requests({ ...params, workitemId: workItem, quotationId: quotationValue.quotationList[0].quotationId })
          .requests({ ...value, ...initialValues, ...workItem })
          .then((data) => {
            msgOk('审批通过');
            onCancel();
          });
      } else if (statusApp === 'reject') {
        API.sale.quotation.back
          .requests({ ...value, ...initialValues, ...workItem })
          .then((data) => {
            msgOk('驳回成功');
            onCancel();
          });
      } else if (statusApp === 'stop') {
        API.sale.quotation.terminal
          .requests({ ...value, ...initialValues, ...workItem })
          .then((data) => {
            msgOk('终止成功');
            onCancel();
          });
      }
    });
  };
  const changeData = (data: any) => {
    const resultList: any = {};
    const arr: any = [];
    for (const obj of data) {
      resultList.quotationTempltCost = obj.COST;
      resultList.custId = obj.CUSTID;
      resultList.effectiveDt = obj.EFFECTIVEDT ? moment(obj.EFFECTIVEDT) : '';
      resultList.productId = obj.PRODUCTID;
      resultList.productLineId = obj.PRODUCTLINEID;
      resultList.productLineName = obj.PRODUCTLINENAME;
      resultList.productName = obj.PRODUCTNAME;
      resultList.productTypeName = obj.QUOTATIONTEMPLTTYPENAME;
      resultList.productTypeId = obj.PRODUCTTYPENAME;
      resultList.quotationTempltId = obj.QUOTATIONTEMPLTID;
      resultList.quotationTempltName = obj.QUOTATIONTEMPLTNAME;
      resultList.quotationTempltScopeName = obj.QUOTATIONTEMPLTSCOPENAME;
      resultList.quotationTempltTypeName = obj.QUOTATIONTEMPLTTYPENAME;
      resultList.validDtType = obj.VALIDDTTYPE;
      resultList.quotationTempltPrice = obj.STANDARDPRICE;
      resultList.approvePrice = obj.APPROVEPRICE;
      resultList.invalidDt = obj.INVALIDDT ? moment(obj.INVALIDDT) : '';
      resultList.minSvcHeadCount = obj.MINSVCHEADCOUNT;
      resultList.maxSvcHeadCount = obj.MAXSVCHEADCOUNT;
      resultList.isLimitedBySvcHeadcount = obj.ISLIMITEDBYSVCHEADCOUNT;
      resultList.remark = obj.REMARK;
      arr.push(resultList);
    }
    return arr;
  };
  const isOrdinary = async (dataList: defs.sale.quotationTemplDTO) => {
    const proddata = await productService.requests({
      quotationTempltId: dataList.quotationTempltId,
    });
    const areaData = await areaService.requests({
      quotationTempltId: dataList.quotationTempltId,
      ...pageInit,
    });
    const infectData = await infectService.requests({
      quotationTempltId: dataList.quotationTempltId,
      ...pageInit,
    });
    setParams({
      ...dataList,
      productList: proddata!.list,
      areaList: areaData!.list,
      infectList: infectData!.list,
    });
    ordinaryDetailModal[1](true);
  };
  const isWelfare = async (dataList: defs.sale.quotationTemplDTO) => {
    if (dataList.quotationTempltScope === '1') {
      const welfareData = await socProdService.requests({
        quotationTempltId: dataList.quotationTempltId,
        custId: dataList.custId,
        ...pageInit,
      });
      const itemList = changeData(welfareData.list);
      setTitleName('福利方案');
      setParams({
        ...dataList,
        socProdList: itemList,
      });
      welfareDetailModal[1](true);
    } else {
      const welfareData1 = await socProdSpoService.requests({
        quotationTempltId: dataList.quotationTempltId,
        custId: dataList.custId,
        ...pageInit,
      });
      const itemList = changeData(welfareData1.list);
      setTitleName('福利方案');
      setParams({
        ...dataList,
        socProdList: itemList,
      });
      welfareDetailModal[1](true);
    }
  };
  const isRisk = async (dataList: defs.sale.quotationTemplDTO) => {
    const data = await productService.requests({ quotationTempltId: dataList.quotationTempltId });
    const data1 = await areaService.requests({
      quotationTempltId: dataList.quotationTempltId,
      ...pageInit,
    });
    setTitleName('风险金方案');
    setParams({ ...dataList, productList: data.list, areaList: data1.list });
    riskFundDetailModal[1](true);
  };
  const isRiskBPO = async (dataList: defs.sale.quotationTemplDTO) => {
    setTitleName('风险金PRO方案');
    const riskData = await productService.requests({
      quotationTempltId: dataList.quotationTempltId,
    });
    const riskData1 = await areaService.requests({
      quotationTempltId: dataList.quotationTempltId,
      ...pageInit,
    });
    setParams({ ...dataList, productList: riskData.list, areaList: riskData1.list });
    riskFundDetailModal[1](true);
  };
  const isGwOrdinary = async (dataList: defs.sale.quotationTemplDTO) => {
    const ordData = await productService.requests({
      quotationTempltId: dataList.quotationTempltId,
    });
    const ordData1 = await areaService.requests({
      quotationTempltId: dataList.quotationTempltId,
      ...pageInit,
    });
    const ordData2 = await infectService.requests({
      quotationTempltId: dataList.quotationTempltId,
      ...pageInit,
    });
    setParams({
      ...dataList,
      productList: ordData.list,
      areaList: ordData1.list,
      infectList: ordData2.list,
    });
    ordinaryDetailModal[1](true);
  };
  const isBussins = async (dataList: defs.sale.quotationTemplDTO) => {
    const busiData = await insuranceService.requests({
      quotationTempltId: dataList.quotationTempltId,
    });
    API.sale.quotationTempl.getQuotationTemplate1700ById
      .requests({ templateId: dataList.quotationTempltId })
      .then((data1) => {
        setParams({ ...dataList, insurance: busiData.list });
        businessDetailModal[1](true);
      });
  };
  const handleQuotationDetail = async (data: any) => {
    const typeName = data.templtType;
    let tempData: any;
    let dataList: any;
    if (typeName !== '1700') {
      tempData = await API.sale.quotationTempl.getOuotationTemplateById.requests({
        quotationTempltId: data.templateId,
      });
      dataList = removeEmpty(changeQuotationTempData(tempData.list));
    } else {
      tempData = await API.sale.quotationTempl.getQuotationTemplate1700ById.requests({
        templateId: data.templateId,
      });
    }
    switch (typeName) {
      case '1':
        setTitleName('普通销售方案模板');
        isOrdinary(dataList);
        break;
      case '1001':
        isWelfare(dataList);
        break;
      case '1002':
        isWelfare(dataList);
        break;
      case '3':
        isRisk(dataList);
        break;
      case '4':
        isRiskBPO(dataList);
        break;
      case '6':
        setTitleName('岗位外包产品模板');
        isGwOrdinary(dataList);
        break;
      case '1700':
        setTitleName('商业保险方案');
        isBussins(tempData);
        break;
    }
  };
  const renderText = () => {
    //正常报价
    return (
      <>
        {form.getFieldValue('quotationItemType') !== '1' ? renderladderQuote() : null}
        <Writable
          service={{ ...service, cacheKey: service.cacheKey + 1 }}
          columns={normalColumns}
          notShowPagination
          noDeleteButton
          noAddButton
          wriTable={wriTable1}
        />
      </>
    );
  };
  //福利保险产品方案
  const renderWefareText = () => {
    return (
      <>
        <Form.Item name={'businessType'}>
          <Checkbox
            defaultChecked={businessDetailType[0]}
            disabled={true}
            onChange={changeBusinessType}
          >
            在账单中仅显示商业保险产品总额，不显示明细
          </Checkbox>
        </Form.Item>
        <Writable
          service={{ ...service, cacheKey: service.cacheKey + 2 }}
          columns={businessProdColumn}
          notShowPagination
          noDeleteButton
          noAddButton
          wriTable={wriTable2}
        />
        <Form.Item name={'healthType'}>
          <Checkbox
            defaultChecked={healthDetailType[0]}
            disabled={true}
            onChange={changehealthType}
          >
            {' '}
            在账单中仅显示健康管理产品总额，不显示明细
          </Checkbox>
        </Form.Item>

        <Writable
          service={{ ...service, cacheKey: service.cacheKey + 3 }}
          columns={businessProdColumn1}
          notShowPagination
          noDeleteButton
          noAddButton
          wriTable={wriTable3}
        />
        <Form.Item name={'employerType'}>
          <Checkbox
            defaultChecked={employerDetailType[0]}
            disabled={true}
            onChange={changeEmployerType}
          >
            {' '}
            在账单中仅显示员工关爱产品总额，不显示明细
          </Checkbox>
        </Form.Item>

        <Writable
          service={{ ...service, cacheKey: service.cacheKey + 4 }}
          columns={businessProdColumn2}
          notShowPagination
          noDeleteButton
          noAddButton
          wriTable={wriTable4}
        />
      </>
    );
  };
  const changeBusinessType = (e) => {
    const value = e.target.checked;
    if (value) {
      businessDetailType[1](true);
    } else {
      businessDetailType[1](false);
    }
    const list = wriTable2.getList().all.map((item: any) => {
      return { ...item, detailType: value ? '1' : '0' };
    });
    wriTable2.setNewData(list);
  };
  const changehealthType = (e) => {
    const value = e.target.checked;
    if (value) {
      healthDetailType[1](true);
    } else {
      healthDetailType[1](false);
    }
    const list = wriTable3.getList().all.map((item: any) => {
      return { ...item, detailType: value ? '1' : '0' };
    });
    wriTable3.setNewData(list);
  };
  const changeEmployerType = (e) => {
    const value = e.target.checked;
    if (value) {
      healthDetailType[1](true);
    } else {
      healthDetailType[1](false);
    }
    const list = wriTable4.getList().all.map((item: any) => {
      return { ...item, detailType: value ? '1' : '0' };
    });
    wriTable4.setNewData(list);
  };
  const renderIndProduct = () => {
    //单项非正常报价
    return (
      <>
        <Writable
          service={{ ...service, cacheKey: service.cacheKey + 'Ind1' }}
          columns={ladderIndColumns}
          notShowPagination
          noDeleteButton
          noAddButton
          wriTable={wriTableInd1}
        />
      </>
    );
  };
  const renderladderQuote = () => {
    //阶梯总价 or 阶梯人均
    return (
      <>
        <Writable
          service={service}
          columns={ladderColumns}
          notShowPagination
          noDeleteButton
          noAddButton
          wriTable={wriTable}
        />
      </>
    );
  };
  const renderIndividual = () => {
    //单项产品
    return (
      <>
        <Writable
          service={{ ...service, cacheKey: service.cacheKey + 'Ind' }}
          columns={indEditColumns}
          notShowPagination
          noDeleteButton
          noAddButton
          wriTable={wriTableInd}
          selectedSingleRow={selectedSingRow}
          onSelectSingleRow={indSelectedRows}
        />
        {addIndModal[0] ? renderIndProduct() : null}
      </>
    );
  };
  //风险金table
  const renderRiskTable = () => {
    return (
      <Writable
        service={{ ...service, cacheKey: service.cacheKey + 'risk' }}
        columns={riskColumns}
        notShowPagination
        noDeleteButton
        noAddButton
        wriTable={wriTableRisk}
      />
    );
  };
  //收费特殊约定
  const renderSpecialText = () => {
    return (
      <>
        <Writable
          service={{ ...service, cacheKey: service.cacheKey + 'Spe' }}
          columns={SpeEditColumns}
          notShowPagination
          noDeleteButton
          noAddButton
          wriTable={wriTableSpe}
        />
      </>
    );
  };
  const renderTabs = () => {
    return (
      <>
        <Tabs defaultActiveKey="base" type="card">
          <Tabs.TabPane tab="普通销售方案" key="base">
            <EnumerateFields outerForm={form} formColumns={formColumns} colNumber={2} />
            {renderText()}
            <TablePop
              rowKeys="quotationTempltId"
              tableColumns={prodTableColumns}
              formColumns={PopColumns}
              visible={prodVisible[0]}
              service={API.sale.quotationTempl.getQuotationTemplate}
              params={{ quotationTempltType: '1' }}
              handlePick={handlePick}
              onCancel={() => prodVisible[1](false)}
            />
            <OrdinarySalesOrderDetail
              title={titleName}
              visible={ordinaryDetailModal[0]}
              initialValues={params}
              onCancel={() => ordinaryDetailModal[1](false)}
              width={1000}
            />
            <WelfareProgDetail
              title={titleName}
              visible={welfareDetailModal[0]}
              initialValues={params}
              onCancel={() => welfareDetailModal[1](false)}
              width={1000}
            />
            <RiskFundDetail
              title={titleName}
              visible={riskFundDetailModal[0]}
              initialValues={params}
              onCancel={() => riskFundDetailModal[1](false)}
              width={1000}
            />
            <BusinessSalesDetail
              title={titleName}
              visible={businessDetailModal[0]}
              initialValues={params}
              onCancel={() => businessDetailModal[1](false)}
              width={1000}
            />
          </Tabs.TabPane>
          <Tabs.TabPane tab="单项产品" key="individual">
            {renderIndividual()}
            <TablePop
              rowKeys="productId"
              tableColumns={individualColumns}
              formColumns={indFormColumns}
              visible={IndModal[0]}
              service={API.sale.productManage.getProductInQuotation}
              params={{ isVirtual: '0', isOneTimePay: '0', status: '1' }}
              handlePick={handleIndPick}
              onCancel={() => IndModal[1](false)}
            />
          </Tabs.TabPane>
          <Tabs.TabPane tab="福利保险产品方案" key="prod">
            <EnumerateFields outerForm={form} formColumns={welfareColumns} colNumber={3} />
            {renderWefareText()}
          </Tabs.TabPane>
          <Tabs.TabPane tab="风险金方案" key="risk">
            <EnumerateFields outerForm={form} formColumns={formRiskColumns} colNumber={2} />
            {renderRiskTable()}
          </Tabs.TabPane>
          <Tabs.TabPane tab="收费特殊约定" key="special">
            {renderSpecialText()}
          </Tabs.TabPane>
        </Tabs>
        {statusForms === 'Approval' ? (
          <Form.Item name={'remarkApp'} label="审批意见">
            <TextArea rows={4} />
          </Form.Item>
        ) : null}
        {statusForms === 'Adopt' ? (
          <Form.Item name={'auditOpinion'} label="历史审批意见审批意见">
            <TextArea rows={4} />
          </Form.Item>
        ) : null}
        {statusForms === 'Adopt' ? (
          <Form.Item
            name={'remarkApp'}
            label="审批意见"
            rules={[{ required: true, message: '请填写审批意见' }]}
          >
            <TextArea rows={4} />
          </Form.Item>
        ) : null}
      </>
    );
  };
  //{renderTabs()}
  const renderSteps = () => {
    return (
      <Card>
        <EnumerateFields outerForm={form} formColumns={formColumns1} colNumber={3} />
        {renderTabs()}
      </Card>
    );
  };
  return (
    <>
      <Codal
        visible={visible}
        title={title}
        destroyOnClose
        footer={renderFooter()}
        onCancel={onCancel}
        {...rest}
      >
        <Card title="方案产品列表">
          <FormElement3
            form={form}
            initialValues={{
              quotationItemType: '1',
              ...initialValue,
              ifNationwide: String(initialValue.ifNationwide),
              ifFirstAudit: String(initialValue.ifFirstAudit),
            }}
          >
            {renderSteps()}
          </FormElement3>
        </Card>
      </Codal>
    </>
  );
};

export default QuotationUpdateForm;

export { QuotationUpdateForm };
