import * as defs from '../../baseClass';
import {
  fetch,
  RequestConfig,
  beforeFetch,
  afterFetch,
  checkResStatus,
  removeSpace,
} from '@/utils/request';

/**
     * @url /rhro-service-1.0/rpac/certificate/emp/batch/add
     * @desc 按人批量获取凭证
按人批量获取凭证
     * hasForm: false
     * hasBody: true
     */

export class Params {}

export const init = new defs.welfaremanage.processInfoQuery();
export const url = '/rhro-service-1.0/rpac/certificate/emp/batch/add:POST';
export const initialUrl = '/rhro-service-1.0/rpac/certificate/emp/batch/add';
export const cacheKey = '_rpac_certificate_emp_batch_add_POST';
export async function request(
  data: defs.welfaremanage.processInfoQuery,
  options?: RequestConfig,
) {
  const reqUrl = `/rhro-service-1.0/rpac/certificate/emp/batch/add`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          reolve(res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
export async function requests(
  data: defs.welfaremanage.processInfoQuery,
  options?: RequestConfig,
) {
  const reqUrl = `/rhro-service-1.0/rpac/certificate/emp/batch/add`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          if (checkResStatus(res)) return reolve(res ? res.data : res);
          reject(res ? res.message : res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
