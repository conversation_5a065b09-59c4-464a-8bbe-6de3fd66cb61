import * as defs from '../../baseClass';
import {
  fetch,
  RequestConfig,
  beforeFetch,
  afterFetch,
  checkResStatus,
  removeSpace,
} from '@/utils/request';

/**
     * @url /rhro-service-1.0/contractManage/getContractFileListByContractId
     * @desc 根据合同ID等条件获得合同附件列表
根据合同ID等条件获得合同附件列表
     * hasForm: false
     * hasBody: true
     */

export class Params {}

export const init = new defs.crm.CommonResponse();
export const url =
  '/rhro-service-1.0/contractManage/getContractFileListByContractId:POST';
export const initialUrl =
  '/rhro-service-1.0/contractManage/getContractFileListByContractId';
export const cacheKey = '_contractManage_getContractFileListByContractId_POST';
export async function request(
  data: defs.crm.ContractFile,
  options?: RequestConfig,
) {
  const reqUrl = `/rhro-service-1.0/contractManage/getContractFileListByContractId`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          reolve(res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
export async function requests(
  data: defs.crm.ContractFile,
  options?: RequestConfig,
) {
  const reqUrl = `/rhro-service-1.0/contractManage/getContractFileListByContractId`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          if (checkResStatus(res)) return reolve(res ? res.data : res);
          reject(res ? res.message : res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
