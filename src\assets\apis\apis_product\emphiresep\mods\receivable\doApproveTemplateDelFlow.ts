import * as defs from '../../baseClass';
import {
  fetch,
  RequestConfig,
  beforeFetch,
  afterFetch,
  checkResStatus,
  removeSpace,
} from '@/utils/request';

/**
     * @url /rhro-service-1.0/receivable/doApproveOffSetFlow
     * @desc 账单模板失效审核通过
账单模板失效审核通过
     * hasForm: false
     * hasBody: true
     */

export class Params {}

export const init = new defs.emphiresep.CommonResponse();
export const url = '/rhro-service-1.0/receivable/doApproveOffSetFlow:POST';
export const initialUrl = '/rhro-service-1.0/receivable/doApproveOffSetFlow';
export const cacheKey = '_receivable_doApproveOffSetFlow_POST';
export async function request(
  data: defs.emphiresep.ReceivableTemplate,
  options?: RequestConfig,
) {
  const reqUrl = `/rhro-service-1.0/receivable/doApproveOffSetFlow`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          reolve(res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
export async function requests(
  data: defs.emphiresep.ReceivableTemplate,
  options?: RequestConfig,
) {
  const reqUrl = `/rhro-service-1.0/receivable/doApproveOffSetFlow`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          if (checkResStatus(res)) return reolve(res ? res.data : res);
          reject(res ? res.message : res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
