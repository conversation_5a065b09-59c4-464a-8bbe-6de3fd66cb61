declare namespace defs {
  export namespace commons {
    export class BaseEntity {
      /** add */
      add: boolean;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** 显示类型 */
      disType: string;

      /** 导入类型,扩充使用 */
      expType: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** 是否薪资查询 */
      isWageQuery: string;

      /** 子项Id */
      itemId: string;

      /** 模拟人 */
      mimicBy: string;

      /** 映射id */
      mppId: string;

      /** noChange */
      noChange: boolean;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** 备注 */
      remark: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** 类型编码 */
      typeCode: string;

      /** 类型Id */
      typeId: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;
    }

    export class ClientExportConfig {
      /** className */
      className: string;

      /** expAnnoId */
      expAnnoId: string;

      /** exportFieldList */
      exportFieldList: Array<defs.commons.ExportField>;

      /** exportParam */
      exportParam: object;

      /** exportType */
      exportType: number;

      /** paramMap */
      paramMap: object;

      /** remark */
      remark: string;

      /** serviceName */
      serviceName: string;

      /** serviceParam */
      serviceParam: object;
    }

    export class CommonQuery {
      /** 城市id */
      cityId: string;

      /** 客户id */
      custId: string;

      /** 部门级别 */
      departmentGrade: string;

      /** 部门内部编号 */
      departmentInternalCode: string;

      /** 部门名称 */
      departmentName: string;

      /** endIndex */
      endIndex: number;

      /** 城市id */
      fileProviderName: string;

      /** 大区id */
      governingArea: string;

      /** 大区id */
      governingAreaId: string;

      /** 分公司id */
      governingBranch: string;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** 供应商类型 */
      providerType: string;

      /** 供应商集团id */
      prvdGroupId: string;

      /** startIndex */
      startIndex: number;
    }

    export class CommonResponse<T0 = any> {
      /** bizCode */
      bizCode: number;

      /** code */
      code: number;

      /** data */
      data: T0;

      /** message */
      message: string;

      /** t */
      t: T0;
    }

    export class CustQuery {
      /** 客户编号 */
      custCode: string;

      /** 客户id */
      custId: string;

      /** 客户名称 */
      custName: string;

      /** 客户规模 */
      customerSize: string;

      /** 分公司id */
      departmentId: string;

      /** endIndex */
      endIndex: number;

      /** 集团公司编号 */
      groupId: string;

      /** 集团公司名称 */
      groupName: string;

      /** 删除状态 */
      isDeleted: string;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** 大区id */
      regionId: string;

      /** 销售姓名 */
      salesName: string;

      /** startIndex */
      startIndex: number;
    }

    export class DropdownList {
      /** 业务大类类型 */
      btType: string;

      /** chargeRate */
      chargeRate: string;

      /** cityId */
      cityId: string;

      /** cityIdForParty */
      cityIdForParty: string;

      /** cityName */
      cityName: string;

      /** contractAvgAmt */
      contractAvgAmt: string;

      /** contractHeadcount */
      contractHeadcount: string;

      /** contractName */
      contractName: string;

      /** contractSubType */
      contractSubType: string;

      /** contractSubTypeName */
      contractSubTypeName: string;

      /** contractType */
      contractType: string;

      /** contractTypeName */
      contractTypeName: string;

      /** currentSalesName */
      currentSalesName: string;

      /** departmentName */
      departmentName: string;

      /** englishTermName */
      englishTermName: string;

      /** exFeeMonth */
      exFeeMonth: string;

      /** 供应商收费模板 */
      exFeeTempltId: string;

      /** governingArea */
      governingArea: string;

      /** 所属大区 */
      governingAreaId: string;

      /** governingBranch */
      governingBranch: string;

      /** 所属分公司 */
      governingBranchId: string;

      /** groupType */
      groupType: string;

      /** 主键 */
      key: string;

      /** liabilityCsName */
      liabilityCsName: string;

      /** 全称 */
      name: string;

      /** 拼音码 */
      pinYinCode: string;

      /** productLineId */
      productLineId: string;

      /** 供应商类型1内部2外部 */
      providerType: string;

      /** 保留名字1 */
      reserveName1: string;

      /** 保留名字2 */
      reserveName2: string;

      /** 储备对象,用于查询下拉框的时候,可能还需要查询其他列的值 */
      reserveObj: string;

      /** 缩写名 */
      shortName: string;

      /** signBrachTitleId */
      signBrachTitleId: string;

      /** signBranchTitleName */
      signBranchTitleName: string;

      /** 社保组ID */
      ssGroupId: string;

      /** svcSubtypeName */
      svcSubtypeName: string;

      /** svcTypeName */
      svcTypeName: string;
    }

    export class ExportField {
      /** dataType */
      dataType: number;

      /** fieldName */
      fieldName: string;

      /** fieldText */
      fieldText: string;

      /** tag */
      tag: string;
    }

    export class ExportQuery {
      /** 查询条件 */
      condition: object;

      /** 表头字段列表 */
      fieldArr: Array<string>;

      /** 表头字段中文拼接 */
      headStr: string;

      /** 表头字段类型列表 */
      typeArr: Array<string>;
    }

    export class IDNumCheckModel {
      /** 号码属地 */
      belong: string;

      /** yyyy-mm-dd */
      birthdate: string;

      /** 出生日期 */
      birthday: string;

      /** 01 */
      gender: string;

      /** 待校验身份证 */
      idc: string;

      /** 15位身份证 */
      idc15: string;

      /** 18位身份证 */
      idc18: string;

      /** 校验结果 */
      result: string;

      /** 性别 */
      sex: string;
    }

    export class ImpErrorType {
      /** add */
      add: boolean;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** endIndex */
      endIndex: number;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column BD_IMP_ERROR_TYPE.ERROR_ID           ibatorgenerated Thu Nov 03 15:30:54 CST 2011 */
      errorId: number;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column BD_IMP_ERROR_TYPE.ERROR_INFO           ibatorgenerated Thu Nov 03 15:30:54 CST 2011 */
      errorInfo: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column BD_IMP_ERROR_TYPE.ERROR_TYPE           ibatorgenerated Thu Nov 03 15:30:54 CST 2011 */
      errorType: number;

      /** exceptId */
      exceptId: number;

      /** 导入类型,扩充使用 */
      expType: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** 是否薪资查询 */
      isWageQuery: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** 页数 */
      pageNum: number;

      /** 每页记录数,默认65536条 */
      pageSize: number;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** startIndex */
      startIndex: number;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column BD_IMP_ERROR_TYPE.TYPE_ID           ibatorgenerated Thu Nov 03 15:30:54 CST 2011 */
      typeId: number;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;
    }

    export class ImpItemTypeQuery {
      /** 显示类型 */
      disType: string;

      /** endIndex */
      endIndex: number;

      /** 是否删除 */
      isDeleted: string;

      /** 是否有效 */
      isEffective: string;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** 规则id */
      ruleId: string;

      /** startIndex */
      startIndex: number;

      /** 类型id */
      typeId: string;
    }

    export class ImpRule {
      /** add */
      add: boolean;

      /** batchId */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** 校验策略组 */
      checkStrategyGroup: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 列编码组 */
      columnCodeGroup: string;

      /** 列属性组 */
      columnFieldGroup: string;

      /** 列头文本组 */
      columnHeadGroup: string;

      /** 备注组 */
      columnMemoGroup: string;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** 客户编码 */
      custCode: string;

      /** 客户id */
      custId: number;

      /** 客户名称 */
      custName: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** endIndex */
      endIndex: number;

      /** 结束行 */
      endLine: number;

      /** 导入类型,扩充使用 */
      expType: string;

      /** filePath */
      filePath: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** 导入数据的字节,若定义了导入类型,则此字段值为空 */
      impDataByte: string;

      /** 规则类型 */
      impType: defs.commons.ImpType;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** 是否有效 */
      isEffective: string;

      /** 是否薪资查询 */
      isWageQuery: string;

      /** 子项集合 */
      items: Array<defs.commons.ImpTypeItem>;

      /** 备注 */
      memo: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** 不允许重复组合 */
      notRepeatGroup: string;

      /** 页数 */
      pageNum: number;

      /** 每页记录数,默认65536条 */
      pageSize: number;

      /** 导入附加参数 */
      params: object;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** 记录id */
      recordId: string;

      /** 必填组 */
      reqGroup: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 规则id */
      ruleId: number;

      /** 规则名称 */
      ruleName: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** startIndex */
      startIndex: number;

      /** 开始行 */
      startLine: number;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** 模版文件位置 */
      templateFileUrl: string;

      /** 导入类型id */
      typeId: number;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;
    }

    export class ImpRuleQuery {
      /** 调基任务号 */
      adjTaskId: string;

      /** 基础数据编码 */
      baseDataCode: string;

      /** 批次id */
      batchId: string;

      /** 列名 */
      columns: string;

      /** 薪资批次id */
      currentRecordId: string;

      /** 客户id */
      custId: string;

      /** 结束时间 */
      endDt: string;

      /** endIndex */
      endIndex: number;

      /** 错误类型 */
      errorType: string;

      /** 导入结果 0：成功 1：失败 */
      impTag: string;

      /** 导入人姓名 */
      impUserName: string;

      /** 是否有效 */
      isEffective: string;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** 导入批次中做详细筛选的id值 */
      recordId: string;

      /** 规则id */
      ruleId: string;

      /** 规则名称 */
      ruleName: string;

      /** 前端传入的参数，判断不同的情况查询语句 */
      selByAuth: string;

      /** 开始时间 */
      startDt: string;

      /** startIndex */
      startIndex: number;

      /** 表名 */
      tableName: string;

      /** 类型id */
      typeId: string;

      /** 薪资类别ID */
      wageClassId: string;
    }

    export class ImpType {
      /** add */
      add: boolean;

      /** 类别编码 */
      baseDataCode: string;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** endIndex */
      endIndex: number;

      /** 导入类型,扩充使用 */
      expType: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** isEffective */
      isEffective: string;

      /** 是否薪资查询 */
      isWageQuery: string;

      /** items */
      items: Array<defs.commons.ImpTypeItem>;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column BD_IMP_TYPE.MEMO           ibatorgenerated Tue Nov 01 16:58:12 CST 2011 */
      memo: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** 页数 */
      pageNum: number;

      /** 每页记录数,默认65536条 */
      pageSize: number;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** startIndex */
      startIndex: number;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** tableName */
      tableName: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column BD_IMP_TYPE.TYPE_ID           ibatorgenerated Tue Nov 01 16:58:12 CST 2011 */
      typeId: number;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column BD_IMP_TYPE.TYPE_NAME           ibatorgenerated Tue Nov 01 16:58:12 CST 2011 */
      typeName: string;

      /** 类型标记 */
      typeTag: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;
    }

    export class ImpTypeItem {
      /** add */
      add: boolean;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 列长度 */
      columnLen: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column BD_IMP_TYPE_ITEM.COLUMN_NAME           ibatorgenerated Tue Nov 01 16:58:12 CST 2011 */
      columnName: string;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column BD_IMP_TYPE_ITEM.DB_TYPE           ibatorgenerated Tue Nov 01 16:58:12 CST 2011 */
      dbType: string;

      /** del */
      del: boolean;

      /** disType */
      disType: string;

      /** endIndex */
      endIndex: number;

      /** 导入类型,扩充使用 */
      expType: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column BD_IMP_TYPE_ITEM.HEAD_TEXT           ibatorgenerated Tue Nov 01 16:58:12 CST 2011 */
      headText: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** 是否有效 */
      isEffective: number;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column BD_IMP_TYPE_ITEM.IS_REQUIRED           ibatorgenerated Tue Nov 01 16:58:12 CST 2011 */
      isRequired: number;

      /** 是否薪资查询 */
      isWageQuery: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column BD_IMP_TYPE_ITEM.ITEM_ID           ibatorgenerated Tue Nov 01 16:58:12 CST 2011 */
      itemId: number;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column BD_IMP_TYPE_ITEM.VALIDREG           ibatorgenerated Tue Nov 01 16:58:12 CST 2011 */
      memo: string;

      /** 模拟人 */
      mimicBy: string;

      /** mppId */
      mppId: string;

      /** noChange */
      noChange: boolean;

      /** 页数 */
      pageNum: number;

      /** 每页记录数,默认65536条 */
      pageSize: number;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** remark */
      remark: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** ruleId */
      ruleId: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** startIndex */
      startIndex: number;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** typeCode */
      typeCode: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column BD_IMP_TYPE_ITEM.TYPE_ID           ibatorgenerated Tue Nov 01 16:58:12 CST 2011 */
      typeId: number;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;
    }

    export class ImpTypeQuery {
      /** 基础数据编码 */
      baseDataCode: string;

      /** endIndex */
      endIndex: number;

      /** 错误类型 */
      errorType: string;

      /** 异常id */
      exceptId: string;

      /** 是否删除 */
      isDeleted: string;

      /** 是否有效 */
      isEffective: string;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** startIndex */
      startIndex: number;

      /** 表名 */
      tableName: string;

      /** 类型id */
      typeId: string;

      /** 类型名 */
      typeName: string;

      /** 类型tag */
      typeTag: string;
    }

    export class ImpTypeVO {
      /** 新增错误类型项 */
      addErrorItems: Array<defs.commons.ImpErrorType>;

      /** 导入类型 */
      impType: defs.commons.ImpType;

      /** 是否删除 */
      isDeleted: string;

      /** 子类型id */
      subIds: Array<string>;

      /** 类型id */
      typeId: string;

      /** 新增错误类型项 */
      uptErrorItems: Array<defs.commons.ImpErrorType>;

      /** 更新项 */
      uptItems: Array<defs.commons.ImpTypeItem>;
    }

    export class InnerEmpQuery {
      /** 大区id */
      areaId: string;

      /** 业务类别 */
      bizCategory: string;

      /** 分公司id */
      branchId: string;

      /** 分公司名称 */
      branchName: string;

      /** 客户id */
      custId: string;

      /** 部门id */
      deptId: string;

      /** 部门名称 */
      deptName: string;

      /** endIndex */
      endIndex: number;

      /** 是否 */
      isExcludeDefault: string;

      /** 管理者id */
      manageId: string;

      /** 权限 */
      noRole: string;

      /** 在线任务权限 */
      olTaskRole: string;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** 姓名 */
      realName: string;

      /** 角色code */
      roleCode: string;

      /** 角色级别 */
      roleGrade: string;

      /** 角色id */
      roleId: string;

      /** 角色名称 */
      roleName: string;

      /** startIndex */
      startIndex: number;

      /** 小合同id */
      subContractId: string;

      /** 用户id */
      userId: string;

      /** 登陆名 */
      userName: string;
    }

    export class Page {
      /** currentPage */
      currentPage: number;

      /** currentPageNo */
      currentPageNo: number;

      /** data */
      data: Array<object>;

      /** pageSize */
      pageSize: number;

      /** result */
      result: Array<object>;

      /** start */
      start: number;

      /** totalCount */
      totalCount: number;

      /** totalPage */
      totalPage: number;

      /** totalPageCount */
      totalPageCount: number;
    }

    export class SocialGroupQuery {
      /** 城市id */
      cityId: string;

      /** endIndex */
      endIndex: number;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** 社保组名称 */
      socialName: string;

      /** 社保组类型 */
      ssGroupType: string;

      /** startIndex */
      startIndex: number;
    }

    export class WageCalculateFeeQuery {
      /** 年终奖 */
      bonus: string;

      /** 年终奖税 */
      bonusTax: string;

      /** endIndex */
      endIndex: number;

      /** 所得 */
      incomeAmount: string;

      /** 1 正算 2倒算 */
      operateType: string;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** 个人所得税 */
      personIncomeTax: string;

      /** 速算扣除数 */
      quickDeduct: string;

      /** 薪资 */
      salary: string;

      /** 1薪资 2 年终奖 3劳务费 */
      salaryType: string;

      /** 劳务费税 */
      serviceFeeTax: string;

      /** startIndex */
      startIndex: number;

      /** 税额 */
      taxAmount: string;

      /** 对应税率表id */
      taxBaseId: string;

      /** 税率 */
      taxRate: string;

      /** 主键 */
      wageCalculateFeeId: string;
    }

    export class fileImportVO {
      /** 业务类型, 据（校长)说一般是function_id */
      bizType: string;

      /** 文件数据 */
      fileData: string;

      /** 文件id */
      fileId: string;

      /** 文件名称 */
      fileName: string;

      /** 文件路径 */
      filePath: string;

      /** 历史文件ID */
      hisFileId: string;

      /** 级联查询条件,逐级网上找 */
      levelQueryByFileId: string;

      /** 非标合同审批id */
      nonStaCoctApprId: string;

      /** 个性化参数 */
      parameters: object;

      /** 记录id */
      recordId: string;

      /** 备注 */
      remark: string;

      /** 导入规则id */
      ruleId: string;

      /** 服务名称 */
      serviceName: string;

      /** 上传人id */
      uploadBy: string;

      /** 上传人名字 */
      uploadByName: string;

      /** 上传日期 */
      uploadDt: string;
    }

    export class fileInfoDTO {
      /** 业务类型, 据（校长)说一般是function_id */
      bizType: string;

      /** 文件数据 */
      fileData: string;

      /** 文件id */
      fileId: string;

      /** 文件名称 */
      fileName: string;

      /** 文件路径 */
      filePath: string;

      /** 历史文件ID */
      hisFileId: string;

      /** 级联查询条件,逐级网上找 */
      levelQueryByFileId: string;

      /** 非标合同审批id */
      nonStaCoctApprId: string;

      /** 个性化参数 */
      parameters: object;

      /** 记录id */
      recordId: string;

      /** 备注 */
      remark: string;

      /** 上传人id */
      uploadBy: string;

      /** 上传人名字 */
      uploadByName: string;

      /** 上传日期 */
      uploadDt: string;
    }

    export class fileInfoQuery {
      /** 业务类型, 据（校长)说一般是function_id */
      bizType: string;

      /** endIndex */
      endIndex: number;

      /** 文件数据 */
      fileData: string;

      /** 文件id */
      fileId: string;

      /** 文件名称 */
      fileName: string;

      /** 文件路径 */
      filePath: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 历史文件ID */
      hisFileId: string;

      /** 删除标记 */
      isDeleted: string;

      /** 级联查询条件,逐级网上找 */
      levelQueryByFileId: string;

      /** 非标合同审批id */
      nonStaCoctApprId: string;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** 个性化参数 */
      parameters: object;

      /** 记录id */
      recordId: string;

      /** 备注 */
      remark: string;

      /** startIndex */
      startIndex: number;

      /** 上传人id */
      uploadBy: string;

      /** 上传人名字 */
      uploadByName: string;

      /** 上传日期 */
      uploadDt: string;
    }
  }
}

declare namespace API {
  export namespace commons {
    /**
     * 公共service
     */
    export namespace common {
      /**
        * 检查是否绑定微信
检查是否绑定微信
        * /common/checkEmpIdBind
        */
      export namespace checkEmpIdBind {
        export class Params {
          /** empId */
          empId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: string;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 检查是否绑定微信
检查是否绑定微信
        * /common/checkEmpIdBind
        */
      export namespace postCheckEmpIdBind {
        export class Params {
          /** empId */
          empId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: string;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 执行导入过程
执行导入过程
        * /common/execImpPrc
        */
      export namespace execImpPrc {
        export class Params {
          /** prcName */
          prcName: string;
          /** tagId */
          tagId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.commons.CommonResponse<number>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 执行导入过程
执行导入过程
        * /common/execImpPrc
        */
      export namespace postExecImpPrc {
        export class Params {
          /** prcName */
          prcName: string;
          /** tagId */
          tagId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.commons.CommonResponse<number>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * execStmst
execStmst
        * /common/execStmst
        */
      export namespace execStmst {
        export class Params {
          /** parameters */
          parameters?: object;
          /** statementName */
          statementName: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: object;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
       * exportSimpleExcelRptByTemplate
       * /common/exportSimpleExcelRptByTemplate
       */
      export namespace exportSimpleExcelRptByTemplate {
        export class Params {
          /** dataMap */
          dataMap?: object;
          /** templateName */
          templateName: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.commons.CommonResponse<Array<byte>>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据编码获取基础数据信息
根据编码获取基础数据信息
        * /common/getBaseDataInfoByCode
        */
      export namespace getBaseDataInfoByCode {
        export class Params {
          /** code */
          code: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.commons.CommonResponse<Array<defs.commons.DropdownList>>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据编码获取基础数据信息
根据编码获取基础数据信息
        * /common/getBaseDataInfoByCode
        */
      export namespace postGetBaseDataInfoByCode {
        export class Params {
          /** code */
          code: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.commons.CommonResponse<Array<defs.commons.DropdownList>>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取批次数据
获取批次数据
        * /common/getBatchData
        */
      export namespace getBatchData {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.commons.CommonResponse<ObjectMap>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: object,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: object,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取客户
获取客户
        * /common/getCustomer
        */
      export namespace getCustomer {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.commons.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.commons.CustQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.commons.CustQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取多个数据字典类型的数据放入map.数序按照code先后,例子参考editEmpBankCard界面.
获取多个数据字典类型的数据放入map.数序按照code先后,例子参考editEmpBankCard界面.
        * /common/getMultiteDictDataByCodes
        */
      export namespace getMultiteDictDataByCodes {
        export class Params {
          /** codes */
          codes: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.commons.CommonResponse<ObjectMap>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取多个数据字典类型的数据放入map.数序按照code先后,例子参考editEmpBankCard界面.
获取多个数据字典类型的数据放入map.数序按照code先后,例子参考editEmpBankCard界面.
        * /common/getMultiteDictDataByCodes
        */
      export namespace postGetMultiteDictDataByCodes {
        export class Params {
          /** codes */
          codes: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.commons.CommonResponse<ObjectMap>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 提供给前台获取消息推送的服务器地址	  <AUTHOR>	  @throws Exception
提供给前台获取消息推送的服务器地址	  <AUTHOR>	  @throws Exception
        * /common/getPushServerIP
        */
      export namespace getPushServerIP {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: string;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 提供给前台获取消息推送的服务器地址	  <AUTHOR>	  @throws Exception
提供给前台获取消息推送的服务器地址	  <AUTHOR>	  @throws Exception
        * /common/getPushServerIP
        */
      export namespace postGetPushServerIP {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: string;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取报表目录
获取报表目录
        * /common/getReportDir
        */
      export namespace getReportDir {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: string;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取报表目录
获取报表目录
        * /common/getReportDir
        */
      export namespace postGetReportDir {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: string;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取sparksql的报表目录
获取sparksql的报表目录
        * /common/getReportDirBySparkSql
        */
      export namespace getReportDirBySparkSql {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: string;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取sparksql的报表目录
获取sparksql的报表目录
        * /common/getReportDirBySparkSql
        */
      export namespace postGetReportDirBySparkSql {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: string;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取BOE4.0的报表目录
获取BOE4.0的报表目录
        * /common/getReportV4Dir
        */
      export namespace getReportV4Dir {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.commons.CommonResponse<ObjectMap>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取BOE4.0的报表目录
获取BOE4.0的报表目录
        * /common/getReportV4Dir
        */
      export namespace postGetReportV4Dir {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.commons.CommonResponse<ObjectMap>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * get sso domain url
get sso domain url
        * /common/getSSODomainUrl
        */
      export namespace getSSODomainUrl {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.commons.CommonResponse<ObjectMap>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * get sso domain url
get sso domain url
        * /common/getSSODomainUrl
        */
      export namespace postGetSSODomainUrl {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.commons.CommonResponse<ObjectMap>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取叮当的token
获取叮当的token
        * /common/getServiceTicket
        */
      export namespace getServiceTicket {
        export class Params {
          /** userName */
          userName: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: string;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取叮当的token
获取叮当的token
        * /common/getServiceTicket
        */
      export namespace postGetServiceTicket {
        export class Params {
          /** userName */
          userName: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: string;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 短信接口 
短信接口 phoneSplits：多个号码逗号分隔  text：短信内容
        * /common/multiSend
        */
      export namespace multiSend {
        export class Params {
          /** app */
          app?: string;
          /** phoneSplits */
          phoneSplits: string;
          /** sign */
          sign?: string;
          /** text */
          text?: string;
        }

        export type Response<T> = defs.commons.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 短信接口 
短信接口 phoneSplits：多个号码逗号分隔  text：短信内容
        * /common/multiSend
        */
      export namespace postMultiSend {
        export class Params {
          /** app */
          app?: string;
          /** phoneSplits */
          phoneSplits: string;
          /** sign */
          sign?: string;
          /** text */
          text?: string;
        }

        export type Response<T> = defs.commons.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 分页获取分公司
分页获取分公司
返回字段{
GOVERNINGAREA=大区,
DEPARTMENTNAME=分公司}
        * /common/queryBranch
        */
      export namespace queryBranch {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.commons.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.commons.CommonQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.commons.CommonQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 网点标识
网点标识
        * /common/queryBranchId
        */
      export namespace queryBranchId {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.commons.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.commons.CommonQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.commons.CommonQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 分页获取外部分公司
分页获取外部分公司
        * /common/queryExBranch
        */
      export namespace queryExBranch {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.commons.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.commons.CommonQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.commons.CommonQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 分页获取档案供应商
分页获取档案供应商
        * /common/queryFileProvider
        */
      export namespace queryFileProvider {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.commons.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.commons.CommonQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.commons.CommonQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 分页获取内部雇员
分页获取内部雇员,
返回字段{
AREANAME=大区,
BRANCHNAME=分公司,
DEPTNAME=部门名称,
USERNAME=用户名,
REALNAME=姓名,
ROLEGROUP=角色名}
        * /common/queryInnerEmp
        */
      export namespace queryInnerEmp {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.commons.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.commons.InnerEmpQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.commons.InnerEmpQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 分页获取内部雇员(A管B权限)
分页获取内部雇员(A管B权限)
        * /common/queryInnerEmpAmb
        */
      export namespace queryInnerEmpAmb {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.commons.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.commons.CommonQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.commons.CommonQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询内部雇员ex
查询内部雇员ex
        * /common/queryInnerEmpEx
        */
      export namespace queryInnerEmpEx {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.commons.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.commons.InnerEmpQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.commons.InnerEmpQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 分页获取社保公积金组
分页获取社保公积金组
        * /common/querySocialGroup
        */
      export namespace querySocialGroup {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.commons.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.commons.SocialGroupQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.commons.SocialGroupQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * Export File Controller
     */
    export namespace exportFile {
      /**
        * 导出
导出
        * /exportProcess/execute
        */
      export namespace execute {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: any;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.commons.ClientExportConfig,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.commons.ClientExportConfig,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * File Controller
     */
    export namespace file {
      /**
       * delCustFile
       * /file/delCustFile
       */
      export namespace delCustFile {
        export class Params {
          /** fileId */
          fileId: string;
        }

        export type Response<T> = defs.commons.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 删除文件
删除文件
        * /file/delFile
        */
      export namespace delFile {
        export class Params {
          /** fileId */
          fileId: string;
        }

        export type Response<T> = defs.commons.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 下载文件接口,下载的文件若为excel则转换csv
下载文件接口,下载的文件若为excel则转换csv
        * /file/downloadCsv
        */
      export namespace downloadFile {
        export class Params {
          /** fileId */
          fileId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: any;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 下载excel
下载excel
        * /file/downloadExcel
        */
      export namespace downloadExcel {
        export class Params {
          /** fileId */
          fileId: number;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: any;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 和downloadFile的区别为,此方法下载的文件不转换
和downloadFile的区别为,此方法下载的文件不转换
        * /file/generalDownloadExcel
        */
      export namespace generalDownloadFile {
        export class Params {
          /** fileId */
          fileId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: any;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询文件信息
查询文件信息
        * /file/getFileInfo
        */
      export namespace getFileInfo {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.commons.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.commons.fileInfoQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.commons.fileInfoQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取文件记录信息
获取文件记录信息
        * /file/getFileInfoById
        */
      export namespace getFileInfoById {
        export class Params {
          /** fileId */
          fileId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.commons.fileInfoDTO;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取服务器地址和端口
获取服务器地址和端口
        * /file/getPublicServerUrl
        */
      export namespace getPublicServerUrl {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: string;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 上传文件接口
上传文件接口
        * /file/uploadFile
        */
      export namespace uploadFile {
        export class Params {
          /** 业务类型, 据（校长)说一般是function_id */
          bizType: any;
          /** file */
          file: File;
          /** 文件数据 */
          fileData?: any;
          /** 文件id */
          fileId?: any;
          /** 文件名称 */
          fileName: any;
          /** 文件路径 */
          filePath?: any;
          /** 历史文件ID */
          hisFileId?: any;
          /** 级联查询条件,逐级网上找 */
          levelQueryByFileId?: any;
          /** 非标合同审批id */
          nonStaCoctApprId?: any;
          /** 个性化参数 */
          parameters?: any;
          /** 记录id */
          recordId?: any;
          /** 备注 */
          remark?: any;
          /** 保存方式,1为tokyotyrant存储(此方式暂不采用),2为文件目录存储, eos */
          savePattern: any;
          /** 上传人id */
          uploadBy?: any;
          /** 上传人名字 */
          uploadByName?: any;
          /** 上传日期 */
          uploadDt?: any;
        }
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: string;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * ID Num Check Controller
     */
    export namespace idNumCheck {
      /**
        * 身份证校验
身份证校验
        * /iDNumCheck/checkIDC
        */
      export namespace checkIDC {
        export class Params {
          /** idc */
          idc: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.commons.IDNumCheckModel;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获得税率表的下拉菜单
获得税率表的下拉菜单
        * /iDNumCheck/getTaxRateDropDownList
        */
      export namespace getTaxRateDropDownList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.commons.CommonResponse<ObjectMap>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 计算个调税、年终奖、劳务费
计算个调税、年终奖、劳务费
        * /iDNumCheck/setWageCalculateFee
        */
      export namespace setWageCalculateFee {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.commons.WageCalculateFeeQuery;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.commons.WageCalculateFeeQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.commons.WageCalculateFeeQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 导入接口
     */
    export namespace impRule {
      /**
        * 删除接口
删除接口
        * /impRule/del
        */
      export namespace del {
        export class Params {
          /** ids */
          ids: Array;
        }
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.commons.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 导出
导出
        * /impRule/export
        */
      export namespace exportFile {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: any;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.commons.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.commons.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获得导入结果
获得导入结果
        * /impRule/getAutoColumnInfo
        */
      export namespace getAutoColumnInfo {
        export class Params {
          /** ruleId */
          ruleId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.commons.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取导入批次信息
获取导入批次信息
        * /impRule/getBatchInfo
        */
      export namespace getBatchInfo {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.commons.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.commons.ImpRuleQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.commons.ImpRuleQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取导入批次信息
获取导入批次信息
        * /impRule/getBatchInfoForPayRoll
        */
      export namespace getBatchInfoForPayRoll {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.commons.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.commons.ImpRuleQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.commons.ImpRuleQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据客户id获取接口
根据客户id获取接口
        * /impRule/getRuleByCustIdAndBDCode
        */
      export namespace getRuleByCustIdAndBDCode {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.commons.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.commons.ImpRuleQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.commons.ImpRuleQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 更新模板备注sheet
更新模板备注sheet
        * /impRule/reloadTemplate
        */
      export namespace reloadTemplate {
        export class Params {
          /** ruleId */
          ruleId: string;
        }

        export type Response<T> = defs.commons.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 保存接口
保存接口
        * /impRule/save
        */
      export namespace save {
        export class Params {}

        export type Response<T> = defs.commons.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.commons.ImpRule,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.commons.ImpRule,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 接口生效
接口生效
        * /impRule/saveEffective
        */
      export namespace saveEffective {
        export class Params {}

        export type Response<T> = defs.commons.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.commons.ImpRule,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.commons.ImpRule,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询接口
查询接口
        * /impRule/sel
        */
      export namespace sel {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.commons.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.commons.ImpRuleQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.commons.ImpRuleQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获得导入结果
获得导入结果
        * /impRule/selImpResult
        */
      export namespace selImpResult {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.commons.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.commons.ImpRuleQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.commons.ImpRuleQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获得导入结果(首次反馈)
获得导入结果(首次反馈)
        * /impRule/selImpResultForFirst
        */
      export namespace selImpResultForFirst {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.commons.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.commons.ImpRuleQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.commons.ImpRuleQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 更新导入模板
更新导入模板
        * /impRule/uptImpRuleTemp
        */
      export namespace uptImpRuleTemp {
        export class Params {
          /** fileId */
          fileId: string;
          /** ruleId */
          ruleId: string;
        }

        export type Response<T> = defs.commons.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 类型子项维护接口
     */
    export namespace impType {
      /**
        * ids批量删除
ids批量删除
        * /impType/del
        */
      export namespace del {
        export class Params {
          /** ids */
          ids: Array;
        }
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.commons.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 主键批量删除
主键批量删除
        * /impType/delErr
        */
      export namespace delErr {
        export class Params {
          /** ids */
          ids: Array;
        }
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.commons.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 删除子类
删除子类
        * /impType/delItems
        */
      export namespace delItems {
        export class Params {}

        export type Response<T> = defs.commons.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.commons.ImpTypeVO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.commons.ImpTypeVO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取表字段及注解信息
获取表字段及注解信息
        * /impType/getTableInfo
        */
      export namespace getTableInfo {
        export class Params {
          /** tableName */
          tableName: string;
        }

        export type Response<T> = defs.commons.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 保存导入类别
保存导入类别
        * /impType/save
        */
      export namespace save {
        export class Params {}

        export type Response<T> = defs.commons.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.commons.ImpTypeVO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.commons.ImpTypeVO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 接口生效
接口生效
        * /impType/saveEffective
        */
      export namespace saveEffective {
        export class Params {}

        export type Response<T> = defs.commons.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.commons.ImpType,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.commons.ImpType,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 规则类别错误维护
规则类别错误维护
        * /impType/saveErr
        */
      export namespace saveErr {
        export class Params {}

        export type Response<T> = defs.commons.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.commons.ImpTypeVO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.commons.ImpTypeVO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 分页查询类别
分页查询类别
        * /impType/sel
        */
      export namespace sel {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.commons.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.commons.ImpTypeQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.commons.ImpTypeQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 主键查询
主键查询
        * /impType/selById
        */
      export namespace selById {
        export class Params {
          /** typeId */
          typeId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.commons.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 分页查询类别
分页查询类别
        * /impType/selErr
        */
      export namespace selErr {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.commons.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.commons.ImpTypeQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.commons.ImpTypeQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 导入类型接口
     */
    export namespace impTypeItem {
      /**
        * 批量id删除
批量id删除
        * /impTypeItem/del
        */
      export namespace del {
        export class Params {
          /** ids */
          ids: Array;
          /** typeId */
          typeId: string;
        }
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.commons.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 更新该类型下属子项未删除项目,且为生效项目状态为生效
更新该类型下属子项未删除项目,且为生效项目状态为生效
        * /impTypeItem/delItemColMapping
        */
      export namespace delItemColMapping {
        export class Params {
          /** itemIds */
          itemIds: string;
        }

        export type Response<T> = defs.commons.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 保存类型子项
保存类型子项
        * /impTypeItem/save
        */
      export namespace save {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.commons.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Array<defs.commons.ImpTypeItem>,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Array<defs.commons.ImpTypeItem>,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 更新该类型下属子项未删除项目,且为生效项目状态为生效
更新该类型下属子项未删除项目,且为生效项目状态为生效
        * /impTypeItem/saveItemColMapping
        */
      export namespace saveItemColMapping {
        export class Params {}

        export type Response<T> = defs.commons.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.commons.BaseEntity,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.commons.BaseEntity,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 分页查询类别
分页查询类别
        * /impTypeItem/sel
        */
      export namespace sel {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.commons.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.commons.ImpItemTypeQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.commons.ImpItemTypeQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取xls导入模板字段列映射的数据字典数据
获取xls导入模板字段列映射的数据字典数据
        * /impTypeItem/selXLSImpItemBDDataMpp
        */
      export namespace selXLSImpItemBDDataMpp {
        export class Params {
          /** itemId */
          itemId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.commons.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 更新该类型下属子项未删除项目,且为生效项目状态为生效
更新该类型下属子项未删除项目,且为生效项目状态为生效
        * /impTypeItem/uptStatusToEffective
        */
      export namespace uptStatusToEffective {
        export class Params {
          /** typeId */
          typeId: string;
        }

        export type Response<T> = defs.commons.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * Import File Controller
     */
    export namespace importFile {
      /**
        * 导入文件接口
导入文件接口
        * /importFile/importData
        */
      export namespace importDataProcess {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: string;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.commons.fileImportVO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.commons.fileImportVO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }
  }
}
