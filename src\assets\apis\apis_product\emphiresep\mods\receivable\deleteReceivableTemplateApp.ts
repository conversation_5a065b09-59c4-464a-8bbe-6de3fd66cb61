import * as defs from '../../baseClass';
import {
  fetch,
  RequestConfig,
  beforeFetch,
  afterFetch,
  checkResStatus,
  removeSpace,
} from '@/utils/request';

/**
     * @url /rhro-service-1.0/receivable/deleteReceivableTemplateApp
     * @desc 删除账单模板app
删除账单模板app
     * hasForm: true
     * hasBody: true
     */

export class Params {
  /** processInsId */
  processInsId?: string;
  /** receivableTemplateId */
  receivableTemplateId: string;
  /** workItemId */
  workItemId?: string;
}
export const init = new defs.emphiresep.CommonResponse();
export const url =
  '/rhro-service-1.0/receivable/deleteReceivableTemplateApp:POST';
export const initialUrl =
  '/rhro-service-1.0/receivable/deleteReceivableTemplateApp';
export const cacheKey = '_receivable_deleteReceivableTemplateApp_POST';
export async function request(data: Params, options?: RequestConfig) {
  const reqUrl = `/rhro-service-1.0/receivable/deleteReceivableTemplateApp`;
  const fetchOption = {
    reqUrl,
    requestType: 'form',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          reolve(res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
export async function requests(data: Params, options?: RequestConfig) {
  const reqUrl = `/rhro-service-1.0/receivable/deleteReceivableTemplateApp`;
  const fetchOption = {
    reqUrl,
    requestType: 'form',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          if (checkResStatus(res)) return reolve(res ? res.data : res);
          reject(res ? res.message : res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
