import { Button, Form, Modal } from 'antd';
import React, { useEffect, useState } from 'react';
import { StandardTableColumnProps } from '@/components/StandardTable';
import { msgErr, msgOk } from '@/utils/methods/message';
import { CachedPage } from '@/components/CachedPage';
import { EditeFormProps } from '@/components/EditeForm';
import { DateRange, MonthRange } from '@/components/DateRange4';
import { DeptmentSelector, ForPartyByDeptIdSelector, mapToSelectors } from '@/components/Selectors';
import { getCurrentMenu, getCurrentUser, getCurrentUserCityId, getUserId } from '@/utils/model';
import { AsyncButton } from '@/components/Forms/Confirm';
import { SelectValue } from 'antd/lib/select';
import { WritableInstance } from '@/components/Writable';
import { SsGroupPop } from '@/components/StandardPop/SsGroupPop';
import ImpReslut from './components/tableList';
import { AuthButtons } from '@/components/Forms/RenderButtons';
import {
  banchListMap,
  empTypeMap,
} from '@/utils/settings/welfaremanage/Socialmanage/socialBatchQuery';
import RpacBatchCodal from './components/RpacBatchStatusCodal';
import CompleteCompanyInfo from '@/components/CompleteCompanyInfo';
import ImportCodal from './components/ImportCodal';
import ImportCodalView from './components/ImportCodalView';
import { SsPackageSelector } from '@/components/Selectors/BaseDataSelectors';
import { SocialCsPop } from '@/components/StandardPop/InnerUserPop';
import { isEmpty, notEmpty } from '@/utils/methods/checker';

interface SocialBatchQueryProps {
  type: string;
}

const restartRpacService = API.welfaremanage.socialBatch.restartRpac;
const columns: StandardTableColumnProps<defs.workflow.RemindDTO>[] = [
  { title: '发放批次号', dataIndex: 'ssBatchId' },
  { title: '增减员', dataIndex: 'empTypeName' },
  { title: '社保公积金组', dataIndex: 'ssGroupName' },
  { title: '福利办理方', dataIndex: 'employerMaintainName' },
  { title: '批次生成时间', dataIndex: 'startDate' },
  { title: '批次处理结束时间', dataIndex: 'endDate' },
  { title: '办理人', dataIndex: 'sendUserName' },
  { title: '报送总笔数', dataIndex: 'sendCount' },
  { title: '报送成功笔数', dataIndex: 'sendSuccess' },
  { title: '报送失败笔数', dataIndex: 'sendFailure' },
  { title: '校验失败笔数', dataIndex: 'verifyFailure' },
  { title: '批次状态', dataIndex: 'statusName' },
  { title: '审核笔数', dataIndex: 'reviewCount' },
  // { title: '机器人状态', dataIndex: 'statusTypeName' },
];
// const jianColumns: StandardTableColumnProps<defs.workflow.RemindDTO>[] = [
//   { title: '发放批次号', dataIndex: 'ssBatchId' },
//   { title: '增减员', dataIndex: 'empTypeName' },
//   { title: '社保公积金组', dataIndex: 'ssGroupName' },
//   { title: '福利办理方', dataIndex: 'employerMaintainName' },
//   // { title: '订单截至时间', dataIndex: 'orderEndDate' },
//   // { title: '福利截至月', dataIndex: 'welfareEndMon' },
//   // { title: '离职原因', dataIndex: 'sepReason' },
//   { title: '批次生成时间', dataIndex: 'startDate' },
//   { title: '批次处理结束时间', dataIndex: 'endDate' },
//   { title: '报送总笔数', dataIndex: 'sendCount' },
//   { title: '报送成功笔数', dataIndex: 'sendSuccess' },
//   { title: '报送失败笔数', dataIndex: 'sendFailure' },
//   { title: '校验失败笔数', dataIndex: 'verifyFailure' },
//   { title: '批次状态', dataIndex: 'statusName' },
//   { title: '机器人状态', dataIndex: 'statusTypeName' },
// ];

const queryBatch = API.welfaremanage.socialBatch.list;
const SocialBatchQuery: React.FC<SocialBatchQueryProps> = (props) => {
  const { type = 1 } = props;
  const service = { ...queryBatch, cacheKey: queryBatch.cacheKey + type };
  const userId = getUserId();
  const [form] = Form.useForm();
  const userCityId = getCurrentUserCityId();
  const [detailData, setDetailData] = useState<any>({});
  const showDetail = useState<boolean>(false);
  const [deptId, setDeptId] = useState<string | undefined>();
  const [empType, setEmpType] = useState('1');
  const [rpacBatchStatus, setRpacBatchStatus] = useState<POJO>({});
  const [rpacBatchShow, setRpacBatchShow] = useState<boolean>(false);
  const [unmaintainedData, setUnmaintainedData] = useState<any>({});
  const [isSending, setIsSending] = useState(false);
  const [companyInfoOpen, setCompanyInfoOpen] = useState(false);
  const showImport = useState<boolean>(false);
  const showImportView = useState<boolean>(false);
  const [curRow, setCurRow] = useState<any>({});

  const currentUser = getCurrentUser();
  const currentMenu = getCurrentMenu();
  const [cityId, setCityId] = useState<string | number | undefined>(userCityId);

  let options: WritableInstance;

  const formColumns: EditeFormProps[] = [
    {
      label: '发放批次号',
      fieldName: 'ssBatchId',
      inputRender: 'string',
    },
    // {
    //   label: '客户',
    //   fieldName: 'custId',
    //   inputRender: () => <CustomerPop rowValue="custId-custName" />,
    // },
    {
      label: '批次生成时间',
      fieldName: '',
      inputRender: () => (
        <DateRange
          fields={[
            {
              title: '批次生成时间>=',
              dataIndex: 'startDate',
            },
            {
              title: '批次生成时间<=',
              dataIndex: 'endDate',
            },
          ]}
        />
      ),
    },
    {
      label: '合并组',
      fieldName: 'ssPackageId',
      inputRender: () => (
        <SsPackageSelector
          params={cityId ? { cityId, typeId: 1 } : { typeId: 1 }}
          // onChange={(value) => setSsPackageId(value)}
          onConfirm={(row) => {
            // setSsPackageId(row.key);
            setCityId(row.cityId || userCityId);
            const ssGroupId = form.getFieldValue('ssGroupId');
            if (!ssGroupId) return;
            form.setFieldsValue({
              ssGroupId: undefined,
              ssGroupName: undefined,
            });
          }}
          // skipEmptyParam
        />
      ),
    },
    {
      label: `社保组`,
      fieldName: 'ssGroupId',
      inputRender: () => {
        return (
          <SsGroupPop
            rowValue="ssGroupId-ssGroupName"
            keyMap={{
              ssGroupId: 'SSGROUPID',
              ssGroupName: 'INSURANCENAME',
              ISPRCNEEDAPPLY: 'ISPRCNEEDAPPLY',
              cityId: 'CITYID',
            }}
            disabledFields={[]}
            fixedValues={{ ssGroupType: 1, cityId }}
            handdleConfirm={(ssGroup: POJO | undefined) => {
              form.setFieldsValue({
                ssPackageId: undefined,
              });
              if (!ssGroup?.ssGroupId) {
                setCityId(userCityId);
              } else {
                setCityId(ssGroup.cityId);
              }
            }}
          />
        );
      },
      formOptions: {
        shouldUpdate: true,
      },
    },
    {
      label: '办理供应商',
      fieldName: 'processProvider',
      inputRender: () => <DeptmentSelector onChange={onProcessProviderChange} />,
    },
    {
      label: '福利办理方',
      fieldName: 'employerMaintainId',
      inputRender: () => {
        const processProvider = form.getFieldValue('processProvider');
        return (
          <ForPartyByDeptIdSelector
            params={{ deptId: processProvider }}
            disabled={!processProvider}
            skipEmptyParam
          />
        );
      },
    },
    {
      label: '批次状态',
      fieldName: 'status',
      inputRender: () => mapToSelectors(banchListMap),
    },
    {
      label: '增减员',
      fieldName: 'empType',
      inputRender: () => mapToSelectors(empTypeMap),
      rules: [{ required: true, message: '请选择增减员' }],
    },
    {
      label: '办理人',
      fieldName: 'sendUserId',
      inputRender: () => (
        <SocialCsPop
          rowValue="sendUserId-sendUserName"
          keyMap={{
            sendUserId: 'EMPID',
            sendUserName: 'REALNAME',
          }}
        />
      ),
    },
  ];

  // 办理供应商变化
  const onProcessProviderChange = (value: SelectValue) => {
    setDeptId(value);
    form.setFieldsValue({ welfareProcessor: '' });
  };
  const handleDetail = () => {
    const value = options.selectedSingleRow;
    setDetailData(value);
    showDetail[1](true);
  };

  const onUnlockBatch = async () => {
    const value = options.selectedSingleRow;
    await API.welfaremanage.socialBatch.unlock.requests({
      ssGroupType: '1',
      ...value,
      employerMaintainId: value.welfareProcessor,
    });
    msgOk('操作成功');
  };

  const onBatchNumber = async () => {
    const value = options.selectedSingleRow;
    const res = await API.welfaremanage.socialBatch.rpacBatchStatus.requests({
      ssBatchId: value.ssBatchId,
    });
    if (res.code !== 200) {
      return msgErr(res.msg);
    }
    setRpacBatchStatus(res.data);
    setRpacBatchShow(!rpacBatchShow);
  };
  // 重新报送
  const resend = async (curRow: any) => {
    if (curRow.empType === 4 || curRow.empType === 15) {
      return msgErr('增减员为报税时，无需进行此操作');
    }
    setIsSending(true);
    let resData;
    if (
      curRow.empType === 1 ||
      curRow.empType === 2 ||
      curRow.empType === 17 ||
      curRow.empType === 18
    ) {
      resData = await restartRpacService
        .requests({ ssBatchId: curRow.ssBatchId, enforce: false })
        .finally(() => setIsSending(false));
    } else {
      resData = await API.welfaremanage.rpaPegging.restartRpac
        .requests({ ssBatchId: curRow.ssBatchId, enforce: false })
        .finally(() => setIsSending(false));
    }
    if (resData.code == 200) {
      // 校验通过后发送
      setIsSending(true);
      if (
        curRow.empType === 1 ||
        curRow.empType === 2 ||
        curRow.empType === 17 ||
        curRow.empType === 18
      ) {
        resData = await restartRpacService
          .requests({ ssBatchId: curRow.ssBatchId, enforce: true })
          .finally(() => setIsSending(false));
      } else {
        resData = await API.welfaremanage.rpaPegging.restartRpac
          .requests({ ssBatchId: curRow.ssBatchId, enforce: true })
          .finally(() => setIsSending(false));
      }
      // await restartRpacService
      //   .requests({ ssBatchId: curRow.ssBatchId, enforce: true })
      //   .finally(() => setIsSending(false));
      msgOk('发送成功');
    } else if (Number(resData.code) === 500 || Number(resData.code) === 800) {
      setUnmaintainedData({
        ...resData.data,
        ssBatchId: curRow.ssBatchId,
        empType: curRow.empType,
      });
      setCompanyInfoOpen(true);
    }
  };

  const onRestartBatch = async (curRow) => {
    if (!curRow.reviewCount) return msgErr('该批次没有社保网审核中数据，请重新选择');
    const resData = await API.welfaremanage.socialBatch.restartBatch.requests({
      ssBatchId: curRow.ssBatchId,
    });
    if (Number(resData.code) === 200) {
      msgOk(resData.msg || '操作成功');
    } else if (Number(resData.code) === 500 || Number(resData.code) === 800) {
      setUnmaintainedData({
        ...resData.data,
        ssBatchId: curRow.ssBatchId,
        empType: curRow.empType,
      });
      setCompanyInfoOpen(true);
    }
  };

  const onImportFile = () => {
    const row = options.selectedSingleRow;
    if (row.status !== 10) return msgErr('批次状态为待敲章时,可进行此操作');
    setDetailData(row);
    showImport[1](true);
  };

  const onImportView = () => {
    const row = options.selectedSingleRow;
    setDetailData(row);
    showImportView[1](true);
  };

  const onIsRecord = async () => {
    const rows = options.selectedRows;
    let disabled = false;
    const batchIds = [];
    rows.forEach((row: POJO) => {
      batchIds.push(row.ssBatchId);
      if ((row.empType != 17 && row.empType != 18) || row.status != 5) {
        disabled = true;
      }
    });
    if (disabled) {
      return msgErr('数据状态必须为增/减员备案，批次状态为完成的批次数据才可进行此操作');
    }
    const res = await API.welfaremanage.socialBatch.startByBatchIds.requests({ batchIds });
    msgOk(res?.msg || '操作成功');
  };
  // 操作按钮
  const renderButtons = (_options: WritableInstance) => {
    options = _options;
    const isBtn = options.selectedSingleRow;
    return (
      <React.Fragment>
        <AsyncButton type="primary" onClick={query_clickHandler}>
          查询
        </AsyncButton>
        <AsyncButton disabled={!isBtn} onClick={handleDetail}>
          详情
        </AsyncButton>
        <AuthButtons hidden={userId !== '1'}>
          <Button disabled={!isBtn} onClick={exportData}>
            手动获取明细反馈
          </Button>
          <AsyncButton disabled={!isBtn} onClick={onUnlockBatch}>
            解锁批次
          </AsyncButton>
        </AuthButtons>
        <AsyncButton disabled={!isBtn} onClick={onBatchNumber}>
          查看进度
        </AsyncButton>
        <AsyncButton
          disabled={!(isBtn?.status === 8 && isBtn?.userId === userId)}
          onClick={() => {
            setCurRow(options.selectedSingleRow);
            resend(isBtn);
          }}
        >
          再次报送
        </AsyncButton>
        <AsyncButton disabled={!isBtn} onClick={() => onRestartBatch(isBtn)}>
          审核确认
        </AsyncButton>
        <AsyncButton onClick={onImportFile} disabled={!isBtn}>
          上传附件
        </AsyncButton>
        <AsyncButton onClick={() => onImportView()} disabled={!isBtn}>
          查看附件
        </AsyncButton>
        <AsyncButton onClick={() => onIsRecord()} disabled={isEmpty(options.selectedRows)}>
          备案后增/减员
        </AsyncButton>
      </React.Fragment>
    );
  };

  const getboUrl = (reportQuery: any, params: any, privateObj: any) => {
    let url: string = reportQuery.baseRptURL + reportQuery.modulePath;
    for (let index = 0; index < params.length; index++) {
      const param = params[index];
      if (reportQuery[param.propName] || reportQuery[param.aliasPropName]) {
        url += param.needAnd ? '&' : '';
        url += param.aliasPropName ? param.aliasPropName : param.propName;
        url += '=';
        //2012-08-06 解决中文乱码问题
        let val = '';
        if (reportQuery[param.propName] || reportQuery[param.aliasPropName]) {
          val = reportQuery[param.propName] || reportQuery[param.aliasPropName];
          if (param.needEncode) {
            val = encodeURI(val);
          }
        }
        url += val;
      }
    }
    return url;
  };

  const exportData = () => {
    const fieldsValue = form.validateFields();
    const values = options.selectedSingleRow;
    const queriesValue = options.queries;
    if (values.statusName === '完成') {
      return msgErr('已完成的不可手动获取反馈');
    }
    const tmpQueryMapping: POJO = {
      userId: currentUser.userId,
      baseRptURL: currentUser.reportDir,
      modulePath:
        values.empType == 1
          ? '/np/ssprocess/ssProcessNew.jsp?ssBatchid='
          : '/np/ssprocess/RPASocialDecrease.jsp?ssBatchId=',
    };

    const boUrl: string = getboUrl(tmpQueryMapping, [], currentUser);
    // if (values.statusTypeName === '成功') {
    //   return msgErr('机器人状态成功的不可获取反馈');
    // }
    API.welfaremanage.socialBatch.restart
      .requests({
        boUrl: boUrl,
        ssBatchId: values.ssBatchId,
        ssGroupType: '1',
        ...queriesValue,
      })
      .then((res) => {
        if (res.code === '200') {
          msgOk(res.msg || '触发反馈成功');
          options.request(fieldsValue);
        } else {
          return msgErr(res.msg || '触发失败');
        }
      });

    // if (isEmpty(values)) {
    //   return msgErr('请选择要导出的数据');
    // }
    // // form.validateFields().then((values) => {
    // values.userId = userId;
    // values.baseRptURL = privateObject.reportDir; //报表系统入口地址
    // values.modulePath = currentMenu.reportUrl || '/np/ssprocess/ssProcessDetail.jsp'; //模块路径
    // // values.exportDetailType = '';
    // const servURL: string = generateReportURL(values, queryParam, privateObject);
    // window.open(servURL, '_blank');
    // // });
  };
  // 查询
  const query_clickHandler = async () => {
    const fieldsValue = await form.validateFields();
    setEmpType(fieldsValue.empType);
    options.request(fieldsValue);
  };

  return (
    <>
      <CachedPage
        service={service as any}
        columns={columns}
        formColumns={formColumns}
        renderButtons={renderButtons}
        initialValues={{ queryMode: '1', strStatusArr: ['3', '4', '5'] }}
        form={form}
      >
        <ImpReslut
          title="详情查询"
          visible={showDetail[0]}
          hideHandle={() => showDetail[1](false)}
          detailData={detailData}
        />
        <RpacBatchCodal
          title="进度查看"
          visible={rpacBatchShow}
          hideHandle={() => {
            setRpacBatchShow(false);
          }}
          data={rpacBatchStatus}
        />
        {/* 用于重新渲染页面 */}
        <span style={{ display: 'none' }}>
          `${empType}_${deptId}`
        </span>
      </CachedPage>
      {/* 没有key的公司，需要扫码的公司 & 未维护的公司 */}
      <CompleteCompanyInfo
        modal={[companyInfoOpen, setCompanyInfoOpen]}
        modalType="1" // 全部强制发送
        data={unmaintainedData}
        loadingInfo={[isSending, setIsSending]}
        cityId={cityId}
        organizationType={curRow?.orgCode}
      />
      <ImportCodal
        title="上传附件界面"
        visible={showImport[0]}
        hideHandle={() => showImport[1](false)}
        detailData={detailData}
      />
      <ImportCodalView
        title="导入记录"
        visible={showImportView[0]}
        hideHandle={() => showImportView[1](false)}
        detailData={detailData}
      />
    </>
  );
};

export default SocialBatchQuery;
