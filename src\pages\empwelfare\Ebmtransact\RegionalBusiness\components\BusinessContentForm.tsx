import React, { useEffect, useState } from 'react';
import { Form, Button, message, FormInstance } from 'antd';
import Codal from '@/components/Codal';
import { FormElement3 } from '@/components/Forms/FormLayouts';
import { EnumerateFields } from '@/components/CachedPage/EnumerateFields';
import { EditeFormProps } from '@/components/EditeForm';
import { mapToSelectors } from '@/components/Selectors/FuncSelectors';
import {
  CitySelector,
  GetBaseBusnameClassDropdownList,
  GetBusinessContentByBusTypeSelector,
} from '@/components/Selectors/BaseDataSelectors';
import {
  busTypeMap,
  orderRequireMap,
  productRequireMap,
  transactObjectMap,
  transactPropertyMap,
  transactTypeMap,
} from '@/utils/settings/empwelfare/businessProcessing';
import { yesNoDataMap } from '@/utils/settings/basedata/comboManage';
import { mapToCheckbox } from '@/components/Selectors/FuncCheckbox';
import { doRequireMap } from '@/utils/settings/basedata/businessType';
import { msgErr, resError } from '@/utils/methods/message';

interface BusinessContentFormProps {
  modal: [boolean, CallableFunction];
  listOptions: any;
  initialInfo?: any;
  defaultCityId: string;
}

const BusinessContentForm: React.FC<BusinessContentFormProps> = ({
  modal,
  defaultCityId,
  listOptions,
  initialInfo,
}) => {
  const [visible, setVisible] = modal;
  if (!visible) return null;

  const [form] = Form.useForm();
  const [busConfigId, setBusConfigId] = useState<string>('');
  const [isEdit, setIsEdit] = useState(false);
  const [categoryId, setCategoryId] = useState<string | undefined>(undefined);
  const [busnameClassId, setBusnameClassId] = useState<string | undefined>(undefined);
  const [transactObject, setTransactObject] = useState<string | undefined>('');
  const [transactType, setTransactType] = useState<string | undefined>('');

  const handleSubmit = async () => {
    const values = await form.validateFields();
    const service = isEdit
      ? API.welfaremanage.ebmBusinessCityConfig.updateBusinessCityConfig
      : API.welfaremanage.ebmBusinessCityConfig.insertBusinessCityConfig;
    const res = await service.request({
      ...values,
      busConfigId: busConfigId,
      orderRequire: values?.orderRequire?.join(','),
      doRequire: values?.doRequire?.join(','),
      productRequire: values?.productRequire?.join(','),
      transactType: values?.transactType?.join(','),
    });
    if (resError(res)) {
      msgErr(res?.message);
      return;
    }

    message.success(isEdit ? '修改成功' : '新增成功');

    setVisible(false);
    form.resetFields();
    // 刷新列表
    listOptions.request(listOptions.queries);
  };

  const renderButtons = () => [
    <Button key="cancel" onClick={() => setVisible(false)}>
      取消
    </Button>,
    <Button key="submit" type="primary" onClick={handleSubmit}>
      确认
    </Button>,
  ];

  const clearCache = () => {
    setIsEdit(false);
    form.resetFields();
    setBusConfigId('');
    setCategoryId(undefined);
    setBusnameClassId(undefined);
    setTransactObject(undefined);
    setTransactType(undefined);
  };

  useEffect(() => {
    if (!visible) {
      clearCache();
      return;
    }

    if (initialInfo && Object.keys(initialInfo).length > 0) {
      setIsEdit(true);
      form.setFieldsValue(initialInfo);
      setBusConfigId(initialInfo?.busConfigId);
      setCategoryId(initialInfo?.categoryId);
      setBusnameClassId(initialInfo?.busnameClassId);
      setTransactObject(initialInfo?.transactObject);
      setTransactType(initialInfo?.transactType);
    }
  }, [visible, initialInfo, form]);

  const initialProductRequire = () => {
    const { productRequire } = form?.getFieldsValue();

    if (!categoryId || !productRequire) return;

    let productRequireStr = '';
    //社保业务
    if (categoryId === '1' && !!busnameClassId) {
      //养老类业务
      if (busnameClassId === '39') productRequireStr = '200';
      //生育类业务
      if (busnameClassId === '28') productRequireStr = '204';
      //医疗类业务
      if (busnameClassId === '22') productRequireStr = '201';
      //工伤类业务
      if (busnameClassId === '24') productRequireStr = '203';
      //失业类业务
      if (busnameClassId === '29') productRequireStr = '202';

      if (productRequireStr === '') productRequireStr = '200,201,202,203,204';
    }

    //公积金业务
    if (categoryId === '2' && productRequireStr === '') productRequireStr = '240';

    //人力资源收费业务
    if (categoryId === '3' && productRequireStr === '') productRequireStr = '200,201,202,203,204';

    form?.setFieldsValue({
      productRequire: productRequireStr?.split(','),
    });
  };

  useEffect(() => {
    if (transactObject === '2') initialProductRequire();
  }, [transactObject, categoryId, busnameClassId]);

  const formColumns: EditeFormProps[] = [
    {
      label: '城市',
      fieldName: 'cityId',
      inputProps: {
        initialValue: defaultCityId,
        disabled: isEdit,
      },
      inputRender: () => (
        <CitySelector allowClear keyMap={{ cityId: 'key', cityName: 'shortName' }} />
      ),
    },
    {
      label: '业务类型',
      fieldName: 'categoryId',
      inputRender: (outerForm: FormInstance) => {
        return mapToSelectors(busTypeMap, {
          allowClear: true,
          placeholder: '请选择业务类型',
          disabled: isEdit,
          onChange: (value) => {
            setCategoryId(value);
            outerForm.setFieldsValue({ busnameClassId: undefined, busContent: undefined });
          },
        });
      },
      rules: [{ required: true, message: '请选择业务类型' }],
    },
    {
      label: '业务项目',
      fieldName: 'busnameClassId',
      inputRender: (outerForm: FormInstance) => (
        <GetBaseBusnameClassDropdownList
          allowClear
          params={{ categoryId: outerForm.getFieldValue('categoryId') ?? '' }}
          disabled={isEdit}
          onChange={(value) => setBusnameClassId(value?.toString())}
        />
      ),
      shouldUpdate: (prevValues, curValues) => {
        return prevValues.categoryId !== curValues.categoryId;
      },
      rules: [{ required: true, message: '请选择业务项目' }],
    },
    {
      label: '业务内容',
      fieldName: 'busContent',
      inputRender: (outerForm: FormInstance) => (
        <GetBusinessContentByBusTypeSelector
          params={{
            busnameClassId: outerForm.getFieldValue('busnameClassId') ?? '',
            categoryId: outerForm.getFieldValue('categoryId') ?? '',
          }}
          onChange={(_, option: any) => {
            setBusConfigId(option?.optiondata?.busConfigId);
            setTransactObject(option?.optiondata?.transactObject);

            outerForm.setFieldsValue({
              transactObject: option?.optiondata?.transactObject,
              transactProperty: option?.optiondata?.transactProperty,
            });
          }}
          placeholder="请选择业务内容"
          allowClear
          disabled={isEdit}
        />
      ),
      shouldUpdate: (prevValues, curValues) => {
        return (
          prevValues.categoryId !== curValues.categoryId ||
          prevValues.busnameClassId !== curValues.busnameClassId
        );
      },
      rules: [{ required: true, message: '请输入业务内容' }],
    },
    {
      label: '办理属性',
      fieldName: 'transactProperty',
      inputRender: () => mapToSelectors(transactPropertyMap, { disabled: true }),
    },
    {
      label: '办理对象',
      fieldName: 'transactObject',
      inputRender: () => mapToSelectors(transactObjectMap, { disabled: true }),
    },
    {
      label: '办理方式',
      fieldName: 'transactType',
      inputProps: { disabled: isEdit },
      inputRender: () =>
        mapToCheckbox(transactTypeMap, {
          allowClear: true,
          onChange: (value: string) => setTransactType(value),
        }),
      rules: [{ required: true, message: '请选择办理方式' }],
    },
    {
      label: '是否微信显示',
      fieldName: 'wxShowState',
      inputRender: () => mapToSelectors(yesNoDataMap, { disabled: true }),
    },
    {
      label: '是否客户端显示',
      fieldName: 'clientShowState',
      inputRender: () => mapToSelectors(yesNoDataMap, { disabled: true }),
    },
    {
      label: '业务内容说明',
      fieldName: 'busContentDesc',
      inputRender: 'text',
    },
    {
      label: '入离职状态',
      fieldName: 'orderRequire',
      inputRender: (outerForm) =>
        mapToCheckbox(orderRequireMap, {
          disabled: outerForm?.getFieldValue('transactObject') !== '2',
        }),
      rules:
        transactObject === '2'
          ? [
              {
                required: true,
                message: '请选择入离职状态',
              },
            ]
          : [],
    },
    {
      label: '缴纳产品要求',
      fieldName: 'productRequire',
      inputRender: () => mapToCheckbox(productRequireMap, { disabled: transactObject !== '2' }),
    },
    {
      label: '实做状态',
      fieldName: 'doRequire',
      inputRender: (outerForm) =>
        mapToCheckbox(doRequireMap, {
          disabled: outerForm?.getFieldValue('transactObject') !== '2',
        }),
      rules:
        transactObject === '2'
          ? [
              {
                required: true,
                message: '请选择实做状态',
              },
            ]
          : [],
    },
    {
      label: '员工自助办理途径',
      fieldName: 'empSelfContent',
      inputRender: 'text',
      inputProps: { disabled: transactType !== '2' },
      rules: transactType === '2' ? [{ required: true, message: '请填写员工自助办理途径' }] : [],
    },
  ];

  return (
    <Codal
      title={isEdit ? '修改业务内容' : '新增业务内容'}
      visible={visible}
      checkEdit={false}
      footer={renderButtons()}
      onCancel={() => {
        setVisible(false);
        form.setFieldsValue({});
        form.resetFields();
      }}
      width="60%"
    >
      <FormElement3 form={form}>
        <EnumerateFields formColumns={formColumns} outerForm={form} colNumber={2} />
      </FormElement3>
    </Codal>
  );
};

export default BusinessContentForm;
