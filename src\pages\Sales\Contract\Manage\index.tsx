/*
 * @Author: 侯成
 * @Email: <EMAIL>
 * @Date: 2020-09-10 18:37:47
 * @LastAuthor: 侯成
 * @LastTime: 2020-12-21 12:43:48
 * @message: 销售账号
 * jiaona.hao, 20120329028		山西国润龙城医药有限公司
 * zengbin.hu
 * 20060307014 中移鼎讯通信股份有限公司 闫海涛 echo.yan 北京易才人力资源顾问有限公司 新派遣(2018-03-01 15:32:20,未签约)
 * 20140317013 北京云峰广告有限公司 闫海涛 echo.yan 北京易才人力资源顾问有限公司 新派遣(2018-03-01 15:32:20,未签约)
 * 杜岩
 * 20060307014	中移鼎讯通信股份有限公司	Q-20071018-0214	中移鼎讯-代理北京
 * 20050525029	丰田汽车（中国）投资有限公司	Q-20071116-0008	丰田北京派遣补充医疗
 * 20071123011	上海湾流仪器技术有限公司	Q-20071128-0015	湾流仪器技术公司报价单100
 * 老hro 用户 yan.du 客户 20070508006
 * 合同编号：20110421-P-0021-066
 * 创建人：刘蕾
 * 	20040607008	北京搜狐信息技术有限公司xu
 * 	销售： huanying1.zhou, wilson.xu,
 * linlin.ji,angel.wang, xia.li
 * 905750992
 * 北京领行智享----单立户(社保含代发)
 *
 *
 *
 * 新作 xiujie.wang
 * 审批 cherry.chen1
 * 开发分支测试
 */
import React, { useEffect, useState } from 'react';
import { Button, Form, Popconfirm, Typography, FormInstance } from 'antd';
import { WritableColumnProps } from '@/utils/writable/types';
import { CachedPage } from '@/components/CachedPage';
import { EditeFormProps } from '@/components/EditeForm';
import { WritableInstance } from '@/components/Writable';
import { CustomerPop } from '@/components/StandardPop/CustomerPop';
import { InnerUserPop } from '@/components/StandardPop/InnerUserPop';
import { mapToSelectors } from '@/components/Selectors';
import { contractStatusMap, contractSvcStateMap } from '@/utils/settings/sales/contract';
import { DateRange } from '@/components/DateRange4';
import { column4 } from '@/utils/forms/typography';
import { stdDateFormat } from '@/utils/methods/times';
import { BooleanSelector } from '@/components/Selectors/BaseDropDown';
import {
  BranchTitleSelector,
  ContractTypeSelector,
  ContractSubTypeSelector,
  BranchTitleSpeSelector,
  ContractgetSubTypeSelector,
} from '@/components/Selectors/BaseDataSelectors';
import { ContractForm } from './ContractForm';
import { msgErr, msgOk, resError } from '@/utils/methods/message';

import { getCurrentMenu, getCurrentUser, getDepartInfo } from '@/utils/model';
import { isEmpty } from 'lodash';
import { stdBoolFalseSrting, stdBoolTrueSrting } from '@/utils/settings';
import ConfirmButton, { AsyncButton, ConfirmLoading } from '@/components/Forms/Confirm';
import { exportFilebyColumns, exportImportFile } from '@/utils/methods/file';
import { DownloadOutlined } from '@ant-design/icons';
import {
  BranchSelector,
  RegionSelector,
  CurrentSalesSelector,
  ContractCategorySelector,
} from './Seletors';
import { AuthButtons } from '@/components/Forms/RenderButtons';
import { QueryApproveProcessWin } from './QueryApproveProcessWin';
import { PopconfirmButton } from '@/components/Forms/PopconfirmButton';
import { ContractView } from '@/pages/emphiresep/sendorder/CustomerSubcontract/ContractView';
import { UpdateContractCurrentSaleWin } from './UpdateContractCurrentSaleWin';
import { mapToRender } from '@/utils/methods/tables';
import { Switchs } from '@/components/Selectors/Switch';
import { removeEmpty } from '@/utils/methods/pagenation';
import Codal from '@/components/Codal';
import { FormElement2, RowElement, ColElementButton } from '@/components/Forms/FormLayouts';
import { EnumerateFields } from '@/components/CachedPage/EnumerateFields';
import { useSelector } from 'umi';
import { shallowEqual } from 'react-redux';
import { ConnectState } from '@/models/connect';
import UpdateSignFlagManualWin from '../Query/UpdateSignFlagManualWin';
import UpdateContractVersion from '../Query/UpdateContractVersion';

const contractCodes = [
  '20180509-B-0010-04510',
  '20190923-D-0021-10845',
  '20170425-H-0021-02632',
  '20170418-H-0571-02468',
  '20170316-H-0021-01559',
  '20170303-H-0021-01237',
  '20170301-H-0021-01186',
];

interface ContractManagementProps {
  _example: string;
}

const service = API.crm.contractManage.selectList;
// const service2 = API.sale.contract.selectList;
const apis = [
  'contractManageService,initData',
  'baseServiceCLS,getDorpDownList',
  'contractManageService,sel',
  'contractManageService,commitContractApprove',
  'contractManageService,uptContractFlagManual',
  'contractManageService,getEffectModelContract',
  'contractManageService,delContract',
  'serviceTypeService,getSubEx',
];
const codalNames = [
  'UpdateContractWin',
  'UpdateContractWin',
  'UpdateContractWin',
  'UpdateContractCurrentSaleWin',
  'UpdateContractWin',
];

type TContractDTO = defs.sale.ContractDTO;

const ContractManagement: React.FC<ContractManagementProps> = () => {
  const [basedata, setBasedata] = useState<POJO<POJO[]>>({});
  const [contractType, setContractType] = useState<string>();
  const [contractTypeAdd, setContractTypeAdd] = useState<string>('');
  const [contractAddVisible, setContractAddVisible] = useState<boolean>(false);
  // const [contractTypeNameMap, setcontractTypeNameMap] = useState<POVO>({});
  const [currentContract, setCurrentContarct] = useState<defs.sale.ContractDTO>({});
  const [additional, setAdditional] = useState<Partial<defs.sale.ContractDTO>>({});
  const [contractCategory, setContractCategory] = useState('');
  const [currentCustId, setcurrentCustId] = useState<string | undefined>('');
  const updateSignFlagManualWinModal = useState(false);
  const updateContractVersionModal = useState(false);
  // const renewable = useState(false);
  const contractFormModal = useState(false);
  const QueryContractDetailInfoWinModal = useState(false);
  const QueryApproveProcessWinModal = useState(false);
  const UpdateContractCurrentSaleWinModal = useState(false);
  const [isCustCode, setIsCustCode] = useState<boolean>(false);
  const [isUpdateContractCategery, setIsUpdateContractCategery] = useState<boolean>(false);
  const popupVisible = useState(false);
  const processInstanceId = useState<string>();

  const contractFormScenes = useState<'add' | 'update' | 'examine' | 'renew' | undefined>();
  const currentMenu = getCurrentMenu();

  const currentUser = useSelector((state: ConnectState) => state.user.currentUser, shallowEqual);
  const userBranchId = currentUser.profile?.governingBranch;
  // console.log('currentMenu in ContractManagement:', currentMenu)
  // console.log('currentUser in ContractManagement:', currentUser)
  let options: WritableInstance;
  const [form] = Form.useForm();
  const [formAdd] = Form.useForm();
  const privateObject = getCurrentUser();
  const [departmentId, setDepartmentId] = useState<string | undefined>('');
  const [salesData, setSalesData] = useState<any>({});
  const [custInfo, setCustInfo] = useState<any>({});

  const onContractTypeChange = (svcTypeId: string) => {
    form.setFieldsValue({ contractSubType: null });
    if (!svcTypeId) return;
    setContractType(svcTypeId);
    // TODO: 刷新Form, 以改变控件中显示的值。
  };
  const viewAproveProcess = (proId?: string) => {
    processInstanceId[1](proId);
    QueryApproveProcessWinModal[1](true);
  };

  const columns: WritableColumnProps<any>[] = [
    {
      title: '客户编号',
      dataIndex: 'custCode',
      fixed: 'left',
    },
    { title: '客户名称', dataIndex: 'custName', fixed: 'left' },
    { title: '合同编号', dataIndex: 'contractCode', fixed: 'left' },
    { title: '合同名称', dataIndex: 'contractName', fixed: 'left' },
    { title: '合同大类名称', dataIndex: 'contractTypeName' },
    { title: '合同小类名称', dataIndex: 'contractSubTypeName' },
    { title: '开始日期', dataIndex: 'contractStartDate' },
    { title: '结束日期', dataIndex: 'contractStopDate' },
    { title: '责任客服', dataIndex: 'liabilityCsName' },
    { title: '服务区域类型', dataIndex: 'areaTypeName' },

    { title: '签约方公司抬头', dataIndex: 'signBranchTitle' },
    { title: '签单分公司', dataIndex: 'signProvider' },
    {
      title: '是否集中一地投保',
      dataIndex: 'isSameInsurName',
    },
    {
      title: '是否增强型代理',
      dataIndex: 'enhancedAgent',
      render: (text: string) => <Switchs code={text} />,
    },
    { title: '首版账单财务应收年月', dataIndex: 'firstOughtMonth' },
    { title: '首版账单客户账单年月', dataIndex: 'firstAccountMonth' },

    { title: '客户对应销售及分公司', dataIndex: 'saleAndBranchName' },
    { title: '最终结束日期', dataIndex: 'contractEndDate' },
    { title: '原销售', dataIndex: 'formerSalesName' },
    {
      title: '原销售所属大区',
      dataIndex: 'formerGoverningAreaName',
    },
    { title: '原销售所属分公司', dataIndex: 'formerGoverningBranchName' },
    { title: '现销售', dataIndex: 'salesName' },
    { title: '现销售所属大区', dataIndex: 'governingAreaName' },
    { title: '现销售所属分公司', dataIndex: 'governingBranchName' },
    { title: '创建时间', dataIndex: 'createDt' },
    { title: '创建者', dataIndex: 'createByName' },
    {
      title: '合同审批状态',
      dataIndex: 'contractStatus',
      render: mapToRender(contractStatusMap),
    },
    {
      title: '合同状态',
      dataIndex: 'contractSvcState',
      render: mapToRender(contractSvcStateMap),
    },
    { title: '新签标识（手工）', dataIndex: 'signFlagManualName' },
    { title: '审批通过日期', dataIndex: 'approveDt' },
    { title: '预估首次账单日期', dataIndex: 'estimateFirstBillDate' },
    { title: '首次出账单时间', dataIndex: 'firstBillDate' },
    { title: '是否为已有客户所推荐', dataIndex: 'isCustRecommendName' },
    {
      title: '审批过程',
      render: (value: string, record: TContractDTO) => (
        <Typography.Link
          disabled={record.processInstanceId === undefined}
          onClick={() => viewAproveProcess(record.processInstanceId)}
        >
          查看
        </Typography.Link>
      ),
    },
    { title: '合同版本号', dataIndex: 'contractVersion' },
    { title: '合同最终结束日期类型', dataIndex: 'contractEndDateType' },
  ];

  const formColumns: EditeFormProps[] = [
    {
      label: '客户编号',
      fieldName: 'custCode',
      inputRender: () => (
        <CustomerPop
          rowValue="custCode-custViewCode"
          keyMap={{
            // 接口写的custCode，实际上用的custId的值
            custCode: 'custId',
            // 这个custViewCode纯作为展示用
            custViewCode: 'custCode',
          }}
          fixedValues={{
            userRoleType: '1',
          }}
        />
      ),
    },
    {
      label: '创建人',
      fieldName: 'createByParty',
      inputRender: () => (
        <InnerUserPop
          rowValue="createByName-createByNameName"
          keyMap={{
            createByName: 'EMPID',
            createByNameName: 'REALNAME',
          }}
        />
      ),
    },
    {
      label: '原销售',
      fieldName: 'formerSales',
      inputRender: () => (
        <InnerUserPop
          rowValue="formerSales-formerSalesName"
          keyMap={{
            formerSales: 'EMPID',
            formerSalesName: 'REALNAME',
            userName: 'USERNAME',
            deptId: 'DEPTID',
          }}
        />
      ),
    },
    {
      label: '现销售',
      fieldName: 'currentSales',
      inputRender: () => (
        <InnerUserPop
          rowValue="currentSales-currentSalesName"
          keyMap={{
            currentSales: 'EMPID',
            empId: 'EMPID',
            currentSalesName: 'REALNAME',
            realName: 'REALNAME',
          }}
        />
      ),
    },
    {
      label: '原销售所属大区',
      fieldName: 'formerGoverningArea',
      inputRender: () => <RegionSelector />,
    },
    {
      label: '原销售所属分公司',
      fieldName: 'formerGoverningBranch',
      // inputRender: () =>
      //   renderListToSelectors(basedata.branchModel, ['governingBranchId', 'governingBranch'], {
      //     allowClear: true,
      //   }),
      inputRender: () => <BranchSelector allowClear />,
    },
    {
      label: '现销售所属大区',
      fieldName: 'governingArea',
      inputRender: () => <RegionSelector />,
    },
    {
      label: '现销售所属分公司',
      fieldName: 'governingBranch',
      // inputRender: () =>
      //   renderListToSelectors(basedata.branchModel, ['governingBranchId', 'governingBranch'], {
      //     allowClear: true,
      //   }),
      inputRender: () => <BranchSelector allowClear />,
    },
    {
      label: '合同编号',
      fieldName: 'contractCode',
      inputRender: 'string',
    },
    { label: '合同名称', fieldName: 'contractName', inputRender: 'string' },
    {
      label: '合同审批状态',
      fieldName: 'contractStatus',
      inputRender: () => mapToSelectors(contractStatusMap, { allowClear: true }),
    },
    {
      label: '合同状态',
      fieldName: 'contractSvcState',
      inputRender: () => mapToSelectors(contractSvcStateMap),
    },
    {
      label: '开始时间从',
      fieldName: 'contractStartDateStart',
      inputRender: () => (
        <DateRange
          fields={[
            {
              title: '开始时间从',
              dataIndex: 'contractStartDateStart',
            },
            {
              title: '开始时间到',
              dataIndex: 'contractStartDateEnd',
            },
          ]}
          colConf={column4}
          format={stdDateFormat}
        />
      ),
    },
    {
      label: '审批通过日期到',
      fieldName: '',
      inputRender: () => (
        <DateRange
          fields={[
            {
              title: '审批通过日期从',
              dataIndex: 'approveDtStart',
            },
            {
              title: '审批通过日期到',
              dataIndex: 'approveDtEnd',
            },
          ]}
          colConf={column4}
          format={stdDateFormat}
        />
      ),
    },
    {
      label: '是否为已有客户所推荐',
      fieldName: 'isCustRecommend',
      inputRender: () => <BooleanSelector />,
    },
    {
      label: '签约方公司抬头',
      fieldName: 'signBranchTitleId',
      inputRender: () => <BranchTitleSelector params={{ departmentId: null }} />,
    },
    {
      label: '创建日期',
      fieldName: 'createDt',
      inputRender: () => (
        <DateRange
          fields={[
            {
              title: '创建日期从',
              dataIndex: 'createDtStart',
            },
            {
              title: '创建日期到',
              dataIndex: 'createDtEnd',
            },
          ]}
          format={stdDateFormat}
        />
      ),
    },
    {
      label: '合同大类名称',
      fieldName: 'contractType',
      inputRender: () => <ContractTypeSelector onChange={onContractTypeChange} />,
    },
    {
      label: '合同小类名称',
      fieldName: 'contractSubType',
      inputRender: () => (
        <ContractSubTypeSelector skipEmptyParam params={{ svcTypeId: contractType }} />
      ),
    },
    {
      label: '是否增强型代理',
      fieldName: 'enhancedAgent',
      inputRender: () => <Switchs />,
    },
  ];
  const formAddColumns: EditeFormProps[] = [
    {
      label: '合同大类名称',
      fieldName: 'contractType',
      rules: [{ required: true, message: '请输入合同大类名称' }],
      inputRender: () => <ContractTypeSelector onChange={onContractTypeAddChange} />,
    },
    {
      label: '合同小类名称',
      fieldName: 'contractSubType',
      rules: [{ required: true, message: '请输入合同小类名称' }],
      inputRender: () => (
        <ContractgetSubTypeSelector
          skipEmptyParam
          disabled={!contractTypeAdd}
          params={{ svcTypeId: contractTypeAdd }}
          onLoaded={onContractSubTypeSelectorLoaded}
          onChange={onContractSubTypeChange}
        />
      ),
    },
    {
      label: '客户编号',
      fieldName: 'custCode',
      rules: [{ required: true, message: '请输入客户编号' }],
      inputRender: (outerForm: FormInstance) => (
        <CustomerPop
          rowValue="custId-custName-custCode"
          handdleConfirm={handdleCustConfirm}
          keyMap={{
            custId: 'custId',
            custCode: 'custCode',
            custName: 'custName',
            hrContract: 'hrContract',
            contactTel: 'contactTel',
            email: 'email',
          }}
          fixedValues={{
            userRoleType: '1',
            // custCode: '***********',
            // custId: '900077841'
          }}
          addonAfter={searchCustTianyan(isCustCode)}
        />
      ),
    },
    {
      label: '现销售',
      fieldName: 'currentSales',
      rules: [{ required: true, message: '请输入现销售' }],
      inputRender: (outerForm: FormInstance) => {
        return (
          <CurrentSalesSelector
            disabled={!currentCustId}
            onConfirm={onCurrentSalesChange}
            // params={{ custId: currentCustId, branchId: userBranchId }}
            params={{ custId: currentCustId, deptId: userBranchId }}
            // paramsData={{ id: currentCustId }}
            skipEmptyParam
          />
        );
      },
    },
    {
      label: '合同类别',
      fieldName: 'contractCategery',
      rules: [{ required: true, message: '请输选择同类别' }],
      inputRender: () => <ContractCategorySelector onChange={onContractCategoryChange} />,
      inputProps: { disabled: !isUpdateContractCategery },
    },
    {
      label: '合同版本',
      fieldName: 'contractVersion',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
  ];
  useEffect(() => {
    API.crm.contractManage.initData.requests().then((data) => {
      setBasedata(data);
      // const contractTypeNames = (data.contractSupModel as POJO[]).reduce((obj, svc) => {
      //   obj[svc.svcTypeName] = svc.svcTypeId
      //   return obj
      // }, {})
      // setcontractTypeNameMap(contractTypeNames)
    });
  }, []);
  const onCurrentSalesChange = async (sales: defs.sale.DropdownList) => {
    if (!sales || isEmpty(sales)) {
      setDepartmentId('');
      return;
    }
    const { key } = sales;
    const departmentId = await API.crm.contractManage.getDepartmentIdByCurrentSales.requests({
      saleId: key!,
    });
    const departmentInfo = await getDepartInfo(departmentId);
    setDepartmentId(departmentId);
    setSalesData({
      cityName: sales?.cityName,
      formerGoverningAreaName: sales?.governingArea,
      areaId: departmentInfo.governingAreaId,
      departmentName: departmentInfo?.departmentName,
    });
  };
  const handdleCustConfirm = (value?: defs.sale.Customer) => {
    const custInfo: any = {
      custId: undefined,
      custName: undefined,
      custCode: undefined,
      hrContract: undefined,
      contactTel: undefined,
      email: undefined,
    };
    if (!value?.custCode) {
      formAdd.setFieldsValue(custInfo);
      setIsCustCode(false);
      return;
    }
    custInfo.custId = value?.custId;
    custInfo.custName = value?.custName;
    custInfo.custCode = value?.custCode;
    custInfo.hrContract = value?.hrContract;
    custInfo.contactTel = value?.contactTel;
    custInfo.email = value?.email;
    formAdd.setFieldsValue(custInfo);
    setCustInfo(custInfo);
    setcurrentCustId(value?.custId);
    setIsCustCode(true);
  };
  const onContractSubTypeChange = (subTypeId: string | undefined) => {
    // console.log('subTypeId in onContractSubTypeChange:', subTypeId)
    if (subTypeId) {
      getContractVersion({ contractSubType: subTypeId });
    } else {
      formAdd.setFieldsValue({
        contractVersion: '未匹配',
      });
    }
    if (subTypeId !== '7' && contractTypeAdd === '4') {
      formAdd.setFieldsValue({ contractCategery: '2' });
      setContractCategory('2');
      setIsUpdateContractCategery(false);
    } else {
      formAdd.setFieldsValue({ contractCategery: null });
      setContractCategory('');
      setIsUpdateContractCategery(true);
    }
  };
  const onContractSubTypeSelectorLoaded = (data: POJO[]) => {
    if (!data) return;
    // const contractSubType = data[0]?.value;
    // const contractSubType = formAdd.getFieldValue('contractSubType') || data[0]?.value;
    // formAdd.setFieldsValue({ contractSubType });
    getContractVersion({});
  };
  const onContractTypeAddChange = (svcTypeId: string) => {
    formAdd.setFieldsValue({ contractSubType: null });
    if (!svcTypeId) {
      setContractTypeAdd('');
      return;
    }

    // if(svcTypeId == "4"){
    //   isUpdateContractCategery = true;
    // }else{
    //   isUpdateContractCategery = true;
    // }

    setContractTypeAdd(svcTypeId);
  };
  const onContractCategoryChange = (contractCategory: string) => {
    setContractCategory(contractCategory);
    const { contractSubType } = formAdd.getFieldsValue(['contractType', 'contractSubType']);
    if (!contractSubType || !contractCategory) {
      formAdd.setFieldsValue({ contractVersion: '未匹配' });
      return;
    }
    getContractVersion({ contractCategery: contractCategory });
  };
  const getContractVersion = (contract?: Partial<TContractDTO>) => {
    // 由于form.setFieldsValue可能存在延时问题，此处的contract接受一个当前及时更改的最新值
    let formAddData = formAdd.getFieldsValue([
      'contractType',
      'contractSubType',
      'contractCategery',
    ]);
    if (contract) {
      formAddData = { ...formAddData, ...contract };
    }
    const { contractType, contractSubType, contractCategery } = formAddData;
    // console.log('contractType, contractSubType, contractCategery in getContractVersion:', contractType, contractSubType, contractCategery)
    if (!contractSubType || !contractType || !contractCategery) return;

    API.sale.contract.getContractVersion
      .request({
        contractVersionType: contractCategery,
        contractType,
        contractSubType,
      })
      .then((res: StdRes<string>) => {
        if (resError(res)) {
          return formAdd.setFieldsValue({ contractVersion: '未匹配' });
        }
        formAdd.setFieldsValue({ contractVersion: res.data || '未匹配' });
      });
  };
  const searchCustTianyan = (custCode: any) => {
    if (custCode) {
      return (
        <Typography.Link
          // disabled={disabled || !nonStaCoctApprId}
          onClick={() =>
            window.open(
              'https://www.tianyancha.com/search?key=' + formAdd.getFieldValue('custName'),
            )
          }
        >
          核查客户
        </Typography.Link>
      );
    } else {
      return null;
    }
  };
  const viewContract = () => {
    setCurrentContarct(options.selectedSingleRow);
    QueryContractDetailInfoWinModal[1](true);
  };

  const addContract = async () => {
    const formValues = await formAdd.validateFields();
    setCurrentContarct({ ...custInfo, ...formValues, departmentId, ...salesData });
    contractFormModal[1](true);
    onCancel();
    contractFormScenes[1]('add');
  };

  const updateContract = () => {
    const contract = options.selectedSingleRow;
    if (contract.contractStatus !== '0') return msgErr('合同状态为初始时,才能修改记录');
    setCurrentContarct(contract);
    contractFormModal[1](true);
    contractFormScenes[1]('update');
  };
  const handleCopyContract = () => {
    if (isEmpty(options.selectedSingleRow)) return msgErr('请选择要复制的合同数据');
    setCurrentContarct({ ...options.selectedSingleRow, isCopyContract: true });
    contractFormModal[1](true);
    contractFormScenes[1]('update');
  };
  const enterExamine = () => {
    const contract = options.selectedSingleRow;
    if (contract.contractStatus !== '0') return msgErr('合同状态为初始时,才能修审批');
    setCurrentContarct(options.selectedSingleRow);
    contractFormModal[1](true);
    contractFormScenes[1]('examine');
  };

  const tryRenewContract = () => {
    const contract = options.selectedSingleRow;
    if (isEmpty(contract)) return 0;
    if (contract.contractStatus !== '2' || !['0', '1'].includes(contract.contractSvcState)) {
      return msgErr('合同状态为审批通过,且为新签或续签时,才能修改记录');
    }
    if (contract.contractType === '2') {
      if (contract.contractSubType === '12' || contract.contractSubType === '15') {
        return msgErr('选择的合同不允许使用续签功能，请联系销管部');
      }
    }
    if (String(contract.currentSales) !== String(privateObject.userId)) {
      return msgErr('只能允许现销售进行合同续签');
    }
    // renewable[1](true);
    setAdditional({ isAdjustRenewContract: '1', winIsView: true, winIsRenew: true });
    setCurrentContarct(options.selectedSingleRow);
    contractFormModal[1](true);
    contractFormScenes[1]('renew');
  };

  const renewContract = (isAdjustRenewContract: string) => {
    // renewable[1](false);
  };

  const downloadModelContract = () => {
    API.crm.contractManage.getEffectModelContract
      .requests({ id: '1' })
      .then((data) => {
        exportImportFile(data.filePath, data.fileName);
      })
      .catch(() => {
        ConfirmLoading.clearLoading(API.commons.file.generalDownloadFile);
      });
  };

  const changeCurrentSale = () => {
    const contract = options.selectedSingleRow;
    if (contract.contractStatus !== '2') return msgErr('合同状态为审批通过时，才可更换现销售');
    setCurrentContarct(contract);
    UpdateContractCurrentSaleWinModal[1](true);
  };

  const deleteProduct = () => {
    const contract = options.selectedSingleRow;
    if (contract.contractStatus !== '0') {
      return msgErr('只有初始状态的合同允许删除');
    }
    API.crm.contractManage.delContract.requests(contract).then(() => {
      msgOk('删除合同成功');
      options.request();
    });
  };

  // const handleSyncCrm = () => {
  //   const contract = options.selectedSingleRow;
  //   if (contract.contractStatus !== '2' || !['0', '1'].includes(contract.contractSvcState)) {
  //     return msgErr('合同状态为审批通过时，并且未终止服务时可同步');
  //   }
  // };

  const downloadData = async () => {
    await exportFilebyColumns(
      API.crm.contractManage.toDownLoad,
      [
        ...columns,
        { title: '风险分担比例%', dataIndex: 'riskSharingRatio' },
        { title: '风险金比例%', dataIndex: 'riskPremiumRatio' },
      ],
      { ...options.queries, expType: '5' },
      '合同导出文件.xlsx',
      undefined,
      undefined,
      {
        areaType: 'areaTypeName',
        contractStatus: 'contractStatusName',
        contractSvcState: 'contractSvcStateName',
        governingArea: 'governingAreaName',
        governingBranch: 'governingBranchName',
        salFlag: 'salFlagName',
        salFlagManual: 'salFlagManualName',
        formerGoverningArea: 'formerGoverningAreaName',
        formerGoverningBranch: 'formerGoverningBranchName',
        isSameInsur: 'isSameInsurName',
      },
    );
  };
  const updateSignFlagManual = () => {
    const contract = options.selectedSingleRow;
    if (contract.contractStatus !== '2') {
      return msgErr('合同状态为审批通过时，才可以修改');
    }
    updateSignFlagManualWinModal[1](true);
    setCurrentContarct(options.selectedSingleRow);
  };
  const updateContractVersion = () => {
    const contract = options.selectedSingleRow;
    if (contract.contractStatus !== '2') {
      return msgErr('合同状态为审批通过时，才可以修改');
    }
    updateContractVersionModal[1](true);
    setCurrentContarct(options.selectedSingleRow);
  };
  const exportQualityData = async () => {
    const formValues = await form.getFieldsValue();
    // const {
    //   custCode,
    //   governingArea,
    //   approveDtStart,
    //   approveDtEnd,
    //   contractEndDateFrom,
    //   contractEndDateTo,
    // } = formValues;
    // if (
    //   !(
    //     custCode ||
    //     governingArea ||
    //     getItemTime(approveDtStart, approveDtEnd) ||
    //     getItemTime(contractEndDateFrom, contractEndDateTo)
    //   )
    // ) {
    //   return message.warn(
    //     ' 审批通过日期从+审批通过日期到或最终结束日期从+最终结束日期到，2个都要填，只能选同一年的日期与客户编号，现销售所属大区四选一才能查询',
    //   );
    // }
    await exportFilebyColumns(
      API.sale.contract.toDownLoad,
      [],
      { ...options.queries, expType: '4' },
      '合同日报质控导出文件.xlsx',
      undefined,
      undefined,
      {},
    );
  };
  const exportData = async () => {
    // const formValues = await form.getFieldsValue();
    // const {
    //   custCode,
    //   governingArea,
    //   approveDtStart,
    //   approveDtEnd,
    //   contractEndDateFrom,
    //   contractEndDateTo,
    // } = formValues;
    // if (
    //   !(
    //     custCode ||
    //     governingArea ||
    //     getItemTime(approveDtStart, approveDtEnd) ||
    //     getItemTime(contractEndDateFrom, contractEndDateTo)
    //   )
    // ) {
    //   return message.warn(
    //     ' 审批通过日期从+审批通过日期到或最终结束日期从+最终结束日期到，2个都要填，只能选同一年的日期与客户编号，现销售所属大区四选一才能查询',
    //   );
    // }

    await exportFilebyColumns(
      API.sale.contract.toDownLoad,
      [],
      { ...options.queries, expType: '1' },
      '合同日报质控导出文件.xlsx',
      undefined,
      undefined,
      {},
    );
  };
  const renderButtons = (_options: WritableInstance) => {
    options = _options;
    const notOne = isEmpty(options.selectedSingleRow);
    return (
      <>
        <Button type="primary" htmlType="submit">
          查询
        </Button>
        <Button disabled={notOne} onClick={viewContract}>
          详情
        </Button>
        <Button
          onClick={() => {
            setContractAddVisible(true);
            formAdd.setFieldsValue({ contractCategery: '2' });
          }}
        >
          新增
        </Button>
        <Button disabled={notOne} onClick={updateContract}>
          修改
        </Button>
        <Button disabled={notOne} onClick={enterExamine}>
          提交审核
        </Button>
        {/* <Popconfirm
          title="本次续签是否需要调整合同条款？"
          // popupVisible={popupVisible[0]}
          onConfirm={() => renewContract(stdBoolTrueSrting)}
          onCancel={() => renewContract(stdBoolFalseSrting)}
          okText="是"
          cancelText="否"
          visible={renewable[0]}
        >
         
        </Popconfirm> */}
        <Button disabled={notOne} onClick={tryRenewContract}>
          合同续签
        </Button>
        <ConfirmButton
          icon={<DownloadOutlined />}
          service={API.commons.file.generalDownloadFile}
          onClick={downloadModelContract}
        >
          下载范本合同
        </ConfirmButton>
        <AuthButtons funcCode="HTGHXXS">
          <Button disabled={notOne} onClick={changeCurrentSale}>
            合同更换现销售
          </Button>
        </AuthButtons>
        <AuthButtons funcCode="HTDCSJ">
          <AsyncButton
            disabled={options.size === 0}
            icon={<DownloadOutlined />}
            // service={API.crm.contractManage.toDownLoad}
            onClick={downloadData}
          >
            导出数据
          </AsyncButton>
        </AuthButtons>
        {/* <AuthButtons funcCode="HTTBCRM">
          <Button disabled={notOne} onClick={handleSyncCrm}>
            同步合同至CRM
          </Button>
        </AuthButtons> */}
        <PopconfirmButton title="确认删除该记录？" disabled={notOne} onConfirm={deleteProduct}>
          删除
        </PopconfirmButton>
        <Button onClick={handleCopyContract}>复制该合同</Button>
        <AuthButtons funcCode="HTGLXSXGXQBS">
          <Button
            disabled={options.size === 0}
            // service={API.crm.contractManage.toDownLoad}
            onClick={updateSignFlagManual}
          >
            修改新签标识
          </Button>
        </AuthButtons>
        <AuthButtons funcCode="HTGLXSWHANQXGROUP1">
          <AsyncButton
            disabled={options.size === 0}
            icon={<DownloadOutlined />}
            // service={API.crm.contractManage.toDownLoad}
            onClick={exportData}
          >
            导出数据（质控）
          </AsyncButton>
          <AsyncButton
            disabled={options.size === 0}
            icon={<DownloadOutlined />}
            // service={API.crm.contractManage.toDownLoad}
            onClick={exportQualityData}
          >
            导出日报（质控）
          </AsyncButton>
          <Button disabled={options.size === 0} onClick={updateContractVersion}>
            维护合同信息（质控）
          </Button>
        </AuthButtons>
      </>
    );
  };
  const onConfirmContractForm = () => {
    setAdditional({});
    setCurrentContarct({});
    options.request();
    contractFormScenes[1](undefined);
    // renewable[1](undefined);
  };
  const onCancel = () => {
    setContractAddVisible(false);
    formAdd.resetFields();
    setContractTypeAdd('');
    formAdd.setFieldsValue({ contractCategery: '2' });
    setIsCustCode(false);
  };
  const renderFooter = () => {
    return (
      <RowElement>
        <ColElementButton
          style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}
        >
          <Button key="cancel" type="primary" onClick={addContract}>
            下一步
          </Button>
          <Button key="cancel" type="primary" onClick={() => onCancel()}>
            取消
          </Button>
        </ColElementButton>
      </RowElement>
    );
  };
  return (
    <CachedPage
      notShowRowSelection
      service={service}
      formColumns={formColumns}
      columns={columns}
      renderButtons={renderButtons}
      form={form}
      // handleQueries={handleQueries}
      // initialValues={{
      //   contractCode: contractCodes[0],
      //   contractName: '珠海市香洲王武林废品回收站代理',
      // }}
      // initialValues={{
      //   currentSales: 5093,
      //   currentSalesName: '闫海涛',
      //   contractStatus: '2',
      //   contractSvcState: '0',
      // }}
      // initialValues={{ "custId": "409503", "custName": "中移鼎讯通信股份有限公司" }}
      // initialValues={{ custId: '900684733', custName: '南京领行科技股份有限公司' }}
      // initialValues={{ contractName: '大合同测试' }}
    >
      <ContractForm
        modal={contractFormModal}
        contract={currentContract}
        additional={additional}
        onConfirm={onConfirmContractForm}
        scene={contractFormScenes[0]}
      />
      <ContractView modal={QueryContractDetailInfoWinModal} contract={currentContract} />
      <QueryApproveProcessWin
        modal={QueryApproveProcessWinModal}
        processInstanceId={processInstanceId[0]}
      />
      <UpdateContractCurrentSaleWin
        modal={UpdateContractCurrentSaleWinModal}
        contract={currentContract}
        onConfirm={() => options.request(options.queries)}
      />
      {/* 修改新签标识 */}
      <UpdateSignFlagManualWin
        modal={updateSignFlagManualWinModal}
        contract={currentContract}
        onConfirm={() => options.request()}
      />
      {/* 维护合同信息（质控） */}
      <UpdateContractVersion
        modal={updateContractVersionModal}
        contract={currentContract}
        onConfirm={() => options.request()}
      />
      <Codal
        title="大合同新增"
        width={'60%'}
        visible={contractAddVisible}
        onCancel={onCancel}
        footer={renderFooter()}
      >
        <FormElement2 form={formAdd} preserve={false}>
          <EnumerateFields outerForm={formAdd} formColumns={formAddColumns} colNumber={3} />
        </FormElement2>
      </Codal>
    </CachedPage>
  );
};

export default ContractManagement;
