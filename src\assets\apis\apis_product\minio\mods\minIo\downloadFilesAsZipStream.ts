import * as defs from '../../baseClass';
import {
  fetch,
  RequestConfig,
  beforeFetch,
  afterFetch,
  checkResStatus,
  removeSpace,
} from '@/utils/request';

/**
 * @url /rhro-service-1.0/minio/downloadFilesAsZipStream
 * @desc downloadFilesAsZipStream
 * hasForm: false
 * hasBody: true
 */

export class Params {
  /** bucket */
  bucket?: string;
  /** downloadZipName */
  downloadZipName?: string;
}

export const init = undefined;
export const url = '/rhro-service-1.0/minio/downloadFilesAsZipStream:POST';
export const initialUrl = '/rhro-service-1.0/minio/downloadFilesAsZipStream';
export const cacheKey = '_minio_downloadFilesAsZipStream_POST';
export async function request(
  data: ObjectMap<any, Array<defs.minio.FileInfo>>,
  options?: RequestConfig,
) {
  const reqUrl = `/rhro-service-1.0/minio/downloadFilesAsZipStream`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          reolve(res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
export async function requests(
  data: ObjectMap<any, Array<defs.minio.FileInfo>>,
  options?: RequestConfig,
) {
  const reqUrl = `/rhro-service-1.0/minio/downloadFilesAsZipStream`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          if (checkResStatus(res)) return reolve(res ? res.data : res);
          reject(res ? res.message : res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
