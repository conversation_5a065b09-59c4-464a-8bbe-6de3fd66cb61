import { But<PERSON>, Card, Form, Modal, Input, Row, Col, PaginationProps } from 'antd';
import React, { useState } from 'react';
import { useRequest } from 'ahooks';
import StandardTable, { StandardTableColumnProps } from '@/components/StandardTable';
import { connect, Dispatch } from 'umi';
import request from 'umi-request';
import axios from 'axios';
import AddForm from '@/components/EditeForm/AddForm';
import UpdateForm from '@/components/EditeForm/UpdateForm';
import { msgOk, resError, resErrorMsg, msgErr } from '@/utils/methods/message';
import { DeptmentSelector, AreaSelector } from '@/components/Selectors';
import { mapToSelectors } from '@/components/Selectors/FuncSelectors';
import { CachedPage, CachedTableOptionsType } from '@/components/CachedPage';
import ColumnForms, { EditeFormProps, FormInstance } from '@/components/EditeForm';
import {
  FormElement3,
  FormElement4,
  RowElement,
  ColElementButton,
} from '@/components/Forms/FormLayouts';
import { EnumerateFields } from '@/components/CachedPage/EnumerateFields';
import { DateRange } from '@/components/DateRange4/dateRange';
import { downloadFileWithAlert } from '@/utils/methods/file';
import Codal from '@/components/Codal';
import { CustPayEntityPop } from '@/components/StandardPop/CustPayEntityPop';
import { isEmpty } from 'lodash';

interface QueryBauschLombProps extends FormInstance {
  dispatch: Dispatch;
}

const RecordTypeMap = new Map<number, string>([
  [1, '正常入职（增员）'],
  [3, '异地转正式（增员）'],
  [2, '减员'],
]);
const StatusMap = new Map<number, string>([
  [0, '未处理'],
  [1, '数据获取成功'],
  [2, '数据获取失败'],
  [3, '提交成功'],
  [4, '提交失败'],
  [5, '不处理'],
  [6, '已减员'],
]);
const columns: StandardTableColumnProps<defs.workflow.RemindDTO>[] = [
  { title: '集团编号', dataIndex: 'GroupId' },
  { title: '客户编号', dataIndex: 'CustCode' },
  { title: '客户名称', dataIndex: 'CustName' },
  { title: 'employment_id', dataIndex: 'EmploymentID' },
  { title: '工号', dataIndex: 'EmployeeNumber' },
  { title: '姓名', dataIndex: 'EmpName' },
  { title: '证件号码', dataIndex: 'IDCardNum' },
  { title: '电话', dataIndex: 'Cellphone' },
  { title: '职级', dataIndex: 'JobLevelName' },
  { title: '序列', dataIndex: 'JobFamilyName' },
  { title: '职务', dataIndex: 'JobName' },
  { title: '入职日期', dataIndex: 'HireDt' },
  { title: '离职日期', dataIndex: 'SepDt' },
  { title: '户口类型', dataIndex: 'HukouType' },
  { title: '户口所在地', dataIndex: 'HukouLocation' },
  { title: '申报工资', dataIndex: 'DecSalary' },
  { title: '社保基数', dataIndex: 'SSBase' },
  { title: '公积金基数', dataIndex: 'PFBase' },
  { title: '社保起缴月', dataIndex: 'SSStartMon' },
  { title: '社保停缴月', dataIndex: 'FsDeclSocialEndMon' },
  { title: '数据处理状态', dataIndex: 'StatusName' },
  { title: '数据来源', dataIndex: 'RecordTypeName' },
  { title: '接收数据时间', dataIndex: 'CreateDtStr' },
  { title: '获取数据时间', dataIndex: 'UpdateDtStr' },
  { title: '修改后社保停缴月', dataIndex: 'DeclSocialEndMon' },
  { title: '提交HRO时间', dataIndex: 'HroCommitDtStr' },
  { title: '服务性质', dataIndex: 'ServiceTypeName' },
  { title: '缴费实体名称', dataIndex: 'CustPayEntity' },
  { title: '处理日志', dataIndex: 'ProcessLog' },
  { title: 'HRO错误信息', dataIndex: 'ErrorMessage' },
  { title: '飞书错误信息', dataIndex: 'ErrMsg' },
  { title: '备注', dataIndex: 'Remark' },
];
export const standardTablePagination = (pagination: PaginationProps | undefined) => ({
  showSizeChanger: true,
  showQuickJumper: true,
  ...pagination,
});
const addService = API.bigcustomer.bigAdd.beforeAddEmp;
const reduceService = API.bigcustomer.bigReduce.beforeAddEmp;
const QueryBauschLomb: React.FC<QueryBauschLombProps> = (props) => {
  const [list, setList] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [total, seTotal] = useState<number>(0);
  const [singleRow, setSingleRow] = useState<any>({}); // 选中的行
  const [selectRow, setSelectRow] = useState<any>([]);
  const [visibleMonth, setVisibleMonth] = useState<any>(false);
  const [tableSearch, setTableSearch] = useState<any>(false);
  const [tablePagination, setPagination] = useState<any>({
    current: 1,
    pageSize: 10,
    showQuickJumper: true,
    showSizeChanger: true,
  });
  const [form] = Form.useForm();
  const [monthForm] = Form.useForm();
  const formColumns: EditeFormProps[] = [
    {
      label: '姓名',
      fieldName: 'empName',
      inputRender: 'string',
    },
    {
      label: '证件号码',
      fieldName: 'iDCardNum',
      inputRender: 'string',
    },
    {
      label: '缴费实体',
      fieldName: 'CustPayEntity',
      inputRender: () => (
        <CustPayEntityPop
          rowValue="CustPayEntity"
          keyMap={{
            CustPayEntity: 'custPayEntityName',
          }}
        />
      ),
    },
    {
      label: '',
      fieldName: '',
      inputRender: () => (
        <DateRange
          fields={[
            {
              title: '接受数据日期从',
              dataIndex: 'CreateDtStart',
            },
            {
              title: '接受数据日期到',
              dataIndex: 'CreateDtEnd',
            },
          ]}
        />
      ),
    },
    // {
    //   label: '',
    //   fieldName: '',
    //   inputRender: () => (
    //     <DateRange
    //       fields={[
    //         {
    //           title: '获取数据日期从',
    //           dataIndex: 'startDt',
    //         },
    //         {
    //           title: '获取数据日期到',
    //           dataIndex: 'endDt',
    //         },
    //       ]}
    //     />
    //   ),
    // },
    {
      label: '',
      fieldName: '',
      inputRender: () => (
        <DateRange
          fields={[
            {
              title: '提交HRO日期从',
              dataIndex: 'HroCommitDtStart',
            },
            {
              title: '提交HRO日期到',
              dataIndex: 'HroCommitDtEnd',
            },
          ]}
        />
      ),
    },
    {
      label: '数据来源',
      fieldName: 'recordType',
      inputRender: () => mapToSelectors(RecordTypeMap),
    },
    {
      label: '数据处理状态',
      fieldName: 'Status',
      inputRender: () => mapToSelectors(StatusMap),
    },
  ];
  const monthFormColumns = [
    {
      label: '社保停缴月',
      fieldName: 'DeclSocialEndMon',
      inputRender: 'month',
      inputProps: {
        format: 'YYYYMM',
      },
    },
  ];
  // const removeFalsy = (obj) => {
  //   const newObj = {};
  //   for (const key in obj) {
  //     if (obj[key]) {
  //       newObj[key] = obj[key];
  //     }
  //   }
  //   return newObj;
  // }
  const fetchData = async ({ pageNum = 1, pageSize }) => {
    const formValues = await form.validateFields();
    setLoading(true);
    // const item = removeFalsy(formValues)
    const response = await fetch('/rhro-service-1.0/fsapi/employee/list?versionid=1754037497233', {
      method: 'POST',
      mode: 'cors', // 明确请求 CORS
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        pageNum: pageNum,
        pageSize: pageSize,
        ...formValues,
      }),
    });
    if (!response.ok) {
      throw new Error('Network response was not ok');
    }
    setLoading(false);
    const { data, total } = await response.json();
    const list = data?.map((item) => {
      const createDt = item?.CreateDt?.Time?.substring(0, 10) || '';
      const updateDt = item?.UpdateDt?.Time?.substring(0, 10) || '';
      return { ...item, CreateDt: createDt, UpdateDt: updateDt };
    });
    if (list.length > 0) {
      setSingleRow(list[0]);
      setSelectRow([]);
    }
    setList(list || []);
    seTotal(total);
  };
  const handleClick = async () => {
    await fetchData({
      pageNum: tableSearch ? 1 : tablePagination?.current,
      pageSize: tablePagination?.pageSize,
    });
    setPagination({
      current: tableSearch ? 1 : tablePagination?.current,
      pageSize: tablePagination?.pageSize,
      showQuickJumper: true,
      showSizeChanger: true,
    });
    setTableSearch(false);
  };
  const notHandle = async () => {
    if (selectRow.length < 1) return msgErr('还未选择数据');
    for (const i of selectRow) {
      if (i?.Status == 5 || i?.Status == 3 || i?.Status == 6) {
        return msgErr(
          '对于数据处理状态为未处理、数据获取成功、数据获取失败、提交失败，数据处理状态标记为不处理',
        );
      }
    }
    const data = selectRow.map((item: any) => {
      return item.FsID;
    });
    const response = await fetch('/rhro-service-1.0/fsapi/employee/batchUpdateStatus?', {
      method: 'POST',
      mode: 'cors',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        fsIds: data,
      }),
    });

    if (!response.ok) {
      msgErr('更新失败');
    }
    const responseJson = await response.json();
    handleClick();
    msgOk(responseJson.message);
  };
  const handleUpdateEmployee = () => {
    ///api/employee/updateEmployeeInsurance
    // if (singleRow?.DeclSocialEndMon) {
    if (isEmpty(singleRow)) return msgErr('还未选择要修改的数据');
    if ((singleRow?.Status !== 0 && singleRow?.Status !== 4) || singleRow?.RecordType !== 2) {
      return msgErr('仅对未处理或者提交失败的正常减员数据才可以修改社保停缴月');
    }
    setVisibleMonth(true);
    monthForm.setFieldsValue({
      DeclSocialEndMon: singleRow?.DeclSocialEndMon,
    });
  };
  const handleUpdateEmp = async () => {
    ///
    if (selectRow.length < 1) return msgErr('还未选择数据');
    for (const i of selectRow) {
      if (i?.Status !== 2 && i?.Status !== 0) {
        return msgErr('仅对数据处理状态为未处理或者数据获取失败的的才可以重新获取信息');
      }
    }
    const addData: any[] = [];
    const reduceData: any[] = [];
    selectRow.forEach((item: any) => {
      if (item?.RecordType === 1 || item?.RecordType === 3) {
        addData.push(item?.FsID);
      } else {
        reduceData.push({ FsId: item?.FsID, EmploymentId: item?.EmploymentID });
      }
    });
    if (addData.length > 0) {
      const responseAdd = await fetch('/rhro-service-1.0/fsapi/employee/updateEmp?', {
        method: 'POST',
        mode: 'cors',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          fsIDs: addData,
        }),
      });
      if (!responseAdd.ok) {
        msgErr('更新增员失败');
      }
      const responseAddJson = await responseAdd.json();
      msgOk(responseAddJson.message);
    }
    if (reduceData.length > 0) {
      const responseReduce = await fetch('/rhro-service-1.0/fsapi/employee/reFetchInfoReduce?', {
        method: 'POST',
        mode: 'cors',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          items: reduceData,
        }),
      });

      if (!responseReduce.ok) {
        msgErr('更新减员失败');
      }
      const responseReduceJson = await responseReduce.json();
      msgOk(responseReduceJson.message);
    }

    handleClick();
  };
  const handleReFetchInfoReduce = async () => {
    if (selectRow.length < 1) return msgErr('还未选择数据');
    for (const i of selectRow) {
      if ((i?.Status !== 1 && i?.Status !== 4) || i?.RecordType !== 2) {
        return msgErr(
          '仅对数据处理状态为数据获取成功或者提交失败的且数据来源是减员的才可以手动HRO减员',
        );
      }
    }
    const data = selectRow.map((item: any) => {
      return item.FsID;
    });
    reduceService.requests(data).then((res) => {
      msgOk('手动提交HRO减员成功');
      handleClick();
    });
  };
  const handleBeforeAdd = () => {
    if (selectRow.length < 1) return msgErr('还未选择数据');
    for (const i of selectRow) {
      if ((i?.Status !== 1 && i?.Status !== 4) || i?.RecordType === 2) {
        return msgErr(
          '仅对数据处理状态为数据获取成功或者提交失败的且数据来源是正常入职（增员）、异动转正式（增员）的才可以手动HRO增员',
        );
      }
    }
    const data = selectRow.map((item: any) => {
      return item.FsID;
    });
    addService.requests(data).then((res) => {
      msgOk('手动提交HRO增员成功');
      handleClick();
    });
  };
  const exportEmployeeInfo = async () => {
    const values = await form.validateFields();
    const itemColumns = columns.map((item) => {
      return { header: item?.title, field: item?.dataIndex };
    });
    const response = await fetch('/rhro-service-1.0/fsapi/employee/exportEmployeeInfo?', {
      method: 'POST',
      mode: 'cors',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ pageNum: 1, pageSize: 2147483647, ...values, columns: itemColumns }),
    });
    const res = await response.blob();
    downloadFileWithAlert(res, `博士眼镜增减员数据.xlsx`);
  };
  const renderButtons = () => {
    return (
      <React.Fragment>
        <div style={{ margin: '10px 10px 10px 0px' }}>
          <Button type="primary" htmlType="submit" loading={loading} onClick={handleClick}>
            查询
          </Button>
          <Button style={{ marginLeft: '10px' }} onClick={handleUpdateEmp}>
            重新获取信息
          </Button>
          <Button style={{ marginLeft: '10px' }} onClick={notHandle}>
            标记为不处理
          </Button>
          <Button style={{ marginLeft: '10px' }} onClick={handleUpdateEmployee}>
            修改社保停缴月
          </Button>
          <Button style={{ marginLeft: '10px' }} onClick={handleBeforeAdd}>
            手动提交HRO增员
          </Button>
          <Button style={{ marginLeft: '10px' }} onClick={handleReFetchInfoReduce}>
            手动提交HRO减员
          </Button>
          <Button style={{ marginLeft: '10px' }} onClick={exportEmployeeInfo}>
            导出
          </Button>
        </div>
      </React.Fragment>
    );
  };
  const handleTableChange = async (pagination, filter) => {
    await fetchData({ pageNum: pagination?.current, pageSize: pagination?.pageSize, pagination });
    setPagination(standardTablePagination({ ...pagination }));
  };
  const renderTable = () => {
    return (
      <StandardTable
        columns={columns}
        rowKey="FsID"
        data={{ list, pagination: { ...tablePagination, total } }}
        // notShowRowSelection
        // notShowPagination
        // editable
        loading={loading}
        onChange={handleTableChange}
        selectedSingleRow={singleRow}
        onSelectSingleRow={setSingleRow}
        selectedRows={selectRow}
        onSelectRow={(rows) => setSelectRow(rows || [])}
      />
    );
  };
  const saveMonth = async () => {
    const month = await monthForm.validateFields();
    const response = await fetch('/rhro-service-1.0/fsapi/employee/updateEmployeeInsurance?', {
      method: 'POST',
      mode: 'cors',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        ...singleRow,
        DeclSocialEndMon: month.DeclSocialEndMon,
      }),
    });
    const res = response.json();
    handleClick();
    msgOk(res?.message || '修改社保停缴月成功');
    setVisibleMonth(false);
  };
  const onCancel = () => {
    setVisibleMonth(false);
  };
  const handleFieldsChange = (changedValues: POJO, values: POJO) => {
    setTableSearch(true);
  };
  const monthButtons = () => {
    return (
      <RowElement>
        <ColElementButton
          style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}
        >
          <Button type="primary" onClick={saveMonth}>
            保存
          </Button>
          <Button type="primary" onClick={onCancel}>
            取消
          </Button>
        </ColElementButton>
      </RowElement>
    );
  };
  return (
    <React.Fragment>
      <Card style={{ minHeight: '500px' }}>
        <FormElement4 form={form} onValuesChange={handleFieldsChange}>
          <EnumerateFields outerForm={form} colNumber={4} formColumns={formColumns} />
        </FormElement4>
        {renderButtons()}
        {renderTable()}
      </Card>
      <Codal
        title={'修改社保停缴月'}
        visible={visibleMonth}
        width={800}
        footer={monthButtons()}
        onCancel={() => {
          setVisibleMonth(false);
        }}
      >
        <FormElement3 form={monthForm}>
          <EnumerateFields formColumns={monthFormColumns} outerForm={monthForm} />
        </FormElement3>
      </Codal>
    </React.Fragment>
  );
};

export default QueryBauschLomb;
