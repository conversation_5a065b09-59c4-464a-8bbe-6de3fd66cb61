import * as defs from '../../baseClass';
import {
  fetch,
  RequestConfig,
  beforeFetch,
  afterFetch,
  checkResStatus,
  removeSpace,
} from '@/utils/request';

/**
     * @url /rhro-service-1.0/secondCustPayer/generateUploadTemplate
     * @desc 批量导入二级户-下载模板
批量导入二级户-下载模板
     * hasForm: false
     * hasBody: true
     */

export class Params {}

export const init = undefined;
export const url =
  '/rhro-service-1.0/secondCustPayer/generateUploadTemplate:GET';
export const initialUrl =
  '/rhro-service-1.0/secondCustPayer/generateUploadTemplate';
export const cacheKey = '_secondCustPayer_generateUploadTemplate_GET';
export async function request(options?: RequestConfig) {
  const reqUrl = `/rhro-service-1.0/secondCustPayer/generateUploadTemplate`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .get(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          reolve(res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
export async function requests(options?: RequestConfig) {
  const reqUrl = `/rhro-service-1.0/secondCustPayer/generateUploadTemplate`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .get(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          if (checkResStatus(res)) return reolve(res ? res.data : res);
          reject(res ? res.message : res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
