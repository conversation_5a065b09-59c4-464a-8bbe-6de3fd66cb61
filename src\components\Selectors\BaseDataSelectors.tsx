/*
 * @Author: 侯成
 * @Email: <EMAIL>
 * @since: 2019-07-26 10:39:12
 * @lastTime: 2019-11-19 15:19:32
 * @LastAuthor: tjj
 * @message:
 */
import React from 'react';

import { TkeyType, TOptionsValue } from '@/utils/methods/cache';
import { CarrayOnMap } from '@/utils/methods/selectors';
import BaseSelectors, { StdSelectProps, TRef, keyValue, BaseSelectorsProps } from './BaseSelectors';

export interface BaseDataSelectorsProps extends StdSelectProps {
  params?: POJO;
  code?: TOptionsValue;
  paramsData?: POJO;
  keyValue?: string;
  nestedKey?: string;
  keyType?: TkeyType;
  dataDisabled?: number;
  onSuccess?: (data: any) => void; // 数据更新后回调
}

// 基础数据
export const BaseDataSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    keyPairs: ['baseDataCode', 'baseDataName'],
    // service: API.basedata.baseData.listByType,
    service: API.basedata.baseData.listByType,
  };
  // return <BaseSelectors<number> {...props} {...carrayOn} />;
  return <BaseSelectors<number> {...props} {...carrayOn} />;
};
// 数据字典大区数据
export const BaseDataAreaSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    keyPairs: ['baseDataCode', 'baseDataName'],
    nestedKey: 'list',
    service: API.basedata.baseDataCls.list,
  };
  // return <BaseSelectors<number> {...props} {...carrayOn} />;
  return <BaseSelectors<number> {...props} {...carrayOn} />;
};
// 通用业务组件
export const CommonSelector: React.FC<BaseDataSelectorsProps & { service: IServiceType }> = (
  props,
) => {
  const { service, ...rest } = props;
  const carrayOn: BaseSelectorsProps<number> = CarrayOnMap[service.cacheKey];
  return <BaseSelectors<number> allowClear {...rest} {...carrayOn} />;
};

// 大区下拉框
export const AreaSelector: React.FC<BaseDataSelectorsProps> = (props: BaseDataSelectorsProps) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.admin.department.areaDropDownList,
    keyPairs: ['key', 'shortName'],
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};

//国际化语种下拉
export const LanguageSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.basedata.baseData.languageDropdownList,
    keyPairs: ['key', 'shortName'],
  };
  return <BaseSelectors<number> {...props} {...carrayOn} />;
};

// 部门下拉框
export const DeptSelector: React.FC<BaseDataSelectorsProps> = (props: BaseDataSelectorsProps) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.admin.department.getSubDepartment,
    nestedKey: 'deptList',
    keyPairs: ['departmentId', 'departmentName'],
    fixedParams: { departmentGrades: '', parentDeptId: '-1' },
  };
  return <BaseSelectors<number> {...props} {...carrayOn} />;
};

export const RoleBizCategorySelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.admin.role.getBizCategoryDropDown,
    nestedKey: 'list',
    keyPairs: ['key', 'shortName'],
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};
// 城市选择
export const CitySelector: React.FC<BaseDataSelectorsProps> = (props: BaseDataSelectorsProps) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.basedata.city.cityDropDownList,
    keyPairs: ['key', 'shortName'],
    showSearch: true,
    filterKeys: ['pinYinCode'],
  };
  return <BaseSelectors<string> {...props} {...carrayOn} />;
};

// 供应商网点城市
export const SupplierCitySelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.basedata.city.providerCityList,
    keyPairs: ['key', 'shortName'],
    showSearch: true,
    filterKeys: ['pinYinCode'],
  };
  return <BaseSelectors<string> {...props} {...carrayOn} />;
};

// 地/县区（简称）
export const DistrictSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.basedata.servicePoint.queryCityList,
    keyPairs: ['key', 'shortName'],
    showSearch: true,
    filterKeys: ['pinYinCode'],
  };
  return <BaseSelectors<string> {...props} {...carrayOn} />;
};

// 省份选择
export const ProvinceSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.basedata.province.provinceDropDownList,
    keyPairs: ['key', 'shortName'],
    showSearch: true,
  };
  return <BaseSelectors<number> {...props} {...carrayOn} />;
};

// 国家选择
export const CountrySelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.basedata.country.countryDropDownList,
    // fixedParams: {type: ''},
    keyPairs: ['key', 'shortName'],
    showSearch: true,
  };
  return <BaseSelectors<number> {...props} {...carrayOn} />;
};

// 出生国家选择
export const CsdCountrySelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.payroll.payRollArchives.queryCountryListForCsd,
    // fixedParams: {type: ''},
    keyPairs: ['key', 'shortName'],
    showSearch: true,
  };
  return <BaseSelectors<number> {...props} {...carrayOn} />;
};

// 提醒类型下拉
export const ProvincelinkageSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.basedata.province.provinceDropDownList,
    keyPairs: ['key', 'shortName'],
    showSearch: true,
  };
  return <BaseSelectors<number> {...props} {...carrayOn} />;
};

// 提醒类型下拉
export const InitRemindTypeSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.workflow.remind.initRemindType,
    nestedKey: 'list',
    keyPairs: ['key', 'shortName'],
  };
  return <BaseSelectors<number> {...props} {...carrayOn} />;
};

export const RoleGradeSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.admin.role.getRoleGradeDropDown,
    nestedKey: 'list',
    keyPairs: ['key', 'shortName'],
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};

export const RoleStatusSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.admin.role.getRoleStatusDropDown,
    nestedKey: 'list',
    keyPairs: ['key', 'shortName'],
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};

// 社保比例维护所属城市下拉
export const ProdratioCitySelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.welfaremanage.socialSecurity.postGetSsGroupDropdownList,
    nestedKey: 'cityList',
    keyPairs: ['key', 'shortName'],
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};

// 服务网点所属城市按区筛选下拉
export const CityByAreaSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.basedata.servicePoint.cityDropDownList,
    keyPairs: ['key', 'shortName'],
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};

// 这里演示了service冲突后应该怎么做
// 社保比例维护产品下拉
export const ProdratioProductSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const service = API.welfaremanage.socialSecurity.postGetSsGroupDropdownList;
  const carrayOn: BaseSelectorsProps<number> = {
    service: { ...service, cacheKey: service.cacheKey + 1 },
    nestedKey: 'productList',
    keyPairs: ['key', 'shortName'],
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};
// 分公司下拉列表 / 派单方 - 只选内部分公司
export const DeptmentSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.admin.department.getDepartmentDropdownList,
    keyPairs: ['key', 'shortName'],
    showSearch: true,
    nestedKey: 'list',
    fixedParams: { departmentGrade: '3', providerType: '1' },
  };
  return <BaseSelectors<number> {...props} {...carrayOn} />;
};

// 分公司下拉列表 / 接单方 - 只选内部分公司或外部供应商
export const ReceDeptmentSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.admin.department.getDepartmentDropdownList,
    keyPairs: ['key', 'shortName'],
    showSearch: true,
    nestedKey: 'list',
    fixedParams: { departmentGrade: '3' },
  };
  return <BaseSelectors<number> {...props} {...carrayOn} />;
};

// 分公司下拉列表 / 派单方
export const BaseDeptmentSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.basedata.baseDataCls.getDorpDownList,
    keyPairs: ['key', 'shortName'],
    showSearch: true,
    nestedKey: 'list',
    fixedParams: { statementName: 'contractManage.getSignBranchTitleByDepartmentId' },
  };
  return <BaseSelectors<number> {...props} {...carrayOn} />;
};

// 分公司下拉列表 -veni
export const DeptmentAndProviderSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.admin.department.getDepartmentDropdownList,
    keyPairs: ['key', 'shortName'],
    showSearch: true,
    nestedKey: 'list',
    fixedParams: { departmentGrade: '3' },
  };
  return <BaseSelectors<number> {...props} {...carrayOn} />;
};

// 金蝶编号下拉列表
export const JindieDeptSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.basedata.jindieOrg.getSuperOrgDropDownList,
    keyPairs: ['key', 'shortName'],
    nestedKey: 'list',
    showSearch: true,
  };
  return <BaseSelectors<number> {...props} {...carrayOn} />;
};

// 金蝶组织编号下拉列表
export const JindieCodeSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.basedata.sysBranchTitle.getJindieOrgDropDownList,
    keyPairs: ['key', 'shortName'],
    nestedKey: 'list',
    showSearch: true,
  };
  return <BaseSelectors<number> {...props} {...carrayOn} />;
};

// 社保组维护的社保比例下拉
export const ProductRatioSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.welfaremanage.socialSecurity.queryProductRatioDropdownList,
    keyPairs: ['productRatioId', 'productRatioName'],
    nestedKey: 'list',
    showSearch: true,
  };
  return <BaseSelectors<number> {...props} {...carrayOn} />;
};

// 社保组维护所属城市下拉
export const SsGroupCitySelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.welfaremanage.socialSecurity.postGetSsGroupDropdownList,
    nestedKey: 'cityList',
    showSearch: true,
    keyPairs: ['key', 'shortName'],
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};

// 社保合并组下拉
export const SsPackageSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.basedata.baseDataCls.getDorpDownList,
    nestedKey: 'list',
    keyPairs: ['key', 'shortName'],
    showSearch: true,
    fixedParams: { statementName: 'socialManage.querySsPackageIdDropDownList' },
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};

// 福利办理方。接收参数：cityId, deptId, filterDeptByUserRoleGrade
export const WalfareProcessorSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.welfaremanage.ssCommon.selForPartyByMap,
    nestedKey: 'list',
    keyPairs: ['key', 'shortName'],
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};

// 分公司id获取福利办理方。接收参数： deptId, isIndependent
export const ForPartyByDeptIdSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.welfaremanage.ssCommon.selForPartyByDeptId,
    keyPairs: ['key', 'shortName'],
    showSearch: true,
    nestedKey: 'list',
  };
  return <BaseSelectors<number> {...props} {...carrayOn} />;
};
// 分公司id获取福利办理方-仅大户。接收参数： deptId, isIndependent
export const ForPartyByDeptIdSelectorBig: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.welfaremanage.ssCommon.selForPartyByDeptInde,
    keyPairs: ['key', 'shortName'],
    showSearch: true,
    nestedKey: 'list',
  };
  return <BaseSelectors<number> {...props} {...carrayOn} />;
};

// 社保操作类型下拉
export const SsHandleTypeSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.basedata.baseDataCls.getDorpDownList,
    nestedKey: 'list',
    keyPairs: ['key', 'shortName'],
    fixedParams: { statementName: 'socialManage.queryNewTypeIdDropDownList' },
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};

// 社保停办方式
export const SsStopTypeSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.welfaremanage.socialManage.queryStopTypeDropDownList,
    nestedKey: 'list',
    keyPairs: ['key', 'shortName'],
    emptyValues: [0],
    // fixedParams: { statementName: 'socialManage.queryStopTypeDropDownList' },
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};

// 社保合并组下拉
export const SsPackageNumberSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.basedata.baseDataCls.getDorpDownList,
    nestedKey: 'list',
    keyPairs: ['key', 'shortName'],
    keyType: 'number',
    fixedParams: { statementName: 'socialManage.querySsPackageIdDropDownList' },
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};

// 社保组对应的产品
export const ProductForPartySelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.welfaremanage.ssCommon.selProductForPartyByMap,
    nestedKey: 'list',
    keyPairs: ['key', 'shortName'],
    keyType: 'number',
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};
// 新标识下拉
export const GetSignFlagManualSelect: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<string> = {
    service: API.sale.contract.getSignFlagManual,
    nestedKey: 'list',
    keyPairs: ['key', 'shortName'],
  };
  return <BaseSelectors<string> {...props} {...carrayOn} />;
};

// 合同类别下拉
export const ContractTypeSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.basedata.serviceType.getContractTypeDropdownList,
    nestedKey: 'list',
    keyPairs: ['key', 'shortName'],
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};

// 合同小类别下拉
export const ContractSubTypeSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.sale.contract.getSubEx,
    nestedKey: 'list',
    keyPairs: ['svcSubtypeId', 'svcSubtypeName'],
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};
//  合同小类别下拉 isdeleted部分不展示情况
export const ContractgetSubTypeSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.sale.contract.getSub,
    nestedKey: 'list',
    keyPairs: ['svcSubtypeId', 'svcSubtypeName'],
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};
// 合同小类所有类别下拉
export const ContractSubTypeAllSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.basedata.serviceType.listSub,
    nestedKey: 'list',
    keyPairs: ['svcSubtypeId', 'svcSubtypeName'],
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};

// 派单方，签单方下拉
export const AssignerProviderSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.emphiresep.subcontract.getSubcontractDropdownList,
    nestedKey: 'assignerProviderList',
    mergedKeys: ['assignerProviderList', 'assigneeProviderList', 'providerList'],
    keyPairs: ['key', 'shortName'],
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};

// 接单方下拉
export const AssigneeProviderSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.emphiresep.subcontract.getSubcontractDropdownList,
    nestedKey: 'assigneeProviderList',
    mergedKeys: ['assignerProviderList', 'assigneeProviderList', 'providerList'],
    keyPairs: ['key', 'shortName'],
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};

// 供应商下拉
export const ProviderSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.emphiresep.subcontract.getSubcontractDropdownList,
    mergedKeys: ['assignerProviderList', 'assigneeProviderList', 'providerList'],
    keyPairs: ['key', 'shortName'],
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};
// 产品类型维护产品类型下拉

export const ProductTypeSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.basedata.baseDataCls.getDorpDownList,
    nestedKey: 'list',
    keyPairs: ['key', 'shortName'],
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};

// 产品类型维护产品大类下拉
export const ProductCategorSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.basedata.baseDataCls.getDropDownList,
    nestedKey: 'list',
    keyPairs: ['key', 'shortName'],
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};

// 产品类型维护产品下拉
export const GetAllDropDownList: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.sale.productManage.getAllDropDownList,
    nestedKey: props.keyValue,
    keyPairs: ['key', 'shortName'],
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};

// 所属产品下拉 供应商下拉
export const GetProductSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.sale.productManage.initData,
    nestedKey: props.keyValue,
    keyPairs: ['key', 'shortName'],
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};

// 报价单
export const QuotationSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.emphiresep.subcontract.getQuotationByCustId,
    nestedKey: 'list',
    keyPairs: ['quotationId', 'quotationName'],
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};

// 报价单编号选择
// {"key":"165021688","shortName":"中国软件代理报价单","name":"Q-20161128-0023","reserveName1":"46.85"}
export const QuotationCodeSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.sale.quotation.getQuotationDropdownList,
    nestedKey: 'list',
    keyPairs: ['name', 'name'],
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};

// 账单模板下拉列表-客户
export const CustBillTempSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.emphiresep.receivable.getReceivableTemplateDropdownList,
    nestedKey: 'list',
    keyPairs: ['key', 'shortName'],
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};
//根据客户选择报价单
export const QuotationCustIdSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.sale.quotation.getQuotationDropdownList,
    nestedKey: 'list',
    keyPairs: ['key', 'shortName'],
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};
// 账单模板下拉列表-帐单方
export const CustBillTempPayerSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.emphiresep.receivable.getReceivableTemplateDropdownList,
    nestedKey: 'list',
    keyPairs: ['key', 'reserveName1'],
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};

// 账单模板下拉列表-收款方
export const CustBillTempBillRecieverSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.emphiresep.receivable.getReceivableTemplateDropdownList,
    nestedKey: 'list',
    keyPairs: ['key', 'reserveName2'],
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};

// 账单频率下拉下拉列表
export const ReceivableFrequencySelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.emphiresep.receivable.getReceivableFrequencyDropdownList,
    nestedKey: 'list',
    keyPairs: ['frequencyId', 'frequencyName'],
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};

// 报价单模板所需产品类型下拉
export const QuotationProductSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.sale.quotationTempl.getProductTypeDropDownList,
    nestedKey: 'productTypeComboList',
    keyPairs: ['key', 'shortName'],
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};
// 产品下拉框时改变列表的选项
export const ProductListSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.emphiresep.receivable.getProductDropdownListBySSGroup,
    nestedKey: 'list',
    keyPairs: ['key', 'shortName'],
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};
// 报价单商保产品
export const QuotationProviderSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.sale.productManage.initData,
    nestedKey: 'productList',
    keyPairs: ['key', 'shortName'],
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};

// 订单内分公司下拉
export const OrderBranchSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.commons.common.queryBranch,
    fixedParams: { pageNum: 1, pageSize: 99999, departmentGrade: 3 },
    nestedKey: 'list',
    keyPairs: ['GOVERNINGBRANCHID', 'GOVERNINGBRANCH'],
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};

// 订单内根据城市获取人员分类
export const OrderPersonTypeSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.client.clientHireSep.getPersonTypeDropdownList,
    nestedKey: 'list',
    keyPairs: ['key', 'shortName'],
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};

// 业务大小类所属类型
export const BusinessTypeSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.basedata.baseDataCls.getDropDownList,
    nestedKey: 'list',
    keyPairs: ['key', 'shortName'],
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};
// 导入类型
export const ImportTypeSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.commons.impRule.sel,
    nestedKey: 'list',
    keyPairs: ['key', 'shortName'],
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};

// 关联城市获取社保组-veni
export const BusinessSsGroupSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.basedata.businessType.getSsGroupDropdownList,
    nestedKey: 'list',
    showSearch: true,
    keyPairs: ['key', 'shortName'],
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};

// 业务大小类所属城市-veni
export const BusinessCityGroupSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.basedata.businessType.getCityDropdownList,
    nestedKey: 'list',
    showSearch: true,
    keyPairs: ['key', 'shortName'],
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};

// 报价单第三方供应商名称
export const ProviderDropSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.sale.quotationTempl.getProviderDropDownList,
    nestedKey: 'providerComboList',
    fixedParams: { departmentGrade: 3, providerType: 2 },
    keyPairs: ['key', 'shortName'],
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};
export const ThirdPartyProviderSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const service = API.sale.quotationTempl.getQuotationTemplTrdCost;
  const carrayOn: BaseSelectorsProps<number> = {
    service: { ...service },
    nestedKey: 'list',
    showSearch: true,
    keyPairs: ['providerId', 'providerName'],
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};
// 销售所需分公司 大区等等下拉
export const SalesBranchSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.sale.contract.initData,
    showSearch: true,
    nestedKey: props.keyValue,
    keyPairs: ['key', 'shortName'],
    mergedKeys: ['branchModel', 'regionModel'],
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};

export const ServiceTypeSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.basedata.baseDataCls.getDropDownList,
    fixedParams: { type: '777', statementName: 'baseData.baseDataDropDownListByMap' },
    nestedKey: 'list',
    keyType: 'number',
    keyPairs: ['key', 'shortName'],
  };
  return <BaseSelectors<number> paramsData={{ type: '777' }} allowClear {...props} {...carrayOn} />;
};

// 通过城市获取社保公积金列表
export const SSGroupListByDepartment: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.welfaremanage.socialSecurity.querySsGroupDropdownList,
    nestedKey: 'list',
    keyPairs: ['key', 'shortName'],
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};

// 根据部门获取签单方公司抬头分公司抬头下拉 这个好像没用
export const BranchTitleSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    // service: API.crm.contractManage.getSignBranchTitleByDepartmentId,
    service: API.basedata.baseDataCls.getDorpDownList,
    nestedKey: 'list',
    showSearch: true,
    keyPairs: ['key', 'shortName'],
    fixedParams: { statementName: 'contractManage.getSignBranchTitleByDepartmentId' },
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};

// 根据部门获取签单方公司抬头分公司抬头下拉，新版本。为了保留老Hro而新开的接口。
export const BranchTitleSpeSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.basedata.baseDataCls.getDorpDownList,
    nestedKey: 'list',
    showSearch: true,
    keyPairs: ['key', 'shortName'],
    fixedParams: { statementName: 'contractManage.getSignBranchTitleSpeByDepartmentId' },
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};
// 基础数据信息
export const BaseInfoSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.commons.common.getBaseDataInfoByCode,
    nestedKey: 'list',
    keyPairs: ['key', 'shortName'],
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};
// 业务大类下拉
export const BusinessBigSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.basedata.businessType.getSupTypeDropdownList,
    nestedKey: 'list',
    showSearch: true,
    keyPairs: ['key', 'shortName'],
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};
//根据业务类型获取业务大类下拉
export const BusTypeDropdownListSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.welfaremanage.ebmtransact.getBusTypeDropdownList,
    nestedKey: 'list',
    showSearch: true,
    keyPairs: ['key', 'shortName'],
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};
//根据业务类型获取业务大类下拉
export const BusinessBigByBusTypeSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.welfaremanage.ebmBusinessConfig.getBusContentDropdownList,
    nestedKey: 'list',
    showSearch: true,
    keyPairs: ['key', 'shortName'],
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};
//福利待遇办理根据业务类型获取业务大类下拉
export const FringeBenefitsListSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.information.fringeBenefits.getBusTypeDropdownList,
    showSearch: true,
    keyPairs: ['key', 'shortName'],
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};
// 业务小类下拉
export const BusinessSmallSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.basedata.businessType.getSubTypeDropdownList,
    nestedKey: 'list',
    showSearch: true,
    keyPairs: ['key', 'shortName'],
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};
// 公共下拉 传type获取不同下拉数据
export const CommonBaseDataSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.basedata.baseDataCls.getDropDownList,
    nestedKey: 'list',
    keyPairs: ['key', 'shortName'],
  };
  return (
    <BaseSelectors<number> paramsData={{ ...props.params }} allowClear {...props} {...carrayOn} />
  );
};

// 根据cityId 取服务网点 -veni
export const GetServicePointSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.information.singlePolicy.getServicePoint,
    keyPairs: ['key', 'shortName'],
    nestedKey: 'object',
    // mergedKeys: ['branchList', 'personCategoryList'],
    showSearch: true,
  };
  return <BaseSelectors<number> {...props} {...carrayOn} />;
};

// 根据cityId 取人员类别 -veni
export const GetPersonCategoryListSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.combo.comboHro.queryPersonCategoryList,
    keyPairs: ['key', 'shortName'],
    nestedKey: 'personCategoryList',
    mergedKeys: ['branchList', 'personCategoryList'],

    showSearch: true,
  };
  return <BaseSelectors<number> {...props} {...carrayOn} />;
};

// 公积金，补充公积金比例id -veni
export const GetProductRatioSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.information.singlePolicy.getProductRatio,
    keyPairs: ['key', 'shortName'],
    nestedKey: 'object',
    showSearch: true,
  };
  return <BaseSelectors<number> {...props} {...carrayOn} />;
};

// 客户账套
export const GetCustmerBillTempSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.basedata.baseDataCls.getDorpDownList,
    keyPairs: ['key', 'shortName'],
    nestedKey: 'list',
    showSearch: true,
  };
  return <BaseSelectors<number> {...props} {...carrayOn} />;
};

// 根据当前登录用户，获取客户
export const GetCustmerByCurUser: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.report.reportFinance.getBPXDropdownListByUserId,
    keyPairs: ['key', 'shortName'],
    nestedKey: 'list',
    showSearch: true,
  };
  return <BaseSelectors<number> {...props} {...carrayOn} />;
};

// 通用getDorpDownList with statementName
export const CommonDropSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.basedata.baseDataCls.getDorpDownList,
    keyPairs: ['key', 'shortName'],
    nestedKey: 'list',
    showSearch: true,
  };
  return <BaseSelectors<number> {...props} {...carrayOn} />;
};

// 财务管理 大区下拉
export const AreaBranchDataSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.report.inDecMember.initAreaBranchData,
    keyPairs: ['key', 'shortName'],
    nestedKey: 'areaList',
    showSearch: true,
  };
  return <BaseSelectors<number> {...props} {...carrayOn} />;
};
// 账单 大区下拉
export const BillDataSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.report.inDecMember.initAreaBranchData,
    keyPairs: ['key', 'shortName'],
    fixedParams: { providerType: '1' },
    nestedKey: 'areaList',
    showSearch: true,
  };
  return <BaseSelectors<number> {...props} {...carrayOn} />;
};
// 财务管理 区域下拉
export const QueryBranchSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.commons.common.queryBranch,
    fixedParams: { pageNum: 1, pageSize: 99999, departmentGrade: 3 },
    nestedKey: 'list',
    keyPairs: ['GOVERNINGBRANCHID', 'GOVERNINGBRANCH'],
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};

// 获取优选网点
export const FirstServicePointCitySelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.information.singlePolicy.getFirstServicePointCity,
    keyPairs: ['key', 'shortName'],
    nestedKey: 'object',
    showSearch: true,
  };
  return <BaseSelectors<number> {...props} {...carrayOn} />;
};

// 客户付款方
export const CustPayerDorpDownListSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.finance.invoiceMain.getCustPayerDorpDownList,
    keyPairs: ['key', 'shortName'],
    showSearch: true,
  };
  return <BaseSelectors<number> paramsData={props.params} {...props} {...carrayOn} />;
};
//获取报价单审批下拉
export const QuotationApproveStatusSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.sale.quotation.getApproveStatusEx,
    keyPairs: ['key', 'shortName'],
    showSearch: true,
    nestedKey: 'list',
  };
  return <BaseSelectors<number> {...props} {...carrayOn} />;
};

// 开票分公司
export const InvoiceDeptmentSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.basedata.baseDataCls.getDorpDownList,
    keyPairs: ['key', 'shortName'],
    showSearch: true,
    nestedKey: 'list',
  };
  return <BaseSelectors<number> {...props} {...carrayOn} />;
};
//离职原因
export const SepReasonDropdownListSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.emphiresep.employeeHireSep.getSepReasonDropdownList,
    keyPairs: ['key', 'shortName'],
    mergedKeys: [
      'sepReasonList',
      'reduceReasonList',
      'detailReasonCompetitorList',
      'detailReasonCustList',
      'detailReasonWithdrawalList',
      'detailReasonPassiveTerminationList',
    ],
    showSearch: true,
    // nestedKey: 'list',
  };
  return <BaseSelectors<number> {...props} {...carrayOn} />;
};

// 所在省份
export const GetProvinceelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.basedata.baseDataCls.getDorpDownList,
    keyPairs: ['key', 'shortName'],
    nestedKey: 'list',
    showSearch: true,
  };
  return <BaseSelectors<number> {...props} {...carrayOn} />;
};

// 基础数据选择
export const GetBaseSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.basedata.baseDataCls.getDorpDownList,
    keyPairs: ['key', 'shortName'],
    nestedKey: 'list',
    showSearch: true,
  };
  return <BaseSelectors<number> {...props} {...carrayOn} />;
};

// 竞争对手
export const CompetitorSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.basedata.competitor.getCompetitorDropdownList,
    keyPairs: ['key', 'shortName'],
    nestedKey: 'list',
    showSearch: true,
  };
  return <BaseSelectors<number> {...props} {...carrayOn} />;
};

// 订单内部部分下拉数据
export const GetEmployeeHireSepDropdownListSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.emphiresep.employeeHireSep.getEmployeeHireSepDropdownList,
    keyPairs: ['key', 'shortName'],
    mergedKeys: ['addReasonList', 'sepReasonList', 'reduceReasonList'],
    showSearch: true,
  };
  return <BaseSelectors<number> {...props} {...carrayOn} />;
};

// 员工详情页面所有下拉
export const EmployeeFilesSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.admin.department.getAllDropDownList,
    keyPairs: ['key', 'shortName'],
    mergedKeys: [
      'genderList',
      'politicalStatusList',
      'ethnicList',
      'eduLevelList',
      'hukouList',
      'marriageList',
      'healthList',
      'disableList',
      'medInsurList',
      'yesNoList',
      'foreignLangList',
      'foreignLangLvList',
      'disabilityLevels',
      'disabledPeopleTypes',
      'subsidyAppStatuses',
      'professionalList',
      'workTypeList',
      'childrenStatusList',
    ],
    showSearch: true,
  };
  return <BaseSelectors<number> {...props} {...carrayOn} />;
};

// 银行卡信息页面 业务类型 银行下拉
export const EmpBankCardSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.emphiresep.empBankCard.initData,
    keyPairs: ['key', 'shortName'],
    mergedKeys: ['bankModel', 'busiTypeModel'],
    showSearch: true,
  };
  return <BaseSelectors<number> {...props} {...carrayOn} />;
};

// 派单大区
export const AssignerAreaSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.admin.department.getDepartmentDropdownList,
    keyPairs: ['key', 'shortName'],
    showSearch: true,
    nestedKey: 'list',
    fixedParams: { departmentGrade: '2' },
  };
  return <BaseSelectors<number> {...props} {...carrayOn} />;
};

// 社保公积金组下拉
export const SSGroupSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.welfaremanage.ssCommon.getSSGroup,
    keyPairs: ['ssGroupId', 'insuranceName'],
    showSearch: true,
    nestedKey: 'list',
  };
  return <BaseSelectors<number> {...props} {...carrayOn} />;
};

// 福利办理方下拉
export const ForPartySelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.welfaremanage.ssCommon.selForPartyByMap,
    keyPairs: ['key', 'shortName'],
    showSearch: true,
    nestedKey: 'list',
  };
  return <BaseSelectors<number> {...props} {...carrayOn} />;
};
// 根据产品id获取产品比例下拉
export const QueryProductRatioByProductIdSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.welfaremanage.socialSecurity.queryProductRatioByProductId,
    keyPairs: ['productRatioId', 'productRatioName'],
    showSearch: true,
    nestedKey: 'list',
  };
  return <BaseSelectors<number> {...props} {...carrayOn} />;
};
// 供应商报价单下拉 getExternalQuotationDropdownList
export const ExternalQuotationSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.externalsupplier.externalEmpHireSep.getExternalQuotationDropdownList,
    keyPairs: ['key', 'shortName'],
    showSearch: true,
  };
  return <BaseSelectors<number> {...props} {...carrayOn} />;
};
// 报表 服务产品
export const ServiceProductDropdownList: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.report.inDecMember.getServiceProductDropdownList,
    fixedParams: { rptType: '1' },
    keyPairs: ['key', 'shortName'],
    nestedKey: 'list',
    showSearch: true,
  };
  return <BaseSelectors<number> {...props} {...carrayOn} />;
};
// 填写原因下拉数据
export const ReasonTypeList: React.FC<BaseDataSelectorsProps> = (props: BaseDataSelectorsProps) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.crm.debtReminder.getReasonTypeList,
    keyPairs: ['key', 'shortName'],
    showSearch: true,
    nestedKey: 'list',
  };
  return <BaseSelectors<number> {...props} {...carrayOn} />;
};

// 城市选择2
export const City2Selector: React.FC<BaseDataSelectorsProps> = (props: BaseDataSelectorsProps) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.basedata.city.getDropDownList,
    keyPairs: ['key', 'shortName'],
    showSearch: true,
    filterKeys: ['pinYinCode'],
  };
  return <BaseSelectors<string> {...props} {...carrayOn} />;
};
// 省份城市联动选择
export const City3Selector: React.FC<BaseDataSelectorsProps> = (props: BaseDataSelectorsProps) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.basedata.city.cityDropDownList,
    // service: API.basedata.city.cityDropDownListParm,
    keyPairs: ['key', 'shortName'],
    showSearch: true,
    filterKeys: ['pinYinCode'],
  };
  return <BaseSelectors<string> {...props} {...carrayOn} />;
};

// 省份城市联动
export const City4Selector: React.FC<BaseDataSelectorsProps> = (props: BaseDataSelectorsProps) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.basedata.city.queryCityList,
    // service: API.basedata.city.cityDropDownListParm,
    keyPairs: ['cityId', 'cityName'],
    showSearch: true,
    filterKeys: ['pinYinCode'],
    nestedKey: 'list',
  };
  return <BaseSelectors<string> {...props} {...carrayOn} />;
};

export const AllLaborRelationUsersComboList: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.welfaremanage.laborContract.getAllDropDownList,
    keyPairs: ['key', 'shortName'],
    showSearch: true,
    nestedKey: 'allLaborRelationUsersComboList',
  };
  return <BaseSelectors<number> {...props} {...carrayOn} />;
};

export const ContracdByCustSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<string> = {
    service: API.sale.contract.getContractIdByCust,
    keyPairs: ['key', 'shortName'],
    showSearch: true,
    nestedKey: 'list',
  };
  return <BaseSelectors<string> {...props} {...carrayOn} />;
};

// 所有有效角色
export const RolesSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
  ref: TRef,
) => {
  const carrayOn: BaseSelectorsProps<string> = {
    service: API.workflow.workFlowFactory.getValidRole,
    keyPairs: ['roleId', 'roleName'],
    showSearch: true,
    nestedKey: 'list',
  };
  return <BaseSelectors<string> {...props} {...carrayOn} />;
};
// 入职分类 -veni
export const HireSvcSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.emphiresep.hireClassify.getDorpDownList,
    keyPairs: ['key', 'shortName'],
    showSearch: true,
    nestedKey: 'list',
  };
  return <BaseSelectors<number> {...props} {...carrayOn} />;
};

// 获取类别-veni
export const MultiteDictSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.commons.common.getMultiteDictDataByCodes,
    keyPairs: ['key', 'shortName'],
    showSearch: true,
    nestedKey: 'data0',
  };
  return <BaseSelectors<number> {...props} {...carrayOn} />;
};
//对应薪资项目名称
export const PureDataInterfaceSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.payroll.wageDataInterface.getAllDropDownList,
    keyPairs: ['key', 'shortName'],
    showSearch: true,
    nestedKey: 'itemComboList',
  };
  return <BaseSelectors<number> {...props} {...carrayOn} />;
};
// 获取类别-veni
// export const EbmtransactBusTypeSelector: React.FC<BaseDataSelectorsProps> = (
//   props: BaseDataSelectorsProps,
// ) => {
//   const carrayOn: BaseSelectorsProps<number> = {
//     service: API.welfaremanage.ebmtransact.getBusTypeDropdownList,
//     keyPairs: ['key', 'shortName'],
//     showSearch: true,
//     nestedKey: 'list',
//   };
//   return <BaseSelectors<number> {...props} {...carrayOn} />;
// };

// 薪资档案下拉
export const PayRollArchivesSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.payroll.payRollArchives.getAllDropDownList,
    keyPairs: ['key', 'shortName'],
    mergedKeys: [
      'taxRateComboList',
      'receivableTemplateComboList',
      'bankList',
      'payRollFormatList',
      'subContractComboList',
      'vatrList',
      'languageTypeList',
    ],
    showSearch: true,
  };
  return <BaseSelectors<number> {...props} {...carrayOn} />;
};

// 薪资类别账单模板
export const ReceivableTemplateComboListSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.payroll.payRollClass.getReceivableTemplateComboListById,
    keyPairs: ['key', 'shortName'],
    showSearch: true,
    nestedKey: 'receivableTemplateComboList',
  };
  return <BaseSelectors<number> {...props} {...carrayOn} />;
};

// 薪资档案账单模板
export const ReceivableTemplateComboSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.payroll.payRollArchives.getReceivableTemplateComboListById,
    keyPairs: ['key', 'shortName'],
    showSearch: true,
    nestedKey: 'receivableTemplateComboList',
  };
  return <BaseSelectors<number> {...props} {...carrayOn} />;
};

// 薪资档案账单模板2全部修改时使用
export const AllReceivableTemplateComboSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.payroll.payRollArchives.getAllReceivableTemplateComboListById,
    keyPairs: ['key', 'shortName'],
    showSearch: true,
    nestedKey: 'receivableTemplateComboList',
  };
  return <BaseSelectors<number> {...props} {...carrayOn} />;
};

// 薪资档案小合同
export const SubContractInClassSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.payroll.payRollArchives.getSubContractInClassList,
    keyPairs: ['key', 'shortName'],
    showSearch: true,
    nestedKey: 'subContractComboList',
  };
  return <BaseSelectors<number> {...props} {...carrayOn} />;
};

// 从业类型
export const EmpTypeSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.payroll.checkEmpType.getAllEmpTypeList,
    keyPairs: ['key', 'shortName'],
    showSearch: true,
  };
  return <BaseSelectors<number> {...props} {...carrayOn} />;
};

//API.payroll.payBatch.getApplyTitleByPayAddress//根据支付地查询支付地抬头数据
// 原码中 "baseServiceCLS","getDorpDownList" . "wgSendBatch.getApplyTitleByPayAddress" 适用于此接口。
export const GetApplyTitleByPayAddress: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const service = API.payroll.payBatch.getApplyTitleByPayAddress;
  const carrayOn: BaseSelectorsProps<number> = {
    // service: API.payroll.payBatch.getApplyTitleByPayAddress,
    service: { ...service, cacheKey: service.cacheKey + props.key || '0' },
    keyPairs: ['key', 'shortName'],
    showSearch: true,
    nestedKey: 'list',
  };
  return <BaseSelectors<number> {...props} {...carrayOn} />;
};

// 社保套餐内选择内容下拉
export const ComboManageDropDownSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.combo.comboHro.queryPersonCategoryList,
    keyPairs: ['key', 'shortName'],
    mergedKeys: ['personCategoryList', 'branchList'],
    showSearch: true,
  };
  return <BaseSelectors<number> {...props} {...carrayOn} />;
};

/**
        * 社保公积金缴纳主体 记录对应分公司的城市，相同省下的所有有效的分公司下签约分公司抬头记录。
社保公积金缴纳主体 记录对应分公司的城市，相同省下的所有有效的分公司下签约分公司抬头记录。
        * /threeToOne/branch/getApplyTitleByPayAddress
        */

export const ThreeToOneBranchDropDownSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.basedata.threeToOneRule.getPolicyDropdownList,
    keyPairs: ['key', 'shortName'],
    showSearch: true,
    nestedKey: 'list',
  };
  return <BaseSelectors<number> {...props} {...carrayOn} />;
};

//小工具税率表
export const RateSelector: React.FC<BaseDataSelectorsProps> = (props: BaseDataSelectorsProps) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.commons.idNumCheck.getTaxRateDropDownList,
    nestedKey: 'taxRateComboList',
    showSearch: true,
    keyPairs: ['key', 'shortName'],
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};
//小工具增值税率
export const AddRateSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.basedata.baseDataCls.getDropDownList,
    nestedKey: 'list',
    showSearch: true,
    fixedParams: { type: '058' },
    keyPairs: ['key', 'shortName'],
  };
  return <BaseSelectors<number> paramsData={{ type: '058' }} allowClear {...props} {...carrayOn} />;
};

// 金碟分公司税率
export const JindieRateSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.basedata.baseDataCls.list,
    nestedKey: 'list',
    showSearch: true,
    fixedParams: { type: '614' },
    keyPairs: ['baseDataCode', 'baseDataName'],
  };
  return <BaseSelectors<number> paramsData={{ type: '614' }} allowClear {...props} {...carrayOn} />;
};

/**
 * 附言的下拉框
 * @param props
 */
export const PostscriptSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.payroll.payBatch.getBaseDataByType,
    nestedKey: 'list',
    showSearch: true,
    fixedParams: { type: '611' },
    keyPairs: ['key', 'shortName'],
  };
  return <BaseSelectors<number> paramsData={{ type: '611' }} allowClear {...props} {...carrayOn} />;
};

// 银行编码
export const CustPayerSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.externalsupplier.externalSupplier.getCustPayerDorpDownList,
    showSearch: true,
    keyPairs: ['key', 'shortName'],
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};

// 薪资档案账号
export const EmpBankCardInClassSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.payroll.payRollArchives.getEmpBankCardInClassList,
    keyPairs: ['key', 'shortName'],
    nestedKey: 'empBankCardComboList',
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};

// 金税涉税事由下拉列表
export const TaxReasonSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.payroll.goldenTaxDropDownList.getTaxReasonDropDownList,
    nestedKey: 'list',
    showSearch: true,
    keyPairs: ['key', 'shortName'],
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};

//第三方电子协议
export const ThirdEleProtrocal: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.payroll.taxPay.getSuperOrgDropDownList,
    nestedKey: 'list',
    showSearch: true,
    keyPairs: ['key', 'shortName'],
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};
// 社保组所属城市
export const IncludCitySelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.combo.comboHro.queryIncludCity,
    keyPairs: ['key', 'shortName'],
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};

// 合同版本名称
export const SignProcessSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.welfaremanage.laborContract.querySignProcessBySubcontractId,
    keyPairs: ['signProcessId', 'signProcessName'],
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};

export const SignProcessSelectorByCustId: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.welfaremanage.laborContract.querySignProcess,
    keyPairs: ['signProcessId', 'signProcessName'],
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};

// 法人单位名称
export const CorporationSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.welfaremanage.laborContract.queryCorporationBySubcontractId,
    keyPairs: ['corporationId', 'corporationName'],
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};
// 业务项目
export const GetBusnameClassDropdownList: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.information.fringeBenefits.getBusnameClassDropdownList,
    keyPairs: ['key', 'shortName'],
    // mergedKeys: ['personCategoryList', 'branchList'],
    showSearch: true,
  };
  return <BaseSelectors<number> {...props} {...carrayOn} />;
};
// 业务项目
export const BusinessItemSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.information.fringeBenefits.getBusnameClassDropdownList,
    keyPairs: ['key', 'shortName'],
    // mergedKeys: ['personCategoryList', 'branchList'],
    showSearch: true,
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};
// 信息公开业务大类
export const GetBusTypeDropdownList: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.information.fringeBenefits.getBusTypeDropdownList,
    keyPairs: ['key', 'shortName'],
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};
// 信息公开业务小类
export const GetBusSubtypeDropdownList: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.information.fringeBenefits.getBusSubtypeDropdownList,
    keyPairs: ['key', 'shortName'],
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};
// 支付地抬头list
export const DeptTitleSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.basedata.sysBranchTitle.listPage,
    keyPairs: ['titleId', 'titleName'],
    nestedKey: 'list',
    showSearch: true,
    fixedParams: { isValid: '1' },
  };
  return <BaseSelectors<number> {...props} {...carrayOn} />;
};
// 基础数据业务项目
export const GetBaseBusnameClassDropdownList: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.basedata.businessType.getBusnameClassList,
    keyPairs: ['busnameClassId', 'busnameClassName'],
    nestedKey: 'list',
    // mergedKeys: ['personCategoryList', 'branchList'],
    showSearch: true,
  };
  return <BaseSelectors<number> {...props} {...carrayOn} />;
};
// 基础数据业务大类
export const GetBaseBusTypeDropdownList: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.basedata.businessType.getBusnameTypeList,
    keyPairs: ['busnameTypeId', 'busnameTypeName'],
    nestedKey: 'list',
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};
// 基础数据业务小类
export const GetBaseBusSubtypeDropdownList: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.basedata.businessType.getBusnameSubtypeList,
    keyPairs: ['busnameSubtypeId', 'busnameSubtypeName'],
    nestedKey: 'list',
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};

// 集团下客户
export const CustByGroupList: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.commons.common.getCustomer,
    keyPairs: ['custId', 'custName'],
    nestedKey: 'list',
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};

// 客户薪资类别下拉
export const GetPayRollClassList: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.payroll.payRollClass.getPublicPayRollClassList,
    keyPairs: ['wageClassId', 'wageClassName'],
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};

// 纯代发导入接口列表
export const GetUploadinterfaceList: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.payroll.eosWageUpload.queryUploadinterfaceList,
    keyPairs: ['wageInterfaceId', 'interfaceName'],
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};

// 发薪城市选择
export const SalaryCitySelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.payroll.eosWageUpload.selCityBywi,
    keyPairs: ['key', 'shortName'],
    showSearch: true,
  };
  return <BaseSelectors<string> {...props} {...carrayOn} />;
};

//离职材料版本名称

export const EcQuitAllSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.ec.quitTask.getEcQuitAll,
    keyPairs: ['materialSpId', 'materialSpIdName'],
    showSearch: true,
    nestedKey: 'list',
  };
  return <BaseSelectors<string> {...props} {...carrayOn} />;
};

//电子离职证明版本名称
export const SignProcessListForCertSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.ec.quitTask.getSignProcessDropdownListForCert,
    keyPairs: ['key', 'shortName'],
    showSearch: true,
  };
  return <BaseSelectors<string> {...props} {...carrayOn} />;
};

//离职材料版本名称
export const MaterialSpSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.ec.quitTask.getSignProcessDropdownList,
    keyPairs: ['signProcessId', 'signProcessName'],
    nestedKey: 'list',
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};
// 报离法人单位名称
export const CorporationSelectorList: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.ec.quitTask.getCorporationDropdownList,
    keyPairs: ['corporationId', 'corporationName'],
    nestedKey: 'list',
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};
//电子离职证明版本名称
export const QuitTaskTypeSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.ec.quitTask.getSignProcessDropdownListForCert,
    keyPairs: ['signProcessId', 'signProcessName'],
    nestedKey: 'list',
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};

//扣缴义务人
export const WithholdAgentSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.welfaremanage.ssCommon.selectWithholdAgent,
    keyPairs: ['key', 'shortName'],
    nestedKey: 'list',
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};

//客户付款方
export const CustPayerListSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.emphiresep.receivable.getReceivableUpdateDropdownList,
    keyPairs: ['key', 'shortName'],
    nestedKey: 'custPayerList',
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};

//派单地抬头
export const SignBranchTitleSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.payroll.payBatch.getSignBranchTitleSpeByDepartmentId,
    keyPairs: ['key', 'shortName'],
    nestedKey: 'list',
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};

// 政策3.0 模板标签下拉

export const QueryTemplateTitlesSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.information.policyTemplateInfo.getTemplateTitlesByScope,
    keyPairs: ['key', 'shortName'],
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};

// 内部分公司的时候 城市的下拉框选项

export const QueryInternalCitySelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.information.policyTemplateInfo.getInternalCityByBranchId,
    keyPairs: ['key', 'shortName'],
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};

// 查询政策层级下拉选择
export const QueryPolicyLevelSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.information.policyLevel.getPolicyLevelDropdownList,
    nestedKey: 'list',
    keyPairs: ['key', 'shortName'],
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};
// 政策标签下拉列表(所有)
export const QueryPolicyLabelSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.information.policyLabel.getAllPolicyLabelDropdownList,
    nestedKey: 'list',
    keyPairs: ['key', 'shortName'],
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};

// 政策标签下拉列表（生效）
export const QueryEffectPolicyLabelSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.information.policyLabel.getPolicyLabelDropdownList,
    nestedKey: 'list',
    keyPairs: ['key', 'shortName'],
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};

// 政策标题，关联业务名称下拉选择
export const BusnameClassSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.information.policyLevel.getBusnameClassDropdownList,
    nestedKey: 'list',
    keyPairs: ['key', 'shortName'],
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};

/**
 * 缴费实体下拉选择
 */
export const CustPayEntitySelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.emphiresep.transfer.getCustPayEntityDropdownList,
    nestedKey: 'list',
    keyPairs: ['key', 'shortName'],
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};

export const CustPayEntityExSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.emphiresep.transfer.getCustPayEntityDropdownListEx,
    nestedKey: 'list',
    keyPairs: ['key', 'shortName'],
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};

/**
 * 社保套餐下拉选择
 */
export const ComboSelector: React.FC<BaseDataSelectorsProps> = (props: BaseDataSelectorsProps) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.emphiresep.transfer.getComboDropdownList,
    nestedKey: 'list',
    keyPairs: ['key', 'shortName'],
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};

// 根据集团GroupId获取客户列表
export const CustomerByGroupIdSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.sale.groupCompany.getCustomerByGroupId,
    keyPairs: ['custId', 'custName'],
    showSearch: true,
    nestedKey: 'list',
  };
  return <BaseSelectors<number> {...props} {...carrayOn} />;
};

/**
 * 业务项目，过滤大类引用的数据
 */
export const BusGetBusnameClassDropdownList: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.basedata.businessType.geBusnameClassDropdownList,
    nestedKey: 'list',
    keyPairs: ['key', 'shortName'],
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};

/**
 * 根据项目名称选择大类
 */
export const GetBusTypeByBusnameClassIdList: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.basedata.businessType.getBusTypeDropdownList,
    nestedKey: 'list',
    keyPairs: ['key', 'shortName'],
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};
// 省份政策维护选择
export const ProvincePolicySelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.information.policyTemplateInfo.getProvinceDropdownList,
    keyPairs: ['key', 'shortName'],
    showSearch: true,
  };
  return <BaseSelectors<number> {...props} {...carrayOn} />;
};
// 特区政策维护选择
export const SpecialAreaPolicySelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.information.policySpecialArea.getDropdownListByBranchId,
    keyPairs: ['key', 'shortName'],
    showSearch: true,
  };
  return <BaseSelectors<number> {...props} {...carrayOn} />;
};
// 所有特区(有效无效)政策维护选择
export const SpecialAreaPolicySelector1: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.information.policySpecialArea.getDropdownList,
    keyPairs: ['key', 'shortName'],
    showSearch: true,
  };
  return <BaseSelectors<number> {...props} {...carrayOn} />;
};
// 供应商网点城市(所在城市没有内部分公司)
export const WithoutInternalCitySelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.information.policyTemplateInfo.getWithoutInternalCity,
    keyPairs: ['key', 'shortName'],
    showSearch: true,
    filterKeys: ['pinYinCode'],
  };
  return <BaseSelectors<string> {...props} {...carrayOn} />;
};

// 签章材料名称 第三方盖章名称取中文
export const EleSignProcessSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.ec.quitTask.getEleSignProcessDropdownList,
    keyPairs: ['signProcessId', 'signProcessName'],
    showSearch: true,
    nestedKey: 'list',
  };
  return <BaseSelectors<number> {...props} {...carrayOn} />;
};

// 签章材料名称不限制法人单位名称
export const GetAllEleSignProcessSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.ec.quitTask.getAllEleSignProcessDropdownList,
    keyPairs: ['signProcessId', 'signProcessName'],
    showSearch: true,
    nestedKey: 'list',
  };
  return <BaseSelectors<number> {...props} {...carrayOn} />;
};

// 电子业务办理法人单位名称
export const EleCorporationSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.ec.quitTask.getEleCorporationDropdownList,
    keyPairs: ['corporationId', 'corporationName'],
    showSearch: true,
    nestedKey: 'list',
  };
  return <BaseSelectors<number> {...props} {...carrayOn} />;
};

/**
 * 政策大全3.0.1 服务类型下拉框
 * @param props
 */
export const ServiceTypeSelector1: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.basedata.baseDataCls.getDorpDownList,
    fixedParams: { type: '1004', statementName: 'baseData.baseDataDropDownListByMap' },
    nestedKey: 'list',
    showSearch: true,
    keyPairs: ['key', 'shortName'],
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};

// 社保对应组织
export const QueryOrgDownSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.welfaremanage.rpaTaxation.queryOrgDown,
    nestedKey: 'list',
    showSearch: true,
    keyPairs: ['key', 'shortName'],
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};

// 大合同下拉
export const GetContractListSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.crm.contractManage.getContractList,
    nestedKey: 'list',
    showSearch: true,
    keyPairs: ['contractId', 'contractName'],
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};

export const GetBaseDataByTypeAndIsValidSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.basedata2.baseDataCls.getDropDownList,
    nestedKey: 'list',
    showSearch: true,
    keyPairs: ['key', 'shortName'],
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};

// 派遣单位名称 客户付款方下拉
export const GetCustPayerDropdownListSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.sale.customerPayer.getCustPayerDropdownList,
    nestedKey: 'list',
    showSearch: true,
    keyPairs: ['custPayerId', 'payerName'],
  };
  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};

// 根据sendId获得账单模板的下拉菜单
export const GetReceivableTemplateDropDownListSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.payroll.payBatch.getReceivableTemplateDropDownListById,
    keyPairs: ['key', 'shortName'],
    showSearch: true,
    nestedKey: 'receivableTemplateComboList',
  };

  return <BaseSelectors<number> allowClear {...props} {...carrayOn} />;
};

// 社保缴纳主体选择内容下拉
export const ComboSsSubjectDropDownSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.combo.comboHro.getSSsubject,
    keyPairs: ['key', 'shortName'],
    showSearch: true,
  };
  return <BaseSelectors<number> {...props} {...carrayOn} />;
};

// 社保缴纳主体选择内容下拉
export const GetBusContentSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.welfaremanage.ebmBusiness.getBusContentDropdownList,
    keyPairs: ['busCityConfigId', 'busContent'],
    showSearch: true,
    nestedKey: 'list',
  };
  return <BaseSelectors<number> {...props} {...carrayOn} />;
};

export const GetEmpInfoSelector: React.FC<BaseDataSelectorsProps> = (
  props: BaseDataSelectorsProps,
) => {
  const carrayOn: BaseSelectorsProps<number> = {
    service: API.welfaremanage.ebmtransact.getEmpInfo,
    keyPairs: ['empCode', 'empName'],
    showSearch: true,
  };
  return <BaseSelectors<number> {...props} {...carrayOn} />;
};
