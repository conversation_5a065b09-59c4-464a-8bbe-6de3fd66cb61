declare namespace defs {
  export namespace crm {
    export class BeforeSaleReportQueryBean {
      /** add */
      add: boolean;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** cityId */
      cityId: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** custName */
      custName: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** endIndex */
      endIndex: number;

      /** 导入类型,扩充使用 */
      expType: string;

      /** expectedSignDateEd */
      expectedSignDateEd: string;

      /** expectedSignDateSt */
      expectedSignDateSt: string;

      /** expectedSignNumEd */
      expectedSignNumEd: string;

      /** expectedSignNumSt */
      expectedSignNumSt: string;

      /** expectedSignPriceEd */
      expectedSignPriceEd: string;

      /** expectedSignPriceSt */
      expectedSignPriceSt: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** 是否薪资查询 */
      isWageQuery: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** 页数 */
      pageNum: number;

      /** 每页记录数,默认65536条 */
      pageSize: number;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** productlineSalerAreaId */
      productlineSalerAreaId: string;

      /** productlineSalerProviderId */
      productlineSalerProviderId: string;

      /** productlineSalername */
      productlineSalername: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** startIndex */
      startIndex: number;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** trackDateEd */
      trackDateEd: string;

      /** trackDateSt */
      trackDateSt: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;

      /** visitStage */
      visitStage: string;
    }

    export class BusinessLogQuery {
      /** 批次 */
      batchId: string;

      /** custCode */
      custCode: string;

      /** 客户ID */
      custId: string;

      /** 客户原名称 */
      custName: string;

      /** ePropertyName */
      ePropertyName: string;

      /** endIndex */
      endIndex: number;

      /** executeDate */
      executeDate: string;

      /** executeDateEd */
      executeDateEd: string;

      /** executeDateSt */
      executeDateSt: string;

      /** expType */
      expType: string;

      /** exportType */
      exportType: string;

      /** 主键 */
      logId: string;

      /** newAreaId */
      newAreaId: string;

      /** newAreaText */
      newAreaText: string;

      /** newCityName */
      newCityName: string;

      /** newDeptName */
      newDeptName: string;

      /** newDeptPre */
      newDeptPre: string;

      /** newSaleId */
      newSaleId: string;

      /** 现销售 */
      newSaler: string;

      /** newexists */
      newexists: string;

      /** newgoverningBranch */
      newgoverningBranch: string;

      /** newgoverningBranchText */
      newgoverningBranchText: string;

      /** oldAreaId */
      oldAreaId: string;

      /** oldAreaText */
      oldAreaText: string;

      /** oldCityName */
      oldCityName: string;

      /** oldDeptName */
      oldDeptName: string;

      /** oldDeptPre */
      oldDeptPre: string;

      /** oldSaleId */
      oldSaleId: string;

      /** 原销售 */
      oldSaler: string;

      /** oldexists */
      oldexists: string;

      /** oldgoverningBranch */
      oldgoverningBranch: string;

      /** oldgoverningBranchText */
      oldgoverningBranchText: string;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** 产品线创建时间 */
      plCreatDt: string;

      /** 产品线修改时间 */
      plUpdateDt: string;

      /** 客户产品线主键ID */
      proKeyId: string;

      /** 产品线 */
      productLine: string;

      /** 产品线名称 */
      productName: string;

      /** realName */
      realName: string;

      /** 备注 */
      remark: string;

      /** startIndex */
      startIndex: number;

      /** 类型ID	  <p>	  101: 客户建立	  <p>	  102:客户删除	  <p>	  103:客户进入共享	  <p>	  104:正在跟进分配	  <p>	  105:客户名称修改	  <p>	  106:共享区分配	  <p>	  107:删除区分配	  <p>	  108:客户基本信息修改	  <P>	  109:市场活动预录入客户分配 */
      typeId: string;

      /** typeName */
      typeName: string;
    }

    export class CommonResponse<T0 = any> {
      /** bizCode */
      bizCode: number;

      /** code */
      code: number;

      /** data */
      data: T0;

      /** message */
      message: string;

      /** t */
      t: T0;
    }

    export class Contract {
      /** 流程节点ID */
      activityDefId: string;

      /** 审批步骤 */
      activityNameCn: string;

      /** activityNameEn */
      activityNameEn: string;

      /** activityStatus */
      activityStatus: string;

      /** add */
      add: boolean;

      /** 新增驳回原因list */
      addSlDisaReasonList: Array<defs.crm.FilterEntity>;

      /** 预付款比例 */
      advancePaymentRatio: string;

      /** 代收代付 */
      agentBusiness: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.AGEREED_AMT_RECEIVE_MON	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      agereedAmtReceiveMon: string;

      /** agreedPayDt */
      agreedPayDt: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.AGREED_WAGE_ARRIVE_DAY	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      agreedWageArriveDay: string;

      /** 非标合同审批单code */
      applyCode: string;

      /** approveDt */
      approveDt: string;

      /** approveDtEnd */
      approveDtEnd: string;

      /** approveDtStart */
      approveDtStart: string;

      /** 页面填写的审核意见 */
      approveOpinion: string;

      /** 合同审核相关的附件 */
      approveRelatedAttachment: string;

      /** 合同审核相关的附件name */
      approveRelatedAttachmentName: string;

      /** areaId */
      areaId: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.AREA_TYPE	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      areaType: string;

      /** areaTypeName */
      areaTypeName: string;

      /** attTypeDraftId */
      attTypeDraftId: string;

      /** attTypeDraftName */
      attTypeDraftName: string;

      /** attTypeLegalId */
      attTypeLegalId: string;

      /** attTypeLegalName */
      attTypeLegalName: string;

      /** 平均价格集合 */
      averageMoneys: string;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.BILL_DT	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      billDt: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** 城市 */
      cityId: string;

      /** cityName */
      cityName: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 提交时间 */
      commitTime: string;

      /** 签约人数集合 */
      compactNumbers: string;

      /** 竞争对手id */
      competitor: string;

      /** 客服竞争对手名称 */
      competitorName: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONFIRMD_WORK_FLOW	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      confirmdWorkFlow: string;

      /** 联系人手机 */
      contactCell: string;

      /** 联系人电话 */
      contactTel: string;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_AVG_AMT	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      contractAvgAmt: string;

      /** 合同类别 */
      contractCategery: string;

      /** isIssuingSalary */
      contractCategeryName: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_CODE	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      contractCode: string;

      /** 合同创建人 */
      contractCreateBy: string;

      /** 最终结束日期 */
      contractEndDate: string;

      /** 最终结束日期从 */
      contractEndDateFrom: string;

      /** 最终结束日期到 */
      contractEndDateTo: string;

      /** 合同最终结束日期类型 */
      contractEndDateType: string;

      /** 合同附件集合 */
      contractFileList: Array<defs.crm.ContractFile>;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_FILE_NAME	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      contractFileName: string;

      /** 合同附件备注 */
      contractFileRemark: string;

      /** 合同附件上传时间 */
      contractFileUploadDt: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_HEADCOUNT	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      contractHeadcount: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_ID	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      contractId: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_NAME	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      contractName: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_PART_A	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      contractPartA: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_PART_B	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      contractPartB: string;

      /** contractProductLineIds */
      contractProductLineIds: string;

      /** 合同关系标签 */
      contractRelationLabel: string;

      /** 合同关系标签 用于接收页面传入的参数 */
      contractRelationLabelList: Array<string>;

      /** 合同关系标签 */
      contractRelationLabelStr: string;

      /** 合同退休人员集合 */
      contractRetireeList: Array<defs.crm.ContractRetiree>;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_RETRIEVE_DT	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      contractRetrieveDt: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_START_DATE	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      contractStartDate: string;

      /** 合同启动时间止 */
      contractStartDateEnd: string;

      /** 合同启动日期起 */
      contractStartDateStart: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_STATUS	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      contractStatus: string;

      /** contractStatusName */
      contractStatusName: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_STOP_DATE	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      contractStopDate: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_STOP_REASON	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      contractStopReason: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_SUB_TYPE	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      contractSubType: string;

      /** 合同类别（子类）name */
      contractSubTypeName: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_STATE	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      contractSvcState: string;

      /** 合同状态集合 */
      contractSvcStateList: Array<string>;

      /** contractSvcStateName */
      contractSvcStateName: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_TEMPLATE_ID	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      contractTemplateId: string;

      /** contractTerminationDate */
      contractTerminationDate: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CONTRACT_TYPE	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      contractType: string;

      /** 合同类型名 */
      contractTypeName: string;

      /** 合同版本号 */
      contractVersion: string;

      /** 合同类别 */
      contractVersionType: string;

      /** isIssuingSalary */
      contractVersionTypeName: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** createByName */
      createByName: string;

      /** createByParty */
      createByParty: string;

      /** 创建日期 */
      createDt: string;

      /** createDtEnd */
      createDtEnd: string;

      /** createDtStart */
      createDtStart: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CREATE_TYPE	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      createType: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CREDIT_PERIOD	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      creditPeriod: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CS_APPROVAL	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      csApproval: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CS_APPROVAL_STATUS	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      csApprovalStatus: string;

      /** 客服联系人地址 */
      cstScAddress: string;

      /** 客服联系人手机 */
      cstScCall: string;

      /** 客服联系人 */
      cstScContact: string;

      /** 客服联系人邮件 */
      cstScEmail: string;

      /** 客服联系人电话 */
      cstScTel: string;

      /** 客服联系人传真 */
      cstScfax: string;

      /** 客服联系人职位 */
      cstScposition: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CURRENT_EXE_CONTRACT_ID	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      currentExeContractId: string;

      /** 现销售 */
      currentSales: string;

      /** 客户唯一号 */
      custCode: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CUST_ID	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      custId: string;

      /** 客户名称 */
      custName: string;

      /** 查询接口类型 传1为只查新签、续签，审批通过的大合同 */
      custPayerId: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.CUST_SEAL_DT	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      custSealDt: string;

      /** 客户显示编号 */
      custViewCode: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** customerSize */
      customerSize: string;

      /** defStatus */
      defStatus: string;

      /** del */
      del: boolean;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.DEPARTMENT_ID	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      departmentId: string;

      /** departmentName */
      departmentName: string;

      /** 驳回原因批次 */
      disaBatchId: string;

      /** draftRemark */
      draftRemark: string;

      /** 联系人邮件 */
      email: string;

      /** endIndex */
      endIndex: number;

      /** 是否增强型代理 */
      enhancedAgent: string;

      /** EOS账号停用 */
      eosStatus: string;

      /** estimateFirstBillDate */
      estimateFirstBillDate: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.ESTIMATED_HEADCOUNT	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      estimatedHeadcount: string;

      /** 执行成本 */
      executionCost: string;

      /** 导入类型,扩充使用 */
      expType: string;

      /** expectedIncrease */
      expectedIncrease: string;

      /** expectedIncreaseOld */
      expectedIncreaseOld: string;

      /** 联系人传真 */
      fax: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** 首次出账单的客户账单年月 */
      firstAccountMonth: string;

      /** 首次出账单时间(锁定时间) */
      firstBillDate: string;

      /** 首次大合同ID */
      firstContractId: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.FIRST_LEGAL_APPROVE_ID	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      firstLegalApproveId: string;

      /** 合同审批的首个法务name */
      firstLegalApproveName: string;

      /** 首次出账单的财务应收年月 */
      firstOughtMonth: string;

      /** firstWgApproveId */
      firstWgApproveId: string;

      /** 原销售所属大区 */
      formerGoverningArea: string;

      /** 原销售所属大区Name */
      formerGoverningAreaName: string;

      /** 原销售所属分公司 */
      formerGoverningBranch: string;

      /** 原销售所属分公司Name */
      formerGoverningBranchName: string;

      /** 原销售 */
      formerSales: string;

      /** 原销售名字 */
      formerSalesName: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.FURTURE_OPPORTUNITY	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      furtureOpportunity: string;

      /** 所属大区 */
      governingArea: string;

      /** private String contractSvcStateName; */
      governingAreaName: string;

      /** 所属分公司 */
      governingBranch: string;

      /** governingBranchName */
      governingBranchName: string;

      /** 毛利 */
      grossProfit: string;

      /** 集团公司编号 */
      groupId: string;

      /** 集团公司名称 */
      groupName: string;

      /** 是否有交接单 */
      hasTransferInfo: string;

      /** 人力资源联系人 */
      hrContract: string;

      /** fileId */
      importFileId: string;

      /** fileName */
      importFileName: string;

      /** inId */
      inId: string;

      /** 收入 */
      income: string;

      /** 内支金额 */
      internalMoney: string;

      /** invoiceMoney */
      invoiceMoney: string;

      /** NP-8564 */
      invoiceNum: string;

      /** 滞纳金比例是否为万分之五 1：是；0：否 */
      is5Per10000FineRate: string;

      /** 滞纳金比例是否为万分之五name */
      is5Per10000FineRateName: string;

      /** 是否有补充附件 */
      isAddedAttachment: string;

      /** 本次续签是否需要调整合同条款？is_adjust_renew_contract */
      isAdjustRenewContract: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.IS_ASSIGN	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      isAssign: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 是否要提交审核 */
      isCommitApprove: string;

      /** 是否为已有客户所推荐 1：是；0：否 */
      isCustRecommend: string;

      /** 是否为已有客户所推荐name */
      isCustRecommendName: string;

      /** isDefer */
      isDefer: string;

      /** isDeferName */
      isDeferName: string;

      /** 删除标记 */
      isDeleted: string;

      /** 是否开通EOS账号 */
      isEosAccount: string;

      /** 是否内支 */
      isInternalPayment: string;

      /** isIssuingSalary */
      isIssuingSalary: string;

      /** 是否加入包含具体金额的对等条款或赔偿性条款 1：是；0：否 */
      isJoinCompensation: string;

      /** 是否加入包含具体金额的对等条款或赔偿性条款name */
      isJoinCompensationName: string;

      /** 是否正常审批 */
      isNormalApprove: string;

      /** 质控计算结果是否为垫付 1：是；0：否 */
      isPaymentQAResult: string;

      /** 质控计算结果是否为垫付name */
      isPaymentQAResultName: string;

      /** 服务人数小于20人，是否季度付款 1：是；0：否 */
      isQuarterlyPaymentLess20: string;

      /** 服务人数小于20人，是否季度付款name */
      isQuarterlyPaymentLess20Name: string;

      /** 是否有赠送退休额度 */
      isRetQuotaGranted: string;

      /** 是否包含退休业务 */
      isRetirementBusiness: string;

      /** 是否包含退休业务name */
      isRetirementBusinessName: string;

      /** 是否抢单 */
      isRob: string;

      /** isSameInsur */
      isSameInsur: string;

      /** 是否集中一地投保name */
      isSameInsurName: string;

      /** isSecondaryDev */
      isSecondaryDev: string;

      /** isSecondaryDevName */
      isSecondaryDevName: string;

      /** 含差旅服务 */
      isTravelServices: string;

      /** 含差旅服务(展示) */
      isTravelServicesName: string;

      /** 是否薪资查询 */
      isWageQuery: string;

      /** 标签备注 */
      labelRemark: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.LEGAL_APPROVAL	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      legalApproval: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.LEGAL_APPROVAL_STATUS	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      legalApprovalStatus: string;

      /** legalRemark */
      legalRemark: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.ASSIGNER_PROVIDER	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      liabilityCs: string;

      /** 责任客服所属分公司id */
      liabilityCsDepartmentId: string;

      /** 责任客服名字 */
      liabilityCsName: string;

      /** 责任客服职员代码 */
      libilityCsCode: string;

      /** 会议记录id */
      meetingRecordId: string;

      /** 会议记录上传附件id */
      meetingRecordImportFileId: string;

      /** 会议记录上传附件名称 */
      meetingRecordImportFileName: string;

      /** memo */
      memo: string;

      /** 模拟人 */
      mimicBy: string;

      /** 范本修改版合同备注 */
      modelModifyVersionRemark: string;

      /** 新销售 */
      newSales: string;

      /** nextContractId */
      nextContractId: string;

      /** nextContractName */
      nextContractName: string;

      /** 下个法务 */
      nextLegalApproveId: string;

      /** noChange */
      noChange: boolean;

      /** 非标合同审批单:NON_STA_COCT_APPR_ID */
      nonStaCoctApprId: string;

      /** 非标标签 */
      nonStandardLabel: string;

      /** 非标标签集合 用于接收页面传入的参数 */
      nonStandardLabelList: Array<string>;

      /** 非标标签 */
      nonStandardLabelStr: string;

      /** 原合同编号 */
      oldContractCode: string;

      /** 最终结束日期 */
      oldContractEndDate: string;

      /** 原合同最终结束日期类型 */
      oldContractEndDateType: string;

      /** 合同版本号 */
      oldContractVersion: string;

      /** 标签备注 */
      oldLabelRemark: string;

      /** 原合同编号 */
      oldOldContractCode: string;

      /** 外包毛利率 */
      oldOutSourcingMargin: string;

      /** 外包毛利率审核人 */
      oldOutSourcingMarginApprover: string;

      /** 回款日 */
      oldPaymentDate: string;

      /** 服务订单数 */
      orderNumber: string;

      /** 外包毛利率 */
      outSourcingMargin: string;

      /** 外包毛利率审核人 */
      outSourcingMarginApprover: string;

      /** 页数 */
      pageNum: number;

      /** 每页记录数,默认65536条 */
      pageSize: number;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.PARENT_CONTRACT_ID	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      parentContractId: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.PAY_COLLECT_POINT	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      payCollectPoint: string;

      /** payMonth */
      payMonth: string;

      /** 缴费类型 */
      payType: string;

      /** 客户付款方id集合 */
      payerIds: string;

      /** 查询接口类型 传1为只查新签、续签，审批通过的大合同 */
      payerName: string;

      /** 回款日 */
      paymentDate: string;

      /** 回款日审核人 */
      paymentDateApprover: string;

      /** 付款方式 */
      paymentMode: string;

      /** 体检预估成本 */
      peExecutionCost: string;

      /** 体检毛利 */
      peGrossProfit: string;

      /** 体检收入 */
      peIncome: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.PREPAY_AMT	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      prepayAmt: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.PREPAY_APPROVAL	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      prepayApproval: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.PREPAY_APPROVAL_STATUS	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      prepayApprovalStatus: string;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** processDefId */
      processDefId: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.PROCESS_INSTANCE_ID	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      processInstanceId: string;

      /** productLineIdLogs */
      productLineIdLogs: string;

      /** 产品线id集合 */
      productLineIds: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.PROJECT_PLAN_REQUEST	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      projectPlanRequest: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.PROJECT_REMARK	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      projectRemark: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** providerType */
      providerType: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** QA审核意见 */
      qaApprove: string;

      /** 查询接口类型 传1为只查新签、续签，审批通过的大合同 */
      queryType: string;

      /** 报价单集合id */
      quoIds: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.RENEWED_CONTRACT_NUM	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      renewedContractNum: string;

      /** reportElEvacuatedDate */
      reportElEvacuatedDate: string;

      /** 客服反馈撤单时间 */
      reportEvacuatedDate: string;

      /** 客服撤单详细原因说明 */
      reportEvacuatedExplantion: string;

      /** 客服撤单原因分类 */
      reportEvacuatedReason: string;

      /** reportGlEvacuatedDate */
      reportGlEvacuatedDate: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 赠送退休数量 */
      retirementGiftCount: string;

      /** 回访历史内容 */
      returnVisitMemo: string;

      /** 最后回访人Id */
      returnVisitorId: string;

      /** 风险金比例% */
      riskPremiumRatio: string;

      /** 风险分担比例% */
      riskSharingRatio: string;

      /** roleCode */
      roleCode: string;

      /** 统计标志位 */
      salFlag: string;

      /** 新增存量标识 （手工） */
      salFlagManual: string;

      /** salFlagManualName */
      salFlagManualName: string;

      /** salFlagName */
      salFlagName: string;

      /** 客户对应销售及分公司 */
      saleAndBranchName: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.SALES_APPROVE	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      salesApprove: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.SALES_APPROVE_STATUS	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      salesApproveStatus: string;

      /** 现销售职员代码 */
      salesCode: string;

      /** 销售所在主部门 */
      salesDeptName: string;

      /** 销售名字 */
      salesName: string;

      /** 所属销售团队类型 */
      salesTeamType: string;

      /** 客服竞争对手优势 */
      sctScComAdvancetage: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.SEAL_APPROVE_STATUS	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      sealApproveStatus: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.SEAL_DT	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      sealDt: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.SEAL_OPINION	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      sealOpinion: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** signArea */
      signArea: string;

      /** 签约方公司抬头 */
      signBranchTitle: string;

      /** 签约方公司抬头id */
      signBranchTitleId: string;

      /** signBranchTitleIdAreaId */
      signBranchTitleIdAreaId: string;

      /** signBranchTitleIdBranchId */
      signBranchTitleIdBranchId: string;

      /** 新签标识（手工） */
      signFlagManual: string;

      /** 新签标识（手工）name */
      signFlagManualName: string;

      /** 签单分公司 */
      signProvider: string;

      /** 驳回原因list */
      slDisaReasonList: Array<defs.crm.FilterEntity>;

      /** startIndex */
      startIndex: number;

      /** 撤单原因 */
      stopReason: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.STOP_SVC_DT	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      stopSvcDt: string;

      /** 终止服务操作日期 查询条件：终止服务日期到 */
      stopSvcEndDt: string;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** 销售补充标志 0需要销售补充信息1审批 */
      supplyMark: string;

      /** 销售补充附件说明(历史) */
      supplyShow: string;

      /** 销售补充附件名称 */
      supplyShowFileName: string;

      /** 销售补充附件路径 */
      supplyShowFilePath: string;

      /** 销售补充附件说明(新增) */
      supplyShowNew: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.SVC_REGION	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      svcRegion: string;

      /** svcSubtypeName */
      svcSubtypeName: string;

      /** svcTypeName */
      svcTypeName: string;

      /** 税费 */
      tax: string;

      /** 总售价 */
      totalPrice: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.TRANFER_PROCESS_ID	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      tranferProcessId: string;

      /** 交接单id */
      transferId: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.TRANSFER_REMARK	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      transferRemark: string;

      /** 差旅服务费比例% */
      travelServicesRatio: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.UPLOAD_FILE_NAME	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      uploadFileName: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_CONTRACT.UPLOAD_URL	  	  ibatorgenerated Fri Aug 19 13:13:31 CST 2011 */
      uploadUrl: string;

      /** upt */
      upt: boolean;

      /** 修改驳回原因list */
      uptSlDisaReasonList: Array<defs.crm.FilterEntity>;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;

      /** 职场健康 预付款金额 */
      whAdvancePaymentAmt: string;

      /** 职场健康 预付款时间 */
      whAdvancePaymentDt: string;

      /** 提成销售 */
      whCommissionSale: string;

      /** 提成销售name */
      whCommissionSaleName: string;

      /** 职场健康 合同寄送地址 */
      whContractSendAddress: string;

      /** 职场健康 预计垫付时长（天） */
      whExpectedPrepayDay: string;

      /** 职场健康 尾款金额 */
      whFinalPaymentAmt: string;

      /** 职场健康 尾款时间 */
      whFinalPaymentDt: string;

      /** 职场健康 开票顺序 */
      whInvoiceOrder: string;

      /** 开票顺序name */
      whInvoiceOrderName: string;

      /** 职场健康 项目编号 */
      whItemCode: string;

      /** 职场健康 毛利率% */
      whMargin: string;

      /** 体检税率% */
      whPeRate: string;

      /** 职场健康 垫付备注 */
      whPrepayRemark: string;

      /** 职场健康 采购发票内容 */
      whPurchaseInvoiceContent: string;

      /** 职场健康 采购发票类型 */
      whPurchaseInvoiceType: string;

      /** 职场健康 采购发票类型name */
      whPurchaseInvoiceTypeName: string;

      /** 职场健康 返佣收入 */
      whRebateIncome: string;

      /** 职场健康 返佣税费 */
      whRebateTax: string;

      /** 职场健康 销售发票内容 */
      whSaleInvoiceContent: string;

      /** 职场健康 销售发票类型 */
      whSaleInvoiceType: string;

      /** 职场健康 销售发票类型name */
      whSaleInvoiceTypeName: string;

      /** 职场健康 支付供货商货款时间 */
      whSupplierPaymentDt: string;

      /** workitemId */
      workitemId: string;
    }

    export class ContractAttachmentDTO {
      /** add */
      add: boolean;

      /** 文件名 */
      attName: string;

      /** 对象类型 1、 报价单   2、 合同法务   3、 合同终稿 */
      attType: string;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** 创建日期 */
      createDt: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** endIndex */
      endIndex: number;

      /** 导入类型,扩充使用 */
      expType: string;

      /** 上传文件id */
      fileId: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** 是否薪资查询 */
      isWageQuery: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** 页数 */
      pageNum: number;

      /** 每页记录数,默认65536条 */
      pageSize: number;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** 备注 */
      remark: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** crm附件表id */
      slAttachmentId: string;

      /** startIndex */
      startIndex: number;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** 对象ID 报价单ID或合同ID */
      tagId: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;
    }

    export class ContractDTO {
      /** 流程节点ID */
      activityDefId: string;

      /** 审批步骤 */
      activityNameCn: string;

      /** 流程节点表的活动英文名称 */
      activityNameEn: string;

      /** 节点状态，0：不可结束不可回退，1：可结束不可回退，2：不可结束可回退，3：可结束可回退 */
      activityStatus: string;

      /** 新增驳回原因list */
      addSlDisaReasonList: Array<defs.crm.FilterEntity>;

      /** 预付款比例 */
      advancePaymentRatio: string;

      /** 代收代付 */
      agentBusiness: string;

      /** 约定到款月 */
      agereedAmtReceiveMon: string;

      /** 薪资发放日/约定薪资发放日 */
      agreedPayDt: string;

      /** 约定到款日 */
      agreedWageArriveDay: string;

      /** 非标合同审批单code */
      applyCode: string;

      /** 审批通过时间 */
      approveDt: string;

      /** 审批通过时间到 */
      approveDtEnd: string;

      /** 审批通过时间从 */
      approveDtStart: string;

      /** 页面填写的审核意见 */
      approveOpinion: string;

      /** 合同审核相关的附件 */
      approveRelatedAttachment: string;

      /** 合同审核相关的附件name */
      approveRelatedAttachmentName: string;

      /** 大区ID */
      areaId: string;

      /** 区域类型 1 本地 2 大区内 3 全国 */
      areaType: string;

      /** 区域类型 1 本地 2 大区内 3 全国 */
      areaTypeName: string;

      /** 终稿 */
      attTypeDraftId: string;

      /** 终稿name */
      attTypeDraftName: string;

      /** 法务 */
      attTypeLegalId: string;

      /** 法务name */
      attTypeLegalName: string;

      /** 平均价格集合 */
      averageMoneys: string;

      /** 账单日期 */
      billDt: string;

      /** 城市 */
      cityId: string;

      /** 城市名称 */
      cityName: string;

      /** 提交时间 */
      commitTime: string;

      /** 签约人数集合 */
      compactNumbers: string;

      /** 竞争对手id */
      competitor: string;

      /** 客服竞争对手名称 */
      competitorName: string;

      /** 已经确认的工作流程 */
      confirmdWorkFlow: string;

      /** 联系人手机 */
      contactCell: string;

      /** 联系人电话 */
      contactTel: string;

      /** 签约人均金额 */
      contractAvgAmt: string;

      /** 合同类别 */
      contractCategery: string;

      /** contractCategeryName */
      contractCategeryName: string;

      /** 合同编号 */
      contractCode: string;

      /** 合同创建人 */
      contractCreateBy: string;

      /** 最终结束日期 */
      contractEndDate: string;

      /** 合同最终结束日期类型 */
      contractEndDateType: string;

      /** 合同附件集合 */
      contractFileList: Array<defs.crm.ContractFile>;

      /** 合同文件名 */
      contractFileName: string;

      /** 合同附件备注 */
      contractFileRemark: string;

      /** 合同附件上传时间 */
      contractFileUploadDt: string;

      /** 签约人数 */
      contractHeadcount: string;

      /** 合同id */
      contractId: string;

      /** 合同名称 */
      contractName: string;

      /** 合同甲方 */
      contractPartA: string;

      /** 合同乙方 */
      contractPartB: string;

      /** 合同产品线id集合 */
      contractProductLineIds: string;

      /** 合同关系标签 */
      contractRelationLabel: string;

      /** 合同关系标签 用于接收页面传入的参数 */
      contractRelationLabelList: Array<string>;

      /** 合同退休人员集合 */
      contractRetireeList: Array<defs.crm.ContractRetiree>;

      /** 客户盖章后，合同回收时间 */
      contractRetrieveDt: string;

      /** 合同起始日期 */
      contractStartDate: string;

      /** 合同启动时间止 */
      contractStartDateEnd: string;

      /** 合同启动日期起 */
      contractStartDateStart: string;

      /** 合同状态：0 初始；1 审批中；2 审批通过；3 退回修改；4 驳回终止 */
      contractStatus: string;

      /** 合同状态name */
      contractStatusName: string;

      /** 合同结束日期 */
      contractStopDate: string;

      /** 合同终止原因 */
      contractStopReason: string;

      /** 合同小类 */
      contractSubType: string;

      /** 合同类别（子类）名称 */
      contractSubTypeName: string;

      /** 合同服务状态：0新签1续签2过期3终止服务 */
      contractSvcState: string;

      /** 合同服务状态name */
      contractSvcStateName: string;

      /** 合同模板编号 */
      contractTemplateId: string;

      /** 新平台合同终止时填写的终止时间 */
      contractTerminationDate: string;

      /** 合同大类 */
      contractType: string;

      /** 合同类型名 */
      contractTypeName: string;

      /** 合同版本号 */
      contractVersion: string;

      /** contractVersionTypeName */
      contractVersionTypeName: string;

      /** 创建人 */
      createByName: string;

      /** 创建人 */
      createByParty: string;

      /** 创建日期到 */
      createDtEnd: string;

      /** 创建日期从 */
      createDtStart: string;

      /** 合同生成方式 */
      createType: string;

      /** 账期（天） */
      creditPeriod: string;

      /** 客服审批 */
      csApproval: string;

      /** 客服审批状态 0：初始态 1：审批中  3：通过 -3：退回修改 -4：驳回终止 */
      csApprovalStatus: string;

      /** 客服联系人地址 */
      cstScAddress: string;

      /** 客服联系人手机 */
      cstScCall: string;

      /** 客服联系人 */
      cstScContact: string;

      /** 客服联系人邮件 */
      cstScEmail: string;

      /** 客服联系人电话 */
      cstScTel: string;

      /** 客服联系人传真 */
      cstScfax: string;

      /** 客服联系人职位 */
      cstScposition: string;

      /** 必须是同一个客户，当前执行的合同的编号，如果续签多次，这个编号是最新的合同编号 */
      currentExeContractId: string;

      /** 现销售 */
      currentSales: string;

      /** 客户唯一号 */
      custCode: string;

      /** 客户ID */
      custId: string;

      /** 客户名称 */
      custName: string;

      /** 客户盖章时间 */
      custSealDt: string;

      /** 供应商ID */
      departmentId: string;

      /** 供应商名称 */
      departmentName: string;

      /** 驳回原因批次 */
      disaBatchId: string;

      /** 草稿备注 */
      draftRemark: string;

      /** 联系人邮件 */
      email: string;

      /** 是否增强型代理 */
      enhancedAgent: string;

      /** EOS账号停用 */
      eosStatus: string;

      /** 预估首次账单日期 */
      estimateFirstBillDate: string;

      /** 预计12个月内可达到人数 */
      estimatedHeadcount: string;

      /** 执行成本 */
      executionCost: string;

      /** 预计增长人数 */
      expectedIncrease: string;

      /** 原预计增长人数 */
      expectedIncreaseOld: string;

      /** 联系人传真 */
      fax: string;

      /** 首次出账单的客户账单年月 */
      firstAccountMonth: string;

      /** 首次出账单时间(锁定时间) */
      firstBillDate: string;

      /** 首次大合同ID */
      firstContractId: string;

      /** 合同审批的首个法务 */
      firstLegalApproveId: string;

      /** 合同审批的首个法务名称 */
      firstLegalApproveName: string;

      /** 首次出账单的财务应收年月 */
      firstOughtMonth: string;

      /**  合同审批的首个易薪税审批人员 */
      firstWgApproveId: string;

      /** 原销售所属大区 */
      formerGoverningArea: string;

      /** 原销售所属大区名称 */
      formerGoverningAreaName: string;

      /** 原销售所属分公司 */
      formerGoverningBranch: string;

      /** 原销售所属分公司名称 */
      formerGoverningBranchName: string;

      /** 原销售 */
      formerSales: string;

      /** 原销售名字 */
      formerSalesName: string;

      /** 未来商机 */
      furtureOpportunity: string;

      /** 所属大区 */
      governingArea: string;

      /** 现销售所属大区名称 */
      governingAreaName: string;

      /** 所属分公司 */
      governingBranch: string;

      /** 现销售所属分公司名称 */
      governingBranchName: string;

      /** 毛利 */
      grossProfit: string;

      /** 集团公司编号 */
      groupId: string;

      /** 集团公司名称 */
      groupName: string;

      /** 是否有交接单 */
      hasTransferInfo: string;

      /** 人力资源联系人 */
      hrContract: string;

      /** 导入文件ID */
      importFileId: string;

      /** 导入文件名称 */
      importFileName: string;

      /** 收入 */
      income: string;

      /** 内支金额 */
      internalMoney: string;

      /** 开票金额 */
      invoiceMoney: string;

      /** 开票张数 */
      invoiceNum: string;

      /** 滞纳金比例是否为万分之五 1：是；0：否 */
      is5Per10000FineRate: string;

      /** 滞纳金比例是否为万分之五name */
      is5Per10000FineRateName: string;

      /** 是否有补充附件 */
      isAddedAttachment: string;

      /** 本次续签是否需要调整合同条款？ */
      isAdjustRenewContract: string;

      /** 是否派单 */
      isAssign: string;

      /** 是否要提交审核 */
      isCommitApprove: string;

      /** 是否为已有客户所推荐 1：是；0：否 */
      isCustRecommend: string;

      /** 是否为已有客户所推荐name */
      isCustRecommendName: string;

      /** 是否降价、垫付、账期延期 1:是,0:否 */
      isDefer: string;

      /** 是否降价、垫付、账期延期名称 */
      isDeferName: string;

      /** isDeleted */
      isDeleted: string;

      /** 是否开通EOS账号 */
      isEosAccount: string;

      /** 是否内支 */
      isInternalPayment: string;

      /** 是否代发薪资 */
      isIssuingSalary: string;

      /** 是否加入包含具体金额的对等条款或赔偿性条款 1：是；0：否 */
      isJoinCompensation: string;

      /** 是否加入包含具体金额的对等条款或赔偿性条款name */
      isJoinCompensationName: string;

      /** 是否正常审批 */
      isNormalApprove: string;

      /** 质控计算结果是否为垫付 1：是；0：否 */
      isPaymentQAResult: string;

      /** 质控计算结果是否为垫付name */
      isPaymentQAResultName: string;

      /** 服务人数小于20人，是否季度付款 1：是；0：否 */
      isQuarterlyPaymentLess20: string;

      /** 服务人数小于20人，是否季度付款name */
      isQuarterlyPaymentLess20Name: string;

      /** 是否有赠送退休额度 */
      isRetQuotaGranted: string;

      /** 是否包含退休业务 */
      isRetirementBusiness: string;

      /** 是否包含退休业务name */
      isRetirementBusinessName: string;

      /** 是否抢单 */
      isRob: string;

      /** 是否集中一地投保 */
      isSameInsur: string;

      /** 是否集中一地投保name */
      isSameInsurName: string;

      /** 是否二次开发 1:是,0:否 */
      isSecondaryDev: string;

      /** 是否二次开发名称 */
      isSecondaryDevName: string;

      /** 含差旅服务 */
      isTravelServices: string;

      /** 含差旅服务(展示) */
      isTravelServicesName: string;

      /** 标签备注 */
      labelRemark: string;

      /** 法务审批 */
      legalApproval: string;

      /** 法务审批状态  0：初始态 1：审批中  3：通过 -3：退回修改 -4：驳回终止 */
      legalApprovalStatus: string;

      /** 合同附件备注 */
      legalRemark: string;

      /** 责任客服 */
      liabilityCs: string;

      /** 责任客服所属分公司id */
      liabilityCsDepartmentId: string;

      /** 责任客服名字 */
      liabilityCsName: string;

      /** 责任客服职员代码 */
      libilityCsCode: string;

      /** 会议记录id */
      meetingRecordId: string;

      /** 会议记录上传附件id */
      meetingRecordImportFileId: string;

      /** 会议记录上传附件名称 */
      meetingRecordImportFileName: string;

      /** 备注 */
      memo: string;

      /** 范本修改版合同备注 */
      modelModifyVersionRemark: string;

      /** 新销售 */
      newSales: string;

      /** 续签合同ID, 存放续签的大合同ID */
      nextContractId: string;

      /** 续签合同名称 */
      nextContractName: string;

      /** 下个法务 */
      nextLegalApproveId: string;

      /** 非标合同审批单 */
      nonStaCoctApprId: string;

      /** 非标标签 */
      nonStandardLabel: string;

      /** 非标标签集合 用于接收页面传入的参数 */
      nonStandardLabelList: Array<string>;

      /** 原合同编号 */
      oldContractCode: string;

      /** 服务订单数 */
      orderNumber: string;

      /** 外包毛利率 */
      outSourcingMargin: string;

      /** 外包毛利率审核人 */
      outSourcingMarginApprover: string;

      /** 父合同id编号 */
      parentContractId: string;

      /** 付款和收款要点 */
      payCollectPoint: string;

      /** 薪资发放月 */
      payMonth: string;

      /** 缴费类型 */
      payType: string;

      /** 客户付款方id集合 */
      payerIds: string;

      /** 回款日 */
      paymentDate: string;

      /** 回款日审核人 */
      paymentDateApprover: string;

      /** 付款方式 */
      paymentMode: string;

      /** 体检预估成本 */
      peExecutionCost: string;

      /** 体检毛利 */
      peGrossProfit: string;

      /** 体检收入 */
      peIncome: string;

      /** 垫款额度 */
      prepayAmt: string;

      /** 垫付审批 */
      prepayApproval: string;

      /** 垫款审批状态  0：初始态 1：审批中  3：通过 -3：退回修改 -4：驳回终止 */
      prepayApprovalStatus: string;

      /** 流程定义id */
      processDefId: string;

      /** 对应的流程实例ID */
      processInstanceId: string;

      /** 产品线id集合 */
      productLineIdLogs: string;

      /** 产品线id集合 */
      productLineIds: string;

      /** 项目前期计划或实施要求 */
      projectPlanRequest: string;

      /** 全国项目交接表单 */
      projectRemark: string;

      /** 供应商类型 */
      providerType: string;

      /** QA审核意见 */
      qaApprove: string;

      /** 报价单集合id */
      quoIds: string;

      /** 被续签的旧合同号 */
      renewedContractNum: string;

      /** 客服反馈撤单预警日期大于等于 */
      reportElEvacuatedDate: string;

      /** 客服反馈撤单时间 */
      reportEvacuatedDate: string;

      /** 客服撤单详细原因说明 */
      reportEvacuatedExplantion: string;

      /** 客服撤单原因分类 */
      reportEvacuatedReason: string;

      /** 客服反馈撤单预警日期小于等于 */
      reportGlEvacuatedDate: string;

      /** 赠送退休数量 */
      retirementGiftCount: string;

      /** 回访历史内容 */
      returnVisitMemo: string;

      /** 最后回访人Id */
      returnVisitorId: string;

      /** 风险金比例% */
      riskPremiumRatio: string;

      /** 风险分担比例% */
      riskSharingRatio: string;

      /** 统计标志位 */
      salFlag: string;

      /** 新增/存量标识 （手工） */
      salFlagManual: string;

      /** 1 纯新增,2 存量,3 纯新增/存量,4 纯新增+滚动存量,5 滚动存量,6 滚动存量+存量 */
      salFlagManualName: string;

      /** 新增/存量标识 （系统）：1历史纯新增、2滚动新增、3存量、4当月启动纯新增、-1未有首版账单 */
      salFlagName: string;

      /** 客户对应销售及分公司 */
      saleAndBranchName: string;

      /** 销售审批 */
      salesApprove: string;

      /** 销售报价审批状态 0：初始态 1：审批中  3：通过 -3：退回修改 -4：驳回终止 */
      salesApproveStatus: string;

      /** 现销售职员代码 */
      salesCode: string;

      /** 销售所在主部门 */
      salesDeptName: string;

      /** 销售名字 */
      salesName: string;

      /** 所属销售团队类型 */
      salesTeamType: string;

      /** 客服竞争对手优势 */
      sctScComAdvancetage: string;

      /** 用章审核状态 0：初始态3：通过 -3：退回修改 -4：驳回终止 */
      sealApproveStatus: string;

      /** 公司盖章时间 */
      sealDt: string;

      /** 用章审核 */
      sealOpinion: string;

      /** 签约方公司抬头 */
      signBranchTitle: string;

      /** 签约方公司抬头id */
      signBranchTitleId: string;

      /** 新签标识（手工） */
      signFlagManual: string;

      /** 新签标识（手工）name */
      signFlagManualName: string;

      /** 签单分公司 */
      signProvider: string;

      /** 驳回原因list */
      slDisaReasonList: Array<defs.crm.FilterEntity>;

      /** 撤单原因 */
      stopReason: string;

      /** 终止服务系统操作时间 */
      stopSvcDt: string;

      /** 终止服务操作日期 查询条件：终止服务日期到 */
      stopSvcEndDt: string;

      /** 销售补充标志 0需要销售补充信息1审批 */
      supplyMark: string;

      /** 销售补充附件说明(历史) */
      supplyShow: string;

      /** 销售补充附件名称 */
      supplyShowFileName: string;

      /** 销售补充附件路径 */
      supplyShowFilePath: string;

      /** 销售补充附件说明(新增) */
      supplyShowNew: string;

      /** 服务区域 */
      svcRegion: string;

      /** 税费 */
      tax: string;

      /** 总售价 */
      totalPrice: string;

      /** 交接单流程ID */
      tranferProcessId: string;

      /** 交接单id */
      transferId: string;

      /** 销售--客服交接单 */
      transferRemark: string;

      /** 差旅服务费比例% */
      travelServicesRatio: string;

      /** 交接上传文件名 */
      uploadFileName: string;

      /** 交接上传URL */
      uploadUrl: string;

      /** 修改驳回原因list */
      uptSlDisaReasonList: Array<defs.crm.FilterEntity>;

      /** 职场健康 预付款金额 */
      whAdvancePaymentAmt: string;

      /** 职场健康 预付款时间 */
      whAdvancePaymentDt: string;

      /** 提成销售 */
      whCommissionSale: string;

      /** 提成销售name */
      whCommissionSaleName: string;

      /** 职场健康 合同寄送地址 */
      whContractSendAddress: string;

      /** 职场健康 预计垫付时长（天） */
      whExpectedPrepayDay: string;

      /** 职场健康 尾款金额 */
      whFinalPaymentAmt: string;

      /** 职场健康 尾款时间 */
      whFinalPaymentDt: string;

      /** 职场健康 开票顺序 */
      whInvoiceOrder: string;

      /** 开票顺序name */
      whInvoiceOrderName: string;

      /** 职场健康 项目编号 */
      whItemCode: string;

      /** 职场健康 毛利率% */
      whMargin: string;

      /** 体检税率% */
      whPeRate: string;

      /** 职场健康 垫付备注 */
      whPrepayRemark: string;

      /** 职场健康 采购发票内容 */
      whPurchaseInvoiceContent: string;

      /** 职场健康 采购发票类型 */
      whPurchaseInvoiceType: string;

      /** 职场健康 采购发票类型name */
      whPurchaseInvoiceTypeName: string;

      /** 职场健康 返佣收入 */
      whRebateIncome: string;

      /** 职场健康 返佣税费 */
      whRebateTax: string;

      /** 职场健康 销售发票内容 */
      whSaleInvoiceContent: string;

      /** 职场健康 销售发票类型 */
      whSaleInvoiceType: string;

      /** 职场健康 销售发票类型name */
      whSaleInvoiceTypeName: string;

      /** 职场健康 支付供货商货款时间 */
      whSupplierPaymentDt: string;

      /** 工作流id */
      workitemId: string;
    }

    export class ContractFile {
      /** 审批节点 */
      activityNameEn: string;

      /** add */
      add: boolean;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 合同附件ID */
      contractFileId: string;

      /** 大合同ID号 */
      contractId: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** 删除人 */
      deleteBy: string;

      /** 删除日期 */
      deleteDt: string;

      /** 导入类型,扩充使用 */
      expType: string;

      /** 附件ID */
      fileId: string;

      /** 附件名称 */
      fileName: string;

      /** 附件路径 */
      filePath: string;

      /** 附件类型 */
      fileType: string;

      /** 附件类型name */
      fileTypeName: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** 是否薪资查询 */
      isWageQuery: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 流程defId */
      processDefId: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** 备注 */
      remark: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** 上传步骤 */
      uploadStep: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;
    }

    export class ContractQuery {
      /** 流程节点ID */
      activityDefId: string;

      /** 新增驳回原因list */
      addSlDisaReasonList: Array<defs.crm.FilterEntity>;

      /** 审批通过日期到 */
      approveDtEnd: string;

      /** 审批通过日期从 */
      approveDtStart: string;

      /** 区域类型 */
      areaType: string;

      /** 合同类别 */
      contractCategery: string;

      /** 合同编号 */
      contractCode: string;

      /** 最终结束日期从 */
      contractEndDateFrom: string;

      /** 最终结束日期到 */
      contractEndDateTo: string;

      /** 合同名称 */
      contractName: string;

      /** 开始时间到 */
      contractStartDateEnd: string;

      /** 开始时间从 */
      contractStartDateStart: string;

      /** 合同审批状态 */
      contractStatus: string;

      /** 合同小类 */
      contractSubType: string;

      /** 合同状态 */
      contractSvcState: string;

      /** 合同状态集合 */
      contractSvcStateList: Array<string>;

      /** 合同大类 */
      contractType: string;

      /** 合同类别 */
      contractVersionType: string;

      /** 创建人 */
      createByName: string;

      /** 创建人查询条件 */
      createByParty: string;

      /** 创建日期到 */
      createDtEnd: string;

      /** 创建日期从 */
      createDtStart: string;

      /** 现销售 */
      currentSales: string;

      /** 客户编号 */
      custCode: string;

      /** 客户名称 */
      custName: string;

      /** 客户显示编号 */
      custViewCode: string;

      /** 驳回原因批次 */
      disaBatchId: string;

      /** endIndex */
      endIndex: number;

      /** 是否增强型代理 */
      enhancedAgent: string;

      /** 原销售所属大区 */
      formerGoverningArea: string;

      /** 原销售所属分公司 */
      formerGoverningBranch: string;

      /** 原销售 */
      formerSales: string;

      /** 现销售所属大区 */
      governingArea: string;

      /** 现销售所属分公司 */
      governingBranch: string;

      /** 是否为已有客户所推荐 */
      isCustRecommend: string;

      /** 责任客服 */
      liabilityCs: string;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** processDefId */
      processDefId: string;

      /** 查询接口类型 传1为只查新签、续签，审批通过的大合同 */
      queryType: string;

      /** 客服反馈撤单预警日期大于等于 */
      reportElEvacuatedDate: string;

      /** 客服反馈撤单预警日期小于等于 */
      reportGlEvacuatedDate: string;

      /** 卡权限(1：客户权限，3：订单权限也就是小合同权限) */
      restrictType: string;

      /** 风险金比例% */
      riskPremiumRatio: string;

      /** 风险分担比例% */
      riskSharingRatio: string;

      /** 签约方公司抬头 */
      signBranchTitleId: string;

      /** 驳回原因list */
      slDisaReasonList: Array<defs.crm.FilterEntity>;

      /** startIndex */
      startIndex: number;

      /** 终止服务日期从 */
      stopSvcDt: string;

      /** 终止服务日期到 */
      stopSvcEndDt: string;

      /** 修改驳回原因list */
      uptSlDisaReasonList: Array<defs.crm.FilterEntity>;
    }

    export class ContractRetiree {
      /** add */
      add: boolean;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** 人员姓名 */
      bz: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 大合同ID号 */
      contractId: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** 人员姓名 */
      empName: string;

      /** 导入类型,扩充使用 */
      expType: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** 身份证号 */
      idCardNum: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** 是否薪资查询 */
      isWageQuery: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 大合同退休人员主键 */
      retireeId: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;
    }

    export class CrmBusinessLog {
      /** add */
      add: boolean;

      /** 批次 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** custCode */
      custCode: string;

      /** 客户ID */
      custId: string;

      /** 客户原名称 */
      custName: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** ePropertyName */
      ePropertyName: string;

      /** endIndex */
      endIndex: number;

      /** executeDate */
      executeDate: string;

      /** executeDateEd */
      executeDateEd: string;

      /** executeDateSt */
      executeDateSt: string;

      /** 导入类型,扩充使用 */
      expType: string;

      /** exportType */
      exportType: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** 是否薪资查询 */
      isWageQuery: string;

      /** 主键 */
      logId: string;

      /** 模拟人 */
      mimicBy: string;

      /** newAreaId */
      newAreaId: string;

      /** newAreaText */
      newAreaText: string;

      /** newCityName */
      newCityName: string;

      /** newDeptName */
      newDeptName: string;

      /** newDeptPre */
      newDeptPre: string;

      /** newSaleId */
      newSaleId: string;

      /** 现销售 */
      newSaler: string;

      /** newexists */
      newexists: string;

      /** newgoverningBranch */
      newgoverningBranch: string;

      /** newgoverningBranchText */
      newgoverningBranchText: string;

      /** noChange */
      noChange: boolean;

      /** oldAreaId */
      oldAreaId: string;

      /** oldAreaText */
      oldAreaText: string;

      /** oldCityName */
      oldCityName: string;

      /** oldDeptName */
      oldDeptName: string;

      /** oldDeptPre */
      oldDeptPre: string;

      /** oldSaleId */
      oldSaleId: string;

      /** 原销售 */
      oldSaler: string;

      /** oldexists */
      oldexists: string;

      /** oldgoverningBranch */
      oldgoverningBranch: string;

      /** oldgoverningBranchText */
      oldgoverningBranchText: string;

      /** 页数 */
      pageNum: number;

      /** 每页记录数,默认65536条 */
      pageSize: number;

      /** 产品线创建时间 */
      plCreatDt: string;

      /** 产品线修改时间 */
      plUpdateDt: string;

      /** 客户产品线主键ID */
      proKeyId: string;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 产品线 */
      productLine: string;

      /** 产品线名称 */
      productName: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** realName */
      realName: string;

      /** 备注 */
      remark: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** startIndex */
      startIndex: number;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** 类型ID	  <p>	  101: 客户建立	  <p>	  102:客户删除	  <p>	  103:客户进入共享	  <p>	  104:正在跟进分配	  <p>	  105:客户名称修改	  <p>	  106:共享区分配	  <p>	  107:删除区分配	  <p>	  108:客户基本信息修改	  <P>	  109:市场活动预录入客户分配 */
      typeId: string;

      /** typeName */
      typeName: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;
    }

    export class CrmContractPayerDTO {
      /** 发票抬头 */
      checkTitle: string;

      /** 合同ID */
      contractId: string;

      /** 合同与客户付款方关系id */
      contractPayerId: string;

      /** 付款方表ID */
      custPayerId: string;

      /** 付款方名称 */
      payerName: string;
    }

    export class CrmContractQuotationDTO {
      /** 合同ID */
      contractId: string;

      /** 合同绑定报价单id */
      contractQuotationId: string;

      /** 报价单编号 */
      quotationCode: string;

      /** 报价单id */
      quotationId: string;

      /** 报价单名称 */
      quotationName: string;

      /** 总的销售价格 */
      quotationTotalSalPrice: string;

      /** 备注 */
      remark: string;

      /** 报价单状态 */
      status: string;

      /** 客户补医保投保人数 */
      suppltMedInsurHeadCount: string;
    }

    export class CrmLittleContractProductlineDTO {
      /** 平均价格 */
      averageMoney: string;

      /** 人数 */
      compactNumber: string;

      /** 合同ID */
      contractId: string;

      /** 0无效 1有效 */
      isEnable: string;

      /** 产品线id */
      productlineId: string;

      /** 产品线名称 */
      productlineName: string;

      /** 大合同产品线子表id */
      smcontractProductlineId: string;
    }

    export class CrmPage<T0 = any> {
      /** pageNum */
      pageNum: number;

      /** pageSize */
      pageSize: number;

      /** rows */
      rows: Array<T0>;

      /** total */
      total: number;

      /** totalPage */
      totalPage: number;
    }

    export class CustLogQuery {
      /** 执行城市 */
      cityId: string;

      /** 竞争对手 */
      competitorId: string;

      /** loseId */
      custId: string;

      /** endIndex */
      endIndex: number;

      /** expType */
      expType: string;

      /** 预计签约日期至 */
      expectedSignDateEd: string;

      /** 预计签约日期起 */
      expectedSignDateSt: string;

      /** 签约人数至 */
      expectedSignNumEd: string;

      /** 签约人数从 */
      expectedSignNumSt: string;

      /** 价格区间至 */
      expectedSignPriceEd: string;

      /** 价格区间从 */
      expectedSignPriceSt: string;

      /** 丢单日期到 */
      loseDtEd: string;

      /** 丢单日期从 */
      loseDtSt: string;

      /** 丢单类型 */
      loseType: string;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** 大区 */
      productlineSalerAreaId: string;

      /** 分公司 */
      productlineSalerProviderId: string;

      /** 销售 */
      productlineSalername: string;

      /** startIndex */
      startIndex: number;

      /** 截止日期 */
      trackDateEd: string;

      /** 起始日期 */
      trackDateSt: string;

      /** 拜访阶段 */
      visitStage: string;
    }

    export class CustLoseReason {
      /** add */
      add: boolean;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 竞争对手 */
      competitorId: string;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** custId */
      custId: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** endIndex */
      endIndex: number;

      /** 导入类型,扩充使用 */
      expType: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** 是否薪资查询 */
      isWageQuery: string;

      /** 丢单日期 */
      loseDt: string;

      /** 主键 */
      loseId: string;

      /** 丢单原因 */
      loseResaon: string;

      /** 丢单类型 */
      loseType: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** 页数 */
      pageNum: number;

      /** 每页记录数,默认65536条 */
      pageSize: number;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** startIndex */
      startIndex: number;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;
    }

    export class CustProductLine {
      /** custId */
      custId: string;

      /** productLine */
      productLine: Array<defs.crm.ProductLine>;
    }

    export class CustReportQuery {
      /** 大区id */
      areaId: string;

      /** 客户城市 */
      cityName: string;

      /** 客户编号 */
      custCode: string;

      /** 创建日期到 */
      custDtEnd: string;

      /** 创建日期从 */
      custDtFrom: string;

      /** custIsDeleted */
      custIsDeleted: string;

      /** 客户名称 */
      custName: string;

      /** 产品线获取日期到 */
      custProductLindDtEnd: string;

      /** 产品线获取日期从 */
      custProductLindDtFrom: string;

      /** 客户状态 */
      custStatus: string;

      /** 分公司id */
      departmentId: string;

      /** endIndex */
      endIndex: number;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** 销售姓名 */
      saleName: string;

      /** startIndex */
      startIndex: number;
    }

    export class CustTrack {
      /** add */
      add: boolean;

      /** 大区id */
      areaId: string;

      /** 大区名称 */
      areaName: string;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** 分公司id */
      branchId: string;

      /** 分公司名称 */
      branchName: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 沟通内容摘要 */
      commuContent: string;

      /** 沟通目标 */
      commuObjective: string;

      /** 沟通结果及跟进计划 */
      commuResult: string;

      /** 竞争对手--------(服务信息字段结束) */
      competitor: string;

      /** 联系电话 */
      contactCell: string;

      /** 联系人性别 */
      contactGender: string;

      /** 联系人 */
      contacter: string;

      /** 联系人手机 */
      contacterPhone: string;

      /** 联系人职位 */
      contacterPost: string;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 合同编号 */
      contractCode: string;

      /** 合同id */
      contractId: string;

      /** 合同名称 */
      contractName: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** 创建时间到 */
      createDtEnd: string;

      /** 创建时间从 */
      createDtStart: string;

      /** 创建人姓名 */
      creatorName: string;

      /** 客户编号 */
      custCode: string;

      /** 客户ID */
      custId: string;

      /** 我心目中的客户分级 */
      custLevelInSale: string;

      /** 客户名称 */
      custName: string;

      /** 主键 */
      custTrackId: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** 客户决策人员角色 */
      customerDecisionRole: string;

      /** 客户关注点 */
      customerFocus: string;

      /** del */
      del: boolean;

      /** departmentId */
      departmentId: string;

      /** departmentName */
      departmentName: string;

      /** 邮件 */
      email: string;

      /** 员工编号 */
      empCode: string;

      /** 订单id/上下岗id */
      empHireSepId: string;

      /** 员工id */
      empId: string;

      /** 员工姓名 */
      empName: string;

      /** endIndex */
      endIndex: number;

      /** 执行城市 */
      execCityId: string;

      /** 导入类型,扩充使用 */
      expType: string;

      /** 预计签约日期 */
      expectedSignDate: string;

      /** 预计签约人数 */
      expectedSignNum: string;

      /** 预计签约价格 */
      expectedSignPrice: string;

      /** 传真--------(基本信息卡字段结束) */
      fax: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** 关注点描述 */
      focusDesc: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** 证件号码 */
      idCardNum: string;

      /** 非正式客户id */
      ifmCustId: string;

      /** 主键 */
      ifmCustTrackId: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** 是否薪资查询 */
      isWageQuery: string;

      /** 模拟人 */
      mimicBy: string;

      /** 下一步工作安排 */
      nextWorkPlan: string;

      /** noChange */
      noChange: boolean;

      /** 原hro供应商 */
      oldHroPrvd: string;

      /** ������id.���ڼ�¼��־ʹ�� */
      oldSalesId: string;

      /** 页数 */
      pageNum: number;

      /** 每页记录数,默认65536条 */
      pageSize: number;

      /** proLineIdStr */
      proLineIdStr: string;

      /** proLineStr */
      proLineStr: string;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** 备注-------------(销售跟进字段结束) */
      remark: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 销售所需支持 */
      salersSupport: string;

      /** salesId */
      salesId: string;

      /** salesIdFilterTrack */
      salesIdFilterTrack: string;

      /** 销售名称 */
      salesName: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** startIndex */
      startIndex: number;

      /** 状态,0:未生效(默认),1:生效 */
      status: string;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** 小合同编号 */
      subcontractCode: string;

      /** 小合同id */
      subcontractId: string;

      /** 小合同名称 */
      subcontractName: string;

      /** 服务类型字符串 */
      svcTypeStr: string;

      /** 跟进日期 */
      trackDate: string;

      /** trackDateEnd */
      trackDateEnd: string;

      /** trackDateStart */
      trackDateStart: string;

      /** 1,售前<br>	  2,售后 */
      trackType: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** 更新人姓名 */
      updaterName: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;

      /** 拜访阶段 */
      visitStage: string;

      /** visitStageName */
      visitStageName: string;
    }

    export class Customer {
      /** cityId */
      cityId: string;

      /** cityName */
      cityName: string;

      /** contact1Post */
      contact1Post: string;

      /** contactTel */
      contactTel: string;

      /** corpId */
      corpId: string;

      /** countryId */
      countryId: string;

      /** createDt */
      createDt: string;

      /** crmCustId */
      crmCustId: string;

      /** custCode */
      custCode: string;

      /** custEnglishName */
      custEnglishName: string;

      /** custId */
      custId: string;

      /** custName */
      custName: string;

      /** custShortName */
      custShortName: string;

      /** ePropertyId */
      ePropertyId: string;

      /** eSizeId */
      eSizeId: string;

      /** email */
      email: string;

      /** haveBranches */
      haveBranches: string;

      /** hrContract */
      hrContract: string;

      /** industryId */
      industryId: string;

      /** organizationCode */
      organizationCode: string;

      /** reqAquirementDt */
      reqAquirementDt: string;

      /** reqAquirementTypeId */
      reqAquirementTypeId: string;

      /** salesId */
      salesId: string;

      /** salesName */
      salesName: string;

      /** sourceId */
      sourceId: string;

      /** updateDt */
      updateDt: string;

      /** userName */
      userName: string;

      /** workAddress */
      workAddress: string;
    }

    export class CustomerLost {
      /** add */
      add: boolean;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** loseId */
      competitorId: string;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** loseId */
      custCode: string;

      /** loseId */
      custId: string;

      /** loseId */
      custName: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** endIndex */
      endIndex: number;

      /** 导入类型,扩充使用 */
      expType: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** 是否薪资查询 */
      isWageQuery: string;

      /** loseId */
      loseDt: string;

      /** loseId */
      loseDtEd: string;

      /** loseId */
      loseDtSt: string;

      /** loseId */
      loseId: string;

      /** loseId */
      loseResaon: string;

      /** loseId */
      loseType: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** 页数 */
      pageNum: number;

      /** 每页记录数,默认65536条 */
      pageSize: number;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** startIndex */
      startIndex: number;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;
    }

    export class CustomerPayerDTO {
      /** 开户名 */
      accountName: string;

      /** 审核日期 */
      approveDt: string;

      /** 审核状态:0待修改,1待审批,2审批通过 */
      approveStatus: string;

      /** 审核人 */
      approver: string;

      /** 帐号 */
      bankAcct: string;

      /** 开户行名称 */
      bankName: string;

      /** 1：工行  0：他行 */
      bankType: string;

      /** 帐单年月 0 收当月 -1收上月 */
      billMon: string;

      /** 发票抬头 */
      checkTitle: string;

      /** 城市id */
      cityId: string;

      /** 联系人 */
      contact: string;

      /** 联系地址 */
      contactAddress: string;

      /** 电子邮件 */
      contactEmail: string;

      /** 传真 */
      contactFax: string;

      /** 联系电话 */
      contactTel1: string;

      /** 联系电话 */
      contactTel2: string;

      /** 邮政编码 */
      contactZipCode: string;

      /** 客户资质:1,一般纳税人、2小规模纳税人 */
      custAptitude: string;

      /** 客户资质Name */
      custAptitudeText: string;

      /** 客户编号 */
      custCode: string;

      /** 客户id */
      custId: string;

      /** 客户名称 */
      custName: string;

      /** 客户付款方表id */
      custPayerId: string;

      /** 附件id */
      fileId: string;

      /** 附件名称 */
      fileName: string;

      /** 发票地址 */
      invoiceAddress: string;

      /** 特殊开发票说明 */
      invoiceDesc: string;

      /** 电子发票邮箱 */
      invoiceEmail: string;

      /** 发票电话 */
      invoiceTel: string;

      /** 删除标志 */
      isDeleted: string;

      /** 是否开特殊发票0: 否1:是 */
      isInvoice: string;

      /** 付款方名称 */
      payerName: string;

      /** 省份id */
      provinceId: string;

      /** 代理人 */
      proxyBy: string;

      /** 备注 */
      remark: string;

      /** 纳税人识别号 */
      taxpayerIdentifier: string;
    }

    export class CustomerPayerQuery {
      /** 客户id */
      custId: string;

      /** 付款方编号 */
      custPayerId: string;

      /** endIndex */
      endIndex: number;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** 付款方名称 */
      payerName: string;

      /** startIndex */
      startIndex: number;
    }

    export class CustomerVisitQuery {
      /** accountsBill */
      accountsBill: string;

      /** areaId */
      areaId: string;

      /** branchId */
      branchId: string;

      /** competitors */
      competitors: string;

      /** competitorsServiceinfo */
      competitorsServiceinfo: string;

      /** competitorsStrong */
      competitorsStrong: string;

      /** continuedTime */
      continuedTime: string;

      /** custCode */
      custCode: string;

      /** custId */
      custId: string;

      /** custName */
      custName: string;

      /** customerNeed */
      customerNeed: string;

      /** customerQuestion */
      customerQuestion: string;

      /** customerVisitId */
      customerVisitId: string;

      /** deptttype */
      deptttype: string;

      /** endIndex */
      endIndex: number;

      /** enterpriseScale */
      enterpriseScale: string;

      /** estimatedTime */
      estimatedTime: string;

      /** followingInfo */
      followingInfo: string;

      /** followingInfoEdit */
      followingInfoEdit: string;

      /** isExecute */
      isExecute: string;

      /** isExecuteStr */
      isExecuteStr: string;

      /** jointMember */
      jointMember: string;

      /** jointMemberStr */
      jointMemberStr: string;

      /** memberCount */
      memberCount: string;

      /** optimizationMethods */
      optimizationMethods: string;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** realName */
      realName: string;

      /** serviceCitys */
      serviceCitys: string;

      /** serviceCount */
      serviceCount: string;

      /** serviceCountIndaili */
      serviceCountIndaili: string;

      /** serviceCountInproxy */
      serviceCountInproxy: string;

      /** serviceProducts */
      serviceProducts: string;

      /** setCustomerAlias */
      setCustomerAlias: string;

      /** startIndex */
      startIndex: number;

      /** visitAddress */
      visitAddress: string;

      /** visitDate */
      visitDate: string;

      /** visitDateEd */
      visitDateEd: string;

      /** visitor */
      visitor: string;
    }

    export class DebtReminderCriteria {
      /** add */
      add: boolean;

      /** agreedWageArriveDay */
      agreedWageArriveDay: string;

      /** amtReceiveMon */
      amtReceiveMon: string;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** billCreator */
      billCreator: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column CRM_DEBT_REMINDER.BILL_MONTH	  	  ibatorgenerated Tue Jul 22 09:35:02 CST 2014 */
      billMonth: string;

      /** billType */
      billType: string;

      /** billVersion */
      billVersion: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** cashDt */
      cashDt: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** contractCode */
      contractCode: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column CRM_DEBT_REMINDER.CONTRACT_ID	  	  ibatorgenerated Tue Jul 22 09:35:02 CST 2014 */
      contractId: string;

      /** contractName */
      contractName: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** 创建日期 */
      createDt: string;

      /** currentGoverningArea */
      currentGoverningArea: string;

      /** currentGoverningAreaId */
      currentGoverningAreaId: string;

      /** currentGoverningBranch */
      currentGoverningBranch: string;

      /** currentGoverningBranchId */
      currentGoverningBranchId: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column CRM_DEBT_REMINDER.CURRENT_SALES	  	  ibatorgenerated Tue Jul 22 09:35:02 CST 2014 */
      currentSales: string;

      /** custCode */
      custCode: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column CRM_DEBT_REMINDER.CUST_ID	  	  ibatorgenerated Tue Jul 22 09:35:02 CST 2014 */
      custId: string;

      /** custName */
      custName: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column CRM_DEBT_REMINDER.DEBT_REMINDER_ID	  	  ibatorgenerated Tue Jul 22 09:35:02 CST 2014 */
      debtReminderId: string;

      /** del */
      del: boolean;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column CRM_DEBT_REMINDER.IS_SEND_MAIL	  	  ibatorgenerated Tue Jul 22 09:35:02 CST 2014 */
      email: string;

      /** endIndex */
      endIndex: number;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column CRM_DEBT_REMINDER.EXP_PAYMENT_String	  	  ibatorgenerated Tue Jul 22 09:35:02 CST 2014 */
      expPaymentDate: string;

      /** 导入类型,扩充使用 */
      expType: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** finReceivableYm */
      finReceivableYm: string;

      /** formerGoverningArea */
      formerGoverningArea: string;

      /** formerGoverningAreaId */
      formerGoverningAreaId: string;

      /** formerGoverningBranch */
      formerGoverningBranch: string;

      /** formerGoverningBranchId */
      formerGoverningBranchId: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column CRM_DEBT_REMINDER.FORMER_SALES	  	  ibatorgenerated Tue Jul 22 09:35:02 CST 2014 */
      formerSales: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** 是否薪资查询 */
      isWageQuery: string;

      /** 模拟人 */
      mimicBy: string;

      /** minorAdjustment */
      minorAdjustment: string;

      /** noChange */
      noChange: boolean;

      /** overdrafAmt */
      overdrafAmt: string;

      /** 页数 */
      pageNum: number;

      /** 每页记录数,默认65536条 */
      pageSize: number;

      /** payeeBranch */
      payeeBranch: string;

      /** payeeBranchId */
      payeeBranchId: string;

      /** payerBranch */
      payerBranch: string;

      /** payerBranchId */
      payerBranchId: string;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column CRM_DEBT_REMINDER.REASON	  	  ibatorgenerated Tue Jul 22 09:35:02 CST 2014 */
      reason: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column CRM_DEBT_REMINDER.REASON_CREATE_BY	  	  ibatorgenerated Tue Jul 22 09:35:02 CST 2014 */
      reasonCreateBy: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column CRM_DEBT_REMINDER.REASON_CREATE_DT	  	  ibatorgenerated Tue Jul 22 09:35:02 CST 2014 */
      reasonCreateDt: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column CRM_DEBT_REMINDER.REASON_TYPE	  	  ibatorgenerated Tue Jul 22 09:35:02 CST 2014 */
      reasonType: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column CRM_DEBT_REMINDER.RECEIVABLE_AMT	  	  ibatorgenerated Tue Jul 22 09:35:02 CST 2014 */
      receivableAmt: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column CRM_DEBT_REMINDER.EF_RECEIVABLE_ID	  	  ibatorgenerated Tue Jul 22 09:35:02 CST 2014 */
      receivableId: string;

      /** receivableTemplate */
      receivableTemplate: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column CRM_DEBT_REMINDER.EF_RECEIVABLE_TEMPLT_ID	  	  ibatorgenerated Tue Jul 22 09:35:02 CST 2014 */
      receivableTempltId: string;

      /** remindDate */
      remindDate: string;

      /** remindEndDt */
      remindEndDt: string;

      /** remindStartDt */
      remindStartDt: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column CRM_DEBT_REMINDER.REMIND_String	  	  ibatorgenerated Tue Jul 22 09:35:02 CST 2014 */
      remindString: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column CRM_DEBT_REMINDER.REMINDER_RECIPIENT	  	  ibatorgenerated Tue Jul 22 09:35:02 CST 2014 */
      reminderRecipient: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column CRM_DEBT_REMINDER.REMINDER_RECIPIENT_ID	  	  ibatorgenerated Tue Jul 22 09:35:02 CST 2014 */
      reminderRecipientId: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** startIndex */
      startIndex: number;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column CRM_DEBT_REMINDER.STATUS	  	  ibatorgenerated Tue Jul 22 09:35:02 CST 2014 */
      status: string;

      /** statusName */
      statusName: string;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;

      /** verifyAmt */
      verifyAmt: string;

      /** verifyStatus */
      verifyStatus: string;
    }

    export class DropdownList {
      /** 业务大类类型 */
      btType: string;

      /** chargeRate */
      chargeRate: string;

      /** cityId */
      cityId: string;

      /** cityIdForParty */
      cityIdForParty: string;

      /** cityName */
      cityName: string;

      /** contractAvgAmt */
      contractAvgAmt: string;

      /** contractHeadcount */
      contractHeadcount: string;

      /** contractName */
      contractName: string;

      /** contractSubType */
      contractSubType: string;

      /** contractSubTypeName */
      contractSubTypeName: string;

      /** contractType */
      contractType: string;

      /** contractTypeName */
      contractTypeName: string;

      /** currentSalesName */
      currentSalesName: string;

      /** departmentName */
      departmentName: string;

      /** englishTermName */
      englishTermName: string;

      /** exFeeMonth */
      exFeeMonth: string;

      /** 供应商收费模板 */
      exFeeTempltId: string;

      /** governingArea */
      governingArea: string;

      /** 所属大区 */
      governingAreaId: string;

      /** governingBranch */
      governingBranch: string;

      /** 所属分公司 */
      governingBranchId: string;

      /** groupType */
      groupType: string;

      /** 主键 */
      key: string;

      /** liabilityCsName */
      liabilityCsName: string;

      /** 全称 */
      name: string;

      /** 拼音码 */
      pinYinCode: string;

      /** productLineId */
      productLineId: string;

      /** 供应商类型1内部2外部 */
      providerType: string;

      /** 保留名字1 */
      reserveName1: string;

      /** 保留名字2 */
      reserveName2: string;

      /** 储备对象,用于查询下拉框的时候,可能还需要查询其他列的值 */
      reserveObj: string;

      /** 缩写名 */
      shortName: string;

      /** signBrachTitleId */
      signBrachTitleId: string;

      /** signBranchTitleName */
      signBranchTitleName: string;

      /** 社保组ID */
      ssGroupId: string;

      /** svcSubtypeName */
      svcSubtypeName: string;

      /** svcTypeName */
      svcTypeName: string;
    }

    export class ExportQuery {
      /** 查询条件 */
      condition: object;

      /** 表头字段列表 */
      fieldArr: Array<string>;

      /** 表头字段中文拼接 */
      headStr: string;

      /** 表头字段类型列表 */
      typeArr: Array<string>;
    }

    export class FilterEntity {
      /** activityNameCn */
      activityNameCn: string;

      /** activityNameEn */
      activityNameEn: string;

      /** add */
      add: boolean;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** contractId */
      contractId: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** createByStr */
      createByStr: string;

      /** 创建日期 */
      createDt: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** disaBatchId */
      disaBatchId: string;

      /** disaReasonId */
      disaReasonId: string;

      /** endIndex */
      endIndex: number;

      /** 导入类型,扩充使用 */
      expType: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** 是否薪资查询 */
      isWageQuery: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** 页数 */
      pageNum: number;

      /** 每页记录数,默认65536条 */
      pageSize: number;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** reasonBz */
      reasonBz: string;

      /** reasonId */
      reasonId: string;

      /** reasonStr */
      reasonStr: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** startIndex */
      startIndex: number;

      /** status */
      status: string;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;

      /** workitemId */
      workitemId: string;
    }

    export class FormalCust {
      /** 市场活动编号 */
      activityCode: string;

      /** 市场活动id */
      activityId: string;

      /** 市场活动名称 */
      activityName: string;

      /** add */
      add: boolean;

      /** approveOpinion */
      approveOpinion: string;

      /** 大区id */
      areaId: string;

      /** 大区名称 */
      areaName: string;

      /** 银行账号 */
      bankAcct: string;

      /** 开户行名称 */
      bankName: string;

      /** 银行开户名 */
      bankNum: string;

      /** 所属银行, 取值为数据字典type=911记录中base_data_code值. */
      bankType: string;

      /** 批次号,用于备份 */
      batchId: string;

      /** 大客户申请时间 */
      bigCompanyApplyDate: string;

      /** 大客户申请人 */
      bigCompanyApplyMan: string;

      /** 大客户审批意见 */
      bigCompanyAudit: string;

      /** 大客户审核时间 */
      bigCompanyAuditDate: string;

      /** 大客户审核人 */
      bigCompanyAuditMan: string;

      /** 大客户处理原由 */
      bigCompanyCause: string;

      /** 大客户处理流程实例id */
      bigCompanyProcessId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务结束时间 */
      bizEndDt: string;

      /** 业务范围 */
      bizScope: string;

      /** 业务开始时间 */
      bizStartDt: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** 分公司id */
      branchId: string;

      /** 分公司名称 */
      branchName: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** 渠道ID */
      channelId: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 公司所在城市编码 */
      companyCityId: string;

      /** 公司所在城市 */
      companyCityName: string;

      /** 联系人1职位 */
      contact1Post: string;

      /** 联系人2职位 */
      contact2Post: string;

      /** 联系人手机 */
      contactCell: string;

      /** 联系人手机2 */
      contactCell2: string;

      /** 联系人性别1 */
      contactGender1: string;

      /** 联系人性别2 */
      contactGender2: string;

      /** 联系人电话 */
      contactTel: string;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 合同编号 */
      contractCode: string;

      /** 合同id */
      contractId: string;

      /** 合同名称 */
      contractName: string;

      /** 核心业务需求 */
      coreBizReqId: string;

      /** 核心业务需求 */
      coreBizReqName: string;

      /** 钉钉客户编号 */
      corpId: string;

      /** 国家ID */
      countryId: string;

      /** 国家Name */
      countryName: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** 创建时间到 */
      createDtEnd: string;

      /** 创建时间从 */
      createDtStart: string;

      /** 创建人姓名 */
      creatorName: string;

      /** 客户资信ID */
      creditHisId: string;

      /** 客户资信Name */
      creditHisName: string;

      /** 客户办公地址所在区县ID （前期可不用） */
      csOfficeCountyId: string;

      /** 客户办公地址所在区县Name */
      csOfficeCountyName: string;

      /** 客户编码 */
      custCode: string;

      /** 客户英文名称 */
      custEnglishName: string;

      /** 客户id */
      custId: string;

      /** 客户中文名称 */
      custName: string;

      /** 客户缩写名称 */
      custShortName: string;

      /** 新增<br>	  从陌生拜访转移 */
      custTransferStatus: string;

      /** 客户类型:0 直销 1 渠道,2:市场 */
      custType: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** 分公司id */
      departmentId: string;

      /** departmentName */
      departmentName: string;

      /** 分支机构分布情况 */
      distributionBranches: string;

      /** 企业性质ID */
      ePropertyId: string;

      /** 企业性质Name */
      ePropertyName: string;

      /** 企业规模ID */
      eSizeId: string;

      /** 企业规模Name */
      eSizeName: string;

      /** 联系人邮件 */
      email: string;

      /** 联系人邮件2 */
      email2: string;

      /** 员工编号 */
      empCode: string;

      /** 订单id/上下岗id */
      empHireSepId: string;

      /** 员工id */
      empId: string;

      /** 员工姓名 */
      empName: string;

      /** endIndex */
      endIndex: number;

      /** 比较的客户名称 */
      equalCustName: string;

      /** 导入类型,扩充使用 */
      expType: string;

      /** 联系人传真 */
      fax: string;

      /** 联系人传真2 */
      fax2: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** 成立月份 */
      foundMonth: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** 如果与company_id相同，表示自己是同一个集团，如果是不一样的，表示是某个集团下的一个成员，程序需要控制只有两层关系 */
      groupId: string;

      /** 是否有分支机构 ：1有，0 否' */
      haveBranches: string;

      /** 历史客户编号 */
      hisFormalCust: defs.crm.FormalCust;

      /** 人力资源联系人 */
      hrContract: string;

      /** 证件号码 */
      idCardNum: string;

      /** 导入批次号 */
      impBatchId: string;

      /** inId */
      inId: string;

      /** 所属行业ID */
      industryId: string;

      /** 所属行业Name */
      industryName: string;

      /** 保险联系人 */
      insuranceContact: string;

      /** 保险联系电话 */
      insuranceContactTel: string;

      /** 情况简介 */
      introduction: string;

      /** 是否是大公司 */
      isBigCompany: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 是否签约合同<br>	  1是<br>	  0否 */
      isContract: string;

      /** 删除标记 */
      isDeleted: string;

      /** 是否删除 */
      isDeletedText: string;

      /** 是否上市ID */
      isPublicTradedId: string;

      /** 是否上市Name */
      isPublicTradedName: string;

      /** 是否签约 */
      isSign: string;

      /** 是否签约text */
      isSignText: string;

      /** 1:未删除0:已删除2:缓存状态 */
      isValid: string;

      /** 是否薪资查询 */
      isWageQuery: string;

      /** 法人代表 */
      legalRep: string;

      /** 备注 */
      memo: string;

      /** 微博公众号 */
      microblogPublicAccount: string;

      /** 模拟人 */
      mimicBy: string;

      /** 审批意见 */
      newApproveOpinion: string;

      /** noChange */
      noChange: boolean;

      /** ������id.���ڼ�¼��־ʹ�� */
      oldSalesId: string;

      /** 组织机构代码 */
      organizationCode: string;

      /** 组织备注 */
      organizationRemark: string;

      /** 使用过外包服务商ID */
      outSvcProviderId: string;

      /** 使用过外包服务商Name */
      outSvcProviderName: string;

      /** 页数 */
      pageNum: number;

      /** 每页记录数,默认65536条 */
      pageSize: number;

      /** 审批人 */
      participant: string;

      /** 产品线获取日期到 */
      proLineGetDtEnd: string;

      /** 产品线获取日期从 */
      proLineGetDtStart: string;

      /** 产品id串 */
      proLineIdStr: string;

      /** proLineIdStrRemain */
      proLineIdStrRemain: string;

      /** proLinePKStr */
      proLinePKStr: string;

      /** 产品线销售id */
      proLineSalesId: string;

      /** 销售姓名 */
      proLineSalesName: string;

      /** 产品线 */
      proLineStr: string;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 非正式客户id */
      prospectiveCustId: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** 注册地址 */
      regAddress: string;

      /** 注册资金（万） */
      regCapital: string;

      /** 注册到期日期 */
      regEndDt: string;

      /** 注册开始日期 */
      regStartDt: string;

      /** 相关业务需求 */
      relaReqId: string;

      /** 相关业务需求 */
      relaReqName: string;

      /** 获得需求时间 */
      reqAquirementDt: string;

      /** 需求获得方式ID */
      reqAquirementTypeId: string;

      /** 需求获得方式Name */
      reqAquirementTypeName: string;

      /** 需求备注 */
      reqRemark: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 销售人员所在城市编码 */
      salesCityId: string;

      /** 销售人员所在城市 */
      salesCityName: string;

      /** 所属销售人员 */
      salesCode: string;

      /** 销售组 */
      salesGroup: string;

      /** 客户编号 */
      salesId: string;

      /** 销售人员名字 */
      salesName: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** 客户来源ID */
      sourceId: string;

      /** 客户来源Name */
      sourceName: string;

      /** startIndex */
      startIndex: number;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** 小合同编号 */
      subcontractCode: string;

      /** 小合同id */
      subcontractId: string;

      /** 小合同名称 */
      subcontractName: string;

      /** tag */
      tag: string;

      /** 临时客户编号 */
      tempCustCode: string;

      /** 已经恢复次数 */
      timesOfRecovery: string;

      /** 转移时间 */
      transTime: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** 更新人姓名 */
      updaterName: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;

      /** 访问状态 */
      visitStatus: string;

      /** 网址 */
      website: string;

      /** 微信公众号 */
      wechatPublicAccount: string;

      /** 办公地址 */
      workAddress: string;

      /** 邮政编码（办公地址） */
      zipCode: string;
    }

    export class FormalCustVo {
      /** cityId */
      cityId: string;

      /** cityName */
      cityName: string;

      /** contact1Post */
      contact1Post: string;

      /** contactTel */
      contactTel: string;

      /** corpId */
      corpId: string;

      /** crmCustId */
      crmCustId: string;

      /** custCode */
      custCode: string;

      /** custEnglishName */
      custEnglishName: string;

      /** custId */
      custId: string;

      /** custName */
      custName: string;

      /** custShortName */
      custShortName: string;

      /** ePropertyId */
      ePropertyId: string;

      /** ePropertyName */
      ePropertyName: string;

      /** eSizeId */
      eSizeId: string;

      /** eSizeName */
      eSizeName: string;

      /** haveBranches */
      haveBranches: string;

      /** hrContract */
      hrContract: string;

      /** industryId */
      industryId: string;

      /** industryName */
      industryName: string;

      /** organizationCode */
      organizationCode: string;

      /** productLine */
      productLine: Array<defs.crm.ProductLine>;

      /** prospectiveCustId */
      prospectiveCustId: string;

      /** reqAquirementDt */
      reqAquirementDt: string;

      /** reqAquirementTypeId */
      reqAquirementTypeId: string;

      /** reqAquirementTypeName */
      reqAquirementTypeName: string;

      /** salesCode */
      salesCode: string;

      /** salesId */
      salesId: string;

      /** salesName */
      salesName: string;

      /** sourceId */
      sourceId: string;

      /** sourceName */
      sourceName: string;

      /** userName */
      userName: string;

      /** workAddress */
      workAddress: string;
    }

    export class FormalQuery {
      /** 大区 */
      areaId: string;

      /** 分公司id */
      branchId: string;

      /** 客户所在城市 */
      companyCityId: string;

      /** 创建日期到 */
      createDtEnd: string;

      /** 创建日期从 */
      createDtStart: string;

      /** 客户编号 */
      custCode: string;

      /** 客户id */
      custId: string;

      /** 客户名称 */
      custName: string;

      /** endIndex */
      endIndex: number;

      /** 飞正式客户id */
      ifmCustId: string;

      /** 删除状态 */
      isDeleted: string;

      /** 销售是否签约 */
      isSign: string;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** 产品线获取日期到 */
      proLineGetDtEnd: string;

      /** 产品线获取日期从 */
      proLineGetDtStart: string;

      /** 产品线销售id */
      proLineSalesId: string;

      /** 销售名字 */
      proLineSalesName: string;

      /** salesIdFilterTrack */
      salesIdFilterTrack: string;

      /** startIndex */
      startIndex: number;

      /** 跟进日期到 */
      trackDateEnd: string;

      /** 跟进日期从 */
      trackDateStart: string;
    }

    export class GlobalResult<T0 = any> {
      /** code */
      code: string;

      /** data */
      data: T0;

      /** message */
      message: string;
    }

    export class HsSlCustomerVisit {
      /** accountsBill */
      accountsBill: string;

      /** add */
      add: boolean;

      /** areaId */
      areaId: string;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** branchId */
      branchId: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** competitors */
      competitors: string;

      /** competitorsServiceinfo */
      competitorsServiceinfo: string;

      /** competitorsStrong */
      competitorsStrong: string;

      /** continuedTime */
      continuedTime: string;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** custCode */
      custCode: string;

      /** custId */
      custId: string;

      /** custName */
      custName: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** customerNeed */
      customerNeed: string;

      /** customerQuestion */
      customerQuestion: string;

      /** customerVisitId */
      customerVisitId: string;

      /** del */
      del: boolean;

      /** deptttype */
      deptttype: string;

      /** endIndex */
      endIndex: number;

      /** enterpriseScale */
      enterpriseScale: string;

      /** estimatedTime */
      estimatedTime: string;

      /** 导入类型,扩充使用 */
      expType: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** followingInfo */
      followingInfo: string;

      /** followingInfoEdit */
      followingInfoEdit: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** isExecute */
      isExecute: string;

      /** isExecuteStr */
      isExecuteStr: string;

      /** 是否薪资查询 */
      isWageQuery: string;

      /** jointMember */
      jointMember: string;

      /** jointMemberStr */
      jointMemberStr: string;

      /** memberCount */
      memberCount: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** optimizationMethods */
      optimizationMethods: string;

      /** 页数 */
      pageNum: number;

      /** 每页记录数,默认65536条 */
      pageSize: number;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** realName */
      realName: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** serviceCitys */
      serviceCitys: string;

      /** serviceCount */
      serviceCount: string;

      /** serviceCountIndaili */
      serviceCountIndaili: string;

      /** serviceCountInproxy */
      serviceCountInproxy: string;

      /** serviceProducts */
      serviceProducts: string;

      /** startIndex */
      startIndex: number;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;

      /** visitAddress */
      visitAddress: string;

      /** visitDate */
      visitDate: string;

      /** visitDateEd */
      visitDateEd: string;

      /** visitor */
      visitor: string;
    }

    export class IfmCust {
      /** 市场活动编号 */
      activityCode: string;

      /** 市场活动id */
      activityId: string;

      /** 市场活动名称 */
      activityName: string;

      /** add */
      add: boolean;

      /** 审批状态:<br>	  0:初始(默认)<br>	  0:初始<br>	  1:提交审核,<br>	  2:者转化成功,<br>	  -1:转化失败, */
      approvalStatus: string;

      /** approvalStatusName */
      approvalStatusName: string;

      /** approveOpinion */
      approveOpinion: string;

      /** 大区id */
      areaId: string;

      /** 大区名称 */
      areaName: string;

      /** 银行账号 */
      bankAcct: string;

      /** 开户行名称 */
      bankName: string;

      /** 银行开户名 */
      bankNum: string;

      /** 所属银行, 取值为数据字典type=911记录中base_data_code值. */
      bankType: string;

      /** 批次号,用于备份 */
      batchId: string;

      /** 大客户申请时间 */
      bigCompanyApplyDate: string;

      /** 大客户申请人 */
      bigCompanyApplyMan: string;

      /** 大客户审批意见 */
      bigCompanyAudit: string;

      /** 大客户审核时间 */
      bigCompanyAuditDate: string;

      /** 大客户审核人 */
      bigCompanyAuditMan: string;

      /** 大客户处理原由 */
      bigCompanyCause: string;

      /** 大客户处理流程实例id */
      bigCompanyProcessId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务结束时间 */
      bizEndDt: string;

      /** 业务范围 */
      bizScope: string;

      /** 业务开始时间 */
      bizStartDt: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** 分公司id */
      branchId: string;

      /** 分公司名称 */
      branchName: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** 同一个客户名称在审批过程中存在的次数 */
      cNameReptCount: string;

      /** 渠道ID */
      channelId: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 公司所在城市编码 */
      companyCityId: string;

      /** 公司所在城市 */
      companyCityName: string;

      /** 联系人1职位 */
      contact1Post: string;

      /** 联系人2职位 */
      contact2Post: string;

      /** 联系人手机 */
      contactCell: string;

      /** 联系人手机2 */
      contactCell2: string;

      /** 联系人性别1 */
      contactGender1: string;

      /** 联系人性别2 */
      contactGender2: string;

      /** 联系人电话 */
      contactTel: string;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 合同编号 */
      contractCode: string;

      /** 合同id */
      contractId: string;

      /** 合同名称 */
      contractName: string;

      /** 核心业务需求 */
      coreBizReqId: string;

      /** 核心业务需求 */
      coreBizReqName: string;

      /** 钉钉客户编号 */
      corpId: string;

      /** 国家ID */
      countryId: string;

      /** 国家Name */
      countryName: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** 创建时间到 */
      createDtEnd: string;

      /** 创建时间从 */
      createDtStart: string;

      /** 创建人姓名 */
      creatorName: string;

      /** 客户资信ID */
      creditHisId: string;

      /** 客户资信Name */
      creditHisName: string;

      /** 客户办公地址所在区县ID （前期可不用） */
      csOfficeCountyId: string;

      /** 客户办公地址所在区县Name */
      csOfficeCountyName: string;

      /** currentOwnerSalesId */
      currentOwnerSalesId: string;

      /** 客户编码 */
      custCode: string;

      /** 客户英文名称 */
      custEnglishName: string;

      /** 客户id */
      custId: string;

      /** 客户中文名称 */
      custName: string;

      /** 客户缩写名称 */
      custShortName: string;

      /** 客户状态(客户为市场客户的时候存值):<br>	  1,新增<br>	  2,已有未签约<br>	  3,已有已签约 */
      custStatus: string;

      /** 新增<br>	  从陌生拜访转移 */
      custTransferStatus: string;

      /** 客户类型:0 直销 1 渠道,2:市场 */
      custType: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** 分公司id */
      departmentId: string;

      /** departmentName */
      departmentName: string;

      /** 分支机构分布情况 */
      distributionBranches: string;

      /** 企业性质ID */
      ePropertyId: string;

      /** 企业性质Name */
      ePropertyName: string;

      /** 企业规模ID */
      eSizeId: string;

      /** 企业规模Name */
      eSizeName: string;

      /** 联系人邮件 */
      email: string;

      /** 联系人邮件2 */
      email2: string;

      /** 员工编号 */
      empCode: string;

      /** 订单id/上下岗id */
      empHireSepId: string;

      /** 员工id */
      empId: string;

      /** 员工姓名 */
      empName: string;

      /** endIndex */
      endIndex: number;

      /** 比较的客户名称 */
      equalCustName: string;

      /** 导入类型,扩充使用 */
      expType: string;

      /** 联系人传真 */
      fax: string;

      /** 联系人传真2 */
      fax2: string;

      /** fileId */
      fileId: string;

      /** fileIds */
      fileIds: Array<object>;

      /** fileName */
      fileName: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** 成立月份 */
      foundMonth: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** 如果与company_id相同，表示自己是同一个集团，如果是不一样的，表示是某个集团下的一个成员，程序需要控制只有两层关系 */
      groupId: string;

      /** 是否有分支机构 ：1有，0 否' */
      haveBranches: string;

      /** 历史客户编号 */
      hisFormalCust: defs.crm.FormalCust;

      /** 人力资源联系人 */
      hrContract: string;

      /** 证件号码 */
      idCardNum: string;

      /** 客户id */
      ifmCustId: string;

      /** 非正式客户类别,<br>	  1:陌拜客户<br>	  2:注册客户<br>	  3:市场客户 */
      ifmCustType: string;

      /** 导入批次号 */
      impBatchId: string;

      /** inId */
      inId: string;

      /** 所属行业ID */
      industryId: string;

      /** 所属行业Name */
      industryName: string;

      /** 保险联系人 */
      insuranceContact: string;

      /** 保险联系电话 */
      insuranceContactTel: string;

      /** 情况简介 */
      introduction: string;

      /** 是否是大公司 */
      isBigCompany: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 是否签约合同<br>	  1是<br>	  0否 */
      isContract: string;

      /** 删除标记 */
      isDeleted: string;

      /** 是否删除 */
      isDeletedText: string;

      /** 是否上市ID */
      isPublicTradedId: string;

      /** 是否上市Name */
      isPublicTradedName: string;

      /** 是否签约 */
      isSign: string;

      /** 是否签约text */
      isSignText: string;

      /** 1:未删除0:已删除2:缓存状态 */
      isValid: string;

      /** 是否薪资查询 */
      isWageQuery: string;

      /** 法人代表 */
      legalRep: string;

      /** 备注 */
      memo: string;

      /** 微博公众号 */
      microblogPublicAccount: string;

      /** 模拟人 */
      mimicBy: string;

      /** 审批意见 */
      newApproveOpinion: string;

      /** noChange */
      noChange: boolean;

      /** ������id.���ڼ�¼��־ʹ�� */
      oldSalesId: string;

      /** 组织机构代码 */
      organizationCode: string;

      /** 组织备注 */
      organizationRemark: string;

      /** 使用过外包服务商ID */
      outSvcProviderId: string;

      /** 使用过外包服务商Name */
      outSvcProviderName: string;

      /** 页数 */
      pageNum: number;

      /** 每页记录数,默认65536条 */
      pageSize: number;

      /** 审批人 */
      participant: string;

      /** 产品线获取日期到 */
      proLineGetDtEnd: string;

      /** 产品线获取日期从 */
      proLineGetDtStart: string;

      /** 产品id串 */
      proLineIdStr: string;

      /** proLineIdStrRemain */
      proLineIdStrRemain: string;

      /** proLinePKStr */
      proLinePKStr: string;

      /** 产品线销售id */
      proLineSalesId: string;

      /** 销售姓名 */
      proLineSalesName: string;

      /** 产品线 */
      proLineStr: string;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 流程实例id */
      processInsId: string;

      /** 非正式客户id */
      prospectiveCustId: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** 注册地址 */
      regAddress: string;

      /** 注册资金（万） */
      regCapital: string;

      /** 注册到期日期 */
      regEndDt: string;

      /** 注册开始日期 */
      regStartDt: string;

      /** 相关业务需求 */
      relaReqId: string;

      /** 相关业务需求 */
      relaReqName: string;

      /** 获得需求时间 */
      reqAquirementDt: string;

      /** 需求获得方式ID */
      reqAquirementTypeId: string;

      /** 需求获得方式Name */
      reqAquirementTypeName: string;

      /** 需求备注 */
      reqRemark: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 销售人员所在城市编码 */
      salesCityId: string;

      /** 销售人员所在城市 */
      salesCityName: string;

      /** 所属销售人员 */
      salesCode: string;

      /** 销售组 */
      salesGroup: string;

      /** 客户编号 */
      salesId: string;

      /** 销售人员名字 */
      salesName: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** 客户来源ID */
      sourceId: string;

      /** 客户来源Name */
      sourceName: string;

      /** startIndex */
      startIndex: number;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** 小合同编号 */
      subcontractCode: string;

      /** 小合同id */
      subcontractId: string;

      /** 小合同名称 */
      subcontractName: string;

      /** tag */
      tag: string;

      /** 临时客户编号 */
      tempCustCode: string;

      /** 已经恢复次数 */
      timesOfRecovery: string;

      /** 转移时间 */
      transTime: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** 更新人姓名 */
      updaterName: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;

      /** 访问状态 */
      visitStatus: string;

      /** 网址 */
      website: string;

      /** 微信公众号 */
      wechatPublicAccount: string;

      /** 办公地址 */
      workAddress: string;

      /** workitemId */
      workitemId: string;

      /** 邮政编码（办公地址） */
      zipCode: string;
    }

    export class IfmCustQuery {
      /** 市场活动名称 */
      activityName: string;

      /** 大区 */
      areaId: string;

      /** 分公司id */
      branchId: string;

      /** 客户所在城市 */
      companyCityId: string;

      /** 注册日期到 */
      createDtEnd: string;

      /** 注册日期从 */
      createDtStart: string;

      /** 当前销售id */
      currentOwnerSalesId: string;

      /** 客户名称 */
      custName: string;

      /** 分公司 */
      departmentId: string;

      /** endIndex */
      endIndex: number;

      /** 非正式客户类型 */
      ifmCustType: string;

      /** 删除状态 */
      isDeleted: string;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** 流程操作人 */
      participant: string;

      /** 销售 */
      salesName: string;

      /** startIndex */
      startIndex: number;
    }

    export class Map<T0 = any, T1 = any> {}

    export class MarketActivityQuery {
      /** 举办城市 */
      activityCityId: string;

      /** 市场活动编号 */
      activityCode: string;

      /** 预估费用额 */
      activityCost: string;

      /** 主键 */
      activityId: string;

      /** 市场活动名称 */
      activityName: string;

      /** custCount */
      custCount: string;

      /** 结束日期 */
      endDt: string;

      /** endIndex */
      endIndex: number;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** 说明 */
      remark: string;

      /** 开始日期 */
      startDt: string;

      /** startIndex */
      startIndex: number;

      /** status */
      status: string;
    }

    export class ModelContract {
      /** add */
      add: boolean;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** createByName */
      createByName: string;

      /** 创建日期 */
      createDt: string;

      /** createDtFrom */
      createDtFrom: string;

      /** createDtTo */
      createDtTo: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column CRM_MODEL_CONTRACT.EFFECT_BY           ibatorgenerated Wed Feb 26 14:23:58 CST 2014 */
      effectBy: string;

      /** effectByName */
      effectByName: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column CRM_MODEL_CONTRACT.EFFECT_DT           ibatorgenerated Wed Feb 26 14:23:58 CST 2014 */
      effectDt: string;

      /** endIndex */
      endIndex: number;

      /** 导入类型,扩充使用 */
      expType: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column CRM_MODEL_CONTRACT.FILE_CODE           ibatorgenerated Wed Feb 26 14:23:58 CST 2014 */
      fileCode: string;

      /** fileName */
      fileName: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column CRM_MODEL_CONTRACT.FILE_PATH           ibatorgenerated Wed Feb 26 14:23:58 CST 2014 */
      filePath: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** 是否薪资查询 */
      isWageQuery: string;

      /** 模拟人 */
      mimicBy: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column CRM_MODEL_CONTRACT.MODEL_CONTRACT_ID           ibatorgenerated Wed Feb 26 14:23:58 CST 2014 */
      modelContractId: string;

      /** noChange */
      noChange: boolean;

      /** 页数 */
      pageNum: number;

      /** 每页记录数,默认65536条 */
      pageSize: number;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column CRM_MODEL_CONTRACT.REMARK           ibatorgenerated Wed Feb 26 14:23:58 CST 2014 */
      remark: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** startIndex */
      startIndex: number;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column CRM_MODEL_CONTRACT.STATUS           ibatorgenerated Wed Feb 26 14:23:58 CST 2014 */
      status: string;

      /** statusName */
      statusName: string;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column CRM_MODEL_CONTRACT.UN_EFFECT_BY           ibatorgenerated Wed Feb 26 14:23:58 CST 2014 */
      unEffectBy: string;

      /** unEffectByName */
      unEffectByName: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column CRM_MODEL_CONTRACT.UN_EFFECT_DT           ibatorgenerated Wed Feb 26 14:23:58 CST 2014 */
      unEffectDt: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;
    }

    export class ModelContractDTO {
      /** 创建人名称 */
      createByName: string;

      /** 创建日期起 */
      createDtFrom: string;

      /** 创建日期到 */
      createDtTo: string;

      /** 生效人 */
      effectBy: string;

      /** 生效人name */
      effectByName: string;

      /** 生效日期 */
      effectDt: string;

      /** 内部编号 */
      fileCode: string;

      /** 文件名称 */
      fileName: string;

      /** 附件路径 */
      filePath: string;

      /** 范本合同表id */
      modelContractId: string;

      /** 备注 */
      remark: string;

      /** 状态 0:未生效 1:生效 2:失效 */
      status: string;

      /** 状态name */
      statusName: string;

      /** 失效人 */
      unEffectBy: string;

      /** 失效人name */
      unEffectByName: string;

      /** 失效日期 */
      unEffectDt: string;
    }

    export class ModelContractQuery {
      /** 上传日期从 */
      createDtFrom: string;

      /** 上传日期到 */
      createDtTo: string;

      /** endIndex */
      endIndex: number;

      /** 文件名称 */
      fileName: string;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** startIndex */
      startIndex: number;
    }

    export class NonStaCoctApprDTO {
      /** 流程节点表的活动英文名称 */
      activityNameEn: string;

      /** 申请单编号 */
      applyCode: string;

      /** 申请见票付款 0 否 1 是 */
      applyTicketPay: string;

      /** 审核意见(暂不用) */
      approvalOpinion: string;

      /** 审批意见 */
      approvalRemark: string;

      /** 审批通过日期 */
      approveDt: string;

      /** 审批通过日期从 */
      approveDtFrom: string;

      /** 审批通过日期到 */
      approveDtTo: string;

      /** 当前审批步骤 */
      approveName: string;

      /** 销售所属大区 */
      areaName: string;

      /** 销售所属分公司 */
      branchName: string;

      /** 城市id */
      cityId: string;

      /** 合同类型 */
      contractType: string;

      /** 创建人name */
      createByName: string;

      /** 申请日期从 */
      createDtFrom: string;

      /** 申请日期到 */
      createDtTo: string;

      /** 现销售 */
      currentSales: string;

      /** 现销售Name */
      currentSalesName: string;

      /** 客户编号 */
      custCode: string;

      /** 客户服务费低于供应商服务费 0 否 1 是 */
      custFeeUnderSupplierFee: string;

      /** 客户服务费低于供应商服务费涉及城市 */
      custFeeUnderSupplierFeeCi: string;

      /** 客户服务费低于供应商服务费服务收费 */
      custFeeUnderSupplierFeeFe: string;

      /** 客户服务费低于供应商服务费签约人数 */
      custFeeUnderSupplierFeeNu: string;

      /** 客户id */
      custId: string;

      /** 客户名称 */
      custName: string;

      /** 暂缓收取或免缴工本费、采暖费等费用 0 否 1 是 */
      deferredNoCostHeating: string;

      /** 暂缓收取或免缴工本费、采暖费等费用涉及城市 */
      deferredNoCostHeatingCity: string;

      /** 暂缓收取或免缴工本费、采暖费等费用服务收费 */
      deferredNoCostHeatingFee: string;

      /** 暂缓收取或免缴工本费、采暖费等费用签约人数 */
      deferredNoCostHeatingNum: string;

      /** 暂缓收取或免缴残障金 0 否 1 是 */
      deferredNoDisabledGold: string;

      /** 暂缓收取或免缴残障金涉及城市 */
      deferredNoDisabledGoldCity: string;

      /** 暂缓收取或免缴残障金服务收费 */
      deferredNoDisabledGoldFee: string;

      /** 暂缓收取或免缴残障金签约人数 */
      deferredNoDisabledGoldNum: string;

      /** 部门id */
      deptId: string;

      /** 上门服务 0 否 1 是 */
      doorToDoorService: string;

      /** 驻场客服 0 否 1 是 */
      fieldCustomerService: string;

      /** 驻场客服附件 */
      fieldCustomerServiceFid: string;

      /** 是否驻场客服Name */
      fieldCustomerServiceName: string;

      /** 5人以下客户非季度付费 0 否 1 是 */
      fivePeopleCust: string;

      /** 5人以下客户非季度付费附件 */
      fivePeopleCustFid: string;

      /** 是否5人以下客户非季度付费Name */
      fivePeopleCustName: string;

      /** 销售所属大区 */
      governingArea: string;

      /** 销售所属分公司 */
      governingBranch: string;

      /** 审批是否通过 */
      isApproval: string;

      /** 审批单总体是否通过 0 否 1 是 */
      isApprovalAll: string;

      /** 审批单总体是否通过Name */
      isApprovalAllName: string;

      /** 约定不开具发票 0 否 1 是 */
      noInvoice: string;

      /** 约定不开具发票附件 */
      noInvoiceFid: string;

      /** 是否约定不开具发票Name */
      noInvoiceName: string;

      /** 不缴纳公积金 0 否 1 是 */
      nonPaymentFund: string;

      /** 不缴纳公积金附件 */
      nonPaymentFundFid: string;

      /** 是否不缴纳公积金Name */
      nonPaymentFundName: string;

      /** 非标合同审批单表id */
      nonStaCoctApprId: string;

      /** 非标准账期 1 新建合同审批 2 已有合同更改回款日或收费频率 */
      nonStandardBillDate: string;

      /** 非标准账期附件 */
      nonStandardBillDateFid: string;

      /** 非标准账期Name */
      nonStandardBillDateName: string;

      /** 协助办理退休产品，低于成本价或非标准服务 0 否 1 是 */
      nonStandardService: string;

      /** 审批人 */
      participant: string;

      /** 代发工资时间为月末最后一个工作日 0 否 1 是 */
      payTimeLastDay: string;

      /** 代发工资时间为月末最后一个工作日附件 */
      payTimeLastDayFid: string;

      /** 是否代发工资时间为月末最后一个工作日Name */
      payTimeLastDayName: string;

      /** 工资发放地与个税缴纳地不一致 0 否 1 是 */
      paymentTaxDiffer: string;

      /** 工资发放地与个税缴纳地不一致附件 */
      paymentTaxDifferFid: string;

      /** 是否工资发放地与个税缴纳地不一致Name */
      paymentTaxDifferName: string;

      /** 流程实例创建人 */
      piCreateBy: string;

      /** 流程实例id */
      processInsId: string;

      /** 备注 */
      remark: string;

      /**  深圳集中投保 0 否 1 是 */
      shenzhenCentralInsurance: string;

      /** 深圳集中投保附件 */
      shenzhenCentralInsuranceFid: string;

      /** 是否深圳集中投保Name */
      shenzhenCentralInsuranceName: string;

      /** 签约方、付款方、发票抬头三者不一致 0 否 1 是 */
      signPayBillDiffer: string;

      /** 签约方、付款方、发票抬头三者不一致附件 */
      signPayBillDifferFid: string;

      /** 是否签约方、付款方、发票抬头三者不一致Name */
      signPayBillDifferName: string;

      /** 特殊工资卡 1 正常代发工资卡银行 2 其他银行 */
      specialSalaryCard: string;

      /** 0 初始、1 审批中、2 退回修改、3 审批通过、4 流程结束 */
      status: string;

      /** 审批单状态 */
      statusName: string;

      /** 落地发工资、落地报个税 0 否 1 是 */
      wagesTax: string;

      /** 与客户签订反委托代发工资协议，工资实发由客户自己发，个税由易才大户申报。 */
      wagesTax1: string;

      /** 工资由客户自己发，个税通过易才大户申报 */
      wagesTax2: string;

      /** 工资由客户自己发，要求易才提供通过客户报税系统代为申报 */
      wagesTax3: string;

      /** 落地发工资、落地报个税附件 */
      wagesTaxFid: string;

      /** 是否落地发工资Name */
      wagesTaxName: string;

      /** 工作流id */
      workItemId: string;
    }

    export class NonStaCoctApprQuery {
      /** endIndex */
      endIndex: number;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** startIndex */
      startIndex: number;
    }

    export class Page {
      /** currentPage */
      currentPage: number;

      /** currentPageNo */
      currentPageNo: number;

      /** data */
      data: Array<object>;

      /** pageSize */
      pageSize: number;

      /** result */
      result: Array<object>;

      /** start */
      start: number;

      /** totalCount */
      totalCount: number;

      /** totalPage */
      totalPage: number;

      /** totalPageCount */
      totalPageCount: number;
    }

    export class ProductLine {
      /** createDt */
      createDt: string;

      /** custId */
      custId: string;

      /** isValid */
      isValid: string;

      /** productLineMappingId */
      productLineMappingId: string;

      /** productline */
      productline: string;

      /** productlineCompanyId */
      productlineCompanyId: string;

      /** productlineId */
      productlineId: string;

      /** productlineSalerId */
      productlineSalerId: string;

      /** productlineSalerName */
      productlineSalerName: string;

      /** productlineSalerProviderId */
      productlineSalerProviderId: string;

      /** productlineSalerProvidername */
      productlineSalerProvidername: string;

      /** productlineStatus */
      productlineStatus: number;

      /** productlineType */
      productlineType: string;

      /** respCode */
      respCode: string;

      /** respMessage */
      respMessage: string;

      /** salerId */
      salerId: string;

      /** updateDt */
      updateDt: string;

      /** userName */
      userName: string;
    }

    export class QuotationItem {
      /** add */
      add: boolean;

      /** approvePrice */
      approvePrice: number;

      /** 附加税 */
      at: number;

      /** 附加税的税率% */
      ator: number;

      /** atr */
      atr: number;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 预计数量 */
      countNum: number;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** endIndex */
      endIndex: number;

      /** 导入类型,扩充使用 */
      expType: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 毛利率 */
      gmr: number;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_ITEM.IS_ORDER_UPDATE	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
      isOrderUpdate: number;

      /** 是否薪资查询 */
      isWageQuery: string;

      /** isYearsToPay */
      isYearsToPay: number;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** 页数 */
      pageNum: number;

      /** 每页记录数,默认65536条 */
      pageSize: number;

      /** 标准售价 */
      priceAt: number;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_ITEM.PRODUCT_COST	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
      productCost: number;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_ITEM.PRODUCT_ID	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
      productId: number;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_ITEM.PRODUCT_PRICE	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
      productPrice: number;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** quotationGroupId */
      quotationGroupId: number;

      /** quotationGroupItemId */
      quotationGroupItemId: number;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_ITEM.QUOTATION_ID	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
      quotationId: number;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_ITEM.QUOTATION_ITEM_ID	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
      quotationItemId: number;

      /** quotationItemType */
      quotationItemType: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_ITEM.REMARK	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
      remark: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 标准售价（含附加税） */
      salesPrice: number;

      /** salesPriceNoTax */
      salesPriceNoTax: number;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** startIndex */
      startIndex: number;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** 总成本 */
      totalCost: number;

      /** 总售价 */
      totalPrice: number;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;

      /** vat */
      vat: number;

      /** vatr */
      vatr: number;

      /** virtualProductId */
      virtualProductId: number;
    }

    export class QuotationItemDetail {
      /** approvePrice */
      approvePrice: number;

      /** 附加税 */
      at: number;

      /** 附加税的税率% */
      ator: number;

      /** atr */
      atr: number;

      /** 预计数量 */
      countNum: number;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_ITEM_DETAIL.QUOTATION_ITEM_DETAIL_ID	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
      detailType: number;

      /** 毛利率 */
      gmr: number;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_ITEM_DETAIL.IF_FOR_THIRDPART	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
      ifForThirdpart: number;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_ITEM_DETAIL.IS_YEARS_TO_PAY	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
      isYearsToPay: number;

      /** 标准售价 */
      priceAt: number;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_ITEM_DETAIL.PRODUCT_COST	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
      productCost: number;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_ITEM_DETAIL.PRODUCT_DESC	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
      productDesc: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_ITEM_DETAIL.PRODUCT_ID	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
      productId: number;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_ITEM_DETAIL.PRODUCT_LINE_ID	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
      productLineId: number;

      /** productLineName */
      productLineName: string;

      /** productName */
      productName: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_ITEM_DETAIL.PRODUCT_PRICE	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
      productPrice: number;

      /** quotationGroupId */
      quotationGroupId: number;

      /** quotationGroupItemDetailId */
      quotationGroupItemDetailId: number;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_ITEM_DETAIL.QUOTATION_ID	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
      quotationId: number;

      /** quotationItemDetailId */
      quotationItemDetailId: number;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_ITEM.QUOTATION_ITEM_ID	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
      quotationItemId: number;

      /** quotationItemType */
      quotationItemType: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column	  SL_QUOTATION_ITEM_DETAIL.QUOTATION_TEMPLATE_INFECT_ID	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
      quotationTemplateInfectId: number;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column	  SL_QUOTATION_ITEM_DETAIL.QUOTATION_TEMPLT_CITY_FACTOR	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
      quotationTempltCityFactor: number;

      /** quotationTempltName */
      quotationTempltName: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_ITEM_DETAIL.REMARK	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
      remark: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_ITEM_DETAIL.SALES_PRICE	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
      salesPrice: number;

      /** salesPriceNoTax */
      salesPriceNoTax: number;

      /** subLadder */
      subLadder: Array<defs.crm.QuotationLadder>;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_ITEM_DETAIL.SUPLMT_MED_END_DT	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
      suplmtMedEndDt: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_ITEM_DETAIL.SUPLMT_MED_START_DT	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
      suplmtMedStartDt: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_ITEM_DETAIL.SVC_AREA	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
      svcArea: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_ITEM_DETAIL.TEMPLATE_ID	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
      templateId: number;

      /** templtScope */
      templtScope: number;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_ITEM_DETAIL.TEMPLT_TYPE	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
      templtType: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_ITEM_DETAIL.THIRD_PARTY_PROVIDER_ID	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
      thirdPartyProviderId: number;

      /** thirdPartyProviderName */
      thirdPartyProviderName: string;

      /** 总成本 */
      totalCost: number;

      /** 总售价 */
      totalPrice: number;

      /** vat */
      vat: number;

      /** vatr */
      vatr: number;

      /** virtualProductId */
      virtualProductId: number;
    }

    export class QuotationLadder {
      /** add */
      add: boolean;

      /** 附加税 */
      at: number;

      /** 附加税的税率% */
      ator: number;

      /** atr */
      atr: number;

      /** 批次号,用于备份 */
      batchId: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_LADDER.BEGIN_NUMBER	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
      beginNumber: number;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** endIndex */
      endIndex: number;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_LADDER.END_NUMBER	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
      endNumber: number;

      /** 导入类型,扩充使用 */
      expType: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** 是否薪资查询 */
      isWageQuery: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** 页数 */
      pageNum: number;

      /** 每页记录数,默认65536条 */
      pageSize: number;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_LADDER.PRICE	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
      price: number;

      /** 标准售价（含附加税） */
      priceAt: number;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** productId */
      productId: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_LADDER.QUOTATION_ID	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
      quotationId: number;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_LADDER.QUOTATION_TEMPLATE_INFECT_ID	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
      quotationTemplateInfectId: number;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_LADDER.QUOTATION_TYPE	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
      quotationType: number;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** salesPriceNoTax */
      salesPriceNoTax: number;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** startIndex */
      startIndex: number;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;

      /** vat */
      vat: number;

      /** vatr */
      vatr: number;
    }

    export class QuotationSpecial {
      /** add */
      add: boolean;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_SPECIAL.BPO_PROJECT_ID	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
      bpoProjectId: number;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** endIndex */
      endIndex: number;

      /** 导入类型,扩充使用 */
      expType: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** 是否薪资查询 */
      isWageQuery: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** 页数 */
      pageNum: number;

      /** 每页记录数,默认65536条 */
      pageSize: number;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_SPECIAL.PRICE	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
      price: number;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_SPECIAL.QUOTATION_ID	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
      quotationId: number;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_SPECIAL.QUOTATION_SPECIAL_ID	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
      quotationSpecialId: number;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_SPECIAL.QUOTATION_TYPE	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
      quotationType: number;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** startIndex */
      startIndex: number;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;
    }

    export class QuotationSpecialProperty {
      /** add */
      add: boolean;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_SPECIAL_PROPERTY.AMT	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
      amt: number;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_SPECIAL_PROPERTY.CUSTOMIZE_TYPE	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
      customizeType: string;

      /** del */
      del: boolean;

      /** endIndex */
      endIndex: number;

      /** 导入类型,扩充使用 */
      expType: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** 是否薪资查询 */
      isWageQuery: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** 页数 */
      pageNum: number;

      /** 每页记录数,默认65536条 */
      pageSize: number;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_SPECIAL_PROPERTY.QUOTATION_ID	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
      quotationId: number;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column	  SL_QUOTATION_SPECIAL_PROPERTY.QUOTATION_SPECIAL_PROPERTY_HIS	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
      quotationSpecialPropertyHis: number;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column	  SL_QUOTATION_SPECIAL_PROPERTY.QUOTATION_SPECIAL_PROPERTY_ID	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
      quotationSpecialPropertyId: number;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_SPECIAL_PROPERTY.REMARK	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
      remark: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** startIndex */
      startIndex: number;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SL_QUOTATION_SPECIAL_PROPERTY.TYPE	  	  ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
      type: number;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;
    }

    export class SaleReportQuery {
      /** 统计年月（审批通过时间） */
      approveDt: string;

      /** 账单月份 */
      billYm: string;

      /** 创建时间到 */
      createDtEd: string;

      /** 创建时间从 */
      createDtSt: string;

      /** 客户编号 */
      custCode: string;

      /** 客户名称 */
      custName: string;

      /** endIndex */
      endIndex: number;

      /** 现销售所属大区 */
      newAreaId: string;

      /** 现销售姓名 */
      newSaleName: string;

      /** 现销售所属分公司 */
      newgoverningBranch: string;

      /** 原销售所属大区 */
      oldAreaId: string;

      /** 原销售姓名 */
      oldSaleName: string;

      /** 原销售所属分公司 */
      oldgoverningBranch: string;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** 客户服务情况 */
      queryType: string;

      /** startIndex */
      startIndex: number;
    }

    export class SendMailDTO {
      /** 邮件内容 */
      mailContent: string;

      /** 邮件标题 */
      mailTitle: string;

      /** 邮件收件人，支持多个逗号分隔 */
      saleEmails: string;

      /** 销售id多个 */
      saleIds: string;
    }

    export class ShareAreaDTO {
      /** list */
      list: Array<defs.crm.ShareAreaProductLines>;

      /** newSaleId */
      newSaleId: string;

      /** operateType */
      operateType: number;
    }

    export class ShareAreaProductLines {
      /** add */
      add: boolean;

      /** 大区id */
      areaId: string;

      /** 大区名称 */
      areaName: string;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** 客户城市 */
      cityName: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** 客户编号 */
      custCode: string;

      /** 创建日期到 */
      custDtEnd: string;

      /** 客户创建时间从 */
      custDtFrom: string;

      /** 客户id */
      custId: string;

      /** 删除状态 */
      custIsDeleted: string;

      /** 客户名称 */
      custName: string;

      /** 产品线获取日期到 */
      custProductLindDtEnd: string;

      /** 客户产品线创建时间从 */
      custProductLindDtFrom: string;

      /** 【客户所处状态】：1正在跟进、0删除区、2共享区 */
      custStatus: string;

      /** 【注册类型】：正式客户、陌拜客户、市场活动 */
      custType: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** 删除时间到 */
      delDtEnd: string;

      /** 删除时间从 */
      delDtFrom: string;

      /** 部门id */
      departmentId: string;

      /** 部门名称 */
      departmentName: string;

      /** endIndex */
      endIndex: number;

      /** 导入类型,扩充使用 */
      expType: string;

      /** 上传关联协议 */
      fileId: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** inId */
      inId: string;

      /** 客户所属行业 */
      industryName: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** 是否跨区 0或空否   1是  主要控制一些页面不限制分公司 一些限制 */
      isLocal: string;

      /** 判断销售是否就是当前登录人 */
      isMe: string;

      /** 是否薪资查询 */
      isWageQuery: string;

      /** 【市场活动】：市场活动名称； */
      marketName: string;

      /** 最长公示天数，用于判断销售是否可以拾取自己的,>=10可拾取 */
      maxShareDays: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** 操作类型2 共享区   0删除区 */
      operateType: string;

      /** 页数 */
      pageNum: number;

      /** 每页记录数,默认65536条 */
      pageSize: number;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 产品线创建时间 */
      productLinCreateDt: string;

      /** 产品线共享时间 */
      productLinShareDt: string;

      /** 产品线id */
      productLineId: string;

      /** 产品线映射表ID */
      productLineMappingId: string;

      /** 逗号分隔的客户产品线ID，用户按客户分配，存储客户销售的所有产品线 */
      productLineMappingIds: string;

      /** 产品线名称 */
      productLineName: string;

      /** 公示区产品线格式如代理(20120618,20120818),派遣(20120618,20120818),Payroll(20120618,20120818),新派遣(20120618,20120818),租赁(20120618,20120818),保险(20120618,20120818),福利(20120618,20120818),一次性产品(20120618,20120818),咨询(20120618,20120818),招聘(20120618,20120818) */
      productLines: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 销售id */
      saleId: string;

      /** 销售名称 */
      saleName: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** 共享时间到 */
      shareDtEnd: string;

      /** 共享时间从 */
      shareDtFrom: string;

      /** 客户来源 */
      sourceName: string;

      /** startIndex */
      startIndex: number;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;
    }

    export class ShareAreaProductLinesQuery {
      /** 大区id */
      areaId: string;

      /** 大区名称 */
      areaName: string;

      /** 客户城市 */
      cityName: string;

      /** 客户编号 */
      custCode: string;

      /** 创建日期到 */
      custDtEnd: string;

      /** 客户创建时间从 */
      custDtFrom: string;

      /** 客户id */
      custId: string;

      /** 删除状态 */
      custIsDeleted: string;

      /** 客户名称 */
      custName: string;

      /** 产品线获取日期到 */
      custProductLindDtEnd: string;

      /** 客户产品线创建时间从 */
      custProductLindDtFrom: string;

      /** 【客户所处状态】：1正在跟进、0删除区、2共享区 */
      custStatus: string;

      /** 【注册类型】：正式客户、陌拜客户、市场活动 */
      custType: string;

      /** 删除时间到 */
      delDtEnd: string;

      /** 删除时间从 */
      delDtFrom: string;

      /** 部门id */
      departmentId: string;

      /** 部门名称 */
      departmentName: string;

      /** endIndex */
      endIndex: number;

      /** 上传关联协议 */
      fileId: string;

      /** 客户所属行业 */
      industryName: string;

      /** 是否跨区 0或空否   1是  主要控制一些页面不限制分公司 一些限制 */
      isLocal: string;

      /** 判断销售是否就是当前登录人 */
      isMe: string;

      /** 【市场活动】：市场活动名称； */
      marketName: string;

      /** 最长公示天数，用于判断销售是否可以拾取自己的,>=10可拾取 */
      maxShareDays: string;

      /** 操作类型2 共享区   0删除区 */
      operateType: string;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** 产品线创建时间 */
      productLinCreateDt: string;

      /** 产品线共享时间 */
      productLinShareDt: string;

      /** 产品线id */
      productLineId: string;

      /** 产品线映射表ID */
      productLineMappingId: string;

      /** 逗号分隔的客户产品线ID，用户按客户分配，存储客户销售的所有产品线 */
      productLineMappingIds: string;

      /** 产品线名称 */
      productLineName: string;

      /** 公示区产品线格式如代理(20120618,20120818),派遣(20120618,20120818),Payroll(20120618,20120818),新派遣(20120618,20120818),租赁(20120618,20120818),保险(20120618,20120818),福利(20120618,20120818),一次性产品(20120618,20120818),咨询(20120618,20120818),招聘(20120618,20120818) */
      productLines: string;

      /** 销售id */
      saleId: string;

      /** 销售名称 */
      saleName: string;

      /** 共享时间到 */
      shareDtEnd: string;

      /** 共享时间从 */
      shareDtFrom: string;

      /** 客户来源 */
      sourceName: string;

      /** startIndex */
      startIndex: number;
    }

    export class UptCustLogDTO {
      /** 客户对象 */
      formalCust: defs.crm.FormalCust;

      /** 日志类型 */
      type: string;
    }

    export class cityQuery {
      /** 客户编码 */
      custCode: string;

      /** endIndex */
      endIndex: number;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** startIndex */
      startIndex: number;
    }

    export class contractVO {
      /** 审批步骤 */
      activityNameCn: string;

      /** 流程节点表的活动英文名称 */
      activityNameEn: string;

      /** 节点状态，0：不可结束不可回退，1：可结束不可回退，2：不可结束可回退，3：可结束可回退 */
      activityStatus: string;

      /** 预付款比例 */
      advancePaymentRatio: string;

      /** 代收代付 */
      agentBusiness: string;

      /** 约定到款月 */
      agereedAmtReceiveMon: string;

      /** 薪资发放日/约定薪资发放日 */
      agreedPayDt: string;

      /** 约定到款日 */
      agreedWageArriveDay: string;

      /** 非标合同审批单code */
      applyCode: string;

      /** 审批通过时间 */
      approveDt: string;

      /** 审批通过时间到 */
      approveDtEnd: string;

      /** 审批通过时间从 */
      approveDtStart: string;

      /** 页面填写的审核意见 */
      approveOpinion: string;

      /** 合同审核相关的附件 */
      approveRelatedAttachment: string;

      /** 合同审核相关的附件name */
      approveRelatedAttachmentName: string;

      /** 大区ID */
      areaId: string;

      /** 区域类型 1 本地 2 大区内 3 全国 */
      areaType: string;

      /** 区域类型 1 本地 2 大区内 3 全国 */
      areaTypeName: string;

      /** 终稿 */
      attTypeDraftId: string;

      /** 终稿name */
      attTypeDraftName: string;

      /** 法务 */
      attTypeLegalId: string;

      /** 法务name */
      attTypeLegalName: string;

      /** 平均价格集合 */
      averageMoneys: string;

      /** 账单日期 */
      billDt: string;

      /** 城市 */
      cityId: string;

      /** 城市名称 */
      cityName: string;

      /** 提交时间 */
      commitTime: string;

      /** 签约人数集合 */
      compactNumbers: string;

      /** 竞争对手id */
      competitor: string;

      /** 客服竞争对手名称 */
      competitorName: string;

      /** 已经确认的工作流程 */
      confirmdWorkFlow: string;

      /** 联系人手机 */
      contactCell: string;

      /** 联系人电话 */
      contactTel: string;

      /** 签约人均金额 */
      contractAvgAmt: string;

      /** 合同类别 */
      contractCategery: string;

      /** 合同编号 */
      contractCode: string;

      /** 最终结束日期 */
      contractEndDate: string;

      /** 合同最终结束日期类型 */
      contractEndDateType: string;

      /** 合同文件名 */
      contractFileName: string;

      /** 合同附件备注 */
      contractFileRemark: string;

      /** 合同附件上传时间 */
      contractFileUploadDt: string;

      /** 签约人数 */
      contractHeadcount: string;

      /** 合同id */
      contractId: string;

      /** 合同名称 */
      contractName: string;

      /** 合同甲方 */
      contractPartA: string;

      /** 合同乙方 */
      contractPartB: string;

      /** 合同产品线id集合 */
      contractProductLineIds: string;

      /** 客户盖章后，合同回收时间 */
      contractRetrieveDt: string;

      /** 合同起始日期 */
      contractStartDate: string;

      /** 合同启动时间止 */
      contractStartDateEnd: string;

      /** 合同启动日期起 */
      contractStartDateStart: string;

      /** 合同状态：0 初始；1 审批中；2 审批通过；3 退回修改；4 驳回终止 */
      contractStatus: string;

      /** 合同状态name */
      contractStatusName: string;

      /** 合同结束日期 */
      contractStopDate: string;

      /** 合同终止原因 */
      contractStopReason: string;

      /** 合同小类 */
      contractSubType: string;

      /** 合同类别（子类）名称 */
      contractSubTypeName: string;

      /** 合同服务状态：0新签1续签2过期3终止服务 */
      contractSvcState: string;

      /** 合同服务状态name */
      contractSvcStateName: string;

      /** 合同模板编号 */
      contractTemplateId: string;

      /** 新平台合同终止时填写的终止时间 */
      contractTerminationDate: string;

      /** 合同大类 */
      contractType: string;

      /** 合同类型名 */
      contractTypeName: string;

      /** 合同版本号 */
      contractVersion: string;

      /** 创建人 */
      createByName: string;

      /** 创建人 */
      createByParty: string;

      /** 创建日期到 */
      createDtEnd: string;

      /** 创建日期从 */
      createDtStart: string;

      /** 合同生成方式 */
      createType: string;

      /** 账期（天） */
      creditPeriod: string;

      /** 客服审批 */
      csApproval: string;

      /** 客服审批状态 0：初始态 1：审批中  3：通过 -3：退回修改 -4：驳回终止 */
      csApprovalStatus: string;

      /** 客服联系人地址 */
      cstScAddress: string;

      /** 客服联系人手机 */
      cstScCall: string;

      /** 客服联系人 */
      cstScContact: string;

      /** 客服联系人邮件 */
      cstScEmail: string;

      /** 客服联系人电话 */
      cstScTel: string;

      /** 客服联系人传真 */
      cstScfax: string;

      /** 客服联系人职位 */
      cstScposition: string;

      /** 必须是同一个客户，当前执行的合同的编号，如果续签多次，这个编号是最新的合同编号 */
      currentExeContractId: string;

      /** 现销售 */
      currentSales: string;

      /** 客户唯一号 */
      custCode: string;

      /** 客户ID */
      custId: string;

      /** 客户名称 */
      custName: string;

      /** 客户盖章时间 */
      custSealDt: string;

      /** 供应商ID */
      departmentId: string;

      /** 供应商名称 */
      departmentName: string;

      /** 草稿备注 */
      draftRemark: string;

      /** 联系人邮件 */
      email: string;

      /** EOS账号停用 */
      eosStatus: string;

      /** 预估首次账单日期 */
      estimateFirstBillDate: string;

      /** 预计12个月内可达到人数 */
      estimatedHeadcount: string;

      /** 执行成本 */
      executionCost: string;

      /** 预计增长人数 */
      expectedIncrease: string;

      /** 原预计增长人数 */
      expectedIncreaseOld: string;

      /** 联系人传真 */
      fax: string;

      /** 首次出账单的客户账单年月 */
      firstAccountMonth: string;

      /** 首次出账单时间(锁定时间) */
      firstBillDate: string;

      /** 首次大合同ID */
      firstContractId: string;

      /** 合同审批的首个法务 */
      firstLegalApproveId: string;

      /** 合同审批的首个法务名称 */
      firstLegalApproveName: string;

      /** 首次出账单的财务应收年月 */
      firstOughtMonth: string;

      /**  合同审批的首个易薪税审批人员 */
      firstWgApproveId: string;

      /** 原销售所属大区 */
      formerGoverningArea: string;

      /** 原销售所属大区名称 */
      formerGoverningAreaName: string;

      /** 原销售所属分公司 */
      formerGoverningBranch: string;

      /** 原销售所属分公司名称 */
      formerGoverningBranchName: string;

      /** 原销售 */
      formerSales: string;

      /** 原销售名字 */
      formerSalesName: string;

      /** 未来商机 */
      furtureOpportunity: string;

      /** 所属大区 */
      governingArea: string;

      /** 现销售所属大区名称 */
      governingAreaName: string;

      /** 所属分公司 */
      governingBranch: string;

      /** 现销售所属分公司名称 */
      governingBranchName: string;

      /** 毛利 */
      grossProfit: string;

      /** 集团公司编号 */
      groupId: string;

      /** 集团公司名称 */
      groupName: string;

      /** 是否有交接单 */
      hasTransferInfo: string;

      /** 人力资源联系人 */
      hrContract: string;

      /** 导入文件ID */
      importFileId: string;

      /** 导入文件名称 */
      importFileName: string;

      /** 收入 */
      income: string;

      /** 内支金额 */
      internalMoney: string;

      /** 开票金额 */
      invoiceMoney: string;

      /** 开票张数 */
      invoiceNum: string;

      /** 滞纳金比例是否为万分之五 1：是；0：否 */
      is5Per10000FineRate: string;

      /** 滞纳金比例是否为万分之五name */
      is5Per10000FineRateName: string;

      /** 是否有补充附件 */
      isAddedAttachment: string;

      /** 本次续签是否需要调整合同条款？ */
      isAdjustRenewContract: string;

      /** 是否派单 */
      isAssign: string;

      /** 是否要提交审核 */
      isCommitApprove: string;

      /** 是否为已有客户所推荐 1：是；0：否 */
      isCustRecommend: string;

      /** 是否为已有客户所推荐name */
      isCustRecommendName: string;

      /** 是否降价、垫付、账期延期 1:是,0:否 */
      isDefer: string;

      /** 是否降价、垫付、账期延期名称 */
      isDeferName: string;

      /** 是否开通EOS账号 */
      isEosAccount: string;

      /** 是否内支 */
      isInternalPayment: string;

      /** 是否代发薪资 */
      isIssuingSalary: string;

      /** 是否加入包含具体金额的对等条款或赔偿性条款 1：是；0：否 */
      isJoinCompensation: string;

      /** 是否加入包含具体金额的对等条款或赔偿性条款name */
      isJoinCompensationName: string;

      /** 质控计算结果是否为垫付 1：是；0：否 */
      isPaymentQAResult: string;

      /** 质控计算结果是否为垫付name */
      isPaymentQAResultName: string;

      /** 服务人数小于20人，是否季度付款 1：是；0：否 */
      isQuarterlyPaymentLess20: string;

      /** 服务人数小于20人，是否季度付款name */
      isQuarterlyPaymentLess20Name: string;

      /** 是否抢单 */
      isRob: string;

      /** 是否集中一地投保 */
      isSameInsur: string;

      /** 是否集中一地投保name */
      isSameInsurName: string;

      /** 是否二次开发 1:是,0:否 */
      isSecondaryDev: string;

      /** 是否二次开发名称 */
      isSecondaryDevName: string;

      /** 法务审批 */
      legalApproval: string;

      /** 法务审批状态  0：初始态 1：审批中  3：通过 -3：退回修改 -4：驳回终止 */
      legalApprovalStatus: string;

      /** 合同附件备注 */
      legalRemark: string;

      /** 责任客服 */
      liabilityCs: string;

      /** 责任客服名字 */
      liabilityCsName: string;

      /** 会议记录id */
      meetingRecordId: string;

      /** 会议记录上传附件id */
      meetingRecordImportFileId: string;

      /** 会议记录上传附件名称 */
      meetingRecordImportFileName: string;

      /** 备注 */
      memo: string;

      /** 范本修改版合同备注 */
      modelModifyVersionRemark: string;

      /** 新销售 */
      newSales: string;

      /** 续签合同ID, 存放续签的大合同ID */
      nextContractId: string;

      /** 续签合同名称 */
      nextContractName: string;

      /** 下个法务 */
      nextLegalApproveId: string;

      /** 非标合同审批单 */
      nonStaCoctApprId: string;

      /** 服务订单数 */
      orderNumber: string;

      /** 父合同id编号 */
      parentContractId: string;

      /** 付款和收款要点 */
      payCollectPoint: string;

      /** 薪资发放月 */
      payMonth: string;

      /** 缴费类型 */
      payType: string;

      /** 客户付款方id集合 */
      payerIds: string;

      /** 付款方式 */
      paymentMode: string;

      /** 体检预估成本 */
      peExecutionCost: string;

      /** 体检毛利 */
      peGrossProfit: string;

      /** 体检收入 */
      peIncome: string;

      /** 垫款额度 */
      prepayAmt: string;

      /** 垫付审批 */
      prepayApproval: string;

      /** 垫款审批状态  0：初始态 1：审批中  3：通过 -3：退回修改 -4：驳回终止 */
      prepayApprovalStatus: string;

      /** 流程定义id */
      processDefId: string;

      /** 对应的流程实例ID */
      processInstanceId: string;

      /** 产品线id集合 */
      productLineIdLogs: string;

      /** 产品线id集合 */
      productLineIds: string;

      /** 项目前期计划或实施要求 */
      projectPlanRequest: string;

      /** 全国项目交接表单 */
      projectRemark: string;

      /** 供应商类型 */
      providerType: string;

      /** QA审核意见 */
      qaApprove: string;

      /** 报价单集合id */
      quoIds: string;

      /** 被续签的旧合同号 */
      renewedContractNum: string;

      /** 客服反馈撤单预警日期大于等于 */
      reportElEvacuatedDate: string;

      /** 客服反馈撤单时间 */
      reportEvacuatedDate: string;

      /** 客服撤单详细原因说明 */
      reportEvacuatedExplantion: string;

      /** 客服撤单原因分类 */
      reportEvacuatedReason: string;

      /** 客服反馈撤单预警日期小于等于 */
      reportGlEvacuatedDate: string;

      /** 回访历史内容 */
      returnVisitMemo: string;

      /** 最后回访人Id */
      returnVisitorId: string;

      /** 统计标志位 */
      salFlag: string;

      /** 新增/存量标识 （手工） */
      salFlagManual: string;

      /** 1 纯新增,2 存量,3 纯新增/存量,4 纯新增+滚动存量,5 滚动存量,6 滚动存量+存量 */
      salFlagManualName: string;

      /** 新增/存量标识 （系统）：1历史纯新增、2滚动新增、3存量、4当月启动纯新增、-1未有首版账单 */
      salFlagName: string;

      /** 客户对应销售及分公司 */
      saleAndBranchName: string;

      /** 销售审批 */
      salesApprove: string;

      /** 销售报价审批状态 0：初始态 1：审批中  3：通过 -3：退回修改 -4：驳回终止 */
      salesApproveStatus: string;

      /** 销售所在主部门 */
      salesDeptName: string;

      /** 销售名字 */
      salesName: string;

      /** 所属销售团队类型 */
      salesTeamType: string;

      /** 客服竞争对手优势 */
      sctScComAdvancetage: string;

      /** 用章审核状态 0：初始态3：通过 -3：退回修改 -4：驳回终止 */
      sealApproveStatus: string;

      /** 公司盖章时间 */
      sealDt: string;

      /** 用章审核 */
      sealOpinion: string;

      /** 签约方公司抬头 */
      signBranchTitle: string;

      /** 签约方公司抬头id */
      signBranchTitleId: string;

      /** 新签标识（手工） */
      signFlagManual: string;

      /** 新签标识（手工）name */
      signFlagManualName: string;

      /** 签单分公司 */
      signProvider: string;

      /** 撤单原因 */
      stopReason: string;

      /** 终止服务系统操作时间 */
      stopSvcDt: string;

      /** 终止服务操作日期 查询条件：终止服务日期到 */
      stopSvcEndDt: string;

      /** 服务区域 */
      svcRegion: string;

      /** 税费 */
      tax: string;

      /** 总售价 */
      totalPrice: string;

      /** 交接单流程ID */
      tranferProcessId: string;

      /** 交接单id */
      transferId: string;

      /** 销售--客服交接单 */
      transferRemark: string;

      /** 交接上传文件名 */
      uploadFileName: string;

      /** 交接上传URL */
      uploadUrl: string;

      /** 工作流id */
      workitemId: string;
    }

    export class customerServiceDTO {
      /** 统计年月（审批通过时间） */
      approveDt: string;

      /** 原销售所属分公司 */
      areaName: string;

      /** 账单月份 */
      billYm: string;

      /** 原销售所属大区 */
      brcName: string;

      /** 合同编号 */
      contractCode: string;

      /** 合同名称 */
      contractName: string;

      /** contractamt */
      contractamt: string;

      /** 创建时间到 */
      createDtEd: string;

      /** 创建时间从 */
      createDtSt: string;

      /** 现销售所属大区 */
      currentAreaName: string;

      /** 现销售所属分公司 */
      currentBrcName: string;

      /** 现销售 */
      currentSalesName: string;

      /** 客户编号 */
      custCode: string;

      /** 客户名称 */
      custName: string;

      /** 服务人数 */
      empCount: string;

      /** 原销售 */
      formerSalesName: string;

      /** 现销售所属大区 */
      newAreaId: string;

      /** 现销售姓名 */
      newSaleName: string;

      /** 现销售所属分公司 */
      newgoverningBranch: string;

      /** 原销售所属大区 */
      oldAreaId: string;

      /** 原销售姓名 */
      oldSaleName: string;

      /** 原销售所属分公司 */
      oldgoverningBranch: string;

      /** 服务费 */
      serviceAmt: string;

      /** 已核销金额 */
      verfiyAmt: string;
    }

    export class customerSimilarityDTO {
      /** 客户A编号 */
      aCode: string;

      /** 客户A名称 */
      aName: string;

      /** 客户A产品线 */
      aProductLine: string;

      /** 客户B编号 */
      bCode: string;

      /** 客户B名称 */
      bName: string;

      /** 客户B产品线 */
      bProductLine: string;
    }

    export class debtReminderCriteriaQuery {
      /** agreedWageArriveDay */
      agreedWageArriveDay: string;

      /** amtReceiveMon */
      amtReceiveMon: string;

      /** billCreator */
      billCreator: string;

      /** 客户账单年月 */
      billMonth: string;

      /** billType */
      billType: string;

      /** billVersion */
      billVersion: string;

      /** cashDt */
      cashDt: string;

      /** 合同编号 */
      contractCode: string;

      /** contractId */
      contractId: string;

      /** 合同名称 */
      contractName: string;

      /** 现销售所属大区 */
      currentGoverningArea: string;

      /** currentGoverningAreaId */
      currentGoverningAreaId: string;

      /** 现销售所属分公司 */
      currentGoverningBranch: string;

      /** currentGoverningBranchId */
      currentGoverningBranchId: string;

      /** 现销售 */
      currentSales: string;

      /** 客户编号 */
      custCode: string;

      /** 客户id */
      custId: string;

      /** 客户名称 */
      custName: string;

      /** debtReminderId */
      debtReminderId: string;

      /** email */
      email: string;

      /** endIndex */
      endIndex: number;

      /** 预计回款日期  */
      expPaymentDate: string;

      /** finReceivableYm */
      finReceivableYm: string;

      /** 原销售所属大区 */
      formerGoverningArea: string;

      /** formerGoverningAreaId */
      formerGoverningAreaId: string;

      /** 原销售所属分公司 */
      formerGoverningBranch: string;

      /** formerGoverningBranchId */
      formerGoverningBranchId: string;

      /** 原销售 */
      formerSales: string;

      /** minorAdjustment */
      minorAdjustment: string;

      /** overdrafAmt */
      overdrafAmt: string;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** 收款方分公司 */
      payeeBranch: string;

      /** payeeBranchId */
      payeeBranchId: string;

      /** payerBranch */
      payerBranch: string;

      /** payerBranchId */
      payerBranchId: string;

      /** 对应原因 */
      reason: string;

      /** reasonCreateBy */
      reasonCreateBy: string;

      /** reasonCreateDt */
      reasonCreateDt: string;

      /** 原因类型 */
      reasonType: string;

      /** receivableAmt */
      receivableAmt: string;

      /** receivableId */
      receivableId: string;

      /** receivableTemplate */
      receivableTemplate: string;

      /** receivableTempltId */
      receivableTempltId: string;

      /** 提醒时间 */
      remindDate: string;

      /** remindEndDt */
      remindEndDt: string;

      /** remindStartDt */
      remindStartDt: string;

      /** remindString */
      remindString: string;

      /** 提醒接收人 */
      reminderRecipient: string;

      /** reminderRecipientId */
      reminderRecipientId: string;

      /** startIndex */
      startIndex: number;

      /** status */
      status: string;

      /** 处理状态 */
      statusName: string;

      /** verifyAmt */
      verifyAmt: string;

      /** verifyStatus */
      verifyStatus: string;
    }

    export class debtReminderQuery {
      /** agreedWageArriveDay */
      agreedWageArriveDay: string;

      /** amtReceiveMon */
      amtReceiveMon: string;

      /** billCreator */
      billCreator: string;

      /** 客户账单年月 */
      billMonth: string;

      /** billType */
      billType: string;

      /** billVersion */
      billVersion: string;

      /** cashDt */
      cashDt: string;

      /** 合同编号 */
      contractCode: string;

      /** contractId */
      contractId: string;

      /** 合同名称 */
      contractName: string;

      /** 现销售所属大区 */
      currentGoverningArea: string;

      /** 现销售所属分公司 */
      currentGoverningBranch: string;

      /** 现销售 */
      currentSales: string;

      /** 客户编号 */
      custCode: string;

      /** 客户id */
      custId: string;

      /** 客户名称 */
      custName: string;

      /** debtReminderId */
      debtReminderId: string;

      /** email */
      email: string;

      /** endIndex */
      endIndex: number;

      /** 预计回款日期  */
      expPaymentDate: string;

      /** finReceivableYm */
      finReceivableYm: string;

      /** 原销售所属大区 */
      formerGoverningArea: string;

      /** 原销售所属分公司 */
      formerGoverningBranch: string;

      /** 原销售 */
      formerSales: string;

      /** minorAdjustment */
      minorAdjustment: string;

      /** overdrafAmt */
      overdrafAmt: string;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** 收款方分公司 */
      payeeBranch: string;

      /** payerBranch */
      payerBranch: string;

      /** 对应原因 */
      reason: string;

      /** reasonCreateBy */
      reasonCreateBy: string;

      /** reasonCreateDt */
      reasonCreateDt: string;

      /** 原因类型 */
      reasonType: string;

      /** receivableAmt */
      receivableAmt: string;

      /** receivableId */
      receivableId: string;

      /** receivableTemplate */
      receivableTemplate: string;

      /** receivableTempltId */
      receivableTempltId: string;

      /** 提醒时间 */
      remindDate: string;

      /** remindString */
      remindString: string;

      /** 提醒接收人 */
      reminderRecipient: string;

      /** reminderRecipientId */
      reminderRecipientId: string;

      /** startIndex */
      startIndex: number;

      /** status */
      status: string;

      /** 处理状态 */
      statusName: string;

      /** verifyAmt */
      verifyAmt: string;

      /** verifyStatus */
      verifyStatus: string;
    }

    export class quotationDTO {
      /** activityNameEn */
      activityNameEn: string;

      /** activityStatus */
      activityStatus: string;

      /** approveDt */
      approveDt: string;

      /** approveDtEnd */
      approveDtEnd: string;

      /** approveDtStart */
      approveDtStart: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.APPROVE_PROCESS_INS_ID           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
      approveProcessInsId: number;

      /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.APPROVE_STATUS           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
      approveStatus: number;

      /** approveStatusName */
      approveStatusName: string;

      /** attId */
      attId: string;

      /** attName */
      attName: string;

      /** auditOpinion */
      auditOpinion: string;

      /** 审批类型 */
      auditType: string;

      /** 计算方式 1 统包价计算价税分离 2不含税计算税后 */
      calculateType: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.CITE_QUOTATION_ID           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
      citeQuotationId: number;

      /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.CITY_ID           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
      cityId: number;

      /** cityName */
      cityName: string;

      /** 创建者name */
      createByName: string;

      /** createDtFrom */
      createDtFrom: string;

      /** createDtTo */
      createDtTo: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.CURRENCY           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
      currency: number;

      /** custCode */
      custCode: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.CUST_ID           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
      custId: number;

      /** custName */
      custName: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.DEPARTMENT_ID           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
      departmentId: number;

      /** depthMark */
      depthMark: string;

      /** effectBy */
      effectBy: string;

      /** effectDt */
      effectDt: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.EXCHANGE_RATE           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
      exchangeRate: number;

      /** governingArea */
      governingArea: string;

      /** governingBranch */
      governingBranch: string;

      /** groupType */
      groupType: string;

      /** groupTypeName */
      groupTypeName: string;

      /** ifAf */
      ifAf: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.IF_FIRST_AUDIT           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
      ifFirstAudit: number;

      /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.IF_NATIONWIDE           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
      ifNationwide: number;

      /** ifSs */
      ifSs: string;

      /** invalidBy */
      invalidBy: string;

      /** invalidDt */
      invalidDt: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.IS_SPECIAL_PRICE           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
      isSpecialPrice: number;

      /** ladderQuotationType */
      ladderQuotationType: string;

      /** markType */
      markType: number;

      /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.NEW_SALE_ID           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
      newSaleId: number;

      /** newSaleName */
      newSaleName: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.OLD_SALE_ID           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
      oldSaleId: number;

      /** participant */
      participant: string;

      /** processDefId */
      processDefId: string;

      /** processInsId */
      processInsId: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.QUOTATION_CODE           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
      quotationCode: string;

      /** quotationGroupId */
      quotationGroupId: number;

      /** quotationHisId */
      quotationHisId: number;

      /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.QUOTATION_ID           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
      quotationId: number;

      /** quotationItemCiList */
      quotationItemCiList: Array<defs.crm.QuotationItem>;

      /** quotationItemCpList */
      quotationItemCpList: Array<defs.crm.QuotationItem>;

      /** quotationItemDetailBusinessList */
      quotationItemDetailBusinessList: Array<defs.crm.QuotationItemDetail>;

      /** quotationItemDetailBusinessListUpdate */
      quotationItemDetailBusinessListUpdate: Array<defs.crm.QuotationItemDetail>;

      /** quotationItemDetailBusinessType */
      quotationItemDetailBusinessType: string;

      /** quotationItemDetailCiList */
      quotationItemDetailCiList: Array<defs.crm.QuotationItemDetail>;

      /** quotationItemDetailCiType */
      quotationItemDetailCiType: string;

      /** quotationItemDetailCpList */
      quotationItemDetailCpList: Array<defs.crm.QuotationItemDetail>;

      /** quotationItemDetailCpType */
      quotationItemDetailCpType: string;

      /** quotationItemDetailEmployerList */
      quotationItemDetailEmployerList: Array<defs.crm.QuotationItemDetail>;

      /** quotationItemDetailEmployerListUpdate */
      quotationItemDetailEmployerListUpdate: Array<defs.crm.QuotationItemDetail>;

      /** quotationItemDetailEmployerType */
      quotationItemDetailEmployerType: string;

      /** quotationItemDetailHealthList */
      quotationItemDetailHealthList: Array<defs.crm.QuotationItemDetail>;

      /** quotationItemDetailHealthListUpdate */
      quotationItemDetailHealthListUpdate: Array<defs.crm.QuotationItemDetail>;

      /** quotationItemDetailHealthType */
      quotationItemDetailHealthType: string;

      /** quotationItemDetailHmsList */
      quotationItemDetailHmsList: Array<defs.crm.QuotationItemDetail>;

      /** quotationItemDetailHmsType */
      quotationItemDetailHmsType: string;

      /** quotationItemDetailPeList */
      quotationItemDetailPeList: Array<defs.crm.QuotationItemDetail>;

      /** quotationItemDetailPeType */
      quotationItemDetailPeType: string;

      /** quotationItemDetailProductList */
      quotationItemDetailProductList: Array<defs.crm.QuotationItemDetail>;

      /** quotationItemDetailProductListUpdate */
      quotationItemDetailProductListUpdate: Array<defs.crm.QuotationItemDetail>;

      /** quotationItemDetailRpList */
      quotationItemDetailRpList: Array<defs.crm.QuotationItemDetail>;

      /** quotationItemDetailSaleList */
      quotationItemDetailSaleList: Array<defs.crm.QuotationItemDetail>;

      /** quotationItemDetailSaleListUpdate */
      quotationItemDetailSaleListUpdate: Array<defs.crm.QuotationItemDetail>;

      /** quotationItemDetailWelList */
      quotationItemDetailWelList: Array<defs.crm.QuotationItemDetail>;

      /** quotationItemDetailWelType */
      quotationItemDetailWelType: string;

      /** quotationItemHmsList */
      quotationItemHmsList: Array<defs.crm.QuotationItem>;

      /** quotationItemPeList */
      quotationItemPeList: Array<defs.crm.QuotationItem>;

      /** quotationItemRisk */
      quotationItemRisk: defs.crm.QuotationItem;

      /** quotationItemRiskDetail */
      quotationItemRiskDetail: defs.crm.QuotationItemDetail;

      /** quotationItemWelList */
      quotationItemWelList: Array<defs.crm.QuotationItem>;

      /** quotationLadderList */
      quotationLadderList: Array<defs.crm.QuotationLadder>;

      /** quotationLadderListUpdate */
      quotationLadderListUpdate: Array<defs.crm.QuotationLadder>;

      /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.QUOTATION_NAME           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
      quotationName: string;

      /** quotationSpecial */
      quotationSpecial: defs.crm.QuotationSpecial;

      /** quotationSpecialPropertyList */
      quotationSpecialPropertyList: Array<defs.crm.QuotationSpecialProperty>;

      /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.QUOTATION_SPECIAL_TYPE           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
      quotationSpecialType: number;

      /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.QUOTATION_TOTAL_COST           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
      quotationTotalCost: number;

      /** 职场健康总售价 */
      quotationTotalOhPrice: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.QUOTATION_TOTAL_PRICE           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
      quotationTotalPrice: number;

      /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.QUOTATION_TOTAL_SAL_PRICE           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
      quotationTotalSalPrice: number;

      /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.REMARK           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
      remark: string;

      /** remarkApp */
      remarkApp: string;

      /** 商业保险审批角色CODE */
      roleCodeCi: string;

      /** 平台业务审批角色CODE */
      roleCodeCp: string;

      /** 健康管理审批角色CODE */
      roleCodeHms: string;

      /** 体检审批角色CODE */
      roleCodePe: string;

      /** 福利审批角色CODE */
      roleCodeWel: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.STATUS           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
      status: number;

      /** statusName */
      statusName: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.SUPPLT_MED_INSUR_HEADCOUNT           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
      suppltMedInsurHeadcount: number;

      /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column SL_QUOTATION.SVC_AREA           @ibatorgenerated Thu Sep 15 15:17:54 CST 2011 */
      svcArea: string;

      /** userId */
      userId: string;

      /** workitemId */
      workitemId: string;
    }
  }
}

declare namespace API {
  export namespace crm {
    /**
     * 账号信息
     */
    export namespace accountData {
      /**
        * 核查账号
核查账号
        * /api/crm/account/chkUser
        */
      export namespace chkUser {
        export class Params {
          /** 用户id */
          userId: string;
          /** 用户账号 */
          userName: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.GlobalResult;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取账号信息
获取账号信息
        * /api/crm/account/getUser
        */
      export namespace getUser {
        export class Params {
          /** 公司id(非必填) */
          companyId?: string;
          /** 截止日期yyyy-MM-DD|yyyyMMDD|yyyy/MM/DD */
          eDate: string;
          /** 起始日期yyyy-MM-DD|yyyyMMDD|yyyy/MM/DD */
          sDate: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.GlobalResult;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 客户状态变更流水
     */
    export namespace beforeSaleReport {
      /**
        * 客户查重报表列表
查询客户查重报表列表
        * /beforeSaleReport/customerSimilarity
        */
      export namespace customerSimilarity {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.customerSimilarityDTO;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.crm.cityQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.cityQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 导出客户查重报表
导出客户查重报表
        * /beforeSaleReport/downloadfile
        */
      export namespace downloadFile {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: any;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.crm.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 导出客户状态变更流水
导出客户状态变更流水
        * /beforeSaleReport/exportExcel
        */
      export namespace exportExcel {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: any;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.crm.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 售前日报查询
售前日报查询
        * /beforeSaleReport/queryBeforeSaleReport
        */
      export namespace queryBeforeSaleReport {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.crm.CustLogQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.CustLogQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 合同时长报表
合同时长报表
        * /beforeSaleReport/queryContractApprovalStepTime
        */
      export namespace queryContractApprovalStepTime {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.crm.SaleReportQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.SaleReportQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 丢单分析
丢单分析
        * /beforeSaleReport/queryCustomerLost
        */
      export namespace queryCustomerLost {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.crm.CustLogQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.CustLogQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询客户服务
查询客户服务
        * /beforeSaleReport/queryCustomerService
        */
      export namespace queryCustomerService {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.crm.SaleReportQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.SaleReportQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 日志查询
日志查询
        * /beforeSaleReport/queryLog
        */
      export namespace queryLog {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.crm.BusinessLogQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.BusinessLogQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 非标合同审批查询
非标合同审批查询
        * /beforeSaleReport/queryNonContractApprovalStepTime
        */
      export namespace queryNonContractApprovalStepTime {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.crm.SaleReportQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.SaleReportQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取客户相似度对比数据
获取客户相似度对比数据
        * /beforeSaleReport/querygetCustomerSimilarity
        */
      export namespace querygetCustomerSimilarity {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.crm.SaleReportQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.SaleReportQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 合同审批时长报表
     */
    export namespace contractApprovalStepTime {
      /**
        * 导出合同时长报表
导出合同时长报表
        * /contractApprovalStepTime/downloadfile
        */
      export namespace downloadFile {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: any;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.crm.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 合同时长报表
合同时长报表
        * /contractApprovalStepTime/queryContractApprovalStepTime
        */
      export namespace queryContractApprovalStepTime {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.crm.SaleReportQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.SaleReportQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 合同管理(销售)
     */
    export namespace contractManage {
      /**
        * 审批
审批
        * /contractManage/approve
        */
      export namespace approve {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.crm.ContractDTO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.ContractDTO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 销售补充数据提交再审批
销售补充数据提交再审批
        * /contractManage/approveBackPrevious
        */
      export namespace approveBackPrevious {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.crm.ContractDTO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.ContractDTO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 需要销售补充附件
需要销售补充附件
        * /contractManage/approveSaleUploadFileBackPrevious
        */
      export namespace approveSaleUploadFileBackPrevious {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.crm.ContractDTO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.ContractDTO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 退回修改
退回修改
        * /contractManage/back
        */
      export namespace back {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.crm.ContractDTO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.ContractDTO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据当前用户id判断其业务类型
根据当前用户id判断其业务类型
        * /contractManage/checkCsRoleByUserId
        */
      export namespace checkCsRoleByUserId {
        export class Params {
          /** userId */
          userId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 大合同提交审核
大合同提交审核
        * /contractManage/commitContractApprove
        */
      export namespace commitContractApprove {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.crm.ContractDTO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.ContractDTO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 删除合同
删除合同
        * /contractManage/delContract
        */
      export namespace delContract {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.crm.ContractDTO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.ContractDTO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 删除报价单合同的关系
删除报价单合同的关系
        * /contractManage/delContractQuotRelation
        */
      export namespace delContractQuotRelation {
        export class Params {}

        export type Response<T> = defs.crm.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.crm.ContractDTO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.ContractDTO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 删除合同附件列表里的文件记录
删除合同附件列表里的文件记录
        * /contractManage/delFileInContract
        */
      export namespace delFileInContract {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.crm.ContractFile,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.ContractFile,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 删除合同里的客户付款方
删除合同里的客户付款方
        * /contractManage/delPayerInContract
        */
      export namespace delPayerInContract {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.crm.CrmContractPayerDTO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.CrmContractPayerDTO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 删除合同里的产品线
删除合同里的产品线
        * /contractManage/delProductLineInContract
        */
      export namespace delProductLineInContract {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.crm.CrmLittleContractProductlineDTO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.CrmLittleContractProductlineDTO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 删除合同里的报价单
删除合同里的报价单
        * /contractManage/delQuotationInContract
        */
      export namespace delQuotationInContract {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.crm.CrmContractQuotationDTO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.CrmContractQuotationDTO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 导出
导出
        * /contractManage/downloadfile
        */
      export namespace toDownLoad {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: any;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.crm.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 生效范本合同记录
生效范本合同记录
        * /contractManage/effectModelContract
        */
      export namespace effectModelContract {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.crm.ModelContractDTO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.ModelContractDTO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据processInsId获取对应的结点信息
根据processInsId获取对应的结点信息
        * /contractManage/getActivityDefIdByProcessInsId
        */
      export namespace getActivityDefIdByProcessInsId {
        export class Params {
          /** processInsId */
          processInsId: string;
        }

        export type Response<T> = defs.crm.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据销售获取其所在的大区
根据销售获取其所在的大区
        * /contractManage/getAreaIdByCurrentSales
        */
      export namespace getAreaIdByCurrentSales {
        export class Params {
          /** saleId */
          saleId: string;
        }

        export type Response<T> = defs.crm.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据合同id来获取对应的合同审核相关的附件信息
根据合同id来获取对应的合同审核相关的附件信息
        * /contractManage/getContractApproveRelatedAttachmentByContractId
        */
      export namespace getContractApproveRelatedAttachmentByContractId {
        export class Params {
          /** contractId */
          contractId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.contractVO;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据合同id来获取对应的合同附件信息
根据合同id来获取对应的合同附件信息
        * /contractManage/getContractAttachmentByContractId
        */
      export namespace getContractAttachmentByContractId {
        export class Params {
          /** contractId */
          contractId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.contractVO;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据合同ID等条件获得合同附件列表
根据合同ID等条件获得合同附件列表
        * /contractManage/getContractFileListByContractId
        */
      export namespace getContractFileListByContractId {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.CommonResponse<Array>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.crm.ContractFile,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.ContractFile,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据客户获取大合同
根据客户获取大合同,参数包括签单放抬头
        * /contractManage/getContractList
        */
      export namespace getContractList {
        export class Params {
          /** custId */
          custId: string;
          /** signBranchTitleId */
          signBranchTitleId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.DropdownList;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取合同产品线
获取合同产品线
        * /contractManage/getContractProLine
        */
      export namespace getContractProLine {
        export class Params {
          /** contractId */
          contractId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.CommonResponse<Array>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据合同ID获得合同报价单
根据合同ID获得合同报价单
        * /contractManage/getContractQuos
        */
      export namespace getContractQuos {
        export class Params {
          /** contractId */
          contractId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.CommonResponse<Array>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据客户获取当前销售
根据客户获取当前销售
        * /contractManage/getCurrentSales
        */
      export namespace getCurrentSales {
        export class Params {
          /** custId */
          custId: string;
          /** deptId */
          deptId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.DropdownList;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取客户产品线列表
获取客户产品线列表
        * /contractManage/getCustProductLine
        */
      export namespace getCustProductLine {
        export class Params {
          /** custId */
          custId: string;
          /** saleId */
          saleId: string;
          /** type */
          type: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.DropdownList;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据销售获取其所在的分公司
根据销售获取其所在的分公司
        * /contractManage/getDepartmentIdByCurrentSales
        */
      export namespace getDepartmentIdByCurrentSales {
        export class Params {
          /** saleId */
          saleId: string;
        }

        export type Response<T> = defs.crm.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 下拉数据
下拉数据
        * /contractManage/getDropDownList
        */
      export namespace getDropDownList {
        export class Params {
          /** type */
          type: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.DropdownList;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取有效的范本合同
获取有效的范本合同
        * /contractManage/getEffectModelContract
        */
      export namespace getEffectModelContract {
        export class Params {
          /** id */
          id: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.CommonResponse<defs.crm.ModelContractDTO>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据现销售来判断是否填写首个法务
根据现销售来判断是否填写首个法务
        * /contractManage/getIsCsRoleByUserId
        */
      export namespace getIsCsRoleByUserId {
        export class Params {
          /** userId */
          userId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询范本合同记录
查询范本合同记录
        * /contractManage/getModelContractList
        */
      export namespace getModelContractList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.ModelContractDTO;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.crm.ModelContractQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.ModelContractQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据条件得到NonStaCoctAppr数据的分页查询结果
根据条件得到NonStaCoctAppr数据的分页查询结果
        * /contractManage/getNonStaCoctAppr
        */
      export namespace getNonStaCoctAppr {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.NonStaCoctApprDTO;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.crm.NonStaCoctApprQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.NonStaCoctApprQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据contractId判断与父合同记录的 产品线相关 的字段是否不相同 list里面的returnType返回1表示新增;2表示签约人数变化;3表示人均销售收入变化;4表示签约人数和人均销售收入都发生变化
根据contractId判断与父合同记录的 产品线相关 的字段是否不相同 list里面的returnType返回1表示新增;2表示签约人数变化;3表示人均销售收入变化;4表示签约人数和人均销售收入都发生变化
        * /contractManage/getNotSameParentProductLineByContractId
        */
      export namespace getNotSameParentProductLineByContractId {
        export class Params {
          /** contractId */
          contractId: string;
          /** parentContractId */
          parentContractId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.CommonResponse<Array>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据contractId判断与父合同记录的 报价单相关 的字段是否不相同 list里面的returnType返回1 表示新增
根据contractId判断与父合同记录的 报价单相关 的字段是否不相同 list里面的returnType返回1 表示新增
        * /contractManage/getNotSameParentQuotsByContractId
        */
      export namespace getNotSameParentQuotsByContractId {
        export class Params {
          /** contractId */
          contractId: string;
          /** parentContractId */
          parentContractId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.CommonResponse<Array>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据contractId判断与父合同记录的不相同字段的返回值 合同大类:1;合同小类:2;预计签约人数:3;签约人均价格:4;总售价:3或者4;服务区域类型:5;是否集中一地投保:6;是否增强型代理:7;是否代发薪资:8;签约方公司抬头:9;缴费类型:10;收入:11;税费:12;执行成本:13;代收代付:14;毛利:11或者12或者13或者14;含差旅服务:15;差旅服务费比例:16;账单日期(天):17;约定到款月:18;约定到款日(天):19;垫款额度:20;薪资发放月:21;薪资发放日:22;是否为已有客户所推荐:23;记录没有变化:0
根据contractId判断与父合同记录的不相同字段的返回值 合同大类:1;合同小类:2;预计签约人数:3;签约人均价格:4;总售价:3或者4;服务区域类型:5;是否集中一地投保:6;是否增强型代理:7;是否代发薪资:8;签约方公司抬头:9;缴费类型:10;收入:11;税费:12;执行成本:13;代收代付:14;毛利:11或者12或者13或者14;含差旅服务:15;差旅服务费比例:16;账单日期(天):17;约定到款月:18;约定到款日(天):19;垫款额度:20;薪资发放月:21;薪资发放日:22;是否为已有客户所推荐:23;记录没有变化:0
        * /contractManage/getNotSameParentRecordByContractId
        */
      export namespace getNotSameParentRecordByContractId {
        export class Params {
          /** contractId */
          contractId: string;
        }

        export type Response<T> = defs.crm.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据合同id条件得到对应的客户付款方的查询结果
根据合同id条件得到对应的客户付款方的查询结果
        * /contractManage/getPayerByContractId
        */
      export namespace getPayerByContractId {
        export class Params {
          /** contractId */
          contractId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.CommonResponse<Array<defs.crm.CrmContractPayerDTO>>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据合同id条件得到对应的产品线的查询结果
根据合同id条件得到对应的产品线的查询结果
        * /contractManage/getProductLineByContractId
        */
      export namespace getProductLineByContractId {
        export class Params {
          /** contractId */
          contractId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.CommonResponse<Array>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 代办任务 条件分页查询合同
代办任务 条件分页查询合同
        * /contractManage/getQueryContractApprove
        */
      export namespace getQueryContractApprove {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.ContractDTO;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.crm.ContractQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.ContractQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据合同id条件得到对应的报价单的查询结果
根据合同id条件得到对应的报价单的查询结果
        * /contractManage/getQuotationByContractId
        */
      export namespace getQuotationByContractId {
        export class Params {
          /** contractId */
          contractId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.CommonResponse<Array>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据主键查询续约大合同
根据主键查询续约大合同
        * /contractManage/getRenewContractById
        */
      export namespace getRenewContractById {
        export class Params {
          /** contractId */
          contractId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.CommonResponse<defs.crm.ContractDTO>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据客户获取销售
根据客户获取销售
        * /contractManage/getSalersByCustId
        */
      export namespace getSalersByCustId {
        export class Params {
          /** custId */
          custId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.CommonResponse<Array>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据条件得到客户付款方数据的查询结果
根据条件得到客户付款方数据的查询结果
        * /contractManage/getSelectedPayer
        */
      export namespace getSelectedPayer {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.CustomerPayerDTO;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.crm.CustomerPayerQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.CustomerPayerQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据条件得到报价单数据的分页查询结果
根据条件得到报价单数据的分页查询结果
        * /contractManage/getSelectedQuotation
        */
      export namespace getSelectedQuotation {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.CommonResponse<Array>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.crm.quotationDTO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.quotationDTO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据部门获取签单方公司抬头
根据部门获取签单方公司抬头
        * /contractManage/getSignBranchTitleByDepartmentId
        */
      export namespace getSignBranchTitleByDepartmentId {
        export class Params {
          /** departmentId */
          departmentId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.DropdownList;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取当前导入批次的退休人员列表
获取当前导入批次的退休人员列表
        * /contractManage/getSlContractRetireeList
        */
      export namespace getSlContractRetireeList {
        export class Params {
          /** batchId */
          batchId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.CommonResponse<Array>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 数据初始化
数据初始化
        * /contractManage/initData
        */
      export namespace initData {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: object;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取大合同详情数据
获取大合同详情数据
        * /contractManage/queryContracViewtById
        */
      export namespace queryContracViewtById {
        export class Params {
          /** contractId */
          contractId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.contractVO;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取大合同详情数据
获取大合同详情数据
        * /contractManage/queryContractById
        */
      export namespace getContractById {
        export class Params {
          /** contractId */
          contractId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.contractVO;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询大合同列表
查询大合同列表
        * /contractManage/queryContractList
        */
      export namespace postQueryContractList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.ContractDTO;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.crm.ContractQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.ContractQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 交接单和派单所需大合同查询
交接单和派单所需大合同查询
        * /contractManage/queryContractListForTransfer
        */
      export namespace getContractListForTransfer {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.ContractDTO;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.crm.ContractQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.ContractQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 保存大合同
保存大合同
        * /contractManage/save
        */
      export namespace save {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.crm.ContractDTO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.ContractDTO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 记录修改历史
记录修改历史
        * /contractManage/saveBizFieldLog
        */
      export namespace saveBizFieldLog {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.crm.ContractDTO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.ContractDTO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 保存范本合同记录
保存范本合同记录
        * /contractManage/saveModelContract
        */
      export namespace saveModelContract {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.crm.ModelContractDTO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.ModelContractDTO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 条件分页查询合同
条件分页查询合同
        * /contractManage/selectList
        */
      export namespace selectList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.ContractDTO;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.crm.ContractQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.ContractQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 续签大合同
续签大合同
        * /contractManage/setRenewContract
        */
      export namespace setRenewContract {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.crm.ContractDTO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.ContractDTO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 驳回终止
驳回终止
        * /contractManage/terminal
        */
      export namespace terminal {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.crm.ContractDTO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.ContractDTO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 合同更换现销售
合同更换现销售
        * /contractManage/updateContractCurrentSale
        */
      export namespace updateContractCurrentSale {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.crm.ContractDTO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.ContractDTO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 更新范本合同记录
更新范本合同记录
        * /contractManage/updateModelContract
        */
      export namespace updateModelContract {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.crm.ModelContractDTO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.ModelContractDTO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 批量更新范本合同记录
批量更新范本合同记录
        * /contractManage/updateModelContractList
        */
      export namespace updateModelContractList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Array<defs.crm.ModelContract>,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Array<defs.crm.ModelContract>,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 修改签单方公司抬头
修改签单方公司抬头
        * /contractManage/updateSignBranchTitle
        */
      export namespace updateSignBranchTitle {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.crm.ContractDTO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.ContractDTO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 批量更新大合同新增存量标志(手工)
批量更新大合同新增存量标志(手工)
        * /contractManage/uptContractFlagManual
        */
      export namespace uptContractFlagManual {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Array<defs.crm.Contract>,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Array<defs.crm.Contract>,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 终止服务合同
终止服务合同
        * /contractManage/uptContractSvcStop
        */
      export namespace uptContractSvcStop {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.crm.ContractDTO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.ContractDTO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 更新合同数据到中间表
更新合同数据到中间表
        * /contractManage/uptContractToCRM
        */
      export namespace uptContractToCRM {
        export class Params {
          /** contractIds */
          contractIds: string;
        }
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 更新合同状态
更新合同状态
        * /contractManage/uptStatus
        */
      export namespace uptStatus {
        export class Params {
          /** contractId */
          contractId: string;
        }
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * crm对外暴露接口
     */
    export namespace crmContract {
      /**
        * 获取合同变更现销售信息
获取合同变更现销售信息
        * /api/crm/contract/getChgSales
        */
      export namespace getChgSales {
        export class Params {
          /** 结束时间，yyyy-MM-dd|yyyyMMdd|yyyy/MM/dd */
          eDate: string;
          /** pageNum */
          pageNum: number;
          /** pageSize */
          pageSize: number;
          /** 开始时间，yyyy-MM-dd|yyyyMMdd|yyyy/MM/dd */
          sDate: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.GlobalResult;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取合同信息
获取合同信息
        * /api/crm/contract/getContract
        */
      export namespace getContract {
        export class Params {
          /** 分公司Id */
          companyId?: string;
          /** 合同编号 */
          contractNumber?: string;
          /** 结束时间，yyyy-MM-dd|yyyyMMdd|yyyy/MM/dd */
          eDate: string;
          /** 页码 */
          pageNum: string;
          /** 每页数量 */
          pageSize: string;
          /** 开始时间，yyyy-MM-dd|yyyyMMdd|yyyy/MM/dd */
          sDate: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.GlobalResult;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 变更客户信息
变更客户信息
        * /api/crm/customer/chgCust
        */
      export namespace chgCust {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.GlobalResult;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.crm.Customer,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.Customer,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 变更产品线销售
变更产品线销售
        * /api/crm/customer/chgSales
        */
      export namespace chgSales {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.GlobalResult;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.crm.CustProductLine,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.CustProductLine,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 检查客户名称
检查客户名称
        * /api/crm/customer/chkCustName
        */
      export namespace chkCustName {
        export class Params {
          /** 客户名称 */
          custName: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.GlobalResult;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 创建客户
创建客户
        * /api/crm/customer/newCust
        */
      export namespace newCust {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.GlobalResult;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.crm.Customer,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.Customer,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 更新客户名称
更新客户名称
        * /api/crm/customer/uptCustName
        */
      export namespace uptCustName {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.GlobalResult;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.crm.Customer,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.Customer,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 核查集团状态
核查集团状态
        * /api/crm/group/chkGroup
        */
      export namespace chkGroup {
        export class Params {
          /** 集团Id */
          groupId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.GlobalResult;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取集团信息
获取集团信息
        * /api/crm/group/getGroup
        */
      export namespace getGroup {
        export class Params {
          /** 结束时间，yyyy-MM-dd|yyyyMMdd|yyyy/MM/dd */
          eDate: string;
          /** 页码 */
          pageNum: string;
          /** 每页数量 */
          pageSize: string;
          /** 开始时间，yyyy-MM-dd|yyyyMMdd|yyyy/MM/dd */
          sDate: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.GlobalResult;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取成员
获取成员
        * /api/crm/group/getMember
        */
      export namespace getMember {
        export class Params {
          /** 集团Id */
          groupId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.GlobalResult;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 客户信息查询(销售)
     */
    export namespace custReport {
      /**
        * 导出
导出
        * /custReport/exportCust
        */
      export namespace exportCust {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: any;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.crm.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 客户信息查询（销售）
客户信息查询（销售）
        * /custReport/queryCustReport
        */
      export namespace queryCustReport {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.ShareAreaProductLines;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.crm.CustReportQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.CustReportQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据销售ID(逗号分隔)查询客户信息查询页面发Email信息
根据销售ID(逗号分隔)查询客户信息查询页面发Email信息
        * /custReport/querySaleMail
        */
      export namespace querySaleMail {
        export class Params {
          /** saleIds */
          saleIds: string;
        }

        export type Response<T> = defs.crm.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据销售ID(逗号分隔)查询客户信息查询页面发Email信息
根据销售ID(逗号分隔)查询客户信息查询页面发Email信息
        * /custReport/querySaleMail
        */
      export namespace postQuerySaleMail {
        export class Params {
          /** saleIds */
          saleIds: string;
        }

        export type Response<T> = defs.crm.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 发送mail
发送mail
        * /custReport/sendMail
        */
      export namespace sendMail {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.CommonResponse<number>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.crm.SendMailDTO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.SendMailDTO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 上传关联协议
上传关联协议
        * /custReport/uploadAssPro
        */
      export namespace uploadAssPro {
        export class Params {
          /** custId */
          custId: string;
          /** fileId */
          fileId: string;
        }

        export type Response<T> = defs.crm.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 客户服务情况(实时/月度)
     */
    export namespace customerService {
      /**
        * 客户查重报表列表
查询客户查重报表列表
        * /customerService/customerSimilarity
        */
      export namespace customerSimilarity {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.customerSimilarityDTO;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.crm.cityQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.cityQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 导出客户服务情况报表
导出客户服务情况报表
        * /customerService/downloadfile
        */
      export namespace downloadFile {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: any;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.crm.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 售前日报查询
售前日报查询
        * /customerService/queryBeforeSaleReport
        */
      export namespace queryBeforeSaleReport {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.crm.BeforeSaleReportQueryBean,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.BeforeSaleReportQueryBean,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 合同时长报表
合同时长报表
        * /customerService/queryContractApprovalStepTime
        */
      export namespace queryContractApprovalStepTime {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.crm.SaleReportQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.SaleReportQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 丢单分析
丢单分析
        * /customerService/queryCustomerLost
        */
      export namespace queryCustomerLost {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.crm.CustomerLost,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.CustomerLost,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 客户服务情况查询
客户服务情况查询
        * /customerService/queryCustomerService
        */
      export namespace queryCustomerService {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.customerServiceDTO;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.crm.SaleReportQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.SaleReportQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 日志查询
日志查询
        * /customerService/queryLog
        */
      export namespace queryLog {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.crm.CrmBusinessLog,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.CrmBusinessLog,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 非标合同审批查询
非标合同审批查询
        * /customerService/queryNonContractApprovalStepTime
        */
      export namespace queryNonContractApprovalStepTime {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.crm.SaleReportQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.SaleReportQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取客户相似度对比数据
获取客户相似度对比数据
        * /customerService/querygetCustomerSimilarity
        */
      export namespace querygetCustomerSimilarity {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.crm.SaleReportQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.SaleReportQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 客户欠款提醒
     */
    export namespace debtReminder {
      /**
        * 导出
导出
        * /debtReminder/downloadfile
        */
      export namespace toDownLoad {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: any;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.crm.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 填写原因下拉数据
填写原因下拉数据
        * /debtReminder/getReasonTypeList
        */
      export namespace getReasonTypeList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.CommonResponse<Array<defs.crm.DropdownList>>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据条件查询客户欠款提醒
根据条件查询客户欠款提醒
        * /debtReminder/queryDebtReminderList
        */
      export namespace queryDebtReminderList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.DebtReminderCriteria;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.crm.debtReminderCriteriaQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.debtReminderCriteriaQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 填写原因
填写原因
        * /debtReminder/updateDebtReminder
        */
      export namespace updateDebtReminder {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.crm.debtReminderQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.debtReminderQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 正式客户维护接口  <AUTHOR>
     */
    export namespace formalCust {
      /**
        * 添加客户丢单原因
添加客户丢单原因
        * /formalCust/addCustLoseReason
        */
      export namespace addCustLoseReason {
        export class Params {}

        export type Response<T> = defs.crm.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.crm.CustLoseReason,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.CustLoseReason,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 添加客户变更流水日志
添加客户变更流水日志
        * /formalCust/addUptCustLog
        */
      export namespace addUptCustLog {
        export class Params {}

        export type Response<T> = defs.crm.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.crm.UptCustLogDTO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.UptCustLogDTO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询客户名称是否存在
查询客户名称是否存在
        * /formalCust/checkCustNameExists
        */
      export namespace checkCustNameExists {
        export class Params {
          /** custName */
          custName: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询客户名称是否存在
查询客户名称是否存在
        * /formalCust/checkCustNameExists
        */
      export namespace postCheckCustNameExists {
        export class Params {
          /** custName */
          custName: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 删除未签约客户(实际就是删除销售的产品线)
删除未签约客户(实际就是删除销售的产品线)
        * /formalCust/delCustPro
        */
      export namespace delCustPro {
        export class Params {}

        export type Response<T> = defs.crm.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.crm.FormalCust,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.FormalCust,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 删除客户跟进记录
删除客户跟进记录
        * /formalCust/delCustTrack
        */
      export namespace delCustTrack {
        export class Params {
          /** id */
          id: string;
        }

        export type Response<T> = defs.crm.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 导出
导出
        * /formalCust/exportCust
        */
      export namespace exportCust {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: any;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.crm.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 转入正式客户从非正式客户表
转入正式客户从非正式客户表
        * /formalCust/postIfmCust2FormalCust
        */
      export namespace postIfmCust2FormalCust {
        export class Params {
          /** ifmCustId */
          ifmCustId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.CommonResponse<number>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 转入正式客户从非正式客户表
转入正式客户从非正式客户表
        * /formalCust/postIfmCust2FormalCust
        */
      export namespace postPostIfmCust2FormalCust {
        export class Params {
          /** ifmCustId */
          ifmCustId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.CommonResponse<number>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询客户跟进记录
查询客户跟进记录
        * /formalCust/queryCustTrack
        */
      export namespace queryCustTrack {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.crm.FormalQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.FormalQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询客户单表信息
查询客户单表信息
        * /formalCust/queryFormalCust
        */
      export namespace queryFormalCust {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.FormalCust;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.crm.FormalCust,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.FormalCust,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询正式客户,带产品线
查询正式客户,带产品线
        * /formalCust/queryFormalCustWithProLine
        */
      export namespace queryFormalCustWithProLine {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.crm.FormalQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.FormalQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 类型ID101: 客户建立102:客户删除103:客户进入共享104:正在跟进分配105:客户名称修改106:共享区分配107:删除区分配108:客户基本信息修改109:市场活动预录入客户分配			  客户查重,随机查询20家客户信息
类型ID101: 客户建立102:客户删除103:客户进入共享104:正在跟进分配105:客户名称修改106:共享区分配107:删除区分配108:客户基本信息修改109:市场活动预录入客户分配			  客户查重,随机查询20家客户信息
        * /formalCust/queryRandomCustGroupData
        */
      export namespace queryRandomCustGroupData {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.CommonResponse<Array>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.crm.FormalCust,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.FormalCust,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据名称获取客户信息,客户信息中包含产品先id和
根据名称获取客户信息,客户信息中包含产品先id和
        * /formalCust/querySinglenCustWithRemainProLine
        */
      export namespace querySinglenCustWithRemainProLine {
        export class Params {
          /** custName */
          custName: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.FormalCust;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据名称获取客户信息,客户信息中包含产品先id和
根据名称获取客户信息,客户信息中包含产品先id和
        * /formalCust/querySinglenCustWithRemainProLine
        */
      export namespace postQuerySinglenCustWithRemainProLine {
        export class Params {
          /** custName */
          custName: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.FormalCust;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 保存客户跟进记录
保存客户跟进记录
        * /formalCust/saveCustTrack
        */
      export namespace saveCustTrack {
        export class Params {}

        export type Response<T> = defs.crm.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.crm.CustTrack,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.CustTrack,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 修改客户名称
修改客户名称
        * /formalCust/uptCustName
        */
      export namespace uptCustName {
        export class Params {}

        export type Response<T> = defs.crm.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.crm.FormalCust,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.FormalCust,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 更新客户基本信息
更新客户基本信息
        * /formalCust/uptFormalCust
        */
      export namespace uptFormalCust {
        export class Params {}

        export type Response<T> = defs.crm.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.crm.FormalCust,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.FormalCust,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 客户拜访
     */
    export namespace hsSlCustomerVisit {
      /**
        * 创建客户拜访
创建客户拜访
        * /hsSlCustomerVisit/createHsSlCustomerVisit
        */
      export namespace createHsSlCustomerVisit {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: string;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.crm.HsSlCustomerVisit,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.HsSlCustomerVisit,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取客户其他
获取客户其他
        * /hsSlCustomerVisit/getCustomerOtherInfo
        */
      export namespace getCustomerOtherInfo {
        export class Params {
          /** custId */
          custId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.CommonResponse<defs.crm.ObjectMap<string, ObjectMap>>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取数据
获取数据
        * /hsSlCustomerVisit/getData
        */
      export namespace getData {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.crm.CustomerVisitQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.CustomerVisitQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取结果
获取结果
        * /hsSlCustomerVisit/getEditableResult
        */
      export namespace getEditableResult {
        export class Params {
          /** custId */
          custId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.CommonResponse<number>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取结果
获取结果
        * /hsSlCustomerVisit/getEditableResult
        */
      export namespace postGetEditableResult {
        export class Params {
          /** custId */
          custId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.CommonResponse<number>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * <AUTHOR>  陌拜客户维护接口
     */
    export namespace ifmCust {
      /**
        * 主键逻辑删除非正式客户
主键逻辑删除非正式客户
        * /ifmCust/delIfmCust
        */
      export namespace delIfmCust {
        export class Params {
          /** id */
          id: string;
        }

        export type Response<T> = defs.crm.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 删除客户跟进记录
删除客户跟进记录
        * /ifmCust/delIfmCustTrack
        */
      export namespace delIfmCustTrack {
        export class Params {
          /** id */
          id: string;
        }

        export type Response<T> = defs.crm.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 提交非正式客户审批
提交非正式客户审批
        * /ifmCust/postIfmCust
        */
      export namespace postIfmCust {
        export class Params {}

        export type Response<T> = defs.crm.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.crm.IfmCust,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.IfmCust,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询待审批客户
查询待审批客户
        * /ifmCust/queryApproveIfmCust
        */
      export namespace queryApproveIfmCust {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.crm.IfmCustQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.IfmCustQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 分页查询陌非正式拜客户
分页查询陌非正式拜客户
        * /ifmCust/queryIfmCust
        */
      export namespace queryIfmCust {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.IfmCust;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.crm.IfmCustQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.IfmCustQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询非正式客户跟进
查询非正式客户跟进
        * /ifmCust/queryIfmCustTrack
        */
      export namespace queryIfmCustTrack {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.crm.FormalQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.FormalQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 保存非正式客户,包含陌拜,预注册,市场客户
保存非正式客户,包含陌拜,预注册,市场客户
        * /ifmCust/saveIfmCust
        */
      export namespace saveIfmCust {
        export class Params {}

        export type Response<T> = defs.crm.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.crm.IfmCust,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.IfmCust,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 生效客户跟进记录
生效客户跟进记录
        * /ifmCust/saveIfmCustTrack
        */
      export namespace saveIfmCustTrack {
        export class Params {}

        export type Response<T> = defs.crm.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.crm.CustTrack,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.CustTrack,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 客户审批通过
客户审批通过
        * /ifmCust/uptCustAprvPass
        */
      export namespace uptCustAprvPass {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: string;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.crm.IfmCust,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.IfmCust,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 客户审批驳回
客户审批驳回
        * /ifmCust/uptCustAprvReject
        */
      export namespace uptCustAprvReject {
        export class Params {}

        export type Response<T> = defs.crm.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.crm.IfmCust,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.IfmCust,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 客户审批终止
客户审批终止
        * /ifmCust/uptCustAprvStop
        */
      export namespace uptCustAprvStop {
        export class Params {}

        export type Response<T> = defs.crm.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.crm.IfmCust,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.IfmCust,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 分配客户确认
分配客户确认
        * /ifmCust/uptMarketCustAllot
        */
      export namespace uptMarketCustAllot {
        export class Params {}

        export type Response<T> = defs.crm.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.crm.IfmCust,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.IfmCust,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 市场活动
     */
    export namespace marketActivity {
      /**
        * 删除
删除
        * /marketActivity/delMarketActivity
        */
      export namespace delMarketActivity {
        export class Params {}

        export type Response<T> = defs.crm.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.crm.MarketActivityQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.MarketActivityQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询
查询
        * /marketActivity/queryMarketActivity
        */
      export namespace queryMarketActivity {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.crm.MarketActivityQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.MarketActivityQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 保存
保存
        * /marketActivity/saveMarketActivity
        */
      export namespace saveMarketActivity {
        export class Params {}

        export type Response<T> = defs.crm.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.crm.MarketActivityQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.MarketActivityQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 每月服务情况
     */
    export namespace monthService {
      /**
        * 每月服务情况
每月服务情况，必须分页，不分页的话查询需要1分钟以上，且返回结果字符串大小已经超40M，浏览器会卡死
        * /api/crm/service/getServicem
        */
      export namespace getServicem {
        export class Params {
          /** 账单月，yyyyMM */
          billYm: string;
          /** 当前页 */
          pageNum: string;
          /** 每页数量 */
          pageSize: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.GlobalResult;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 组织架构信息
     */
    export namespace orginData {
      /**
        * 获取分公司信息
获取分公司信息
        * /api/crm/company/getCompany
        */
      export namespace getCompany {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.GlobalResult;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 共享区六大区
     */
    export namespace shareArea {
      /**
        * 拾取产品线 operateType=1表示共享区 =4表示删除区
拾取产品线 operateType=1表示共享区 =4表示删除区
        * /shareArea/allotProductLine
        */
      export namespace allotProductLine {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.CommonResponse<number>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.crm.ShareAreaDTO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.ShareAreaDTO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 跨区分配产品线 operateType=2表示共享区单客户 =3表示共享区多客户  =5 删除区单客户 =6删除区多客户 =7正在跟进单客户 =8正在跟进多客户
跨区分配产品线 operateType=2表示共享区单客户 =3表示共享区多客户  =5 删除区单客户 =6删除区多客户 =7正在跟进单客户 =8正在跟进多客户
        * /shareArea/allotProductLineMult
        */
      export namespace allotProductLineMult {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.CommonResponse<number>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.crm.ShareAreaDTO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.ShareAreaDTO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 跨区分配单客户实体ShareAreaProductLines客户为逗号分割
跨区分配单客户实体ShareAreaProductLines客户为逗号分割
        * /shareArea/getMultCust
        */
      export namespace getMultCust {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.CommonResponse<Array<defs.crm.ShareAreaProductLines>>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.crm.ShareAreaProductLinesQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.ShareAreaProductLinesQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询共享区
查询共享区
        * /shareArea/getShareArea
        */
      export namespace getShareArea {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.crm.ShareAreaProductLinesQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.ShareAreaProductLinesQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询跨区分配
查询跨区分配
        * /shareArea/getShareAreaAllot
        */
      export namespace getShareAreaAllot {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.crm.ShareAreaProductLinesQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.ShareAreaProductLinesQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询共享区供选择
查询共享区供选择
        * /shareArea/getShareAreaForPick
        */
      export namespace getShareAreaForPick {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.CommonResponse<Array<defs.crm.ShareAreaProductLines>>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.crm.ShareAreaProductLinesQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.ShareAreaProductLinesQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 跨区分配单客户实体ShareAreaProductLines 销售为逗号分割(暂不用)
跨区分配单客户实体ShareAreaProductLines 销售为逗号分割(暂不用)
        * /shareArea/getSingleCust
        */
      export namespace getSingleCust {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.CommonResponse<Array<defs.crm.ShareAreaProductLines>>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.crm.ShareAreaProductLinesQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.ShareAreaProductLinesQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
       * getUnContraceCustNum
       * /shareArea/getUnContraceCustNum
       */
      export namespace getUnContraceCustNum {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.CommonResponse<number>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.crm.ShareAreaProductLinesQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.ShareAreaProductLinesQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
       * getUnContraceCustNumBySalesId
       * /shareArea/getUnContraceCustNumBySalesId
       */
      export namespace getUnContraceCustNumBySalesId {
        export class Params {
          /** salesId */
          salesId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.CommonResponse<number>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
       * getUnContraceCustNumBySalesId
       * /shareArea/getUnContraceCustNumBySalesId
       */
      export namespace postGetUnContraceCustNumBySalesId {
        export class Params {
          /** salesId */
          salesId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.CommonResponse<number>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * Sl Disa Reason Controller
     */
    export namespace slDisaReason {
      /**
        * 批量新增驳回原因
批量新增驳回原因
        * /slDisaReason/addList
        */
      export namespace getContractList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.crm.Contract,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.Contract,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 批量新增驳回原因
批量新增驳回原因
        * /slDisaReason/deleteById
        */
      export namespace deleteById {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Array<defs.crm.FilterEntity>,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Array<defs.crm.FilterEntity>,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 导出Excel
导出Excel
        * /slDisaReason/exportExcel
        */
      export namespace exportExcel {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: any;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.crm.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据大合同查询驳回原因
根据大合同查询驳回原因
        * /slDisaReason/queryByPage
        */
      export namespace queryByPage {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.crm.CommonResponse<Array<defs.crm.FilterEntity>>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.crm.Contract,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.crm.Contract,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }
  }
}
