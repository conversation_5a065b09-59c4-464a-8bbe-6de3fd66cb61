import * as defs from '../../baseClass';
import {
  fetch,
  RequestConfig,
  beforeFetch,
  afterFetch,
  checkResStatus,
  removeSpace,
} from '@/utils/request';

/**
     * @url /rhro-service-1.0/rpac/certificate/cust/doapprove
     * @desc 下载审批通过
下载审批通过
     * hasForm: false
     * hasBody: true
     */

export class Params {}

export const init = '';
export const url = '/rhro-service-1.0/rpac/certificate/cust/doapprove:POST';
export const initialUrl = '/rhro-service-1.0/rpac/certificate/cust/doapprove';
export const cacheKey = '_rpac_certificate_cust_doapprove_POST';
export async function request(
  data: Array<defs.welfaremanage.RpaSsCustCertificate>,
  options?: RequestConfig,
) {
  const reqUrl = `/rhro-service-1.0/rpac/certificate/cust/doapprove`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          reolve(res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
export async function requests(
  data: Array<defs.welfaremanage.RpaSsCustCertificate>,
  options?: RequestConfig,
) {
  const reqUrl = `/rhro-service-1.0/rpac/certificate/cust/doapprove`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          if (checkResStatus(res)) return reolve(res ? res.data : res);
          reject(res ? res.message : res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
