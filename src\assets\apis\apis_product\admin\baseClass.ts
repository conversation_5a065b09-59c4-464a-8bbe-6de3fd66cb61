class AccountManageDTO {
  /** 登陆账号 */
  account = '';

  /** 账号名称 */
  accountName = '';

  /** 帐号状态 */
  accountStatus = '';

  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** 所在分公司 */
  branchName = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 分公司id */
  companyId = '';

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 所在部门 */
  departName = '';

  /** 用户编号 */
  empCode = '';

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 员工状态是否有效 */
  isRelationDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 邮箱 */
  mail = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 在线状态 */
  onlineStatus = '';

  /** 密码 */
  password = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 状态 */
  status = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class AccountQuery {
  /** 账号 */
  account = '';

  /** 账号名 */
  accountName = '';

  /** 账号状态0无效1有效 */
  accountStatus = '';

  /** endIndex */
  endIndex = undefined;

  /** 在线状态0离线大于0在线 */
  onlineStatus = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** startIndex */
  startIndex = undefined;
}

class AuthorityAllocation {
  /** add */
  add = false;

  /** 接单客服id */
  assigneeCsId = '';

  /** 派单客服id */
  assignerCsId = '';

  /** 签单方派单人 */
  assignerId = '';

  /** 派单方id */
  assignerProviderId = '';

  /** 派单方 */
  assignerProviderName = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_SUBCONTRACT_BIZ_AUTH.BIZ_ID	  	  ibatorgenerated Thu Sep 15 09:22:37 CST 2011 */
  bizId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_SUBCONTRACT_BIZ_AUTH.BIZMAN_ID	  	  ibatorgenerated Thu Sep 15 09:22:37 CST 2011 */
  bizmanId = '';

  /** 客服名称 */
  bizmanName = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_SUBCONTRACT_BIZ_AUTH.BIZMAN_TYPE	  	  ibatorgenerated Thu Sep 15 09:22:37 CST 2011 */
  bizmanType = '';

  /** 客服类型 */
  bizmanTypeName = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** contractId */
  contractId = '';

  /** 大合同名称 */
  contractName = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_SUBCONTRACT_BIZ_AUTH.CUST_ID	  	  ibatorgenerated Thu Sep 15 09:22:37 CST 2011 */
  custId = '';

  /** 客户名称 */
  custName = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** 客户规模 */
  customerSize = '';

  /** del */
  del = false;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_SUBCONTRACT_BIZ_AUTH.DEPARTMENT_ID	  	  ibatorgenerated Thu Sep 15 09:22:37 CST 2011 */
  departmentId = '';

  /** 分公司名称 */
  departmentName = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_SUBCONTRACT_BIZ_AUTH.END_DT	  	  ibatorgenerated Thu Sep 15 09:22:37 CST 2011 */
  endDt = '';

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** 新客服id */
  newBizmanId = '';

  /** 新分公司idid */
  newBranchId = '';

  /** 生效日期 */
  newEffectiveDt = '';

  /** noChange */
  noChange = false;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 替换权限级别 */
  privilegeLevel = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 责任客服id */
  signCsId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_SUBCONTRACT_BIZ_AUTH.START_DT	  	  ibatorgenerated Thu Sep 15 09:22:37 CST 2011 */
  startDt = '';

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_SUBCONTRACT_BIZ_AUTH.SUBCONTRACT_BIZ_AUTH_ID	  	  ibatorgenerated Thu Sep 15 09:22:37 CST 2011 */
  subcontractBizAuthId = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column HS_SUBCONTRACT_BIZ_AUTH.SUBCONTRACT_ID	  	  ibatorgenerated Thu Sep 15 09:22:37 CST 2011 */
  subcontractId = '';

  /** 小合同名称 */
  subcontractName = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class AuthorityAllocationVO {
  /** 权限分配 */
  authorityAllocation = new AuthorityAllocation();

  /** 权限分配列表 */
  list = [];
}

class BaseEntity {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 菜单编码 */
  functionCode = '';

  /** 菜单id */
  functionId = '';

  /** 菜单信息id */
  functionInfoId = '';

  /** 菜单名称 */
  functionName = '';

  /** 菜单类型 */
  functionType = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** 帮助url */
  helpUrl = '';

  /** 图标url */
  iconUrl = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是不是我工作 */
  isMyWork = '';

  /** 新是否有效：1有效，0无效 */
  isValid = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 菜单层级 */
  level = '';

  /** 链接url */
  linkUrl = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** rownum */
  num = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 新菜单url */
  reactUrl = '';

  /** 报表url */
  reportUrl = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 是否选中 */
  selected = '';

  /** 特例类型 */
  specialType = '';

  /** 状态 */
  status = '';

  /** subMenuList */
  subMenuList = [];

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 上级菜单id */
  superFunctionId = '';

  /** 类型,用于区分操作类型参数 */
  type = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class CommonResponse {
  /** bizCode */
  bizCode = undefined;

  /** code */
  code = undefined;

  /** data */
  data = new DeptDTO();

  /** message */
  message = '';

  /** t */
  t = new DeptDTO();
}

class DeptDTO {
  /** add */
  add = false;

  /** 联系地址 */
  address = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单地址 */
  billAddress = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 账单邮编 */
  billZipCode = '';

  /** 部门业务类别: 10  全部20  客服30  新产品销售31  大客户销售32  渠道部销售33  电销部销售34  BPO事业部销售35  福利事业部销售40  销售50  财务60  法务70  人事80  其他 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** children */
  children = [];

  /** 城市id */
  cityId = '';

  /** 城市名称 */
  cityName = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 联系电话1，电话 */
  contactTel1 = '';

  /** 联系电话2，手机 */
  contactTel2 = '';

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 部门英文名称 */
  departmentEnglishName = '';

  /** 部门级别：1:集团 2:大区 3 :分公司 4:部门 */
  departmentGrade = '';

  /** 部门id */
  departmentId = '';

  /** 部门内部编号 */
  departmentInternalCode = '';

  /** 部门名称 */
  departmentName = '';

  /** 部门属性 是否是业务部门 */
  departmentProperty = '';

  /** email */
  email = '';

  /** 导入类型,扩充使用 */
  expType = '';

  /** 传真 */
  fax = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 所属大区 */
  governingArea = '';

  /** 所属分公司 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 负责人 */
  personInCharge = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 部门类型1 内部 2外部 */
  providerType = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 用户名 */
  realName = '';

  /** 备注 */
  remark = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 是否选中 */
  selected = '';

  /** 状态 */
  status = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 上级部门id */
  superDepartmentId = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';

  /** 邮编 */
  zipCode = '';
}

class DropdownList {
  /** 业务大类类型 */
  btType = '';

  /** chargeRate */
  chargeRate = '';

  /** cityId */
  cityId = '';

  /** cityIdForParty */
  cityIdForParty = '';

  /** cityName */
  cityName = '';

  /** contractAvgAmt */
  contractAvgAmt = '';

  /** contractHeadcount */
  contractHeadcount = '';

  /** contractName */
  contractName = '';

  /** contractSubType */
  contractSubType = '';

  /** contractSubTypeName */
  contractSubTypeName = '';

  /** contractType */
  contractType = '';

  /** contractTypeName */
  contractTypeName = '';

  /** currentSalesName */
  currentSalesName = '';

  /** departmentName */
  departmentName = '';

  /** englishTermName */
  englishTermName = '';

  /** exFeeMonth */
  exFeeMonth = '';

  /** 供应商收费模板 */
  exFeeTempltId = '';

  /** governingArea */
  governingArea = '';

  /** 所属大区 */
  governingAreaId = '';

  /** governingBranch */
  governingBranch = '';

  /** 所属分公司 */
  governingBranchId = '';

  /** groupType */
  groupType = '';

  /** 主键 */
  key = '';

  /** liabilityCsName */
  liabilityCsName = '';

  /** 全称 */
  name = '';

  /** 拼音码 */
  pinYinCode = '';

  /** productLineId */
  productLineId = '';

  /** 供应商类型1内部2外部 */
  providerType = '';

  /** 保留名字1 */
  reserveName1 = '';

  /** 保留名字2 */
  reserveName2 = '';

  /** 储备对象,用于查询下拉框的时候,可能还需要查询其他列的值 */
  reserveObj = '';

  /** 缩写名 */
  shortName = '';

  /** signBrachTitleId */
  signBrachTitleId = '';

  /** signBranchTitleName */
  signBranchTitleName = '';

  /** 社保组ID */
  ssGroupId = '';

  /** svcSubtypeName */
  svcSubtypeName = '';

  /** svcTypeName */
  svcTypeName = '';
}

class EmpInfoInter {
  /** add */
  add = false;

  /** address */
  address = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** empId */
  empId = '';

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** id */
  id = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** languageType */
  languageType = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class EmpRelative {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 生日 */
  birthday = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 雇员id */
  empId = '';

  /** 亲属id */
  empRelativesId = '';

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 性别 */
  gender = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** 证件号 */
  idCardNum = '';

  /** 证件类型 */
  idCardType = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 亲属姓名 */
  relativeName = '';

  /** 亲属类型 */
  relativeType = '';

  /** 备注 */
  remark = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class Employee {
  /** add */
  add = false;

  /** 联系地址 */
  address = '';

  /** 养老金帐号 */
  annuityAccount = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** 首次参加工作日期 */
  beginWorkDate = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 生日 */
  birthday = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 联系电话1，电话 */
  contactTel1 = '';

  /** 联系电话2，手机 */
  contactTel2 = '';

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 国家id */
  countryId = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 残疾类型 */
  disabilityType = '';

  /** 教育程度 */
  educationLevel = '';

  /** email */
  email = '';

  /** 员工编号 */
  employeeCode = '';

  /** 员工id */
  employeeId = '';

  /** 员工姓名 */
  employeeName = '';

  /** 上岗状态 */
  employeeStatus = '';

  /** endIndex */
  endIndex = undefined;

  /** 民族 */
  ethnic = '';

  /** 查询使用排除的雇员id */
  exceptEmpId = '';

  /** 导入类型,扩充使用 */
  expType = '';

  /** 家属劳保卡号 */
  familyLaborCardNo = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** 外语等级 */
  foreignLanguageLevel = '';

  /** 外语语种 */
  foreignLanguageType = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 性别 */
  gender = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** 是否有不良记录 */
  haveBadnessRecord = '';

  /** 健康状况 */
  healthStatus = '';

  /** 入职日期 */
  hireDate = '';

  /** 户口所在地 */
  hukouAddress = '';

  /** 户口类别 */
  hukouType = '';

  /** 证件号 */
  idCardNum = '';

  /** 证件类型 */
  idCardType = '';

  /** inId */
  inId = '';

  /** 初始化类别 */
  initType = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** 是否市级保健对象 */
  isCareObject = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否伤残军人 */
  isDisabledMilitary = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 参军日期 */
  joinMilitaryDate = '';

  /** 婚姻状况 */
  marriageStatus = '';

  /** 医疗卡 */
  medicalCardId = '';

  /** 医疗保障类别 */
  medicareType = '';

  /** 模拟人 */
  mimicBy = '';

  /** 兵役状况 */
  nationalServiceStatus = '';

  /** noChange */
  noChange = false;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 密码 */
  password = '';

  /** 政治面貌 */
  politicalStatus = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 职业工种发证日期 */
  professionAwardDate = '';

  /** 职业工种发证单位 */
  professionAwardUnit = '';

  /** 职业工种等级 */
  professionLevel = '';

  /** 职业工种名称 */
  professionName = '';

  /** 专业技术职称 */
  professionTitle = '';

  /** 公积金账号 */
  providentFundAccount = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 省份id */
  provinceId = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 雇员信息备注 */
  remark = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 转业日期 */
  retireFromMilitaryDate = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 离职提起 */
  sepDate = '';

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 补充公积金账号 */
  supProvidentFundAccount = '';

  /** 职称发证日期 */
  techAwardDate = '';

  /** 职称发证单位 */
  techAwardUnit = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';

  /** 用户名 */
  userName = '';

  /** 邮政编码 */
  zipCode = '';
}

class EmployeeFee {
  /** add */
  add = false;

  /** 调整情况分类：1费用添加、2费用清空、3比例调整、4基数调整、5基数+比例调整、6其他调整 */
  adjSituType = '';

  /** 金额 */
  amount = undefined;

  /** 金额(不含税) */
  amtNoTax = '';

  /** 附加税费 */
  atr = '';

  /** 基数绑定级次 */
  baseBindingLevel = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 账单起始月 */
  billStartMonth = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** 年缴计算顺序 1:先计算结果再乘以12；2:基数乘以12再计算结果  */
  calculationOrder = '';

  /** 类型 */
  category = '';

  /** 收费结束时间 */
  chargeEndDate = '';

  /** 缴费频率 */
  chargeRate = '';

  /** 收费起始时间 */
  chargeStartDate = '';

  /** cityId */
  cityId = undefined;

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 套餐id */
  comboId = '';

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 企业附加金额 */
  eAdditionalAmt = undefined;

  /** 企业金额 */
  eAmt = undefined;

  /** 企业基数 */
  eBase = undefined;

  /** eBillTemplt */
  eBillTemplt = '';

  /** 企业账单模板id */
  eBillTempltId = '';

  /** 企业计算方法:4四舍五入,9见零进整,3截位,0先四舍五入再见零进整,8先截位再见零见整 */
  eCalculationMethod = '';

  /** 企业收费月,月收:, 两月收:数月:偶数月.,三月收:,4,7,10;,5,8,11;,6,9,12.,半年收:1,7;2,8;3,9;4,10;5,11;6,12,年收:无 */
  eFeeMonth = '';

  /** 收费模板名 */
  eFeeTemplt = '';

  /** 企业收费模板id */
  eFeeTempltId = '';

  /** 企业频率,1:月收、2:两月收、3:三月收、6:半年收、12:年收 */
  eFrequency = '';

  /** 企业最高比例 */
  eMaxRatio = undefined;

  /** 企业最低比例 */
  eMinRatio = undefined;

  /** 企业提前几个月收,默认为0，选项0-3 */
  eMonthInAdvance = '';

  /** 企业精确值0：0位小数1：1位小数2：2位小数5： 精确值 */
  ePrecision = '';

  /** 企业比例 */
  eRatio = undefined;

  /** 企业比例步长 */
  eRatioStep = undefined;

  /** 费用段历史id */
  empFeeHisId = '';

  /** 费用段id */
  empFeeId = '';

  /** 费用段操作id */
  empFeeOprId = '';

  /** 员工入离职id */
  empHireSepId = '';

  /** 员工id */
  empId = '';

  /** 外部供应商账单起始月 */
  exBillStartMonth = '';

  /** 供应商收费月 */
  exFeeMonth = '';

  /** 供应商收费模板名 */
  exFeeTemplt = '';

  /** 外部供应商收费模板id */
  exFeeTempltId = '';

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否一次性付费 */
  isOneTimePay = '';

  /** 是否显示 1:是0:否 */
  isShow = '';

  /** 是否更新月度表1:是0:否 */
  isUptFeeMon = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 原金额 */
  oldAmount = '';

  /** 原账单起始月 */
  oldBillStartMonth = '';

  /** 个人附加金额 */
  pAdditionalAmt = undefined;

  /** 个人金额 */
  pAmt = undefined;

  /** 个人基数 */
  pBase = undefined;

  /** pBillTemplt */
  pBillTemplt = '';

  /** 个人部分账单模板id */
  pBillTempltId = '';

  /** 个人计算方式:4四舍五入,9见零进整,3截位,0先四舍五入再见零进整,8先截位再见零见整 */
  pCalculationMethod = '';

  /** 个人收费月,月收:, 两月收:数月:偶数月.,三月收:,4,7,10;,5,8,11;,6,9,12.,半年收:1,7;2,8;3,9;4,10;5,11;6,12,年收:无 */
  pFeeMonth = '';

  /** 收费模板名 */
  pFeeTemplt = '';

  /** 个人收费模板id */
  pFeeTempltId = '';

  /** 个人频率,1:月收、2:两月收、3:三月收、6:半年收、12:年收 */
  pFrequency = '';

  /** 个人最高比例 */
  pMaxRatio = undefined;

  /** 个人最低比例 */
  pMinRatio = undefined;

  /** 个人提前几个月收,提前几个月收,默认为0，选项0-3 */
  pMonthInAdvance = '';

  /** 个人精度0：0位小数1：1位小数2：2位小数5： 精确值 */
  pPrecision = '';

  /** 个人比例 */
  pRatio = undefined;

  /** 个人比例步长 */
  pRatioStep = undefined;

  /** 支付频率1:月缴,2季度缴,3年缴(不足一年按年缴),4年缴(不足一年按月缴) */
  payFrequency = '';

  /** 支付最后服务年月 */
  payLastServiceMonth = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 产品id */
  productId = '';

  /** 产品名称 */
  productName = '';

  /** 产品比例id */
  productRatioId = '';

  /** 产品比例名称 */
  productRatioName = '';

  /** 产品类型id */
  productTypeId = undefined;

  /** 供应商id */
  providerId = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 报价单id */
  quotationId = '';

  /** 报价单子项id */
  quotationItemId = '';

  /** 应收金额 */
  receivableAmt = undefined;

  /** 应收几个月 */
  receivableMonth = '';

  /** 备注 */
  remark = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 社保组id */
  ssGroupId = '';

  /** 社保组名称 */
  ssGroupName = '';

  /** 社保福利包id */
  ssWelfarePkgId = '';

  /** 社保福利包名称 */
  ssWelfarePkgName = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 标签 */
  tag = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';

  /** 增值税 */
  vat = '';

  /** 增值税率 */
  vatr = '';

  /** 实收金额 */
  verifyAmt = undefined;

  /** 实收金额(不含税) */
  verifyAmtNoTax = '';

  /** 实收金额增值税 */
  verifyAmtVat = '';
}

class EmployeeHireSep {
  /** 开户人姓名 */
  accountEmployeeName = '';

  /** 实际工作地 */
  actualWorkLoc = '';

  /** add */
  add = false;

  /** 增员确认人 */
  addConfirmBy = '';

  /** 增员确认时间 */
  addConfirmDate = '';

  /** 增员过程 */
  addConfirmPro = '';

  /** 增员状态:1增员未提交 20 等待接单方确认 22 接单方挂起 30 等待派单方确认40 增员完成 */
  addConfirmStatus = '';

  /** 增员状态名称 */
  addConfirmStatusName = '';

  /** 增员接单确认时间 */
  addPerfectBy = '';

  /** 增员接单确认人 */
  addPerfectDate = '';

  /** 增员原因:1.正常增员2.从竞争对手中转入 3.转移 4不详 */
  addReason = '';

  /** 增员备注 */
  addRemark = '';

  /** 年龄 */
  age = '';

  /** 变更确认人 */
  alterConfirmBy = '';

  /** 变更确认时间 */
  alterConfirmDate = '';

  /** 变更确认过程 */
  alterConfirmPro = '';

  /** 变更接单确认人 */
  alterPerfectBy = '';

  /** 变更接单确时间 */
  alterPerfectDate = '';

  /** 变更备注 */
  alterRemark = '';

  /** 变更状态:1变更未提交 20 等待接单方确认 25:派单方驳回 30 等待派单方确认 40 变更最终确认 */
  alterStatus = '';

  /** 变更状态名称 */
  alterStatusName = '';

  /** 大区类型 */
  areaType = '';

  /** 大区类型名称 */
  areaTypeName = '';

  /** 接单城市id */
  assigneeCityId = '';

  /** 接单客服name */
  assigneeCs = '';

  /** 接单客服 */
  assigneeCsId = '';

  /** 接单方 */
  assigneeProvider = '';

  /** 接单方 */
  assigneeProviderId = '';

  /** 派单客服name */
  assignerCs = '';

  /** 派单客服 */
  assignerCsId = '';

  /** 派单方 */
  assignerProvider = '';

  /** 派单方 */
  assignerProviderId = '';

  /** 派单类型1 执行单2 协调单3 收集单 */
  assignmentType = '';

  /** 关联状态 */
  associationStatus = '';

  /** 银行卡号 */
  bankAcct = '';

  /** 银行卡更新人 */
  bankCardUpdateBy = '';

  /** 银行卡更新时间 */
  bankCardUpdateDt = '';

  /** baseInfo主键 */
  baseInfoId = '';

  /** 批次号,用于生成社保服务信息 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 账单模板id */
  billTempltId = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** 类型 */
  category = '';

  /** 离职证明电子版本 */
  certificateSpId = '';

  /** certificateStatusName */
  certificateStatusName = '';

  /** 编辑方式1:增员完成提交变更 2:增员确认了,只变更报价3:增员确认了,变更社保公积金 ,接单方是内部供应商4:增员确认了,变更社保公积金	  ,接单方是外部供应商 */
  changeMethod = '';

  /** 收费截至日期 */
  chargeEndDate = '';

  /** 城市id */
  cityId = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 商保订单状态 */
  commInsurStatus = '';

  /** 商保订单状态name */
  commInsurStatusName = '';

  /** 确认备注 */
  confirmRemark = '';

  /** 联系电话1，电话 */
  contactTel1 = '';

  /** 联系电话2，手机 */
  contactTel2 = '';

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 合同编号 */
  contractCode = '';

  /** 合同id */
  contractId = '';

  /** 合同名称 */
  contractName = '';

  /** contractStartDate */
  contractStartDate = '';

  /** contractStopDate */
  contractStopDate = '';

  /** 法人单位id */
  corporationId = '';

  /** 法人单位名称 */
  corporationName = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** 创建日期 */
  createDt = '';

  /** 客户编号 */
  custCode = '';

  /** 客户id */
  custId = '';

  /** 客户方内部编号 */
  custInternalNum = '';

  /** 客户姓名 */
  custName = '';

  /** 缴费实体id */
  custPayEntityId = undefined;

  /** 缴费实体 */
  custPayEntityName = '';

  /** 客户类型 */
  custType = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** 客户规模 */
  customerSize = '';

  /** 类型: 1,正常 2,大客户  */
  dataType = undefined;

  /** 申报工资 */
  decSalary = '';

  /** decSalaryRemark */
  decSalaryRemark = '';

  /** del */
  del = false;

  /** email */
  email = '';

  /** 客户端增员ID */
  empAddId = '';

  /** 客户端变更ID */
  empAlterId = '';

  /** feeId数组 */
  empFeeIdArray = '';

  /** 历史表主键 */
  empHireSepHisId = '';

  /** 员工入离职id */
  empHireSepId = '';

  /** 入职主记录ID */
  empHiresepMainId = '';

  /** 员工id */
  empId = '';

  /** 停缴id */
  empStopId = '';

  /** 停缴处理进程 0: 停缴未启动 1：停缴处理中 2：停缴完成 */
  empStopProcessState = '';

  /** 人员分类: 1 派遣 2 代理 3 BPO人员 4 外包 */
  empType = '';

  /** 人员分类: 1 派遣 2 代理 3 BPO人员 4 外包 */
  empTypeId = undefined;

  /** 人员分类 */
  empTypeName = '';

  /** 唯一号 */
  employeeCode = '';

  /** 费用段列表 */
  employeeFeeList = [];

  /** 雇员姓名 */
  employeeName = '';

  /** 雇员状态 */
  employeeStatus = '';

  /** endIndex */
  endIndex = undefined;

  /** enhancedAgent */
  enhancedAgent = '';

  /** enhancedAgentName */
  enhancedAgentName = '';

  /** 外部供应商收费模板 */
  exFeeTemplt = '';

  /** 外部供应商账单id */
  exFeeTempltId = '';

  /** exQuotationFeeList */
  exQuotationFeeList = [];

  /** 导入类型,扩充使用 */
  expType = '';

  /** 收费月,月收:, 两月收:数月:偶数月.,三月收:,4,7,10;,5,8,11;,6,9,12.,半年收:1,7;2,8;3,9;4,10;5,11;6,12,年收:无 */
  feeMonth = '';

  /** 收费模板名称 */
  feeTemplt = '';

  /** 收费模板id */
  feeTempltId = '';

  /** 档案柜编号 */
  fileCabCode = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** 文件夹编号 */
  folderCode = '';

  /** 频率,1:月收、2:两月收、3:三月收、6:半年收、12:年收 */
  frequency = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** 是否关联订单 */
  hasAssociations = undefined;

  /** 入职竞争对手 */
  hireCompetitor = '';

  /** 入职时间 */
  hireDt = '';

  /** 入职时间止 */
  hireEndDt = '';

  /** 入职报价单 */
  hireQuotationId = '';

  /** 入职备注 */
  hireRemark = '';

  /** 入职时间起 */
  hireStartDt = '';

  /** 证件号码 */
  idCardNum = '';

  /** 证件类型 */
  idCardType = '';

  /** 接单客服name */
  idCardTypeName = '';

  /** inId */
  inId = '';

  /** 内外部类型(1内部2外部3全部) */
  innerType = '';

  /** 是否需要实做 */
  isAddProcess = '';

  /** 增员是否需要实做 */
  isAddProcessName = '';

  /** 是否需要签订劳动合同 */
  isArchive = '';

  /** 是否归档名称 */
  isArchiveName = '';

  /** 银行卡是否上传 */
  isBankCardUpload = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否入职呼叫 */
  isHireCall = '';

  /** 是否入职呼叫名称 */
  isHireCallName = '';

  /** 身份证是否上传 */
  isIDCardUpload = '';

  /** 是否单立户1 是0 否 */
  isIndependent = '';

  /** 劳动合同是否上传 */
  isLaborContractUpload = '';

  /** 是否需要签订劳动合同 */
  isNeedSign = '';

  /** 是否需要实做 */
  isReduceProcess = '';

  /** 是否退费 0否  1是 */
  isRefund = '';

  /** isRelated */
  isRelated = '';

  /** 是否集中一地投保 */
  isSameInsur = '';

  /** 是否集中一地投保中文 */
  isSameInsurName = '';

  /** 是否离职外呼1 呼叫中心通知  2 客服自行通知  3 不需通知  客户代通知 */
  isSepCall = '';

  /** 是否离职呼叫名 */
  isSepCallName = '';

  /** 是否有统筹医疗 */
  isThereACoordinateHealth = '';

  /** 是否有统筹医疗名称 */
  isThereACoordinateHealthText = '';

  /** 是否有社保卡 */
  isThereSsCard = '';

  /** 是否有社保卡名称 */
  isThereSsCardText = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 离职动态模板json */
  jsonStr = [];

  /** 劳动关系单位 */
  laborRelationUnit = '';

  /** 责任客服 */
  liabilityCs = '';

  /** 材料列表 */
  materialList = [];

  /** materialSignStatus */
  materialSignStatus = undefined;

  /** materialSignStatusName */
  materialSignStatusName = '';

  /** 离职材料电子版本id */
  materialSpId = '';

  /** materialStatusName */
  materialStatusName = '';

  /** 模拟人 */
  mimicBy = '';

  /** 操作方式  单立户1、大户2 */
  modeOfOperation = '';

  /** 提前几个月收,默认为0，选项0-3 */
  monthInAdvance = '';

  /** 后指针 */
  nextPointer = '';

  /** noChange */
  noChange = false;

  /** 非社保列表 */
  nonSsGroupList = [];

  /** 银行名称 */
  openBankName = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 挂起原因 */
  pendingReason = '';

  /** 挂起原因中文 */
  pendingReasonName = '';

  /** 人员分类id */
  personCategoryId = '';

  /** 职位id */
  positionId = '';

  /** 前指针 */
  prevPointer = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 流程实例化id */
  processInsId = '';

  /** 供应商编码 */
  providerCode = '';

  /** 供应商客服 */
  providerCs = '';

  /** 供应商客服id */
  providerCsId = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 供应商类型1内部2外部 */
  providerType = '';

  /** 代理人 */
  proxyBy = '';

  /** 供应商集团id */
  prvdGroupId = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 供应商集团 */
  prvdGroupName = '';

  /** 离职材料签订形式 */
  quitSignType = undefined;

  /** quitSignTypeName */
  quitSignTypeName = '';

  /** 电子离职合同任务主键 */
  quitTaskId = undefined;

  /** 报价单编码 */
  quotationCode = '';

  /** 报价单名称 */
  quotationName = '';

  /** 减少详细原因 */
  reduceDetailReason = '';

  /** 减原详细原因名称 */
  reduceDetailReasonName = '';

  /** 客户端减员ID */
  reduceId = '';

  /** 减少原因:1正常减员2转至竞争对手3服务原因4准备撤单和已报撤单正在减员中5客户原因6其他原因7变更合同名称8变更服务项目9易才内部转单 */
  reduceReason = '';

  /** 减员原因名称 */
  reduceReasonName = '';

  /** 参考日期，页面传入 */
  referDate = '';

  /** 备注 */
  remark = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** riskPremiumRatio */
  riskPremiumRatio = '';

  /** riskSharingRatio */
  riskSharingRatio = '';

  /** 报入职人员id */
  rptHireBy = '';

  /** 报入职人 */
  rptHireByName = '';

  /** 报入职时间 */
  rptHireDt = '';

  /** 报入职日期止 */
  rptHireEndDt = '';

  /** 报入职日期起 */
  rptHireStartDt = '';

  /** 报离职人员id */
  rptSepBy = '';

  /** 报离职人 */
  rptSepByName = '';

  /** 报离职日期 */
  rptSepDt = '';

  /** 报离职日期止 */
  rptSepEndDt = '';

  /** 报离职日期起 */
  rptSepStartDt = '';

  /** 用章对象 */
  sealObject = '';

  /** 用章类型 */
  sealType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 离职确认人 */
  sepConfirmBy = '';

  /** 离职确认日期 */
  sepConfirmDate = '';

  /** 离职确认历史 */
  sepConfirmHis = '';

  /** 离职确认进程 */
  sepConfirmPro = '';

  /** 离职确认状态: 1离职未提交 20 等待接单方确认 23接单方驳回 30 等待派单方确认40 离职完成 */
  sepConfirmStatus = '';

  /** 离职状态名称 */
  sepConfirmStatusName = '';

  /** 离职详细原因 */
  sepDetailReason = '';

  /** 离职详细原因名称 */
  sepDetailReasonName = '';

  /** 离职日期 */
  sepDt = '';

  /** 离职时间止 */
  sepEndDt = '';

  /** 离职接单确认人 */
  sepPerfectBy = '';

  /** 离职接单确认时间 */
  sepPerfectDate = '';

  /** 离职手续办理状态:0  未完成   1  完成 */
  sepProcessStatus = '';

  /** 离职报价单 */
  sepQuotationId = '';

  /** 离职原因:1 合同到期终止2 试用期解除3 合同主动解除4 死亡5 合同被动解除10其它 */
  sepReason = '';

  /** 离职原因key */
  sepReasonKey = '';

  /** 离职原因名称 */
  sepReasonName = '';

  /** 离职备注 */
  sepRemark = '';

  /** 离职时间止 */
  sepStartDt = '';

  /** 离职导出类型:1离职接单确认,2离职派单确认 */
  sepType = '';

  /** sigle */
  sigle = false;

  /** 签约方分公司抬头 */
  signBranchTitle = '';

  /** 签约方分公司抬头id */
  signBranchTitleId = '';

  /** 签约方分公司抬头name */
  signBranchTitleName = '';

  /** 签单供应商 */
  signProvider = '';

  /** 签单方 */
  signProviderId = '';

  /** signStatus */
  signStatus = undefined;

  /** 短信发送日期 */
  smsSendDt = '';

  /** 短信发送状态: 0未发送, 1成功, 2失败 */
  smsSendStatus = '';

  /** 短信发送状态中文: 未发送, 成功, 失败 */
  smsSendStatusStr = '';

  /** 分拆方分公司:分拆方客服 */
  splitServiceProviderCs = '';

  /** 社保列表 */
  ssGroupList = [];

  /** 员工社保参与地 */
  ssParticipateLocation = '';

  /** startIndex */
  startIndex = undefined;

  /** 状态 1入职未生效2在职3离职 */
  status = '';

  /** 状态名称 */
  statusName = '';

  /** 小类名称 */
  subTypeName = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 小合同编号 */
  subcontractCode = '';

  /** 小合同id */
  subcontractId = '';

  /** 小合同名称 */
  subcontractName = '';

  /** 大类名称 */
  superTypeName = '';

  /** 总收费日期 */
  totalFeeDt = '';

  /** eos转移id */
  transferId = undefined;

  /** 类型:1增员接单完善离职接单确认,2增员派单确认离职派单确认,3变更派单确认 */
  type = undefined;

  /** 机动分类项目1 */
  type1 = '';

  /** 机动分类项目2 */
  type2 = '';

  /** 机动分类项目3 */
  type3 = '';

  /** 机动分类项目4 */
  type4 = '';

  /** 机动分类项目5 */
  type5 = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';

  /** uuid */
  uuid = '';
}

class EmployeeQuery {
  /** 业务层级 */
  bizCategory = '';

  /** 雇员姓名 */
  employeeName = '';

  /** endIndex */
  endIndex = undefined;

  /** 所属分公司id */
  governingBranch = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** 角色编码 */
  roleCode = '';

  /** 角色层级 */
  roleGrade = '';

  /** startIndex */
  startIndex = undefined;
}

class EmployeeVO {
  /** 部门id */
  departmentId = '';

  /** 部门角色关系id */
  deptUserId = '';

  /** 内部雇员亲属列表新增 */
  empRelativeAddList = [];

  /** 内部雇员亲属列表更新 */
  empRelativeUptList = [];

  /** 内部雇员 */
  employee = new Employee();

  /** 菜单列表 */
  functionList = [];

  /** 原部门角色关系id */
  origDeptUserId = '';

  /** 角色id */
  userId = '';

  /** 人管人列表 */
  userManageList = [];
}

class FilterEntity {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** 城市id */
  cityId = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 部门级别：1:集团 2:大区 3 :分公司 4:部门 */
  departmentGrade = '';

  /** 部门id */
  departmentId = '';

  /** 部门名称 */
  departmentName = '';

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 功能编码 */
  functionCode = '';

  /** 功能名称 */
  functionName = '';

  /** 所属大区id */
  governingAreaId = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 供应商类型，1内部2外部 */
  providerType = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 特殊类型 */
  specialType = '';

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class FunctionDept {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** 主键 */
  checkId = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 部门id */
  departmentId = '';

  /** 部门名称 */
  departmentName = '';

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 功能编码 */
  functionCode = '';

  /** 功能id */
  functionId = '';

  /** 功能名称 */
  functionName = '';

  /** 所在分公司 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 备注 */
  remark = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 特例 */
  specialType = '';

  /** 特例名称 */
  specialTypeName = '';

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class LoginDTO {
  /** 菜单列表 */
  functionList = [];

  /** 是否需要更新密码 */
  isNeedUpdatePw = false;

  /** 鉴权接口返回沐容url根地址 */
  murongServer = '';

  /** needUpdatePw */
  needUpdatePw = false;

  /** 推送服务 */
  pushServerIP = '';

  /** 契约锁服务器 */
  qysServer = '';

  /** 报表目录 */
  reportDir = '';

  /** 报表目录sparkSql */
  reportDirBySparkSql = '';

  /** 报表map */
  reportV4DirMap = undefined;

  /** 角色列表 */
  roleList = [];

  /** token,给MR用 */
  token = '';

  /** 用户对象 */
  user = new UserDTO();

  /** 用户id */
  userId = '';
}

class Map {}

class Material {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column HS_MATERIAL.MATERIAL_ID           ibatorgenerated Wed Nov 30 13:28:25 CST 2011 */
  materialId = undefined;

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column HS_MATERIAL.MATERIAL_NAME           ibatorgenerated Wed Nov 30 13:28:25 CST 2011 */
  materialName = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column HS_MATERIAL.REMARK           ibatorgenerated Wed Nov 30 13:28:25 CST 2011 */
  remark = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class RoleDTO {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 角色业务类别: 10  全部20  客服30  新产品销售31  大客户销售32  渠道部销售33  电销部销售34  BPO事业部销售35  福利事业部销售40  销售50  财务60  法务70  人事80  其他 */
  bizCategory = '';

  /** 角色业务类别Name */
  bizCategoryName = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 角色编号 */
  roleCode = '';

  /** 角色描述 */
  roleDesc = '';

  /** 角色级别:1:集团,2:大区,3:分公司,4:部门,5:自身 */
  roleGrade = '';

  /** 角色级别Name */
  roleGradeName = '';

  /** 角色id */
  roleId = '';

  /** 角色名称 */
  roleName = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** startIndex */
  startIndex = undefined;

  /** 状态 */
  status = '';

  /** 状态Name */
  statusName = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class RoleUser {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 部门id */
  deptId = '';

  /** 部门名称 */
  deptName = '';

  /** 员工编号 */
  employeeCode = '';

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 所属大区 */
  governingArea = '';

  /** 所属分公司 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 姓名 */
  realName = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 角色id */
  roleId = '';

  /** 角色名称 */
  roleName = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** startIndex */
  startIndex = undefined;

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';

  /** 用户角色id */
  userRoleId = '';
}

class RoleVO {
  /** 菜单id列表 */
  menuList = [];

  /** 角色id */
  roleId = '';

  /** 角色用户列表 */
  roleUserList = [];

  /** 用户id */
  userId = '';
}

class UserDTO {
  /** add */
  add = false;

  /** 地址 */
  address = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** 手机 */
  cellphone = '';

  /** 所属分公司城市 */
  cityId = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 联系电话 */
  contactTel1 = '';

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** 默认部门 */
  defaultDept = '';

  /** del */
  del = false;

  /** 部门层级 */
  departmentGrade = '';

  /** 部门id */
  departmentId = '';

  /** 部门名称 */
  departmentName = '';

  /** 部门列表 */
  deptList = [];

  /** 用户部门关系id */
  deptUserId = '';

  /** 电子邮件 */
  email = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SYS_USER_INFO.EMPLOYEE_ID	  	  ibatorgenerated Tue Mar 15 10:09:26 CST 2011 */
  employeeId = '';

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 功能按钮列表 */
  funcBtnList = [];

  /** 功能列表 */
  functionList = [];

  /** 所属大区 */
  governingArea = '';

  /** governingAreaName */
  governingAreaName = '';

  /** 所属分公司 */
  governingBranch = '';

  /** 所属分公司名字 */
  governingBranchName = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否需要更新密码 */
  isNeedUpdatePw = false;

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** needUpdatePw */
  needUpdatePw = false;

  /** noChange */
  noChange = false;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SYS_USER_INFO.PASSWORD	  	  ibatorgenerated Tue Mar 15 10:09:26 CST 2011 */
  password = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SYS_USER_INFO.REAL_NAME	  	  ibatorgenerated Tue Mar 15 10:09:26 CST 2011 */
  realName = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 角色列表 */
  rolesList = [];

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SYS_USER_INFO.STATUS	  	  ibatorgenerated Tue Mar 15 10:09:26 CST 2011 */
  status = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SYS_USER_INFO.USER_ID	  	  ibatorgenerated Tue Mar 15 10:09:26 CST 2011 */
  userId = '';

  /** 下属用户列表 */
  userList = [];

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';

  /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column SYS_USER_INFO.USER_NAME	  	  ibatorgenerated Tue Mar 15 10:09:26 CST 2011 */
  userName = '';
}

class UserDept {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 部门id */
  departmentId = '';

  /** 姓名 */
  departmentName = '';

  /** 部门用户id */
  deptUserId = '';

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 是否默认部门 */
  isDefault = '';

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 选中标记 */
  selected = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';

  /** 用户名 */
  userName = '';
}

class UserManage {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 被管人id */
  subordinateId = '';

  /** 类型 */
  type = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 人管人id */
  userManageId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class roleQuery {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 业务类别 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** endIndex */
  endIndex = undefined;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** 姓名 */
  realName = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 角色编码 */
  roleCode = '';

  /** 角色层级 */
  roleGrade = '';

  /** 角色id */
  roleId = '';

  /** 角色名称 */
  roleName = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** startIndex */
  startIndex = undefined;

  /** 状态 */
  status = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

export const admin = {
  AccountManageDTO,
  AccountQuery,
  AuthorityAllocation,
  AuthorityAllocationVO,
  BaseEntity,
  CommonResponse,
  DeptDTO,
  DropdownList,
  EmpInfoInter,
  EmpRelative,
  Employee,
  EmployeeFee,
  EmployeeHireSep,
  EmployeeQuery,
  EmployeeVO,
  FilterEntity,
  FunctionDept,
  LoginDTO,
  Map,
  Material,
  RoleDTO,
  RoleUser,
  RoleVO,
  UserDTO,
  UserDept,
  UserManage,
  roleQuery,
};
