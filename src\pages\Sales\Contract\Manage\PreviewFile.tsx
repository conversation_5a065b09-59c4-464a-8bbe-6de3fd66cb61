import React, { useEffect, useState } from 'react';
import XLSX from 'xlsx';
import * as mammoth from 'mammoth';
import { Image, Spin, Progress, Alert, Button } from 'antd';
import { Document, Page, pdfjs } from 'react-pdf';
import Codal from '@/components/Codal';
import { FileExcelOutlined, FileWordOutlined } from '@ant-design/icons';
// import { Document, Page } from 'react-pdf';

interface PreviewCodalProps {
  [props: string]: any;
  fileUrl: string;
  fileType: string;
  file: string;
  modal: [boolean, CallableFunction];
}
pdfjs.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.js`;
function FilePreview(fileUrl: string, fileType: string, file: any) {
  const [numPages, setNumPages] = useState(null);
  const [pageNumber, setPageNumber] = useState(1);
  const getFileExtension = (url) => {
    return url.split('.').pop().toLowerCase();
  };

  const changePage = (offset) => {
    setPageNumber((prevPageNumber) => {
      return Math.max(1, Math.min(numPages, prevPageNumber + offset));
    });
  };
  const ext = fileType || getFileExtension(fileUrl);

  // 图片类型
  if (['jpg', 'jpeg', 'png', 'gif', 'webp'].includes(ext)) {
    return <Image src={fileUrl} alt="预览" />;
  }

  // PDF类型
  if (ext === 'pdf') {
    return (
      <Document file={fileUrl} onLoadSuccess={(pdf) => setNumPages(pdf?._pdfInfo?.numPages || 1)}>
        <Page pageNumber={pageNumber} />
        <p>
          共 {numPages} 页，当前 {pageNumber}
        </p>
        <Button onClick={() => changePage(-1)} disabled={pageNumber <= 1}>
          上一页
        </Button>
        <Button
          style={{ marginLeft: '10px' }}
          onClick={() => changePage(1)}
          disabled={pageNumber >= numPages}
        >
          下一页
        </Button>
      </Document>
    );
  }

  // Office文件类型
  if (['doc', 'docx'].includes(ext)) {
    return <WordViewer file={file} />;
  }
  // if (['xls', 'xlsx', 'ppt', 'pptx'].includes(ext)) {
  if (['xls', 'xlsx'].includes(ext)) {
    return <ExcelViewer file={file} />;
  }

  return <div>不支持的文件格式: {ext}</div>;
}
const ExcelViewer = ({ file }) => {
  const [sheets, setSheets] = useState([]);
  const [loading, setLoading] = useState(true);
  const [activeSheet, setActiveSheet] = useState(0);

  useEffect(() => {
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const data = new Uint8Array(e.target.result);
        const workbook = XLSX.read(data, { type: 'array' });

        const sheetNames = workbook.SheetNames;
        const sheetData = sheetNames.map((sheetName) => {
          const worksheet = workbook.Sheets[sheetName];
          return {
            name: sheetName,
            data: XLSX.utils.sheet_to_html(worksheet),
          };
        });

        setSheets(sheetData);
        setLoading(false);
      } catch (error) {
        setLoading(false);
      }
    };
    reader.readAsArrayBuffer(file);
  }, [file]);

  if (!file) {
    return (
      <div className="placeholder">
        <FileExcelOutlined style={{ fontSize: 48, color: '#52c41a' }} />
        <p>请上传Excel文件进行预览</p>
      </div>
    );
  }

  return (
    <div className="office-container">
      {loading ? (
        <div className="loading-container">
          <Spin size="large" tip="正在处理Excel文件..." />
          <Progress percent={50} status="active" style={{ width: '80%', marginTop: 20 }} />
        </div>
      ) : sheets.length === 0 ? (
        <Alert message="无法解析Excel文件" type="error" showIcon />
      ) : (
        <div className="excel-preview">
          {sheets.length > 1 && (
            <div className="excel-sheets-tabs">
              {sheets.map((sheet, index) => (
                <Button
                  key={index}
                  type={activeSheet === index ? 'primary' : 'default'}
                  onClick={() => setActiveSheet(index)}
                  style={{ marginRight: 8 }}
                >
                  {sheet.name}
                </Button>
              ))}
            </div>
          )}

          <div
            className="excel-table-container"
            dangerouslySetInnerHTML={{ __html: sheets[activeSheet].data }}
          />
        </div>
      )}
    </div>
  );
};

const WordViewer = ({ file }) => {
  const [htmlContent, setHtmlContent] = useState('');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (!file) return;

    const reader = new FileReader();
    reader.onload = async (e) => {
      try {
        const arrayBuffer = e.target.result;
        const result = await mammoth.convertToHtml({ arrayBuffer });
        setHtmlContent(result.value);
      } catch (error) {
        setHtmlContent('<p>无法预览Word文档</p>');
      } finally {
        setLoading(false);
      }
    };
    reader.readAsArrayBuffer(file);
  }, [file]);

  if (!file) {
    return (
      <div className="placeholder">
        <FileWordOutlined style={{ fontSize: 48, color: '#1890ff' }} />
        <p>请上传Word文档进行预览</p>
      </div>
    );
  }

  return (
    <div className="office-container">
      {loading ? (
        <div className="loading-container">
          <Spin size="large" tip="正在转换Word文档..." />
          <Progress percent={70} status="active" style={{ width: '80%', marginTop: 20 }} />
        </div>
      ) : (
        <div className="word-preview" dangerouslySetInnerHTML={{ __html: htmlContent }} />
      )}
    </div>
  );
};
const PreviewCodal: React.FC<PreviewCodalProps> = (props) => {
  const { modal, fileUrl, fileType, file } = props;
  const [visible, setVisible] = modal;
  if (!visible) return null;

  return (
    <Codal
      title="文件预览"
      visible={visible}
      onCancel={() => {
        setVisible(false);
        URL.revokeObjectURL(fileUrl);
      }}
      width={1000}
    >
      {FilePreview(fileUrl, fileType, file)}
    </Codal>
  );
};
export { PreviewCodal };
