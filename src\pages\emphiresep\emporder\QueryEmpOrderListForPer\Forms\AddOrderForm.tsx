import React, { useState, useEffect } from 'react';
import Codal from '@/components/Codal';
import { Form, Button, Input, InputNumber, Table, Spin, Card } from 'antd';
import { EnumerateFields, EditeFormProps } from '@/components/CachedPage/EnumerateFields';
import { FormElement3, ColElementButton, RowElement } from '@/components/Forms/FormLayouts';
import { SubContractPop } from '@/components/StandardPop/SubContractForOrderPop';
import { BranchPop } from '@/components/StandardPop/BranchPop';
import { renderListToSelectors, mapToSelectors } from '@/components/Selectors';
import dict from '@/locales/zh-CN/locale';
import { msgErr, msgOk, resError } from '@/utils/methods/message';
import {
  Writable,
  useWritable,
  OnWriteFromChangeOptions,
  WritableInstance,
} from '@/components/Writable';
import { WritableColumnProps } from '@/utils/writable/types';
import { DateRange } from '@/components/DateRange4';
import { column3 } from '@/utils/forms/typography';
import { validateCard } from '@/utils/forms/validate';
import {
  yesNoDataProvider,
  pendingReasonList,
} from '@/utils/settings/emphiresep/emporder/QueryEmpOrderListForPer';
import { stdDateFormat, stdMonthFormatMoDash, toMoment } from '@/utils/methods/times';
import CheckboxItem, { RadioItem } from '@/components/EditeForm/Item/CheckboxItem';
import {
  calculateReceivableAmt,
  copyQuotationProperties,
  copyTemplateProperties,
  checkEValues,
  calInsurance,
  checkPValues,
  copyRatioProperties,
  toTaxSalesPrice,
  clearFeeTemplate,
} from '../../QueryEmpOrderListForGen/Forms/BizUtil';
import { AddOneTimeSsPop } from '../../QueryEmpOrderListForGen/Forms/AddOneTimeSsPop';
import { QueryRatioDropdown } from '../../QueryEmpOrderListForGen/Forms/QueryRatioDropdown';
import ConfigSsGroup from '../../QueryEmpOrderListForGen/Forms/ConfigSsGroup';
import { GeneralInputRenderOption } from '@/components/Writable/libs/GeneralInput';
import { ForceDeleteSsGroupPop } from './ForceDeleteSsGroupPop';
import {
  tableIndexKey,
  tableLineStateMap,
  tableLineState,
  tablePropsKey,
} from '@/utils/settings/forms';
import { addIndexKeyRhro } from '@/utils/methods/pagenation';
import { Calculator } from '@/utils/methods/calculator';
import { AsyncButton } from '@/components/Forms/Confirm';
import { AuthButtons } from '@/components/Forms/RenderButtons';
import { ReceivableFrequencySelector } from '@/components/Selectors/BaseDataSelectors';
import moment from 'moment';
import { serviceScopeDict } from '@/pages/emphiresep/sendorder/CustomerSubcontract/AddTransferAndSubcontract';
import {
  employeeDataCollectionList,
  contractSigningFormList,
  workingHoursSystemList,
  laborContractVersionList,
  bankCardSystemMaintenanceList,
  insuranceLocationList,
  stopPaymentMonthList,
  startPaymentMonthList,
  employeeReduReportingMethodList,
} from '@/utils/settings/emphiresep/sendorder/CustomerSubcontract';
import { yesornoList } from '@/utils/settings/emphiresep/emporder/queryClientOrderForAdd';

interface AddOrderFormProps {
  title: string;
  visible: boolean;
  hideHandle: CallableFunction;
  updateitem: POJO;
}

const service = API.emphiresep.employeeHireSep.updateEmpHireSepAndFee;
const service2 = { ...service, cacheKey: service.cacheKey };
const serviceGetDropdownList = API.emphiresep.employeeHireSep.getEmployeeHireSepDropdownList;
const serviceGetBillAndQuotationDropdownList =
  API.emphiresep.employeeHireSep.getBillAndQuotationDropdownList;
const serviceGetCitiesAndPersonCategories = API.emphiresep.combo.getCitiesAndPersonCategories;

const serviceSelectPersonCategoryByCityId = API.emphiresep.combo.selectPersonCategoryByCityId;
const serviceGetCombo = API.emphiresep.combo.getComboList;
const serviceGetReceivableFrequencyDropdownList =
  API.emphiresep.receivable.getReceivableFrequencyDropdownList;
const serviceLoadMinBase = API.emphiresep.employeeHireSep.getMinBase;
let oldCityId = '';
let oldPersonCategoryId = '';
let oldTotalFeeDt = '';

let qutationWriTable: WritableInstance;
let serviceFeeWriTable: WritableInstance;

const qutationService = API.crm.contractManage.getSelectedQuotation;
const AddOrderForm: React.FC<AddOrderFormProps> = (props) => {
  const { title, visible, hideHandle, updateitem } = props;
  if (!visible) return null;
  qutationWriTable = useWritable({
    service: { ...qutationService, cacheKey: service.cacheKey + 1 },
  });
  serviceFeeWriTable = useWritable({
    service: { ...qutationService, cacheKey: service.cacheKey + 2 },
  });
  const [form] = Form.useForm();
  const [formTransfer] = Form.useForm();
  const [formTransfer1] = Form.useForm();
  const [formContract] = Form.useForm();
  const [formWage] = Form.useForm();
  const [formInsurance] = Form.useForm();
  const wriTable = useWritable({ service });
  const wriTable2 = useWritable({ service: service2 });
  const [cityList, setCityList] = useState([]);
  const [onShowRatio, setOnShowRatio] = useState(false);
  const [loading, setLoading] = useState(false);
  const [onShowConfigSsGroup, setOnShowConfigSsGroup] = useState(false);
  const [ssGroupLineData, setSsGroupLineData] = useState({});
  const [configData, setConfigData] = useState({});
  const [personCategoryList, setPersonCategoryList] = useState([]);
  const [billTempltList, setBillTempltList] = useState([]);
  const [idCardTypeList, setIdCardTypeList] = useState([]);
  const [feeTempltList, setFeeTempltList] = useState([]);
  const [isIssuingSalary, setIsIssuingSalary] = useState('');
  const [groupList, setGroupList] = useState([] as any[]);
  const [forceDeleteList, setForceDeleteList] = useState([] as any[]);
  useEffect(() => {
    serviceGetDropdownList.requests().then((res) => {
      setIdCardTypeList(res.idCardTypeList);
    });
  }, []);
  useEffect(() => {
    if (updateitem.transferId) {
      API.emphiresep.transfer.queryTransferInfoById
        .request({ transferId: updateitem.transferId })
        .then((res) => {
          if (resError(res)) {
            return;
          }
          const { data } = res;
          setIsIssuingSalary(data?.isIssuingSalary);
          formTransfer.setFieldsValue(data);
          formTransfer1.setFieldsValue(data);
          formContract.setFieldsValue(data);
          formWage.setFieldsValue(data);
          formInsurance.setFieldsValue(data);
        });
      API.emphiresep.transfer.getTransferFeeList
        .request({ transferId: updateitem.transferId })
        .then((res) => {
          if (resError(res)) {
            return;
          }
          const { data } = res;
          qutationWriTable.setNewData(data?.serviceFeeList);
          serviceFeeWriTable.setNewData(data?.suppServiceFeeList);
        });
    }
  }, [updateitem]);
  useEffect(() => {
    if (updateitem.empHireSepId) {
      const { subcontract, ssGroupList, nonSsGroupList, ...rest } = updateitem;
      if (!oldTotalFeeDt && rest.empHireSepId) {
        oldTotalFeeDt = rest.totalFeeDt;
      }
      oldCityId = rest.cityId;
      if (subcontract && subcontract.subcontractId) {
        form.setFieldsValue(subcontract);
        getList(subcontract);
      }
      form.setFieldsValue(rest);
      wriTable.setNewData(ssGroupList);
      const _nonSsGroupList = nonSsGroupList.map((item: any) => {
        const nonSsObj = item;
        if (nonSsObj.quotationId) {
          nonSsObj[tablePropsKey] = { amount: { disabled: true } };
          if (nonSsObj.category == '3') {
            nonSsObj[tablePropsKey] = { amtNoTax: { disabled: true } };
          }
        } else {
          nonSsObj[tablePropsKey] = { amtNoTax: { disabled: true } };
        }
        return nonSsObj;
      });
      wriTable2.setNewData(_nonSsGroupList);
    }
  }, [updateitem]);
  const QutationColumns: WritableColumnProps<any>[] = [
    { title: '报价单编号', dataIndex: 'quotationCode', inputRender: 'string' },
    { title: '报价单名称', dataIndex: 'quotationName', inputRender: 'string' },
    { title: '售价', dataIndex: 'quotationTotalSalPrice', inputRender: 'number' },
  ];
  const transferServiceFormColumns: EditeFormProps[] = [
    {
      label: '服务员工类型',
      fieldName: 'employeeServiceType',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    {
      label: '服务内容',
      fieldName: 'serviceContent',
      inputProps: { disabled: true },
      inputRender: () => <CheckboxItem options={serviceScopeDict}></CheckboxItem>,
    },
  ];
  const transferServiceFormColumns1 = [
    {
      label: '员工资料收集',
      fieldName: 'employeeDataCollection',
      inputRender: () => {
        return mapToSelectors(employeeDataCollectionList);
      },
      inputProps: { disabled: true },
    },
    {
      label: '增员申报方式',
      fieldName: 'employeeAddReportingMethod',
      inputRender: () => {
        return mapToSelectors(employeeReduReportingMethodList);
      },
      inputProps: { disabled: true },
    },
    {
      label: '减员员申报方式',
      fieldName: 'employeeReduReportingMethod',
      inputRender: () => {
        return mapToSelectors(employeeReduReportingMethodList);
      },
      inputProps: { disabled: true },
    },
    {
      label: '在职业务申请方式',
      fieldName: 'inserviceBizAppMethod',
      inputRender: () => {
        return mapToSelectors(employeeReduReportingMethodList);
      },
      inputProps: { disabled: true },
    },
    {
      label: '补医保的收费和服务约定要求',
      fieldName: 'suppMedicalInsuranceAgree',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    {
      label: '客户其他服务约定',
      fieldName: 'customerOtherServiceContent',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
  ];
  const transferContractFormColumns: EditeFormProps[] = [
    {
      label: '合同开始时间及期限约定',
      fieldName: 'contractStartTimeDuration',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    {
      label: '合同签订形式',
      fieldName: 'contractSigningForm',
      inputRender: () => {
        return mapToSelectors(contractSigningFormList);
      },
      inputProps: { disabled: true },
    },
    {
      label: '工时制',
      fieldName: 'workingHoursSystem',
      inputRender: () => <CheckboxItem options={workingHoursSystemList}></CheckboxItem>,
      inputProps: { disabled: true },
    },
    {
      label: '劳动合同版本',
      fieldName: 'laborContractVersion',
      inputRender: () => {
        return mapToSelectors(laborContractVersionList);
      },
      inputProps: { disabled: true },
    },
    {
      label: '客户版合同说明',
      fieldName: 'customerVersionContractDesc',
      inputRender: 'string',
      colNumber: 1,
      inputProps: { disabled: true },
    },
  ];
  const transferWageFormColumns: EditeFormProps[] = [
    {
      label: '员工收取工资的银行要求',
      fieldName: 'employeesBankRequirements',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    {
      label: '银行卡系统维护规则',
      fieldName: 'bankCardSystemMaintenance',
      inputRender: () => {
        return mapToSelectors(bankCardSystemMaintenanceList);
      },
      inputProps: { disabled: true },
    },
  ];
  const transferInsuranceFormColumns: EditeFormProps[] = [
    {
      label: '起缴月',
      fieldName: 'startPaymentMonth',
      inputRender: () => {
        return mapToSelectors(startPaymentMonthList);
      },
      inputProps: { disabled: true },
    },
    {
      label: '起缴约定日',
      fieldName: 'startPaymentAgreementDate',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    {
      label: '停缴月',
      fieldName: 'stopPaymentMonth',
      inputRender: () => {
        return mapToSelectors(stopPaymentMonthList);
      },
      inputProps: { disabled: true },
    },
    {
      label: '停缴约定日',
      fieldName: 'stopPaymentAgreementDate',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    {
      label: '补缴约定',
      fieldName: 'suppPaymentAgreement',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    {
      label: '参保地',
      fieldName: 'insuranceLocation',
      inputRender: () => {
        return mapToSelectors(insuranceLocationList);
      },
      inputProps: { disabled: true },
    },
    {
      label: '社保公积金其他说明',
      fieldName: 'socialFundOtherNotes',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
  ];
  const formColumns: EditeFormProps[] = [
    {
      label: '唯一号',
      fieldName: 'employeeCode',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    {
      label: '雇员姓名',
      fieldName: 'employeeName',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    {
      label: '客户方编号',
      fieldName: 'custInternalNum',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    {
      label: '客户编号',
      fieldName: 'custCode',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    { label: '客户', fieldName: 'custName', inputRender: 'string', inputProps: { disabled: true } },
    {
      label: '证件类型',
      fieldName: 'idCardType',
      inputRender: () => {
        return renderListToSelectors(idCardTypeList, ['key', 'shortName']);
      },
      inputProps: { disabled: true },
    },

    {
      label: '证件号码',
      fieldName: 'idCardNum',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    {
      label: '电话',
      fieldName: 'contactTel1',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    {
      label: '手机',
      fieldName: 'contactTel2',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    {
      label: '入职日期',
      fieldName: 'hireDt',
      inputRender: 'string',
      inputProps: {
        disabled: true,
      },
    },
    {
      label: '申报入职时间',
      fieldName: 'rptHireDt',
      inputRender: 'string',
      inputProps: {
        disabled: true,
      },
    },
    {
      label: '申报入职人',
      fieldName: 'rptHireByName',
      inputRender: 'string',
      inputProps: {
        disabled: true,
      },
    },
    {
      label: '小合同名称',
      fieldName: 'subcontractName',
      inputRender: () => {
        return (
          <SubContractPop
            rowValue="subcontractId-empType-custId-assignerProviderId-assigneeProviderId-isSameInsur-assignerCsId-assigneeCsId-providerType-billTempltId-feeTempltId-feeTemplt-monthInAdvance-frequency-feeMonth-liabilityCs-isIndependent-subcontractName"
            disabled
            noClear
          />
        );
      },
    },
    {
      label: '城市',
      fieldName: 'cityId',
      inputRender: () => {
        return renderListToSelectors(cityList, ['cityId', 'cityName'], { disabled: true });
      },
    },
    {
      label: '人员分类',
      fieldName: 'personCategoryId',
      inputRender: () => {
        return renderListToSelectors(personCategoryList, ['personCategoryId', 'categoryName'], {
          onChange: changePersonCategory,
        });
      },
    },
    {
      label: '派单类型',
      fieldName: 'assignmentType',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    {
      label: '派单方',
      fieldName: 'assignerProvider',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    {
      label: '接单方',
      fieldName: 'assigneeProvider',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    {
      label: '员工社保参与地',
      fieldName: 'ssParticipateLocation',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    {
      label: '劳动关系单位',
      fieldName: 'laborRelationUnit',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    {
      label: '是否有社保卡',
      fieldName: 'isThereSsCard',
      inputRender: () => {
        return mapToSelectors(yesNoDataProvider, { disabled: true });
      },
    },
    {
      label: '是否有统筹医疗',
      fieldName: 'isThereACoordinateHealth',
      inputRender: () => {
        return mapToSelectors(yesNoDataProvider, { disabled: true });
      },
    },
    {
      label: '订单状态',
      fieldName: 'addConfirmStatusName',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    {
      label: '是否需要实做',
      fieldName: 'isAddProcess',
      inputRender: () => mapToSelectors(yesornoList),
      rules: [
        {
          required: true,
          message: '请选择是否需要实做',
        },
      ],
    },
    {
      label: '是否通知员工交资料',
      fieldName: 'isHireCallName',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    {
      label: '是否存档 ',
      fieldName: 'isArchiveName',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    {
      label: '离职日期',
      fieldName: 'sepDt',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    {
      label: '申报离职日期',
      fieldName: 'rptSepDt',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    {
      label: '离职原因',
      fieldName: 'sepReasonName',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    {
      label: '申报离职人',
      fieldName: 'rptSepByName',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    {
      label: '离职备注',
      fieldName: 'sepRemark',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    {
      label: '总收费开始时间',
      fieldName: 'totalFeeDt',
      inputRender: 'month',
      inputProps: {
        format: stdMonthFormatMoDash,
        onChange: () => getComboInfoByCondition(),
      },
    },
    {
      label: '入职备注',
      fieldName: 'hireRemark',
      inputRender: 'text',
      inputProps: { disabled: true },
    },
    {
      label: '入职过程',
      fieldName: 'addConfirmPro',
      inputRender: 'text',
      inputProps: { disabled: true },
    },
    {
      label: '缴费实体',
      fieldName: 'custPayEntityName',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    {
      label: '是否集中一地投保',
      fieldName: 'isSameInsurName',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    {
      label: '是否增强型代理',
      fieldName: 'enhancedAgentName',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    {
      label: '开户人姓名',
      fieldName: 'accountEmployeeName',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    {
      label: '银行名称',
      fieldName: 'openBankName',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    {
      label: '银行卡号',
      fieldName: 'bankAcct',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    {
      label: '银行卡更新时间',
      fieldName: 'bankCardUpdateDt',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    {
      label: '银行卡更新人',
      fieldName: 'bankCardUpdateBy',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    {
      label: '派遣单位名称',
      fieldName: 'payerName',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
    {
      label: '统一社会信用码',
      fieldName: 'taxpayerIdentifier',
      inputRender: 'string',
      inputProps: { disabled: true },
    },
  ];
  const columns: WritableColumnProps<defs.emphiresep.EmployeeFee>[] = [
    { title: '产品名称', dataIndex: 'productName' },
    { title: '社保公积金', dataIndex: 'ssGroupName' },

    {
      title: '收费起始月',
      dataIndex: 'chargeStartDate',
      inputRender: 'month',
      inputProps: { format: stdMonthFormatMoDash },
      onGridChange: (value: any, options: GeneralInputRenderOption<any>) => {
        const { chargeStartDate } = value;
        const { serial } = options;
        chargeStartDate_changeHandler(chargeStartDate, serial);
      },
      rules: [{ required: true, message: '请输入收费起始月' }],
    },
    {
      title: '收费截至月',
      dataIndex: 'chargeEndDate',
      inputProps: { format: stdMonthFormatMoDash },
      inputRender: 'month',
    },
    {
      title: '账单起始月',
      dataIndex: 'billStartMonth',
      inputRender: 'month',
      inputProps: {
        format: stdMonthFormatMoDash,
        disabledDate: (current: string | undefined) => {
          const nowDate = moment(new Date()).add(3, 'months').format(stdMonthFormatMoDash);
          const tooLate = current && moment(current).diff(nowDate, 'months') > 0;
          return tooLate;
        },
      },
      onGridChange: (value: any, options: GeneralInputRenderOption<any>) => {
        const { billStartMonth } = value;
        const { serial } = options;
        billStartMonth_changeHandler(billStartMonth, serial);
      },
      rules:
        updateitem.assigneeCsId == updateitem.assignerCsId
          ? [
              {
                required: true,
                message: '账单起始月',
              },
            ]
          : [],
    },
    {
      title: '企业基数',
      dataIndex: 'eBase',
      inputRender: (options: GeneralInputRenderOption<any>) => {
        return <InputNumber onBlur={(e) => changeEBase(e.target.value, options)} />;
      },
      // rules: [{ required: true, message: '请输入企业基数' }],
    },
    {
      title: '个人基数',
      dataIndex: 'pBase',
      inputRender: (options: GeneralInputRenderOption<any>) => {
        return <InputNumber onBlur={(e) => changePBase(e.target.value, options)} />;
      },
      // rules: [{ required: true, message: '请输入个人基数' }],
    },
    {
      title: '金额',
      dataIndex: 'amount',
      rules: [{ required: true, message: '请输入金额' }],
    },
    {
      title: '企业金额',
      dataIndex: 'eAmt',
      inputRender: (options: GeneralInputRenderOption<any>) => {
        return <InputNumber onBlur={(e) => changeEAmt(e.target.value, options)} />;
      },
      rules: [{ required: true, message: '请输入企业金额' }],
    },
    {
      title: '个人金额',
      dataIndex: 'pAmt',
      inputRender: (options: GeneralInputRenderOption<any>) => {
        return <InputNumber onBlur={(e) => changePAmt(e.target.value, options)} />;
      },
      rules: [{ required: true, message: '请输入个人金额' }],
    },
    { title: '比例名称', dataIndex: 'productRatioName' },
    {
      title: '企业比例',
      dataIndex: 'eRatio',
      inputRender: (options: GeneralInputRenderOption<any>) => {
        return <Input readOnly onClick={() => taggleOnShowRatio(options)} />;
      },
    },
    {
      title: '个人比例',
      dataIndex: 'pRatio',
      inputRender: (options: GeneralInputRenderOption<any>) => {
        return <Input readOnly onClick={() => taggleOnShowRatio(options)} />;
      },
    },
    { title: '企业附加', dataIndex: 'eAdditionalAmt' },
    { title: '个人附加', dataIndex: 'pAdditionalAmt' },
    {
      title: '账单模板',
      dataIndex: 'eBillTempltId',
      inputRender: (options: GeneralInputRenderOption<any>) => {
        return renderListToSelectors(billTempltList, ['key', 'shortName'], {
          onChange: (eBillTempltId) => {
            // 统一改变所有社保公积金大类账单模板并清空收费模板
            const { serial } = options;
            if (
              eBillTempltId &&
              options.record.category &&
              ['1', '2'].includes(options.record.category)
            ) {
              const { visible: list } = wriTable.getList();
              const _list: any[] = [];
              const newList = list.map((item: any) => {
                if (item.category && ['1', '2'].includes(item.category)) {
                  const _item = {
                    ...item,
                    ...clearFeeTemplate(item),
                    eBillTempltId,
                  };
                  _list.push(_item);
                  return _item;
                }
                if (serial == item[tableIndexKey])
                  _list.push({
                    ...item,
                    ...clearFeeTemplate(item),
                    eBillTempltId,
                  });
                return item;
              });
              wriTable.setData({ list: newList, pagination: {} });
              selectRow(_list);
            } else {
              wriTable.setFieldsValue(options.serial, {
                ...clearFeeTemplate(options.record),
                eBillTempltId,
              });
            }
          },
        });
      },
      rules: [{ required: true, message: '请输入账单模板' }],
    },
    {
      title: '收费模板',
      dataIndex: 'eFeeTempltId',
      inputRender: (options: GeneralInputRenderOption<any>) => {
        return (
          <ReceivableFrequencySelector
            params={{ receivableTemplateId: options.record.eBillTempltId || '' }}
            paramsData={{
              receivableTemplateId: options.record.eBillTempltId || '',
            }}
            keyMap={{
              eFeeTempltId: 'frequencyId',
              eMonthInAdvance: 'monthInAdvance',
              eFrequency: 'frequency',
              eFeeMonth: 'feeMonth',
              pFeeTempltId: 'frequencyId',
              pMonthInAdvance: 'monthInAdvance',
              pFrequency: 'frequency',
              pFeeMonth: 'feeMonth',
            }}
          />
        );
      },
      onGridChange: (
        value: Partial<defs.emphiresep.EmployeeFee>,
        options: GeneralInputRenderOption<any>,
      ) => {
        const { eFeeTempltId } = value;
        if (eFeeTempltId) {
          changeEFeeTempltSS(value, options);
        }
      },
      rules: [{ required: true, message: '请输入收费模板' }],
    },
    { title: '备注', dataIndex: 'remark', inputRender: 'text' },
  ];
  const columns2: WritableColumnProps<defs.emphiresep.EmployeeFee>[] = [
    { title: '产品名称', dataIndex: 'productName' },
    {
      title: '收费起始月',
      dataIndex: 'chargeStartDate',
      // inputRender: 'month',
      // inputProps: { format: stdMonthFormatMoDash },
      // rules: [{ required: true, message: '请输入收费起始月' }],
      // onGridChange: (value: any, options: GeneralInputRenderOption<any>) => {
      //   const { chargeStartDate } = value;
      //   nonSsChargeStartDate_changeHandler(chargeStartDate, options);
      // },
    },
    {
      title: '收费截至月',
      dataIndex: 'chargeEndDate',
    },
    {
      title: '账单起始月',
      dataIndex: 'billStartMonth',
      inputProps: {
        format: stdMonthFormatMoDash,
        disabledDate: (current: string | undefined) => {
          const nowDate = moment(new Date()).add(3, 'months').format(stdMonthFormatMoDash);
          const tooLate = current && moment(current).diff(nowDate, 'months') > 0;
          return tooLate;
        },
      },
      inputRender: 'month',
      onGridChange: (value: any) => {
        const { billStartMonth } = value;
        nonSsBillStartMonth_changeHandler(billStartMonth);
      },
      rules:
        updateitem.assigneeCsId == updateitem.assignerCsId
          ? [
              {
                required: true,
                message: '账单起始月',
              },
            ]
          : [],
    },
    {
      title: '金额',
      dataIndex: 'amount',
      rules: [{ required: true, message: '请输入金额' }],
    },
    {
      title: '金额(不含税)',
      dataIndex: 'amtNoTax',
      inputRender: (options: GeneralInputRenderOption<any>) => {
        return <InputNumber onBlur={(e) => changeAmtNoTax(e.target.value, options)} />;
      },
    },
    { title: '增值税率', dataIndex: 'vatr' },
    {
      title: '附加税率',
      dataIndex: 'atr',
    },
    {
      title: '增值税',
      dataIndex: 'vat',
    },
    {
      title: '账单模板',
      dataIndex: 'eBillTempltId',
      inputRender: () => {
        return renderListToSelectors(billTempltList, ['key', 'shortName'], { disabled: true });
      },
      // rules: [{ required: true, message: '请输入账单模板' }],
    },
    {
      title: '收费模板',
      dataIndex: 'eFeeTemplt',
      // inputRender: (options: GeneralInputRenderOption<any>) => {
      //   return renderListToSelectors(feeTempltList, ['frequencyId', 'frequencyName'], {
      //     onClick: () => getFeeTempltList(options),
      //   });
      // },
      // onGridChange: (value: any, options: GeneralInputRenderOption<any>) => {
      //   const { eFeeTemplt } = value;
      //   if (eFeeTemplt) {
      //     changeEFeeTemplt(eFeeTemplt, options);
      //   }
      // },
      // rules: [{ required: true, message: '请输入收费模板' }],
    },
    // { title: '应收几个月', dataIndex: 'receivableMonth' },
    // { title: '应收金额', dataIndex: 'receivableAmt' },
    {
      title: '实收金额',
      dataIndex: 'verifyAmt',
    },
    {
      title: '实收金额(不含税)',
      dataIndex: 'verifyAmtNoTax',
      // inputRender: (options: GeneralInputRenderOption<any>) => {
      //   return <InputNumber onBlur={(e) => changeVerifyAmtNoTax(e.target.value, options)} />;
      // },
    },
    {
      title: '实收金额增值税',
      dataIndex: 'verifyAmtVat',
    },
    { title: '备注', dataIndex: 'remark' },
  ];
  const formColumns2: EditeFormProps[] = [
    {
      label: '挂起原因',
      fieldName: 'pendingReason',
      inputRender: () => {
        return mapToSelectors(pendingReasonList);
      },
    },
    {
      label: '备注',
      fieldName: 'confirmRemark',
      inputRender: 'text',
    },
  ];

  const getList = (data: any) => {
    serviceGetBillAndQuotationDropdownList
      .requests(
        {
          custId: data.custId,
          contractId: data.contractId,
        } as any,
        {
          params: {
            custId: data.custId,
            contractId: data.contractId,
          },
        },
      )
      .then((res) => {
        setBillTempltList(res.billTempltList);
      });
    serviceGetCitiesAndPersonCategories
      .requests(
        { subcontractId: data.subcontractId, cityId: data.cityId },
        { params: { subcontractId: data.subcontractId, cityId: data.cityId } },
      )
      .then((res) => {
        setCityList(res.cityList);
        setPersonCategoryList(res.personCategoryList);
      });
  };

  // 改变人员分类,根据人员类别和城市获取套餐信息
  const changePersonCategory = () => {
    getComboInfoByCondition();
  };

  // 获取套餐列表
  const getComboInfoByCondition = () => {
    setLoading(true);
    const data = { ...updateitem, ...form.getFieldsValue() };
    if (!data.personCategoryId) {
      setLoading(false);
      return msgErr(dict.selectPersonCategory);
    }
    if (!data.cityId) {
      setLoading(false);
      return msgErr(dict.selectCity);
    }
    if (!data.totalFeeDt) {
      setLoading(false);
      return msgErr(dict.inputTotalFeeDate);
    }
    if (
      data.cityId != oldCityId ||
      data.personCategoryId != oldPersonCategoryId ||
      data.totalFeeDt != oldTotalFeeDt
    ) {
      oldCityId = data.cityId;
      oldPersonCategoryId = data.personCategoryId;
      oldTotalFeeDt = data.totalFeeDt;
    }
    serviceGetCombo
      .requests({ ...updateitem.subcontract, ...data })
      .then((res) => {
        const list = res.map((item: any) => {
          const newItem = { ...item, ...copyTemplateProperties(item, data) };
          const eAmt = getEAmt(newItem);
          const pAmt = getpAmt(newItem);
          const amount = Calculator.add(Number(eAmt), Number(pAmt));
          return { ...newItem, eAmt, pAmt, amount };
        });
        wriTable.resetFields();
        wriTable.setData({ list: addIndexKeyRhro(tableIndexKey, list) });
        setLoading(false);
      })
      .catch(() => {
        setLoading(false);
        wriTable.resetFields();
        wriTable.setData({ list: [] });
      });
  };

  const getFeeTempltList = (options: GeneralInputRenderOption<any>) => {
    const { record } = options;
    serviceGetReceivableFrequencyDropdownList
      .requests(
        {
          receivableTemplateId: record.eBillTempltId || '',
        },
        {
          params: {
            receivableTemplateId: record.eBillTempltId || '',
          },
        },
      )
      .then((res) => {
        setFeeTempltList(res.list || []);
      });
  };

  // 改变金额不含税
  const changeAmtNoTax = (value: string, options: GeneralInputRenderOption<any>) => {
    const { serial, record } = options;
    if (record.quotationId && value && record.vatr && record.atr) {
      const result = toTaxSalesPrice(value, record.vatr, record.atr);
      const amount = result['result1'];
      const vat = result['result2'];
      wriTable2.setFieldsValue(serial, { amtNoTax: value, amount, vat });
    }
  };

  // 选择非社保公积金账单起始月
  const nonSsBillStartMonth_changeHandler = (value: string) => {
    if (value) {
      const { visible: list } = wriTable2.getList();
      const newList = list.map((item: any) => {
        return { ...item, billStartMonth: item.billStartMonth ? item.billStartMonth : value };
      });
      wriTable2.setData({ list: newList });
    }
  };

  const renderSummary = (pageData: POJO) => {
    let totalAmount = 0;
    pageData.forEach(({ amount, ...row }: POJO) => {
      if (row[tableLineState] !== tableLineStateMap.delete && amount) {
        totalAmount = Calculator.add(totalAmount, Number(amount) || 0);
      }
    });
    return (
      <Table.Summary.Row>
        <Table.Summary.Cell index={0} colSpan={2}>
          合计
        </Table.Summary.Cell>
        <Table.Summary.Cell index={2} />
        <Table.Summary.Cell index={3} />
        <Table.Summary.Cell index={4} />
        <Table.Summary.Cell index={5} />
        <Table.Summary.Cell index={6} />
        <Table.Summary.Cell index={7}>{totalAmount}</Table.Summary.Cell>
      </Table.Summary.Row>
    );
  };

  // 选择社保公积金组一次性
  const selectOneTimeSsGroupChangeFunction = (selectData: POJO) => {
    const { visible: list } = wriTable.getList();
    for (let index = 0; index < list.length; index++) {
      const element: any = list[index];
      if (element.productId == selectData.productId && element.ssGroupId == selectData.ssGroupId) {
        return msgErr(dict.socialSecurityPovidentFundDuplicated);
      }
    }
    let employeeFee: POJO = {};
    employeeFee.ssGroupId = selectData.ssGroupId;
    employeeFee.ssGroupName = selectData.ssGroupName;
    employeeFee.productId = selectData.productId;
    employeeFee.productName = selectData.productName;
    employeeFee.isOneTimePay = selectData.isOneTimePay;
    employeeFee.category = selectData.category;
    // employeeFee.eReadOnly = true;
    // employeeFee.pReadOnly = true;
    employeeFee = copyTemplateProperties(employeeFee, { ...updateitem, ...form.getFieldsValue() });
    wriTable.addRows(employeeFee, {
      props: { eRatio: { disabled: true }, pRatio: { disabled: true } },
    });
  };

  // 选择社保公积金的收费模板
  const changeEFeeTempltSS = (value: POJO, options: GeneralInputRenderOption<any>) => {
    const { serial } = options;
    const row: any = wriTable.getRow(serial);
    const newRow = {
      ...row,
      ...value,
    };
    wriTable.setFieldsValue(serial, newRow);
  };

  // 选择社保公积金组的收费起始月
  const chargeStartDate_changeHandler = (value: any, serial: number) => {
    if (value) {
      const { visible: list } = wriTable.getList();
      const _list: any[] = [];
      const newList = list.map((item: any) => {
        const _item = {
          ...item,
          chargeStartDate: item.chargeStartDate ? item.chargeStartDate : value,
        };
        if (!item.chargeStartDate) _list.push(_item);
        if (serial == item[tableIndexKey]) _list.push(_item);
        return _item;
      });
      wriTable.setData({ list: newList, pagination: {} });
      selectRow(_list);
    }
  };
  // 选择社保公积金组的账单起始月
  const billStartMonth_changeHandler = (value: any, serial: number) => {
    if (value) {
      const { visible: list } = wriTable.getList();
      const _list: any[] = [];
      const newList = list.map((item: any) => {
        const _item = {
          ...item,
          billStartMonth: item.billStartMonth ? item.billStartMonth : value,
        };
        if (!item.billStartMonth) _list.push(_item);
        if (serial == item[tableIndexKey]) _list.push(_item);
        return _item;
      });
      wriTable.setData({ list: newList, pagination: {} });
      selectRow(_list);
    }
  };

  // 输入企业基数
  const changeEBase = (eBase: number, options: GeneralInputRenderOption<any>) => {
    setLoading(true);
    const { record } = options;
    const { visible: ssGroupList } = wriTable.getList();
    const _selectedRows: any[] = [];
    const newList = ssGroupList.map((employeeFee: any) => {
      if (
        (Number(employeeFee.category) < 3 &&
          (!employeeFee.isOneTimePay || employeeFee.isOneTimePay == '0') &&
          ((!employeeFee.eBase && employeeFee.eBase !== 0) ||
            (record.ssGroupId == employeeFee.ssGroupId &&
              record.baseBindingLevel &&
              record.baseBindingLevel == employeeFee.baseBindingLevel &&
              record.chargeStartDate == employeeFee.chargeStartDate))) ||
        options.serial === employeeFee[tableIndexKey]
      ) {
        const newEmployeeFee = { ...employeeFee, eBase, pBase: employeeFee.pBase || eBase };
        const eAmt = getEAmt(newEmployeeFee);
        const pAmt = employeeFee.pBase ? newEmployeeFee.pAmt : getpAmt(newEmployeeFee);
        _selectedRows.push({
          ...newEmployeeFee,
          eAmt,
          pAmt,
          amount: Calculator.add(Number(eAmt), Number(pAmt)),
        });
        return {
          ...newEmployeeFee,
          eAmt,
          pAmt,
          amount: Calculator.add(Number(eAmt), Number(pAmt)),
        };
      }
      return employeeFee;
    });
    selectRow(_selectedRows);
    wriTable.setData({ list: [...newList] });
    setLoading(false);
  };

  const getEAmt = (newEmployeeFee: any) => {
    let eAmt = 0;
    if (checkEValues(newEmployeeFee)) {
      eAmt = calInsurance(
        Number(newEmployeeFee.eBase),
        Number(newEmployeeFee.eRatio),
        Number(newEmployeeFee.eAdditionalAmt),
        Number(newEmployeeFee.ePrecision),
        Number(newEmployeeFee.eCalculationMethod),
        Number(newEmployeeFee.eLateFee || 0),
        Number(newEmployeeFee.payFrequency),
        Number(newEmployeeFee.calculationOrder),
      );
    }
    return eAmt;
  };

  // 输入个人基数
  const changePBase = (pBase: number, options: GeneralInputRenderOption<any>) => {
    setLoading(true);
    const { record } = options;
    const { visible: ssGroupList } = wriTable.getList();
    const _selectedRows: any[] = [];
    const newList = ssGroupList.map((employeeFee: any) => {
      if (
        (Number(employeeFee.category) < 3 &&
          (!employeeFee.isOneTimePay || employeeFee.isOneTimePay == '0') &&
          ((!employeeFee.pBase && employeeFee.pBase !== 0) ||
            (record.ssGroupId == employeeFee.ssGroupId &&
              record.baseBindingLevel &&
              record.baseBindingLevel == employeeFee.baseBindingLevel &&
              record.chargeStartDate == employeeFee.chargeStartDate))) ||
        options.serial === employeeFee[tableIndexKey]
      ) {
        const newEmployeeFee = { ...employeeFee, pBase, eBase: employeeFee.eBase || pBase };
        const eAmt = employeeFee.eBase ? newEmployeeFee.eAmt : getEAmt(newEmployeeFee);
        const pAmt = getpAmt(newEmployeeFee);
        _selectedRows.push({
          ...newEmployeeFee,
          pAmt,
          eAmt,
          amount: Calculator.add(Number(eAmt), Number(pAmt)),
        });
        return {
          ...newEmployeeFee,
          pAmt,
          eAmt,
          amount: Calculator.add(Number(eAmt), Number(pAmt)),
        };
      }
      return employeeFee;
    });
    selectRow(_selectedRows);
    wriTable.setData({ list: [...newList] });
    setLoading(false);
  };

  const getpAmt = (newEmployeeFee: any) => {
    let pAmt = 0;
    if (checkPValues(newEmployeeFee)) {
      pAmt = calInsurance(
        Number(newEmployeeFee.pBase),
        Number(newEmployeeFee.pRatio),
        Number(newEmployeeFee.pAdditionalAmt),
        Number(newEmployeeFee.pPrecision),
        Number(newEmployeeFee.pCalculationMethod),
        Number(newEmployeeFee.pLateFee || 0),
        Number(newEmployeeFee.payFrequency),
        Number(newEmployeeFee.calculationOrder),
      );
    }
    return pAmt;
  };

  // 改变基数联动后选中行
  const selectRow = (rows: any[]) => {
    const newRows = rows.filter((c) => {
      if (wriTable.selectedRows.findIndex((item) => item[tableIndexKey] === c[tableIndexKey]) < 0) {
        return true;
      }
      return false;
    });
    wriTable.setSelectedRows([...wriTable.selectedRows, ...newRows]);
  };

  // 改变企业金额
  const changeEAmt = (eAmt: number, options: GeneralInputRenderOption<any>) => {
    const { serial } = options;
    const { visible: ssGroupList } = wriTable.getList();
    ssGroupList.forEach((item: any) => {
      if (serial === item[tableIndexKey])
        wriTable.setFieldsValue(serial, {
          ...item,
          amount: Calculator.add(Number(String(eAmt).replaceAll(' ', '')), Number(item.pAmt) || 0),
        });
    });
  };

  // 改变个人金额
  const changePAmt = (pAmt: number, options: GeneralInputRenderOption<any>) => {
    const { serial } = options;
    const { visible: ssGroupList } = wriTable.getList();
    ssGroupList.forEach((item: any) => {
      if (serial === item[tableIndexKey])
        wriTable.setFieldsValue(serial, {
          ...item,
          amount: Calculator.add(Number(String(pAmt).replaceAll(' ', '')), Number(item.eAmt) || 0),
        });
    });
  };

  // 选择比例
  const taggleOnShowRatio = (options?: GeneralInputRenderOption<any>) => {
    if (!onShowRatio) {
      const { serial } = options!;
      const data = wriTable.getRow(serial);
      setSsGroupLineData(data);
      setOnShowRatio(!onShowRatio);
      return;
    }
    setOnShowRatio(!onShowRatio);
    setSsGroupLineData({});
  };

  const chooseRatio = (row: any) => {
    const newRow = copyRatioProperties(ssGroupLineData, row);
    const eAmt = getEAmt(newRow);
    const pAmt = getpAmt(newRow);
    wriTable.updateRows({
      ...newRow,
      pAmt,
      eAmt,
      amount: Calculator.add(Number(eAmt), Number(pAmt)),
    });
    if (wriTable.selectedRows.findIndex((item) => item[tableIndexKey] === row[tableIndexKey]) < 0) {
      wriTable.setSelectedRows([
        ...wriTable.selectedRows,
        {
          ...newRow,
          pAmt,
          eAmt,
          amount: Calculator.add(Number(eAmt), Number(pAmt)),
        },
      ]);
    }
    taggleOnShowRatio();
  };

  const renderSummarySs = (pageData: POJO) => {
    let totalAmount = 0;
    let totalAmount2 = 0;
    let totalAmount3 = 0;
    pageData.forEach(({ amount, eAmt, pAmt, ...row }: POJO) => {
      if (row[tableLineState] !== tableLineStateMap.delete) {
        totalAmount = Calculator.add(totalAmount, Number(amount) || 0);
        totalAmount2 = Calculator.add(totalAmount2, Number(eAmt) || 0);
        totalAmount3 = Calculator.add(totalAmount3, Number(pAmt) || 0);
      }
    });
    return (
      <Table.Summary.Row>
        <Table.Summary.Cell index={0} colSpan={2}>
          合计
        </Table.Summary.Cell>
        <Table.Summary.Cell index={2} />
        <Table.Summary.Cell index={3} />
        <Table.Summary.Cell index={4} />
        <Table.Summary.Cell index={5} />
        <Table.Summary.Cell index={6} />
        <Table.Summary.Cell index={7} />
        <Table.Summary.Cell index={8} />
        <Table.Summary.Cell index={9} />
        <Table.Summary.Cell index={10}>{totalAmount}</Table.Summary.Cell>
        <Table.Summary.Cell index={11}>{totalAmount2}</Table.Summary.Cell>
        <Table.Summary.Cell index={12}>{totalAmount3}</Table.Summary.Cell>
      </Table.Summary.Row>
    );
  };

  // 设定社保组
  const taggleOnShowConfigSsGroup = () => {
    if (!onShowConfigSsGroup) {
      const employeeHireSep = { ...updateitem, ...form.getFieldsValue() };
      const { visible: ssGroupList } = wriTable.getList();
      if (employeeHireSep.subcontractId && employeeHireSep.custId) {
        const newGroupList = groupList.map((socialSecurityGroup) => {
          const newItem = socialSecurityGroup;
          newItem.socialSecurityGroupProductList =
            socialSecurityGroup.socialSecurityGroupProductList.map((subObj: any) => {
              let flag = false;
              for (let index = 0; index < ssGroupList.length; index++) {
                const employeeFee: any = ssGroupList[index];
                if (
                  employeeFee.productId == subObj.productId &&
                  employeeFee.ssGroupId == subObj.ssGroupId &&
                  !employeeFee.ssWelfarePkgId
                ) {
                  flag = true;
                  break;
                }
              }
              subObj.clientSelected = flag;
              return subObj;
            });
          return newItem;
        });
        setConfigData(employeeHireSep);
        setGroupList(newGroupList);
        setOnShowConfigSsGroup(!onShowConfigSsGroup);
      } else {
        return msgErr(dict.chooseSubcontract);
      }
      return;
    }
    setConfigData({});
    setOnShowConfigSsGroup(!onShowConfigSsGroup);
  };

  const submitConfigSsGroup = (list: any[]) => {
    setLoading(true);
    const tempList: any[] = [];
    const employeeHireSep = { ...updateitem, ...form.getFieldsValue() };
    const { visible: ssGroupList } = wriTable.getList();
    setGroupList(list);
    list.forEach((item) => {
      item.socialSecurityGroupProductList.forEach((item: any) => {
        if (item.clientSelected) tempList.push(item);
      });
    });
    const rows = [];
    for (let index = 0; index < tempList.length; index++) {
      const empFee = tempList[index];
      const row = copyTemplateProperties(empFee, employeeHireSep);
      rows.push({
        ...row,
        [tableIndexKey]: ssGroupList.length + 1 + index,
        [tableLineState]: tableLineStateMap.add,
      });
    }
    // if (rows.length !== 0) wriTable.addRows(rows);
    let count: number;
    let i: number = ssGroupList.length;
    const deleteRowKeys: string[] = [];
    while (--i > -1) {
      count = 0;
      const employeeFee: any = ssGroupList[i];
      if (!employeeFee.empFeeId && !employeeFee.ssWelfarePkgId && employeeFee.isOneTimePay == '0') {
        tempList.forEach((tempGroupProduct) => {
          if (
            tempGroupProduct.productId == employeeFee.productId &&
            tempGroupProduct.ssGroupId == employeeFee.ssGroupId
          )
            count++;
        });
        if (count == 0) {
          deleteRowKeys.push(employeeFee[tableIndexKey]);
          // wriTable.deleteRows(employeeFee);
        }
      }
    }
    const newList = ssGroupList.filter((item: any) => !deleteRowKeys.includes(item[tableIndexKey]));
    wriTable.resetFields();
    wriTable.setData({ list: [...newList, ...rows], pagination: {} });
    setOnShowConfigSsGroup(!onShowConfigSsGroup);
    setLoading(false);
  };

  // 删除
  const delete_clickHandler = () => {
    for (let index = 0; index < wriTable.selectedRows.length; index++) {
      const element = wriTable.selectedRows[index];
      if (element.empFeeId || element.empFeeHisId) {
        msgErr(dict.ifDeleteSsInfoPleaseSetAmountToZero);
        return;
      }
    }
    wriTable.deleteRows();
  };

  // 载入最低基数
  const loadMinBase_clickHandler = async () => {
    setLoading(true);
    const { selected: ssGroupList } = wriTable.getList();
    const newList = ssGroupList.map(async (item: any) => {
      if (
        !item.chargeStartDate ||
        (!item.pRatio && item.pRatio !== 0) ||
        (!item.eRatio && item.eRatio !== 0)
      )
        return item;
      try {
        const res = await serviceLoadMinBase.requests(item);
        const newItem = { ...item, eBase: res.eMinBase, pBase: res.pMinBase };
        const eAmt = getEAmt(newItem);
        const pAmt = getpAmt(newItem);
        const amount = Calculator.add(Number(eAmt), Number(pAmt));
        return { ...newItem, eAmt, pAmt, amount };
      } catch (error) {
        setLoading(false);
        return item;
      }
    });
    const list = await Promise.all(newList);
    wriTable.updateRows(list);
    setLoading(false);
  };

  // 强制删除
  const forceDelete_clickHandler = (ssGroupId: string) => {
    const { visible: ssGroupList } = wriTable.getList();
    const _forceDeleteList: any[] = forceDeleteList;
    const newList: any[] = [];
    for (let index = 0; index < ssGroupList.length; index++) {
      const item: any = ssGroupList[index];
      if (item.ssGroupId === ssGroupId && !item.empFeeId) return msgErr('无法进行强制删除');
      if (item.ssGroupId !== ssGroupId) {
        newList.push(item);
      } else {
        _forceDeleteList.push(item.empFeeId);
      }
    }
    setForceDeleteList(_forceDeleteList);
    wriTable.setNewData(newList);
  };

  const oKhandle = async (operation: string) => {
    await form.validateFields().then(async (_employeeHireSep) => {
      const employeeHireSep = { ...updateitem, ..._employeeHireSep };
      // if (wriTable.selectedRows.length == 0 && wriTable2.selectedRows.length == 0) {
      //   return msgErr(dict.noDataSelected);
      // }
      const { visible: ssGroupList } = await wriTable.validateFields({ validateSelected: true });
      const { visible: nonSsGroupList } = await wriTable2.validateFields({
        validateSelected: true,
      });
      const data = {
        employeeHireSep,
        employeeBaseInfo: { ...employeeHireSep, isArchive: undefined },
      } as defs.emphiresep.EmployeeHireSepVO;
      //只校验社保公积金类产品的比例
      const monthArray = [];
      const maxObj = {}; //存放最大时间段
      employeeHireSep.empFeeIdArray =
        forceDeleteList && forceDeleteList.length > 0 ? String(forceDeleteList) : '';
      const ssGroupListKeys = wriTable.selectedRows.map((item) => item[tableIndexKey]);
      const nonSsGroupListKeys = wriTable2.selectedRows.map((item) => item[tableIndexKey]);
      employeeHireSep.ssGroupList = ssGroupList.filter((item: any) =>
        ssGroupListKeys.includes(item[tableIndexKey]),
      );
      employeeHireSep.nonSsGroupList = nonSsGroupList.filter((item: any) =>
        nonSsGroupListKeys.includes(item[tableIndexKey]),
      );
      for (let index = 0; index < employeeHireSep.ssGroupList.length; index++) {
        const obj = employeeHireSep.ssGroupList[index];
        if (
          !parseInt(obj.chargeEndDate) ||
          parseInt(obj.chargeStartDate) <= parseInt(obj.chargeEndDate)
        ) {
          if (monthArray.length == 0) {
            maxObj[obj.productId + obj.ssGroupId] = obj;
          }
          for (let index = 0; index < monthArray.length; index++) {
            const months = monthArray[index];
            if (
              !months[1] &&
              !obj.chargeEndDate &&
              obj.productId == months[2] &&
              obj.ssGroupId == months[3]
            ) {
              return msgErr(dict.onlyOneEndMonthCanbeEmpty);
            }
            if (
              !(
                parseInt(obj.chargeEndDate) < months[0] ||
                (months[1] && parseInt(obj.chargeStartDate) > months[1])
              ) &&
              obj.productId == months[2] &&
              obj.ssGroupId == months[3]
            ) {
              return msgErr(dict.betweenMultipleRecordsStartMonthAndEndMonthCannotHaveACrossRange);
            }
            if (
              !maxObj.hasOwnProperty(obj.productId + obj.ssGroupId) ||
              parseInt(obj.chargeStartDate) > months[0]
            )
              //放入最大的月份段替换原来的
              maxObj[obj.productId + obj.ssGroupId] = obj;
          }
          monthArray.push([
            parseInt(obj.chargeStartDate),
            parseInt(obj.chargeEndDate),
            obj.productId,
            obj.ssGroupId,
          ]);
        } else {
          return msgErr(dict.startMonthCannotGreaterThenEndMonth);
        }
        if (obj.isOneTimePay == '0') {
          if (!obj.eBase && obj.eBase !== 0) {
            return msgErr('请填写企业基数');
          }
          if (!obj.pBase && obj.pBase !== 0) {
            return msgErr('请填写个人基数');
          }
        }
      }
      if (operation === '1') {
        //保存
        await service
          .requests({
            ...data,
            employeeHireSep,
            type: 1,
            isNewBatch: 0,
          })
          .then((res) => {
            msgOk(dict.hintMsgOk);
            hideHandle(true);
          });
      } else {
        if (updateitem.assigneeCsId == updateitem.assignerCsId) {
          employeeHireSep.addConfirmStatus = '40'; // 派单确认或者接单确认本地单增员完成
        } else {
          employeeHireSep.addConfirmStatus = '30'; // 等待派单方确认
        }
        //提交确认
        await service.requests({ ...data, employeeHireSep, type: 1, isNewBatch: 0 }).then((res) => {
          msgOk(dict.hintMsgOk);
          hideHandle(true);
        });
      }
    });
  };

  const pending_clickHandler = async () => {
    const employeeHireSep = await form.validateFields();
    if (updateitem.addConfirmStatus == '22') {
      msgErr(dict.alreadyPending);
      return;
    }
    if (!employeeHireSep.pendingReason) {
      msgErr('请选择挂起原因');
      return;
    } else if (employeeHireSep.pendingReason == '8' && !employeeHireSep.confirmRemark) {
      msgErr(dict.plzInputInputRemark);
      return;
    } else {
      employeeHireSep.type = 22;
      employeeHireSep.addConfirmStatus = '22';
      await API.emphiresep.employeeHireSep.updateEmpHireSepReject
        .requests({ ...updateitem, ...employeeHireSep })
        .then((res) => {
          msgOk(dict.hintMsgOk);
          hideHandle(true);
        });
    }
  };

  const reject_clickHandler = async () => {
    const employeeHireSep = await form.validateFields();
    if (employeeHireSep) {
      if (employeeHireSep.confirmRemark) {
        employeeHireSep.type = '1';
        employeeHireSep.addConfirmStatus = '1';
        await API.emphiresep.employeeHireSep.updateEmpHireSepReject.requests({
          ...updateitem,
          ...employeeHireSep,
        });
        msgOk(dict.hintMsgOk);
        hideHandle(true);
      } else {
        msgErr(dict.plzInputRemark);
        return;
      }
    } else {
      msgErr(dict.noDataSelected);
      return;
    }
  };

  const renderFooter = () => {
    // updateitem
    // const { providerType, isRelated, associationStatus } = updateitem;
    // const unRelated = providerType !== '2' && isRelated === '1' && associationStatus === '未关联';
    return (
      <RowElement>
        <ColElementButton
          style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}
        >
          <AsyncButton key="save" type="primary" onClick={() => oKhandle('1')}>
            保存
          </AsyncButton>
          <AsyncButton
            key="saveAll"
            type="primary"
            // disabled={unRelated}
            onClick={() => oKhandle('2')}
          >
            保存并提交确认
          </AsyncButton>
          <AsyncButton key="pending" type="primary" onClick={pending_clickHandler}>
            挂起
          </AsyncButton>
          <AuthButtons funcId="20201301">
            <AsyncButton key="cancel" type="primary" onClick={reject_clickHandler}>
              驳回
            </AsyncButton>
          </AuthButtons>
        </ColElementButton>
      </RowElement>
    );
  };

  const onWriteFromChange = (options: OnWriteFromChangeOptions) => {
    const { row, serial } = options;
    if (wriTable.selectedRows.findIndex((item) => item[tableIndexKey] === serial) < 0)
      wriTable.setSelectedRows([...wriTable.selectedRows, row]);
  };
  const onWriteFromChange2 = (options: OnWriteFromChangeOptions) => {
    const { row, serial } = options;
    if (wriTable2.selectedRows.findIndex((item) => item[tableIndexKey] === serial) < 0)
      wriTable2.setSelectedRows([...wriTable2.selectedRows, row]);
  };

  return (
    <Codal
      title={title}
      width={1200}
      visible={visible}
      onCancel={() => hideHandle()}
      footer={renderFooter()}
    >
      <Spin spinning={loading}>
        <FormElement3 form={form}>
          <EnumerateFields outerForm={form} colNumber={3} formColumns={formColumns} />
        </FormElement3>
        <Card title="服务内容约定">
          <FormElement3 form={formTransfer}>
            <EnumerateFields
              outerForm={formTransfer}
              colNumber={3}
              formColumns={transferServiceFormColumns}
            />
          </FormElement3>
          <>
            <>服务费</>
            <Writable
              service={{ ...qutationService, cacheKey: qutationService.cacheKey + 1 }}
              columns={QutationColumns}
              notShowPagination
              noDeleteButton
              noAddButton
              wriTable={qutationWriTable}
              editable={false}
              disabled
            />
          </>
          <>
            <>补缴服务费</>
            <Writable
              service={{ ...qutationService, cacheKey: qutationService.cacheKey + 1 }}
              columns={QutationColumns}
              notShowPagination
              noDeleteButton
              noAddButton
              wriTable={serviceFeeWriTable}
              editable={false}
              disabled
            />
          </>
          <FormElement3 form={formTransfer1}>
            <EnumerateFields
              outerForm={formTransfer1}
              colNumber={3}
              formColumns={transferServiceFormColumns1}
            />
          </FormElement3>
        </Card>
        <Card title="员工劳动合同约定">
          <FormElement3 form={formContract}>
            <EnumerateFields
              outerForm={formContract}
              colNumber={3}
              formColumns={transferContractFormColumns}
            />
          </FormElement3>
        </Card>
        {isIssuingSalary ? (
          <Card title="薪税服务约定">
            <FormElement3 form={formWage}>
              <EnumerateFields
                outerForm={formWage}
                colNumber={3}
                formColumns={transferWageFormColumns}
              />
            </FormElement3>
          </Card>
        ) : null}
        <Card title="社保公积金缴费约定">
          <FormElement3 form={formInsurance}>
            <EnumerateFields
              outerForm={formInsurance}
              colNumber={3}
              formColumns={transferInsuranceFormColumns}
            />
          </FormElement3>
        </Card>
        <h3>社保公积金</h3>
        <ColElementButton>
          <Button onClick={taggleOnShowConfigSsGroup}>设定社保公积金组</Button>
          <AddOneTimeSsPop
            disabled={!form.getFieldValue('subcontractId')}
            submitHandle={selectOneTimeSsGroupChangeFunction}
            ssGroupList={wriTable.data.list}
          />
          <Button onClick={delete_clickHandler} disabled={wriTable.selectedRows.length === 0}>
            删除
          </Button>
          <ForceDeleteSsGroupPop
            disabled={!form.getFieldValue('subcontractId')}
            submitHandle={forceDelete_clickHandler}
            ssGroupList={wriTable.data.list}
          />
          <Button
            disabled={wriTable.data.list.length === 0 || wriTable.selectedRows.length === 0}
            onClick={loadMinBase_clickHandler}
          >
            载入最低基数
          </Button>
        </ColElementButton>
        <Writable
          service={service}
          columns={columns}
          noDeleteButton
          noAddButton
          notShowPagination
          wriTable={wriTable}
          summary={renderSummarySs}
          onWriteFromChange={onWriteFromChange}
        />
        <h3>非社保公积金</h3>
        <Writable
          service={service2}
          columns={columns2}
          noDeleteButton
          noAddButton
          wriTable={wriTable2}
          notShowPagination
          summary={renderSummary}
          onWriteFromChange={onWriteFromChange2}
        />
        <FormElement3 form={form}>
          <EnumerateFields outerForm={form} colNumber={3} formColumns={formColumns2} />
        </FormElement3>
      </Spin>
      <QueryRatioDropdown
        visible={onShowRatio}
        hideHandle={taggleOnShowRatio}
        submitHandle={chooseRatio}
        lineData={ssGroupLineData}
      />
      <ConfigSsGroup
        visible={onShowConfigSsGroup}
        configData={configData}
        hideHandle={taggleOnShowConfigSsGroup}
        submitHandle={submitConfigSsGroup}
        groupList={groupList}
      />
    </Codal>
  );
};

export default AddOrderForm;
