declare namespace defs {
  export namespace workflow {
    export class ActivityDefDTO {
      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column WFL_ACTIVITY_DEF.ACTIVITY_CODE	  	  ibatorgenerated Mon Mar 21 15:10:48 CST 2011 */
      activityCode: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column WFL_ACTIVITY_DEF.ACTIVITY_DEF_ID	  	  ibatorgenerated Mon Mar 21 15:10:48 CST 2011 */
      activityDefId: number;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column WFL_ACTIVITY_DEF.ACTIVITY_DESC	  	  ibatorgenerated Mon Mar 21 15:10:48 CST 2011 */
      activityDesc: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column WFL_ACTIVITY_DEF.ACTIVITY_NAME_CN	  	  ibatorgenerated Mon Mar 21 15:10:48 CST 2011 */
      activityNameCn: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column WFL_ACTIVITY_DEF.ACTIVITY_NAME_EN	  	  ibatorgenerated Mon Mar 21 15:10:48 CST 2011 */
      activityNameEn: string;

      /** createBy */
      createBy: number;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column WFL_PROCESS_DEF.CREATE_DT	  	  ibatorgenerated Mon Mar 21 15:10:48 CST 2011 */
      createDt: string;

      /** depthMark */
      depthMark: number;

      /** ifWait */
      ifWait: number;

      /** 跳签标志 */
      jump: number;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column WFL_PROCESS_DEF.MIMIC_BY	  	  ibatorgenerated Mon Mar 21 15:10:48 CST 2011 */
      mimicBy: number;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column WFL_ACTIVITY_DEF.PROCESS_DEF_ID	  	  ibatorgenerated Mon Mar 21 15:10:48 CST 2011 */
      processDefId: number;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column WFL_ACTIVITY_DEF.ROLE_DEF_ID	  	  ibatorgenerated Mon Mar 21 15:10:48 CST 2011 */
      roleDefId: number;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column WFL_ACTIVITY_DEF.SIGN_TYPE	  	  ibatorgenerated Mon Mar 21 15:10:48 CST 2011 */
      signType: number;

      /** status */
      status: number;

      /** strategyConfig */
      strategyConfig: Array<defs.workflow.StrategyConfig>;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column WFL_PROCESS_DEF.UPDATE_BY	  	  ibatorgenerated Mon Mar 21 15:10:48 CST 2011 */
      updateBy: number;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column WFL_PROCESS_DEF.UPDATE_DT	  	  ibatorgenerated Mon Mar 21 15:10:48 CST 2011 */
      updateDt: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column WFL_ACTIVITY_DEF.URL	  	  ibatorgenerated Mon Mar 21 15:10:48 CST 2011 */
      url: string;
    }

    export class ActivitySet {
      /** 活动名称 */
      activityNameCn: string;

      /** 财务大类 */
      bizCategory: string;

      /** 创建人名字 */
      createByName: string;

      /** endIndex */
      endIndex: number;

      /** 无效时间 */
      failDt: string;

      /** 审批分公司 */
      newBranch: string;

      /** 审批分公司 */
      newBranchName: string;

      /** 提交分公司 */
      oldBranch: string;

      /** 提交分公司 */
      oldBranchName: string;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** 流程识别标识 */
      processDefCode: string;

      /** 流程识别标识 */
      processDefCodeName: string;

      /** 审批人名字 */
      proxyByName: string;

      /** 发布时间 */
      pubDt: string;

      /** 审批角色 */
      roleDefId: string;

      /** 审批角色 */
      roleDefName: string;

      /** startIndex */
      startIndex: number;

      /** 状态(0.初始，1生效，2无效)) */
      status: string;

      /** 状态(0.初始，1生效，2无效)) */
      statusName: string;

      /** 主键 */
      wflActivitySetId: string;
    }

    export class CommonResponse<T0 = any> {
      /** bizCode */
      bizCode: number;

      /** code */
      code: number;

      /** data */
      data: T0;

      /** message */
      message: string;

      /** t */
      t: T0;
    }

    export class ExportQuery {
      /** 查询条件 */
      condition: object;

      /** 表头字段列表 */
      fieldArr: Array<string>;

      /** 表头字段中文拼接 */
      headStr: string;

      /** 表头字段类型列表 */
      typeArr: Array<string>;
    }

    export class RemindDTO {
      /** activity_DEF_ID */
      activity_DEF_ID: string;

      /** business_REMIND_ID */
      business_REMIND_ID: string;

      /** input_DT */
      input_DT: string;

      /** process_INS_ID */
      process_INS_ID: string;

      /** remind_DESC */
      remind_DESC: string;

      /** remind_TYPE */
      remind_TYPE: string;

      /** remind_TYPE_NAME */
      remind_TYPE_NAME: string;

      /** rn */
      rn: string;

      /** workitem_ID */
      workitem_ID: string;
    }

    export class RemindQuery {
      /** endIndex */
      endIndex: number;

      /** fields */
      fields: Array<string>;

      /** fromInputDt */
      fromInputDt: string;

      /** headers */
      headers: Array<string>;

      /** limit */
      limit: string;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** proDefId */
      proDefId: string;

      /** remindDesc */
      remindDesc: string;

      /** remindType */
      remindType: string;

      /** start */
      start: string;

      /** startIndex */
      startIndex: number;

      /** toInputDt */
      toInputDt: string;

      /** userId */
      userId: string;
    }

    export class StrategyConfig {
      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column WFL_STRATEGY_CONFIG.ACTIVITY_DEF_ID	  	  ibatorgenerated Mon Mar 21 15:10:48 CST 2011 */
      activityDefId: number;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column WFL_STRATEGY_CONFIG.CREATE_BY	  	  ibatorgenerated Mon Mar 21 15:10:48 CST 2011 */
      createBy: number;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column WFL_STRATEGY_CONFIG.CREATE_DT	  	  ibatorgenerated Mon Mar 21 15:10:48 CST 2011 */
      createDt: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column WFL_STRATEGY_CONFIG.FOLLOWED_ACTIVITY_CODE_ID	  	  ibatorgenerated Mon Mar 21 15:10:48 CST 2011 */
      followedActivityCodeId: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column WFL_STRATEGY_CONFIG.MIMIC_BY	  	  ibatorgenerated Mon Mar 21 15:10:48 CST 2011 */
      mimicBy: number;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column WFL_STRATEGY_CONFIG.STRATEGY_FORMULA	  	  ibatorgenerated Mon Mar 21 15:10:48 CST 2011 */
      strategyFormula: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column WFL_STRATEGY_CONFIG.STRATEGY_ID	  	  ibatorgenerated Mon Mar 21 15:10:48 CST 2011 */
      strategyId: number;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column WFL_STRATEGY_CONFIG.UPDATE_BY	  	  ibatorgenerated Mon Mar 21 15:10:48 CST 2011 */
      updateBy: number;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column WFL_STRATEGY_CONFIG.UPDATE_DT	  	  ibatorgenerated Mon Mar 21 15:10:48 CST 2011 */
      updateDt: string;
    }

    export class WorkFlowBean {
      /** 活动编号 */
      activityCode: string;

      /** 活动中文名 */
      activityNameCn: string;

      /** 活动英文名 */
      activityNameEn: string;

      /** add */
      add: boolean;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** 创建日期 */
      createDt: string;

      /** currentActivityCode */
      currentActivityCode: string;

      /** 当前活动定义id */
      currentActivityDefId: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** 节点深度 */
      depthMark: string;

      /** 结束时间 */
      endDate: string;

      /** 导入类型,扩充使用 */
      expType: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** 是否薪资查询 */
      isWageQuery: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 流程识别标识 */
      processDefCode: string;

      /** 流程id */
      processDefId: string;

      /** 流程实例id */
      processInsId: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** 开始时间 */
      startDate: string;

      /** 活动开始人id */
      startUserId: string;

      /** 状态 */
      status: number;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;

      /** 代办任务id */
      workitemId: string;
    }

    export class WorkItem {
      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column WFL_WORKITEM.ACTIVITY_DEF_ID	  	  ibatorgenerated Mon Mar 21 15:10:48 CST 2011 */
      activityDefId: number;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column WFL_WORKITEM_HISTORY.CREATE_BY	  	  ibatorgenerated Mon Mar 21 15:10:48 CST 2011 */
      createBy: number;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column WFL_WORKITEM_HISTORY.CREATE_DT	  	  ibatorgenerated Mon Mar 21 15:10:48 CST 2011 */
      createDt: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column WFL_WORKITEM_HISTORY.MIMIC_BY	  	  ibatorgenerated Mon Mar 21 15:10:48 CST 2011 */
      mimicBy: number;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column WFL_WORKITEM.PARTICIPANT	  	  ibatorgenerated Mon Mar 21 15:10:48 CST 2011 */
      participant: number;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column WFL_WORKITEM.PROCESS_INS_ID	  	  ibatorgenerated Mon Mar 21 15:10:48 CST 2011 */
      processInsId: number;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column WFL_WORKITEM_HISTORY.UPDATE_BY	  	  ibatorgenerated Mon Mar 21 15:10:48 CST 2011 */
      updateBy: number;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column WFL_WORKITEM_HISTORY.UPDATE_DT	  	  ibatorgenerated Mon Mar 21 15:10:48 CST 2011 */
      updateDt: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds	  to the database column WFL_WORKITEM.WORKITEM_ID	  	  ibatorgenerated Mon Mar 21 15:10:48 CST 2011 */
      workitemId: number;
    }

    export class processDefDTO {
      /** activityDef */
      activityDef: Array<defs.workflow.ActivityDefDTO>;

      /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column WFL_PROCESS_DEF.CREATE_BY           @ibatorgenerated Mon Mar 21 15:10:48 CST 2011 */
      createBy: number;

      /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column WFL_PROCESS_DEF.CREATE_DT           @ibatorgenerated Mon Mar 21 15:10:48 CST 2011 */
      createDt: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column WFL_PROCESS_DEF.MIMIC_BY           @ibatorgenerated Mon Mar 21 15:10:48 CST 2011 */
      fileId: number;

      /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column WFL_PROCESS_DEF.MIMIC_BY           @ibatorgenerated Mon Mar 21 15:10:48 CST 2011 */
      mimicBy: number;

      /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column WFL_PROCESS_DEF.PROCESS_DEF_CODE           @ibatorgenerated Mon Mar 21 15:10:48 CST 2011 */
      processDefCode: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column WFL_PROCESS_DEF.PROCESS_DEF_ID           @ibatorgenerated Mon Mar 21 15:10:48 CST 2011 */
      processDefId: number;

      /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column WFL_PROCESS_DEF.PROCESS_DESC           @ibatorgenerated Mon Mar 21 15:10:48 CST 2011 */
      processDesc: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column WFL_PROCESS_DEF.PROCESS_NAME_CN           @ibatorgenerated Mon Mar 21 15:10:48 CST 2011 */
      processNameCn: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column WFL_PROCESS_DEF.PROCESS_NAME_EN           @ibatorgenerated Mon Mar 21 15:10:48 CST 2011 */
      processNameEn: string;

      /** 发布状态 */
      status: number;

      /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column WFL_PROCESS_DEF.UPDATE_BY           @ibatorgenerated Mon Mar 21 15:10:48 CST 2011 */
      updateBy: number;

      /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column WFL_PROCESS_DEF.UPDATE_DT           @ibatorgenerated Mon Mar 21 15:10:48 CST 2011 */
      updateDt: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column WFL_PROCESS_DEF.URL           @ibatorgenerated Mon Mar 21 15:10:48 CST 2011 */
      url: string;
    }

    export class processDefQuery {
      /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column WFL_PROCESS_DEF.CREATE_BY           @ibatorgenerated Mon Mar 21 15:10:48 CST 2011 */
      createBy: number;

      /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column WFL_PROCESS_DEF.CREATE_DT           @ibatorgenerated Mon Mar 21 15:10:48 CST 2011 */
      createDt: string;

      /** endIndex */
      endIndex: number;

      /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column WFL_PROCESS_DEF.MIMIC_BY           @ibatorgenerated Mon Mar 21 15:10:48 CST 2011 */
      fileId: number;

      /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column WFL_PROCESS_DEF.MIMIC_BY           @ibatorgenerated Mon Mar 21 15:10:48 CST 2011 */
      mimicBy: number;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column WFL_PROCESS_DEF.PROCESS_DEF_CODE           @ibatorgenerated Mon Mar 21 15:10:48 CST 2011 */
      processDefCode: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column WFL_PROCESS_DEF.PROCESS_DEF_ID           @ibatorgenerated Mon Mar 21 15:10:48 CST 2011 */
      processDefId: number;

      /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column WFL_PROCESS_DEF.PROCESS_DESC           @ibatorgenerated Mon Mar 21 15:10:48 CST 2011 */
      processDesc: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column WFL_PROCESS_DEF.PROCESS_NAME_CN           @ibatorgenerated Mon Mar 21 15:10:48 CST 2011 */
      processNameCn: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column WFL_PROCESS_DEF.PROCESS_NAME_EN           @ibatorgenerated Mon Mar 21 15:10:48 CST 2011 */
      processNameEn: string;

      /** startIndex */
      startIndex: number;

      /** 发布状态 */
      status: number;

      /** type */
      type: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column WFL_PROCESS_DEF.UPDATE_BY           @ibatorgenerated Mon Mar 21 15:10:48 CST 2011 */
      updateBy: number;

      /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column WFL_PROCESS_DEF.UPDATE_DT           @ibatorgenerated Mon Mar 21 15:10:48 CST 2011 */
      updateDt: string;

      /** This field was generated by Apache iBATIS ibator. This field corresponds      to the database column WFL_PROCESS_DEF.URL           @ibatorgenerated Mon Mar 21 15:10:48 CST 2011 */
      url: string;
    }
  }
}

declare namespace API {
  export namespace workflow {
    /**
     * 业务提醒
     */
    export namespace remind {
      /**
        * 创建业务提醒
创建业务提醒
        * /remind/createRemind
        */
      export namespace createRemind {
        export class Params {
          /** participantUsersIdStr */
          participantUsersIdStr: string;
          /** remindMsg */
          remindMsg: string;
          /** remindType */
          remindType: string;
        }

        export type Response<T> = defs.workflow.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 创建业务提醒
创建业务提醒
        * /remind/createRemind
        */
      export namespace postCreateRemind {
        export class Params {
          /** participantUsersIdStr */
          participantUsersIdStr: string;
          /** remindMsg */
          remindMsg: string;
          /** remindType */
          remindType: string;
        }

        export type Response<T> = defs.workflow.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 业务提醒导出
根据条件导出业务提醒护
        * /remind/exportExcel
        */
      export namespace exportExcel {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: any;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.workflow.RemindQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.workflow.RemindQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询业务提醒
查询业务提醒  userId,proDefId,remindType,remindDesc,fromInputDt YYYY-MM-DD,toInputDt,pageNum,pageSize
        * /remind/findRemind
        */
      export namespace findRemind {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.workflow.RemindDTO;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.workflow.RemindQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.workflow.RemindQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询提醒类型下拉框的数据
查询提醒类型下拉框的数据
        * /remind/initRemindType
        */
      export namespace initRemindType {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.workflow.CommonResponse<Array>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询提醒类型下拉框的数据
查询提醒类型下拉框的数据
        * /remind/initRemindType
        */
      export namespace postInitRemindType {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.workflow.CommonResponse<Array>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 更新业务提醒
更新业务提醒
        * /remind/updateRemindList
        */
      export namespace updateRemindList {
        export class Params {}

        export type Response<T> = defs.workflow.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: Array<ObjectMap>,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Array<ObjectMap>,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 工作流
     */
    export namespace workFlow {
      /**
        * 导出
导出
        * /workflow/downloadfile
        */
      export namespace toDownLoad {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: any;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.workflow.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.workflow.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据条件得到财务审批节点配置的分页查询结果
根据条件得到财务审批节点配置的分页查询结果
        * /workflow/getActivitySet
        */
      export namespace getActivitySet {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.workflow.ActivitySet;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.workflow.ActivitySet,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.workflow.ActivitySet,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取财务审批节点配置
获取财务审批节点配置
        * /workflow/getActivitySetEx
        */
      export namespace getActivitySetEx {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.workflow.CommonResponse<ObjectMap>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取财务审批节点配置
获取财务审批节点配置
        * /workflow/getActivitySetEx
        */
      export namespace postGetActivitySetEx {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.workflow.CommonResponse<ObjectMap>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.workflow.ActivitySet,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.workflow.ActivitySet,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据insId获取审批过程
根据insId获取审批过程
        * /workflow/getApproveProcess
        */
      export namespace getApproveProcess {
        export class Params {
          /** processInsId */
          processInsId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.workflow.CommonResponse<ObjectMap>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取代办列表
获取代办列表
        * /workflow/getWorkitemList
        */
      export namespace getWorkitemList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.workflow.CommonResponse<ObjectMap>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取代办列表
获取代办列表
        * /workflow/getWorkitemList
        */
      export namespace postGetWorkitemList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.workflow.CommonResponse<ObjectMap>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取代办记录数
获取代办记录数
        * /workflow/getWorkitemListCount
        */
      export namespace getWorkitemListCount {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.workflow.CommonResponse<ObjectMap>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取代办记录数
获取代办记录数
        * /workflow/getWorkitemListCount
        */
      export namespace postGetWorkitemListCount {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.workflow.CommonResponse<ObjectMap>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取审批类型
获取审批类型
        * /workflow/getprocessDefCodeActivityCn
        */
      export namespace getprocessDefCodeActivityCn {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.workflow.CommonResponse<ObjectMap>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取审批类型
获取审批类型
        * /workflow/getprocessDefCodeActivityCn
        */
      export namespace postGetprocessDefCodeActivityCn {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.workflow.CommonResponse<ObjectMap>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.workflow.ActivitySet,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.workflow.ActivitySet,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 创建财务审批节点配置
创建财务审批节点配置
        * /workflow/insertActivitySet
        */
      export namespace insertActivitySet {
        export class Params {}

        export type Response<T> = defs.workflow.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.workflow.ActivitySet,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.workflow.ActivitySet,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 修改财务审批节点配置
修改财务审批节点配置
        * /workflow/updateActivitySet
        */
      export namespace updateActivitySet {
        export class Params {}

        export type Response<T> = defs.workflow.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.workflow.ActivitySet,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.workflow.ActivitySet,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 失效
失效
        * /workflow/updateActivitySetFail
        */
      export namespace updateActivitySetFail {
        export class Params {}

        export type Response<T> = defs.workflow.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.workflow.ActivitySet,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.workflow.ActivitySet,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 生效
生效
        * /workflow/updateActivitySetPub
        */
      export namespace updateActivitySetPub {
        export class Params {}

        export type Response<T> = defs.workflow.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.workflow.ActivitySet,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.workflow.ActivitySet,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 流程创建工厂接口
     */
    export namespace workFlowFactory {
      /**
        * 复制流程定义
复制流程定义
        * /workFlowFactoryController/copyProcessDef
        */
      export namespace copyProcessDef {
        export class Params {
          /** processDefId */
          processDefId: string;
          /** userId */
          userId: string;
        }

        export type Response<T> = defs.workflow.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 删除流程
删除流程
        * /workFlowFactoryController/delProcessDef
        */
      export namespace delProcessDef {
        export class Params {
          /** processDefId */
          processDefId: string;
        }

        export type Response<T> = defs.workflow.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获得所有流程
获得所有流程
        * /workFlowFactoryController/getAllProcessIns
        */
      export namespace getAllProcessIns {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.workflow.CommonResponse<Array>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据insId获取审批过程
根据insId获取审批过程
        * /workFlowFactoryController/getApproveProcess
        */
      export namespace getApproveProcess {
        export class Params {
          /** processInsId */
          processInsId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.workflow.CommonResponse<Array>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获得流程
获得流程
        * /workFlowFactoryController/getProcessDef
        */
      export namespace getProcessDef {
        export class Params {
          /** processDefCode */
          processDefCode: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.workflow.CommonResponse<ObjectMap>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 通过 流程定义id获得流程
通过 流程定义id获得流程
        * /workFlowFactoryController/getProcessDefById
        */
      export namespace getProcessDefById {
        export class Params {
          /** processDefId */
          processDefId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.workflow.CommonResponse<ObjectMap>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获得流程实例
获得流程实例
        * /workFlowFactoryController/getProcessIns
        */
      export namespace getProcessIns {
        export class Params {
          /** processDefId */
          processDefId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.workflow.CommonResponse<Array>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 通过 流程实例id获得流程实例
通过 流程实例id获得流程实例
        * /workFlowFactoryController/getProcessInsById
        */
      export namespace getProcessInsById {
        export class Params {
          /** processInsId */
          processInsId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.workflow.CommonResponse<Array>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获得有效角色列表
获得有效角色列表
        * /workFlowFactoryController/getValidRole
        */
      export namespace getValidRole {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.workflow.CommonResponse<Array>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * sami add 	  新增获取 workitem 方法,返回workitem
sami add 	  新增获取 workitem 方法,返回workitem
        * /workFlowFactoryController/getWorkItemById
        */
      export namespace getWorkItemById {
        export class Params {
          /** id */
          id: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.workflow.WorkItem;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * workItemId得到一个流程实例
workItemId得到一个流程实例
        * /workFlowFactoryController/getWorkflowIns
        */
      export namespace getWorkflowIns {
        export class Params {
          /** workItemId */
          workItemId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.workflow.WorkFlowBean;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据userId和processInsId得到一个流程实例
根据userId和processInsId得到一个流程实例
        * /workFlowFactoryController/getWorkflowInsBy
        */
      export namespace getWorkflowInsBy {
        export class Params {
          /** processInsId */
          processInsId: string;
          /** userId */
          userId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.workflow.WorkFlowBean;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * processInsId得到一个流程实例
processInsId得到一个流程实例
        * /workFlowFactoryController/getWorkflowInsByProcessInsId
        */
      export namespace getWorkflowInsByProcessInsId {
        export class Params {
          /** processInsId */
          processInsId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.workflow.WorkFlowBean;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 创建流程
创建流程
        * /workFlowFactoryController/insertCreateProcessDef
        */
      export namespace insertCreateProcessDef {
        export class Params {}

        export type Response<T> = defs.workflow.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.workflow.processDefDTO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.workflow.processDefDTO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 流程定义发布
流程定义发布
        * /workFlowFactoryController/processDefPublish
        */
      export namespace processDefPublish {
        export class Params {
          /** processDefCode */
          processDefCode: string;
          /** processDefId */
          processDefId: string;
          /** userId */
          userId: string;
        }

        export type Response<T> = defs.workflow.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 流程定义查询
流程定义查询
        * /workFlowFactoryController/selectAllProcessDefByMap
        */
      export namespace selectAllProcessDefByMap {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.workflow.CommonResponse<Array>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.workflow.processDefQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.workflow.processDefQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 修改流程
修改流程
        * /workFlowFactoryController/updateProcessDef
        */
      export namespace updateProcessDef {
        export class Params {}

        export type Response<T> = defs.workflow.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.workflow.processDefDTO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.workflow.processDefDTO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 仅修改流程所有信息
仅修改流程所有信息
        * /workFlowFactoryController/updateProcessDefInfo
        */
      export namespace updateProcessDefInfo {
        export class Params {}

        export type Response<T> = defs.workflow.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.workflow.processDefDTO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.workflow.processDefDTO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
       * uploadImage
       * /workFlowFactoryController/uploadImage
       */
      export namespace uploadImage {
        export class Params {
          /** 业务类型, 据（校长)说一般是function_id */
          bizType: string;
          /** 文件数据 */
          fileData?: string;
          /** 文件id */
          fileId?: string;
          /** 文件名称 */
          fileName: string;
          /** 文件路径 */
          filePath?: string;
          /** 历史文件ID */
          hisFileId?: string;
          /** 级联查询条件,逐级网上找 */
          levelQueryByFileId?: string;
          /** 非标合同审批id */
          nonStaCoctApprId?: string;
          /** 个性化参数 */
          parameters?: object;
          /** processDefId */
          processDefId?: string;
          /** 记录id */
          recordId?: string;
          /** 备注 */
          remark?: string;
          /** 保存方式,1为tokyotyrant存储(此方式暂不采用),2为文件目录存储, eos */
          savePattern: string;
          /** 上传人id */
          uploadBy?: string;
          /** 上传人名字 */
          uploadByName?: string;
          /** 上传日期 */
          uploadDt?: string;
        }

        export type Response<T> = defs.workflow.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }
  }
}
