/*
 * @Author: your name
 * @Date: 2020-09-08 17:47:27
 * @LastTime: 2022-07-19 16:24:31
 * @LastAuthor: Veni_刘夏梅
 * @Description: In User Settings Edit
 * @FilePath: \rhro_web\src\pages\finance\Receive\BillPrintReport\index.ts
 */
import React, { useState, useEffect, useRef } from 'react';
import { Form, Row, Checkbox, Modal, Spin, Card, Tabs, Button, Space } from 'antd';
import { WritableColumnProps } from '@/utils/writable/types';
import { CachedPage } from '@/components/CachedPage';
import { EditeFormProps } from '@/components/EditeForm';
import { mapToSelectors } from '@/components/Selectors/FuncSelectors';
import { mapToRatio } from '@/components/Selectors/FuncRatio';
import { CustomerPop } from '@/components/StandardPop/CustomerPop';
import { commonRadio, billTypeRadio, billOrderGroup } from '@/utils/settings/finance/billPrint';
import { GetBaseSelector } from '@/components/Selectors/BaseDataSelectors';
import { CommonDropSelector } from '@/components/Selectors';
import TableTransfer from './components/TableTransfer';
import { getCurrentUser, getCurrentMenu } from '@/utils/model';
import { generateReportURL } from '@/pages/emphiresep/emporder/QueryEmpOrderListForGen/Forms/BizUtil';
import { stdMonthFormatMoDash } from '@/utils/methods/times';
import { isEmpty } from 'lodash';
import { AsyncButton } from '@/components/Forms/Confirm';
import { FormElement4 } from '@/components/Forms/FormLayouts';
import { EnumerateFields } from '@/components/CachedPage/EnumerateFields';
import ViewBillTempPrint from './components/viewBillTempPrint';
import { msgErr, msgOk } from '@/utils/methods/message';

const columns: WritableColumnProps<any>[] = [];

const queryParams = [
  { propName: 'cust_id', needAnd: false, aliasPropName: 'custId' },
  { propName: 'receivable_templt_id', needAnd: true, aliasPropName: 'receivableTempltId' },
  { propName: 'bill_ym', needAnd: true, aliasPropName: 'billYm' },
  { propName: 'bill_order', needAnd: true, aliasPropName: 'columnName' },
  { propName: 'isGroup', needAnd: true },
  { propName: 'isMerge', needAnd: true },
  { propName: 'billType', needAnd: true },
  { propName: 'itemLength', needAnd: true, aliasPropName: 'itemLength' },
  { propName: 'itemOrder', needAnd: true, aliasPropName: 'displayItem' },
  { propName: 'groups', needAnd: true, aliasPropName: 'groups' },
  { propName: 'userId', needAnd: true },
  { propName: 'exportType', needAnd: true, aliasPropName: 'excelToPdf' },
  { propName: 'payeeId', needAnd: true, aliasPropName: 'payee_id' } /*NP-3464*/,
  { propName: 'isFinFormat', needAnd: true, aliasPropName: 'isfin' } /*NP-5381*/,
  { propName: 'isSelTemplts', needAnd: true } /*NP-9615*/,
  { propName: 'allTemplteIds', needAnd: true },
];

const queryParamsEOS = [
  { propName: 'billYm', needAnd: false },
  { propName: 'custId', needAnd: true },
  { propName: 'type', needAnd: true },
  { propName: 'groups', needAnd: true },
  { propName: 'signBranchTitleId', needAnd: true },
  { propName: 'isEle', needAnd: true },
];

export interface IInitdata {
  cust?: {
    custId: string;
    custName: string;
  };
  bill_ym?: string;
  temp?: {
    name: string;
    id: string;
    curCustTemp?: POJO;
  };
}
interface BillPrintReportProps {
  [props: string]: any;
  initialValues?: IInitdata;
}

const listService = API.report.reportFinance.getAllBillTemplateFromDetail;
const sheetNumberService = API.report.reportFinance.getAuditOrNotifyCountNumberFromDetail;
const saveEfEleSealInfoService = API.finance.efEleSealInfo.saveEfEleSealInfo;
const selectHroCheckCount = API.finance.efEleSealInfo.selectHroCheckCount;
const selectEosCheckCount = API.finance.efEleSealInfo.selectEosCheckCount;

export const typeList = new Map<string, string>([
  ['1', '标准版'],
  ['2', '简易版'],
  ['3', '合并版'],
]);

const BillPrintReport: React.FC<BillPrintReportProps> = ({ initialValues = {} }) => {
  const [form] = Form.useForm();
  const [custId, setCustId] = useState('');
  const [isMerge, setIsMerge] = useState('0');
  const [isGroup, setIsGroup] = useState('0');
  const [signBrachTitle, setSignBrachTitle] = useState(false);
  const [selTmp, setSelTmp] = useState(false);
  const [loading, setLoading] = useState(false);
  const [visible, setVisible] = useState(false);
  const [data, setData] = useState([]);
  const [selData, setSelData] = useState([]);
  const [curCustTemp, setCurCustTemp] = useState<POJO>({});
  const [signBrachName, setSignBrachName] = useState<POJO>({});
  const [custTmepArr, setCustTmepArr] = useState([]);
  const [formValues, setFormValues] = useState<POJO>({});
  const [key, setKey] = useState<'HRO' | 'EOS' | undefined>('HRO');
  const selTmpRef = useRef<any[]>([]);
  const [formEos] = Form.useForm();

  // 用户信息
  const {
    profile: { userId, governingBranch = '' },
  } = getCurrentUser();
  const curMenu: any = getCurrentMenu();
  const privateObject: any = getCurrentUser();
  const defaultSet = {
    isGroup: '0',
    isMerge: '0',
    billType: '1',
  };
  useEffect(() => {
    (async () => {
      const formData = await form.getFieldsValue();
      form.setFieldsValue({
        ...initialValues,
        ...defaultSet,
        receivable_templt_id: initialValues.temp?.id ? `${initialValues.temp?.id}` : undefined,
      });
      setCustId(initialValues?.cust?.custId || formData.cust_id || '');
      if (!isEmpty(initialValues?.temp?.curCustTemp)) {
        setCurCustTemp(initialValues?.temp?.curCustTemp || {});
      }
      console.log(
        'initialValues',initialValues
      )
    })();
  }, []);

  // 查询账套
  const searchTemp = async () => {
    try {
      const extraParam = { userId, payeeId: governingBranch };
      const { bill_ym, cust_id } = await form.validateFields();
      setLoading(true);
      listService
        .requests({
          ...extraParam,
          bill_ym,
          cust_id,
          pageSize: 999,
        })
        .then((res) => setData(res?.list))
        .finally(() => setLoading(false));
    } catch (error) {
      // msgErr('查询失败');
    }
  };

  // 打印
  const handlePrint = async (param: POVO) => {
    const values = await form.validateFields();
    values.baseRptURL = privateObject.reportDir; //报表系统入口地址
    values.modulePath = curMenu.reportUrl;
    if (isEmpty(values.custId) && custId) {
      param.cust_id = custId;
    }
    param.userId = userId;
    param.payee_id = governingBranch;
    if (isEmpty(curCustTemp) && isEmpty(custTmepArr)) {
      param.itemLength = null;
      param.itemOrder = null;
      param.temp_templt_id = undefined;
    } else {
      const tempArr = selTmp ? selTmpRef.current : custTmepArr;
      const curTemp = isEmpty(curCustTemp) ? tempArr[0] : curCustTemp;
      const itemOrder = curTemp?.reserveName1;
      param.itemLength = curTemp?.reserveObj;
      console.log('selTmpRef.current', tempArr)
      console.log('curCustTemp', curCustTemp)
      console.log('custTmepArr[0]', custTmepArr[0])
      console.log('curTemp.reserveObj', curTemp.reserveObj)
      param.itemOrder =
        itemOrder == itemOrder.replace('a', '') ? itemOrder : 'a' + itemOrder.replace('a', '');
      if (curCustTemp.key) {
        param.temp_templt_id = curCustTemp.key;
      }
    }

    /*NP-5471*/
    let governingPram = '15';
    if (privateObject.user && privateObject.user.governingArea) {
      if (privateObject.user.governingArea == '102') {
        //东
        governingPram = '11';
      } else if (privateObject.user.governingArea == '104') {
        //南
        governingPram = '12';
      } else if (privateObject.user.governingArea == '105') {
        //西
        governingPram = '13';
      } else if (privateObject.user.governingArea == '100') {
        //北
        governingPram = '14';
      }
    }
    const urltemp = privateObject.reportV4DirMap[governingPram];
    if (urltemp) {
      values.baseRptURL = urltemp;
    }

    // 合并打印
    if (values.isMerge == '1') {
      values.receivable_templt_id = undefined;
      values.itemLength = null;
      values.itemOrder = null;
    }

    if (selTmp) {
      // 批量选择账单模板
      param.isSelTemplts = '1';
      param.allTemplteIds = selData.join('|');
      if (!selData.length) {
        Modal.confirm({ content: '请选择要打印的账单模板' });
        return;
      }
    } else {
      param.isSelTemplts = '0';
      param.allTemplteIds = '';
    }

    // 获取IP，sheet数
    if (!param.appType) {
      console.log('11111')
      await sheetNumberService.requests({ ...values, ...param }).then((res) => {
        if (Number(res.result) === 0) {
          Modal.confirm({
            content: '没有可打印的账单数据',
          });
        } else if (res.result > 300 && !selTmp) {
          Modal.confirm({
            content: '账单打印大于最大打印数，请选择客户账套或取消分组，再次进行打印。',
          });
        } else {
          if (!selTmp) {
            param.groups = res.result;
          } else {
            param.groups = String(selData.length);
          }

          const rptQuery = { ...values, ...param };
          if (res.ip) {
            //  “//”开头的，它会判断当前的页面协议是http 还是 https 来决定请求 url 的协议
            // res.ip 是IP需要http，是域名需要https
            rptQuery.baseRptURL = res.ip.includes('http') ? res.ip : `http://${res.ip}`; //报表系统入口地址
          }
          rptQuery.modulePath = curMenu.reportUrl; //模块路径
          const servURL: string = generateReportURL(rptQuery, queryParams, privateObject);
          console.log('rptQuery', rptQuery)
          console.log('queryParams', queryParams)
          console.log('privateObject', privateObject)
          window.open(servURL, '_blank');
        }
      });
    } else {
      console.log('2222')
      values.signBranchTitleId = curCustTemp.signBrachTitleId;
      values.signBranchTitleName = curCustTemp.signBranchTitleName;
      values.billYm = values.bill_ym;
      values.customerId = values.cust_id;
      if (!selTmp) {
        if (!values.receivable_templt_id) {
          msgErr('请选择客户账套');
          return;
        }
        values.templteIds = curCustTemp.key;
      } else {
        const branchTitleId = {};
        const selectedTmpList = selTmpRef.current.reduce((acc, cur) => {
          branchTitleId[cur.signBranchTitleId]
            ? ''
            : (branchTitleId[cur.signBranchTitleId] = true && acc.push(cur));
          return acc;
        }, []);
        if (selectedTmpList.length !== 1) {
          msgErr('选择的账单模板签约方公司抬头不一致');
          return;
        }
        values.signBranchTitleId = selectedTmpList[0].signBranchTitleId;
        values.signBranchTitleName = selectedTmpList[0].signBranchTitleName;
        values.templteIds = selData.join(',');
      }
      const data = await selectHroCheckCount.request({ ...values });
      if (data.code === 200) {
        param.groups = String(data.data.count);
      } else {
        msgErr(data.message || '系统异常');
        return;
      }
      const eleSealRptQuery = { ...values, ...param };
      values.url = generateReportURL(eleSealRptQuery, queryParams, privateObject);
      await saveEfEleSealInfoService.request({ ...values, ...param }).then((res: any) => {
        if (res.code === 200) {
          msgOk('打印电子章保存成功， 数据正在生成中，稍后请查看已打印账单');
        } else {
          msgErr(res.message || '系统异常');
        }
      });
    }
  };
  // 选择批量账套
  const handleBatchBillTemp = (checked: boolean) => {
    setSelTmp(checked);
    if (checked) {
      form.setFieldsValue({
        receivable_templt_id: undefined,
        isGroup: '0',
        isMerge: '0',
        billType: '1',
      });
      setIsGroup('0');
      setIsMerge('0');
    }
  };

  const renderButtons = () => {
    return (
      <>
        <Row style={{ marginBottom: 8 }}>
          <AsyncButton
            onClick={async () => await handlePrint({ exportType: '1', isFinFormat: '0' })}
          >
            Excel打印
          </AsyncButton>
          <AsyncButton
            onClick={async () => await handlePrint({ exportType: '2', isFinFormat: '0' })}
          >
            PDF打印
          </AsyncButton>
          <AsyncButton
            onClick={async () => await handlePrint({ exportType: '1', isFinFormat: '1' })}
          >
            财务格式打印
          </AsyncButton>
          <AsyncButton
            onClick={async () =>
              await handlePrint({ exportType: '2', isFinFormat: '0', appType: 1 })
            }
            disabled={isMerge === '1' || isGroup === '1'}
          >
            打印电子章账单
          </AsyncButton>
          <AsyncButton onClick={async () => await handleViewElePrint({ appType: 1 })}>
            查看已打印账单
          </AsyncButton>
        </Row>
        <Row align="bottom">
          <Checkbox checked={selTmp} onChange={(e) => handleBatchBillTemp(e.target.checked)}>
            批量选择账单模板
          </Checkbox>
          {selTmp && (
            <AsyncButton type="primary" onClick={searchTemp}>
              查询
            </AsyncButton>
          )}
        </Row>
      </>
    );
  };
  // 选择账套
  const handleTempChange = (value: any, option: any) => {
    setCurCustTemp(option?.optiondata || {});
  };

  const formColumns: EditeFormProps[] = [
    {
      label: '客户',
      fieldName: 'cust_id',
      inputRender: () => (
        <CustomerPop
          rowValue="cust_id-custName"
          outerForm={form}
          initData={{ ...initialValues.cust, cust_id: initialValues?.cust?.custId || '' } as POJO}
          handdleConfirm={(value: any) => {
            setCustId(value.cust_id);
            setData([]);
            setSelData([]);
            form.setFieldsValue({ receivable_templt_id: undefined });
          }}
          keyMap={{
            cust_id: 'custId',
            custName: 'custName',
          }}
        />
      ),
      rules: [{ required: true, message: '请输入客户' }],
    },
    {
      label: '客户账套',
      fieldName: 'receivable_templt_id',
      rules: [{ required: isMerge === '0' && isGroup === '1', message: '请选择客户账套' }],
      inputRender: () => (
        <CommonDropSelector
          params={{
            statementName: 'receivable.getbillTempDropDownByCustId',
            custId: custId ? custId : form.getFieldsValue()?.cust_id,
          }}
          disabled={isMerge === '1' || selTmp}
          onChange={handleTempChange}
          onSuccess={(data) => {
            setCustTmepArr(data?.list || []);
          }}
        />
      ),
    },
    {
      label: '账单年月',
      fieldName: 'bill_ym',
      inputRender: 'month',
      rules: [{ required: true, message: '请输入账单年月' }],
      inputProps: {
        format: stdMonthFormatMoDash,
        onChange: () => {
          setData([]);
          setSelData([]);
        },
      },
    },
    {
      label: '账单排序',
      fieldName: 'bill_order',
      inputRender: () => mapToSelectors(billOrderGroup),
    },
    {
      label: '是否分组打印',
      fieldName: 'isGroup',
      inputRender: () =>
        mapToRatio(commonRadio, {
          defaultValue: '0',
          onChange: (e) => setIsGroup(e.target.value),
          disabled: isMerge === '1' || selTmp,
        }),
    },
    {
      label: '是否合并打印',
      fieldName: 'isMerge',
      inputRender: () =>
        mapToRatio(commonRadio, {
          defaultValue: '0',
          disabled: selTmp,
          onChange: (e) => {
            setIsMerge(e.target.value);
            if (e.target.value === '1') {
              form.setFieldsValue({ isGroup: '0' });
            }
          },
        }),
    },
    {
      label: '账单方式',
      fieldName: 'billType',
      inputRender: () => mapToRatio(billTypeRadio, { defaultValue: '1', disabled: selTmp }),
    },
  ];

  const formColumnsEOS: EditeFormProps[] = [
    {
      label: '客户名称',
      fieldName: 'custName',
      inputRender: () => (
        <CustomerPop
          rowValue="custId-custName"
          keyMap={{
            custId: 'custId',
            custName: 'custName',
          }}
        />
      ),
      rules: [{ required: true, message: '请输入客户' }],
    },
    {
      label: '客户账单年月',
      fieldName: 'billYm',
      inputRender: 'month',
      rules: [{ required: true, message: '请选择客户账单年月' }],
    },
    {
      label: '签单方分公司抬头',
      fieldName: 'signBranchTitleId',
      inputRender: () => (
        <GetBaseSelector
          params={{ statementName: 'OR_INVOICE_INFO.getSysBranchTitleList' }}
          onConfirm={(e) => {
            setSignBrachName(e);
          }}
          showSearch
          allowClear
        />
      ),
      rules: [{ required: !signBrachTitle, message: '签单方分公司抬头' }],
      // inputProps: { disabled: signBrachTitle },
    },
    {
      label: '打印模板',
      fieldName: 'type',
      inputRender: () => mapToSelectors(typeList),
      rules: [{ required: true, message: '请选择打印模板' }],
    },
  ];

  const eOSExportAndPrint = async (param: POVO) => {
    if (!param.appType) {
      setSignBrachTitle(true);
    }
    const values = await formEos.validateFields();
    values.baseRptURL = privateObject.reportDir; //报表系统入口地址
    values.modulePath = '/eos-np/ZBJ.jsp'; //模块路径
    if (param.appType) {
      values.signBranchTitleName = signBrachName.shortName;
      values.customerId = values.custId;
      values.printType = values.type;
      const data = await selectEosCheckCount.request({ ...values });
      if (data.code !== 200) {
        msgErr(data.message || '系统异常');
        return;
      }
      const rptQuery = { ...values, ...param };
      const servURL: string = generateReportURL(
        { ...rptQuery, groups: String(data.data.count) },
        queryParamsEOS,
        privateObject,
      );
      values.url = servURL;
      await saveEfEleSealInfoService.request({ ...values, ...param }).then((res: any) => {
        if (res.code === 200) {
          msgOk('打印电子章保存成功， 数据正在生成中，稍后请查看已打印账单');
        } else {
          msgErr(res.message || '系统异常');
        }
      });
    } else {
      const resCount = await API.report.reportFinance.getReceivableTempltCount.requests({
        custId: values.custId,
        billYM: values.billYm,
        signBranchTitleId: values.signBranchTitleId,
      });
      const rptQuery = { ...values, ...param };
      const servURL: string = generateReportURL(
        {
          ...rptQuery,
          groups: resCount.count.toString(),
        },
        queryParamsEOS,
        privateObject,
      );
      window.open(servURL, '_blank');
      setTimeout(() => {
        setSignBrachTitle(false);
      }, 100);
    }
  };
  const handleViewElePrint = async (param: POVO) => {
    const values =
      param.appType === 1 ? await form.validateFields() : await formEos.validateFields();
    values.signBranchTitleName = signBrachName.shortName;
    if (param.appType === 2) {
      values.printType = values.type;
      values.customerId = values.custId;
    } else {
      values.billYm = values.bill_ym;
      values.customerId = values.cust_id;
    }
    setFormValues({ ...values, ...param });
    setVisible(true);
  };
  return (
    <Card>
      <Tabs
        className="ant-card-height"
        defaultActiveKey="baseGrid"
        type="card"
        onTabClick={(key: any) => setKey(key)}
      >
        <Tabs.TabPane tab="系统账单打印" key="HRO" forceRender={true}>
          <CachedPage
            service={listService}
            formColumns={formColumns}
            columns={columns}
            renderButtons={renderButtons}
            form={form}
            cardProps={{
              style: { minHeight: '500px' },
            }}
          >
            {selTmp && (
              <Spin spinning={loading}>
                <TableTransfer
                  data={data}
                  handleChange={(selData) =>{console.log('2121212',selData); setSelData(selData)}}
                  handlefilteredItemsData={(data) => {
                    console.log('data', data);
                    selTmpRef.current = data;
                  }}
                />
              </Spin>
            )}
          </CachedPage>
        </Tabs.TabPane>
        <Tabs.TabPane tab="EOS账单打印" key="EOS" forceRender={true} style={{ minHeight: '500px' }}>
          <FormElement4 form={formEos}>
            <EnumerateFields outerForm={formEos} colNumber={4} formColumns={formColumnsEOS} />
            <Space size="middle">
              <Button type="primary" onClick={() => eOSExportAndPrint({})}>
                导出
              </Button>
              <AsyncButton onClick={() => eOSExportAndPrint({ appType: 2, isEle: '1' })}>
                打印电子章账单
              </AsyncButton>
              <AsyncButton onClick={async () => await handleViewElePrint({ appType: 2 })}>
                查看已打印账单
              </AsyncButton>
            </Space>
          </FormElement4>
        </Tabs.TabPane>
      </Tabs>
      <ViewBillTempPrint modal={[visible, setVisible]} formValues={formValues} type={key} />
    </Card>
  );
};

export default BillPrintReport;
