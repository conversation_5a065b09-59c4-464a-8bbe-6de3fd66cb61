import * as defs from '../../baseClass';
import {
  fetch,
  RequestConfig,
  beforeFetch,
  afterFetch,
  checkResStatus,
  removeSpace,
} from '@/utils/request';

/**
     * @url /rhro-service-1.0/rpac/tax/notmatch/list
     * @desc 查询匹配失败的报税信息
查询匹配失败的报税信息
     * hasForm: false
     * hasBody: true
     */

export class Params {}

export const init = new defs.welfaremanage.FilterEntity();
export const url = '/rhro-service-1.0/rpac/tax/notmatch/list:POST';
export const initialUrl = '/rhro-service-1.0/rpac/tax/notmatch/list';
export const cacheKey = '_rpac_tax_notmatch_list_POST';
export async function request(
  data: defs.welfaremanage.FilterEntity,
  options?: RequestConfig,
) {
  const reqUrl = `/rhro-service-1.0/rpac/tax/notmatch/list`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          reolve(res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
export async function requests(
  data: defs.welfaremanage.FilterEntity,
  options?: RequestConfig,
) {
  const reqUrl = `/rhro-service-1.0/rpac/tax/notmatch/list`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          if (checkResStatus(res)) return reolve(res ? res.data : res);
          reject(res ? res.message : res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
