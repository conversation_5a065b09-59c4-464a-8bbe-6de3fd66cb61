import React, { useState, useEffect } from 'react';
import { Button, Modal, Typography } from 'antd';
import { WritableColumnProps } from '@/utils/writable/types';
import { CachedPage } from '@/components/CachedPage';
import { EditeFormProps } from '@/components/EditeForm';
import { AreaSelector, GetBaseSelector, mapToSelectors } from '@/components/Selectors';
import { freezeStatusMap, alllowMap, statusMap } from '@/utils/settings/finance/upload';
import { formatAmount } from '@/utils/methods/format';
import ImportForm from '@/components/UploadForm/ImportForm';
import { msgErr, msgOk } from '@/utils/methods/message';
import { downloadFile, downloadFileWithId } from '@/utils/methods/file';
import { WritableInstance } from '@/components/Writable';
import { isEmpty } from 'lodash';
import { Calculator } from '@/utils/methods/calculator';
import VerifyMoreReceivable from './components/VerifyMoreReceivable';
import { getUserId, getCurrentUser, getUserRole, getUserGoverningBranch } from '@/utils/model';
import { AsyncButton } from '@/components/Forms/Confirm';
import InvoiceModal from './components/InvoiceModal';
import { BizUtil } from '@/utils/settings/bizUtil';
import ReceiptsDetails from './components/ReceiptsDetails';
import LinkForCustomerBalance from './components/LinkForCustomerBalance';
import AllowancePay from './components/AllowancePay';
import { AuthButtons } from '@/components/Forms/RenderButtons';
import { stdDateFormat } from '@/utils/methods/times';
import { DateRange } from '@/components/DateRange4';
import { SelectInnerEmpAmbPop } from '@/components/StandardPop/SelectInnerEmpAmb';
import PaymentOfBenefits from './components/PaymentOfBenefits';
import PaymentDetailList from './components/PaymentDetailList';

let _options: WritableInstance | undefined;
const Query: React.FC<POJO> = () => {
  const service = API.finance.uploadCashRecord.getCashRecordDetail;
  const [deptmentMap, setDeptmentMap] = useState<Map<number, string>>(new Map());
  const [invoiceParam, setInvoiceParam] = useState<POJO>({}); // 开票Modal

  const [verifyMed, setVerifyMed] = useState<string>('2'); // 核销模式
  const [verifyVisible, setVerifyVisible] = useState<boolean>(false); // 核销Modal是否可视

  const userId = getUserId();
  const realName = getCurrentUser().profile.realName;
  const governingBranch = getUserGoverningBranch();
  const financeManager = !!getUserRole().find((e: POJO) => e.roleCode === BizUtil.ROLE_FINANCE_MGR);
  const cseditable = !!getUserRole().find((e: POJO) => e.roleCode === BizUtil.ROLE_CS);
  const fneditable = !!getUserRole().find((e: POJO) => e.roleCode === BizUtil.ROLE_FINANCE_TSR);
  const [singleRow, setSingleRow] = useState<POJO>({});
  const [receiptsVisible, setReceiptsVisible] = useState<boolean>(false); // 已核销金额Modal
  const [unreceiptsVisible, setUnreceiptsVisible] = useState<boolean>(false); // 为核销金额Modal
  const [allowanceVisible, setAllowanceVisible] = useState<boolean>(false); // 津贴支付Modal
  const [treatmentAmtVisible, setTreatmentAmtVisible] = useState<boolean>(false); // 待遇支付明细Modal
  const [treatmentAmtList, setTreatmentAmtList] = useState<any[]>([]); // 待遇支付明细list
  const [allowPaymentOfBenefitsVisible, setAllowPaymentOfBenefitsVisible] =
    useState<boolean>(false); // 待遇支付Modal
  const exportColunms = [
    { title: '到款编号', dataIndex: 'cashId' },
    { title: '到款客户', dataIndex: 'cashUser' },
    { title: '到款时间', dataIndex: 'cashDt' },
    { title: '到款金额', dataIndex: 'cashAmount' },
    { title: '到款备注', dataIndex: 'cashMark' },
    { title: '上传时间', dataIndex: 'createDt' },
    { title: '客户名称', dataIndex: 'custName' },
    { title: '财务应收年月', dataIndex: 'finMonth' },
    { title: '客户账套', dataIndex: 'templtName' },
    { title: '核销金额', dataIndex: 'verifyDetailAmt' },
    { title: '审批状态', dataIndex: 'approvalStatus' },
  ];
  const formColumns: EditeFormProps[] = [
    {
      label: '大区',
      fieldName: 'areaId',
      inputRender: () => <AreaSelector allowClear onChange={(val: any) => requestDepart(val)} />,
    },
    {
      label: '分公司',
      fieldName: 'providerIds',
      inputRender: () => mapToSelectors(deptmentMap, { allowClear: true, showSearch: true }),
    },
    {
      label: '',
      fieldName: '',
      inputRender: () => (
        <DateRange
          fields={[
            {
              title: '到款时间>=',
              dataIndex: 'cashDtStart',
            },
            {
              title: '到款时间<=',
              dataIndex: 'cashDtEnd',
            },
          ]}
          format={stdDateFormat}
        />
      ),
    },
    { label: '到款金额>=', fieldName: 'arriveAmtS', inputRender: 'number' },
    { label: '到款金额<=', fieldName: 'arriveAmtE', inputRender: 'number' },
    { label: '余额>=', fieldName: 'balanceSt', inputRender: 'number' },
    { label: '余额<=', fieldName: 'balanceEd', inputRender: 'number' },
    { label: '未核销金额>=', fieldName: 'unVerifyamtSt', inputRender: 'number' },
    { label: '未核销金额<=', fieldName: 'unVerifyamtEd', inputRender: 'number' },
    { label: '到款客户', fieldName: 'cashUser', inputRender: 'string' },
    {
      label: '是否有效',
      fieldName: 'state',
      inputRender: () => mapToSelectors(statusMap, { allowClear: true }),
    },
    {
      label: '',
      fieldName: '',
      inputRender: () => (
        <DateRange
          fields={[
            {
              title: '上传时间>=',
              dataIndex: 'createDtSt',
            },
            {
              title: '上传时间<=',
              dataIndex: 'createDtEd',
            },
          ]}
          format={stdDateFormat}
        />
      ),
    },
    {
      label: '冻结状态',
      fieldName: 'freeze',
      inputRender: () => mapToSelectors(freezeStatusMap, { allowClear: true }),
    },
    {
      label: '签单分公司抬头',
      fieldName: 'titleId',
      inputRender: () => (
        <GetBaseSelector
          params={{ statementName: 'OR_INVOICE_INFO.getSysBranchTitleList' }}
          showSearch
          allowClear
        />
      ),
    },
    {
      label: '核销人',
      fieldName: 'verifyBy',
      inputRender: () => (
        <SelectInnerEmpAmbPop
          title=""
          rowValue="verifyBy-verifyByName"
          keyMap={{
            verifyBy: 'EMPID',
            verifyByName: 'REALNAME',
          }}
        />
      ),
    },
    { label: '付款方名称', fieldName: 'payerName', inputRender: 'string' },
    { label: '付款方账号', fieldName: 'payerBankAcct', inputRender: 'string' },
    { label: '到款备注', fieldName: 'cashMark', inputRender: 'string' },
  ];
  const columns: WritableColumnProps<defs.finance.OrUploadCashRecordDetail>[] = [
    { title: '到款编号', dataIndex: 'cashId' },
    { title: '到款客户', dataIndex: 'cashUser' },
    { title: '到款时间', dataIndex: 'cashDt' },
    { title: '到款银行', dataIndex: 'cashBank' },
    { title: '银行流水号', dataIndex: 'bankSerialNo' },
    { title: '银行账号', dataIndex: 'bankAccount' },
    { title: '银行顺序号', dataIndex: 'sequenceNumber' },
    { title: '到款金蝶分公司编号', dataIndex: 'jindieOrgId' },
    { title: '签单分公司抬头', dataIndex: 'titleName' },
    { title: '上传人', dataIndex: 'realName' },
    { title: '核销人', dataIndex: 'verifyBy' },
    { title: '大区', dataIndex: 'areaName' },
    { title: '分公司', dataIndex: 'providerName', width: 275 },
    { title: '上传时间', dataIndex: 'createDt' },
    { title: '到款金额', dataIndex: 'cashAmount', render: formatAmount },
    { title: '到款备注', dataIndex: 'cashMark' },
    { title: '付款方名称', dataIndex: 'payerName' },
    { title: '付款方账号', dataIndex: 'payerBankAcct' },
    { title: '来源', dataIndex: 'source' },
    {
      title: '财务备注',
      dataIndex: 'financeMark',
      width: 200,
      inputProps: { disabled: !fneditable },
      inputRender: 'tableText',
    },
    {
      title: '客服备注',
      dataIndex: 'customerMark',
      width: 200,
      inputProps: { disabled: !cseditable },
      inputRender: 'tableText',
    },
    { title: '实收金额', dataIndex: 'receiptsAmt', render: formatAmount },
    { title: '已支付津贴金额', dataIndex: 'allwanceAmt', render: formatAmount },
    {
      title: '已支付待遇金额',
      dataIndex: 'treatmentAmt',
      render: (value, r) => (
        <Typography.Link
          onClick={(e) => {
            e.preventDefault();
            treatmentAmtClick(r);
          }}
        >
          {formatAmount(value)}
        </Typography.Link>
      ),
    },
    { title: '余额', dataIndex: 'balance', render: formatAmount },
    {
      title: '已核销金额',
      dataIndex: 'verifyAmt',
      render: (value, r) => (
        <Typography.Link
          onClick={(e) => {
            e.preventDefault();
            setSingleRow(r);
            verifyAmtClick();
          }}
        >
          {formatAmount(value)}
        </Typography.Link>
      ),
    },
    {
      title: '未核销金额',
      dataIndex: 'unverifyAmt',
      render: (value, r) => {
        return (
          <Typography.Link
            onClick={(e) => {
              e.preventDefault();
              setSingleRow(r);
              unverifyAmtClick();
            }}
          >
            {formatAmount(value)}
          </Typography.Link>
        );
      },
    },
    { title: '状态', dataIndex: 'stateText' },
    { title: '冻结状态', dataIndex: 'freezeName' },
    { title: '费用属性', dataIndex: 'allowanceType', render: (value) => alllowMap.get(+value) },
    {
      title: '津贴支付上次文件',
      dataIndex: 'allowanceFileName',
      render: (value, record) => {
        return (
          <Typography.Link
            onClick={(e) => {
              e.preventDefault();
              download(record.allowanceFileId, record.allowanceFileName);
            }}
          >
            {value}
          </Typography.Link>
        );
      },
    },
  ];

  useEffect(() => {
    requestDepart();
  }, []);

  const treatmentAmtClick = async (row: any) => {
    const res = await API.finance.uploadCashRecord.getTreatmentDetail.requests({
      cashId: row.cashId,
    });
    setTreatmentAmtList(res?.list || []);
    setTreatmentAmtVisible(true);
  };

  const verifyAmtClick = () => {
    setReceiptsVisible(true);
  };

  const unverifyAmtClick = () => {
    setUnreceiptsVisible(true);
  };

  const requestDepart = async (areaId?: string) => {
    const params = {
      departmentGrade: '3',
      providerType: '1',
      governingAreaId: areaId,
    };
    const res = await API.admin.department.getDepartmentDropdownList.requests(params);
    const map = new Map<number, string>();
    res.list.forEach((e: any) => map.set(+e.key, e.shortName));
    setDeptmentMap(map);
  };

  /** 下载文件 */
  const download = async (fileId?: string, fileName?: string) => {
    if (!fileId) return msgErr('文件不存在');
    downloadFileWithId(fileId, fileName || '');
  };

  /** 核销 */
  const toVerifyAmt = () => {
    const selected = _options?.selectedSingleRow;
    if (isEmpty(selected)) return msgErr('请选择需要用做核销的网银记录!');
    const cashAmount = selected.cashAmount;
    const receiptsAmt = selected.receiptsAmt;
    const state = selected.state;
    const overage = Calculator.subtract(cashAmount, receiptsAmt);
    if (overage > 0 && state !== '2') {
      if (selected.freeze === '1') return msgErr('已冻结,不能核销!');
      if (selected.allowanceType && selected.allowanceType != '1') return msgErr('不能用来核销!');
      setVerifyVisible(true);
    } else {
      msgErr('本条记录余额不足以再次进行核销!');
    }
  };

  /** 转到商保 */
  const toCommercialInsurance = async () => {
    const selected = _options?.selectedSingleRow;
    if (isEmpty(selected)) return msgErr('请选择需要用做商保的网银记录!');
    const cashAmount = selected.cashAmount;
    const receiptsAmt = selected.receiptsAmt;
    const state = selected.state;
    const overage = Calculator.subtract(cashAmount, receiptsAmt);
    if (overage > 0 && state !== '2') {
      if (selected.freeze === '1') return msgErr('已冻结,不能转到商保!');
      // 1变4，0变5
      if (selected.allowanceType && selected.allowanceType != '1') return msgErr('不能转到商保!');
      await API.finance.uploadCashRecord.updateAllowanceType.requests({
        ...selected,
        allowanceType: selected.allowanceType ? '4' : '5',
      });
      msgOk('操作成功');
      _options?.request(_options.queries);
    } else {
      msgErr('本条记录余额不足以转到商保!');
    }
  };

  /** 设置无效 */
  const onSetInvalid = () => {
    const row = _options?.selectedSingleRow;
    if (isEmpty(row)) return msgErr('请选择数据');
    Modal.confirm({
      content: '是否作废?',
      onOk: async () => {
        const receiptsAmt = +row.receiptsAmt;
        const state = row.state;
        if (receiptsAmt === 0) {
          if (state === '2') {
            msgErr('该记录已经作废');
          } else {
            await API.finance.uploadCashRecord.updateCashRecordDetail.requests({
              cashId: row.cashId,
              state: '2',
            });
            msgOk('修改成功');
            _options?.request(_options.queries);
          }
        } else {
          msgErr('该记录存在实收记录,无法作废!');
        }
      },
    });
  };

  /** 保存备注 */
  const onSaveRemark = async () => {
    const res = _options?.getList();
    if ((res?.updatedCount || 0) <= 0) return msgErr('没有可操作的数据');
    if (!fneditable && !cseditable) return msgErr('请登录财务或者客服账号');
    const updated = (res?.updated || []) as POJO[];
    const list = updated.map((e) => ({
      cashId: e.cashId,
      financeMark: e.financeMark,
      customerMark: e.customerMark,
      realName: realName,
    }));
    await API.finance.uploadCashRecord.updateRecordDetailR.requests(list);
    msgOk('更新成功');
    _options?.request(_options.queries);
  };

  /** 冻结 */
  const onFreeze = async () => {
    const rows = _options?.selectedRows as POJO[];
    if (isEmpty(rows)) return msgErr('请选择数据');
    const list: POJO[] = [];
    for (let i = 0; i < rows.length; i++) {
      const obj = rows[i];
      const cashAmount = +(obj.cashAmount || 0);
      const receiptsAmt = +(obj.receiptsAmt || 0);
      const overage = Calculator.subtract(cashAmount, receiptsAmt);
      if (overage <= 0) return msgErr('没有余额的记录不能冻结');
      list.push({
        cashId: obj.cashId,
        freeze: '1',
        logType: '1',
        createBy: userId,
        createDt: '1',
      });
      await API.finance.uploadCashRecord.updateRecordDetailR.requests(list);
      msgOk('保存成功');
      _options?.request(_options.queries);
    }
  };

  /** 解冻 */
  const onUnfreeze = async () => {
    const row = _options?.selectedSingleRow as POJO;
    if (isEmpty(row)) return msgErr('请选择数据');
    if (row.freeze !== '1') return msgErr('只有已冻结记录才能解冻');
    const list = [
      {
        cashId: row.cashId,
        freeze: '0',
        logType: '0',
        createBy: userId,
        createDt: '1',
      },
    ];
    await API.finance.uploadCashRecord.updateRecordDetailR.requests(list);
    msgOk('保存成功');
    _options?.request(_options.queries);
  };

  /** 导出 */
  const onExport = async () => {
    await _options?.handleExport(
      { service: API.finance.uploadCashRecord.toDownLoad },
      {
        columns: columns.map((e) => {
          if (e.dataIndex == 'allowanceType') {
            return {
              ...e,
              dataIndex: 'allowanceTypeStr',
            };
          }
          return e;
        }),
        fileName: '网银记录.xls',
      },
    );
  };
  /** 导出明细 */
  const onExportDetail = async () => {
    await _options?.handleExport(
      { service: API.finance.uploadCashRecord.toDownLoad },
      {
        columns: exportColunms,
        condition: { exporterFlag: 'receiptsDetail' },
        fileName: '网银明细.xls',
      },
    );
  };
  /** 申请津贴支付 */
  const onPayAllow = async () => {
    const row = _options?.selectedSingleRow;
    if (isEmpty(row)) return msgErr('请选择数据');
    if (row.freezeName !== '解冻') return msgErr('冻结状态必须为解冻!');
    if (row.stateText !== '有效') return msgErr('状态必须为有效!');
    if (row.allowanceType && row.allowanceType != '2') return msgErr('不能申请津贴支付!');
    if (+(row.cashAmount || 0) - +(row.balance || 0) !== 0 && row.allowanceType?.toString() !== '2')
      return msgErr('余额必须等于到款金额!');
    await API.welfaremanage.socialManage.getPayAllowwanceDetail.requests({
      cashId: row.cashId,
      processIdisNotNull: '1',
    });
    setAllowanceVisible(true);
  };

  /** 预检测 */
  const beforeUpload = async () => {
    const row = _options?.selectedSingleRow;
    if (isEmpty(row)) {
      msgErr('请先选择数据!');
      return false;
    }
    if (row.freezeName !== '解冻') {
      msgErr('冻结状态必须为解冻!');
      return false;
    }
    if (row.stateText !== '有效') {
      msgErr('状态必须为有效!');
      return false;
    }
    if (row.allowanceType?.toString() === '1') {
      msgErr('费用属性不能是核销金额!');
      return false;
    }
    if (row.allowanceType?.toString() === '3') {
      msgErr('费用属性不能是待遇金额!');
      return false;
    }
    if (
      +(row.cashAmount || 0) - +(row.balance || 0) !== 0 &&
      row.allowanceType?.toString() !== '2'
    ) {
      msgErr('余额必须等于到款金额!');
      return false;
    }
    const res = await API.welfaremanage.socialManage.getPayAllowwanceDetailStatus.requests({
      id: row.cashId,
    });
    if (+(res || 0) > 0) {
      msgErr('已提交过审批，不能执行导入!');
      return false;
    }
    return true;
  };

  const handleQueries = (values?: POJO) => {
    const row = _options?.selectedSingleRow;
    return {
      ...values,
      parameters: { cashId: row.cashId },
    } as POJO;
  };

  /** 上传完成之后 */
  const afterUpload = () => {
    _options?.request(_options.queries);
  };

  // 申请待遇支付
  const onPaymentOfBenefits = async () => {
    const row = _options?.selectedSingleRow;
    if (isEmpty(row)) return msgErr('请选择数据');
    if (row.freezeName !== '解冻') return msgErr('冻结状态必须为解冻!');
    if (row.stateText !== '有效') return msgErr('状态必须为有效!');
    if (row.allowanceType == '1' || row.allowanceType == '2') return msgErr('不能申请待遇支付!');
    setAllowPaymentOfBenefitsVisible(true);
  };

  // 不收该付款方
  const onNoPayer = async () => {
    const row = _options?.selectedSingleRow;
    if (isEmpty(row)) return msgErr('请选择数据');
    if (row?.source !== '自动上传') return msgErr('仅沐融的数据可用');
    const _governingBranch = row.providerId;
    if (
      (!row.payerBankAcct || row.payerBankAcct == '') &&
      (!row.payerName || row.payerName == '')
    ) {
      return msgErr('付款方关键信息缺失，无法添加');
    }
    if (financeManager && _governingBranch == governingBranch) {
      await API.finance.uploadCashRecord.toCustpayerBlack.requests(row);
      msgOk('操作成功');
      _options?.request();
    } else {
      msgErr('仅收款账号对应分公司的分公司财务经理可以操作');
    }
  };

  const renderButton = (options: WritableInstance) => {
    _options = options;
    return (
      <React.Fragment>
        <AuthButtons funcId="********">
          <Button type="primary" htmlType="submit">
            查询
          </Button>
        </AuthButtons>
        <AuthButtons funcId="********">
          <Button onClick={toVerifyAmt}>核销</Button>
        </AuthButtons>
        <AuthButtons funcId="********">
          <Button onClick={onSetInvalid}>作废</Button>
        </AuthButtons>
        <AuthButtons funcId="********">
          <AsyncButton onClick={onSaveRemark}>保存备注</AsyncButton>
          <AsyncButton onClick={onExport} disabled={isEmpty(_options.queries)}>
            导出数据
          </AsyncButton>
        </AuthButtons>
        <AuthButtons funcId="50301050">
          <AsyncButton onClick={onFreeze}>冻结</AsyncButton>
        </AuthButtons>
        <AuthButtons funcId="50301060">
          <AsyncButton onClick={onUnfreeze}>解冻</AsyncButton>
        </AuthButtons>
        <ImportForm
          btnName="导入"
          downBtnName="下载模板"
          fileSuffix=".xls"
          ruleId="900000280"
          serviceName="socialManageService"
          btnEnable
          showImpHisBtn
          beforeUpload={beforeUpload}
          handleQueries={handleQueries}
          afterUpload={afterUpload}
          selectedRec={isEmpty(_options.selectedSingleRow) ? [] : [_options.selectedSingleRow]}
        />
        <AsyncButton onClick={onPayAllow}>申请津贴支付</AsyncButton>
        {/* 权限暂定 */}
        <AuthButtons funcId="50301070">
          <AsyncButton onClick={onPaymentOfBenefits}>申请待遇支付</AsyncButton>
        </AuthButtons>
        <AuthButtons funcId="50301080">
          <Button onClick={toCommercialInsurance}>转到商保</Button>
        </AuthButtons>
        <AsyncButton onClick={onNoPayer}>不收该付款方</AsyncButton>
        <AsyncButton onClick={onExportDetail} disabled={isEmpty(_options.queries)}>
          导出明细
        </AsyncButton>
      </React.Fragment>
    );
  };

  const renderModals = () => {
    return (
      <React.Fragment>
        <VerifyMoreReceivable
          visible={verifyVisible}
          data={_options?.selectedSingleRow}
          hideHandle={(refresh, params) => {
            setVerifyVisible(false);
            refresh && _options?.request(_options?.queries);
            if (params) {
              setInvoiceParam(params);
            }
          }}
        />
        <InvoiceModal
          visible={!isEmpty(invoiceParam)}
          receivableIds={invoiceParam.receivableIds}
          data={{
            custId: invoiceParam.custId,
            custName: invoiceParam.custName,
          }}
          hideHandle={() => setInvoiceParam({})}
        />
        <ReceiptsDetails
          visible={receiptsVisible}
          hideHandle={() => setReceiptsVisible(false)}
          param={{ cashId: singleRow?.cashId }}
        />
        <LinkForCustomerBalance
          visible={unreceiptsVisible}
          hideHandle={() => setUnreceiptsVisible(false)}
          param={singleRow?.cashId}
        />
        <AllowancePay
          visible={allowanceVisible}
          hideHandle={() => setAllowanceVisible(false)}
          data={_options?.selectedSingleRow}
        />
        <PaymentOfBenefits
          visible={allowPaymentOfBenefitsVisible}
          hideHandle={() => setAllowPaymentOfBenefitsVisible(false)}
          data={_options?.selectedSingleRow}
        />
        <PaymentDetailList
          visible={treatmentAmtVisible}
          list={treatmentAmtList || []}
          hideHandle={() => setTreatmentAmtVisible(false)}
        />
      </React.Fragment>
    );
  };

  return (
    <React.Fragment>
      <CachedPage
        cardProps={{ bordered: false, bodyStyle: { padding: 0 } }}
        service={service}
        formColumns={formColumns}
        columns={columns}
        renderButtons={renderButton}
        cached
        editable
        initialValues={{ state: 1 }}
      />
      {renderModals()}
    </React.Fragment>
  );
};

export default Query;
