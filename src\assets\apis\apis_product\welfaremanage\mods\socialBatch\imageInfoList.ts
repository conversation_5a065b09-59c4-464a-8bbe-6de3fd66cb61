import * as defs from '../../baseClass';
import {
  fetch,
  RequestConfig,
  beforeFetch,
  afterFetch,
  checkResStatus,
  removeSpace,
} from '@/utils/request';

/**
     * @url /rhro-service-1.0/socialBatch/imageInfoList
     * @desc 根据mainid和uuid获取证件
根据mainid和uuid获取证件
     * hasForm: false
     * hasBody: true
     */

export class Params {
  /** mainId */
  mainId: string;
  /** uuid */
  uuid: string;
}

export const init = new defs.welfaremanage.FilterEntity();
export const url = '/rhro-service-1.0/socialBatch/imageInfoList:GET';
export const initialUrl = '/rhro-service-1.0/socialBatch/imageInfoList';
export const cacheKey = '_socialBatch_imageInfoList_GET';
export async function request(params: Params, options?: RequestConfig) {
  const reqUrl = `/rhro-service-1.0/socialBatch/imageInfoList`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',
    params: removeSpace(params),

    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .get(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          reolve(res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
export async function requests(params: Params, options?: RequestConfig) {
  const reqUrl = `/rhro-service-1.0/socialBatch/imageInfoList`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',
    params: removeSpace(params),

    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .get(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          if (checkResStatus(res)) return reolve(res ? res.data : res);
          reject(res ? res.message : res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
