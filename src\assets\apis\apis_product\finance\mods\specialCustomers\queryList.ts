import * as defs from '../../baseClass';
import {
  fetch,
  RequestConfig,
  beforeFetch,
  afterFetch,
  checkResStatus,
  removeSpace,
} from '@/utils/request';

/**
     * @url /rhro-service-1.0/specialCustomers/queryList
     * @desc 根据条件分页查询结果
根据条件分页查询结果
     * hasForm: false
     * hasBody: true
     */

export class Params {}

export const init = new defs.finance.Page();
export const url = '/rhro-service-1.0/specialCustomers/queryList:POST';
export const initialUrl = '/rhro-service-1.0/specialCustomers/queryList';
export const cacheKey = '_specialCustomers_queryList_POST';
export async function request(
  data: defs.finance.SpecialCustomersQuery,
  options?: RequestConfig,
) {
  const reqUrl = `/rhro-service-1.0/specialCustomers/queryList`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          reolve(res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
export async function requests(
  data: defs.finance.SpecialCustomersQuery,
  options?: RequestConfig,
) {
  const reqUrl = `/rhro-service-1.0/specialCustomers/queryList`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          if (checkResStatus(res)) return reolve(res ? res.data : res);
          reject(res ? res.message : res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
