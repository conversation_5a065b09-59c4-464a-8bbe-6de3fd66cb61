class CommonResponse {
  /** bizCode */
  bizCode = undefined;

  /** code */
  code = undefined;

  /** data */
  data = undefined;

  /** message */
  message = '';

  /** t */
  t = undefined;
}

class DelBillQuery {
  /** applyerAreaId */
  applyerAreaId = '';

  /** applyerBranchId */
  applyerBranchId = '';

  /** billYm */
  billYm = '';

  /** 创建人 */
  createBy = '';

  /** 客户ID */
  custId = '';

  /** endIndex */
  endIndex = undefined;

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** 供应商ID */
  pdProviderId = '';

  /** 帐套ID */
  receivableTempltId = '';

  /** startIndex */
  startIndex = undefined;
}

class ExBillDTO {
  /** add */
  add = false;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** empIds */
  empIds = '';

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** receivableVersionid */
  receivableVersionid = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** serviceMonth */
  serviceMonth = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** type */
  type = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class ExOneCharges {
  /** add */
  add = false;

  /** 金额 */
  amount = undefined;

  /** areaId */
  areaId = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 账单年月 */
  billYm = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** 财务应收年月 */
  finReceiableYm = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** 人数 */
  headCount = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 金额 */
  oldamount = undefined;

  /** onecharesId */
  onecharesId = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 产品id */
  productId = '';

  /** 产品线 */
  productLine = '';

  /** 产品名称 */
  productName = '';

  /** 产品大类 */
  productTypeId = '';

  /** providerId */
  providerId = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** quotationId */
  quotationId = '';

  /** quotationItemId */
  quotationItemId = '';

  /** receivableId */
  receivableId = '';

  /** 帐套名称 */
  receivableTempltName = '';

  /** 注释 */
  remark = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** templtId */
  templtId = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class ExOneChargesDTO {
  /** add */
  add = false;

  /** 金额 */
  amount = undefined;

  /** areaId */
  areaId = '';

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 账单年月 */
  billYm = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** del */
  del = false;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** 财务应收年月 */
  finReceiableYm = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** 人数 */
  headCount = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** 金额 */
  oldamount = undefined;

  /** onecharesId */
  onecharesId = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 产品id */
  productId = '';

  /** 产品线 */
  productLine = '';

  /** 产品名称 */
  productName = '';

  /** 产品大类 */
  productTypeId = '';

  /** providerId */
  providerId = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** quotationId */
  quotationId = '';

  /** quotationItemId */
  quotationItemId = '';

  /** receivableId */
  receivableId = '';

  /** 帐套名称 */
  receivableTempltName = '';

  /** 注释 */
  remark = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** templtId */
  templtId = '';

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';
}

class ExportQuery {
  /** 查询条件 */
  condition = undefined;

  /** 表头字段列表 */
  fieldArr = [];

  /** 表头字段中文拼接 */
  headStr = '';

  /** 表头字段类型列表 */
  typeArr = [];
}

class GenerateBillContionDTO {
  /** 大区ID */
  areaId = '';

  /** 账单年月 */
  billYm = '';

  /** billYmEd */
  billYmEd = '';

  /** billYmSt */
  billYmSt = '';

  /** billtempId */
  billtempId = '';

  /** chargeDTOs */
  chargeDTOs = [];

  /** chargesDTOs */
  chargesDTOs = [];

  /** contractIds */
  contractIds = '';

  /** createDt */
  createDt = '';

  /** 生成人 */
  creater = '';

  /** custCode */
  custCode = '';

  /** 客户ID */
  custId = '';

  /** custName */
  custName = '';

  /** 部门ID */
  departmentId = '';

  /** empCode */
  empCode = '';

  /** 雇员ID */
  empId = '';

  /** empName */
  empName = '';

  /** endIndex */
  endIndex = undefined;

  /** 应收年月 */
  finReceivableYm = '';

  /** finReceivableYmEd */
  finReceivableYmEd = '';

  /** finReceivableYmSt */
  finReceivableYmSt = '';

  /** genType */
  genType = '';

  /** ids */
  ids = '';

  /** invoiceStatus */
  invoiceStatus = '';

  /** 是否锁定 */
  isLocked = '';

  /** 是否支付申请 */
  isPayed = '';

  /** isoldData */
  isoldData = '';

  /** 0 客户一次性项目 1 外包一次性税费 */
  onechargesType = '';

  /** 帐套ID */
  pBillTempltId = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数 */
  pageSize = undefined;

  /** payeeId */
  payeeId = '';

  /** 账单方ID */
  payerId = '';

  /** 产品ID */
  productId = '';

  /** 大区ID */
  providerId = '';

  /** 报价类型 */
  quotationItemType = '';

  /** receivableAmt */
  receivableAmt = '';

  /** receivableAmtEd */
  receivableAmtEd = '';

  /** receivableAmtSt */
  receivableAmtSt = '';

  /** 应收主记录ID */
  receivableId = '';

  /** 帐套ID */
  receivableTempltId = '';

  /** 应收ID串 */
  receivableTempltIds = '';

  /** receivableTempltName */
  receivableTempltName = '';

  /** 账单版本ID */
  receivableVersionId = '';

  /** genType */
  sdr = '';

  /** 服务年月 */
  serviceMonth = '';

  /** 社保组ID */
  ssGroupId = '';

  /** startIndex */
  startIndex = undefined;

  /** status */
  status = '';

  /** 帐套ID */
  tempId = '';

  /** tempIdArray */
  tempIdArray = '';

  /** userId */
  userId = '';

  /** verifyStatus */
  verifyStatus = '';

  /** wageIds */
  wageIds = '';

  /** zdr */
  zdr = '';
}

class HashMap {}

class Map {}

class OneChargesDTO {
  /** add */
  add = false;

  /** 金额 */
  amount = undefined;

  /** amtNoTax */
  amtNoTax = undefined;

  /** atr */
  atr = undefined;

  /** 批次号,用于备份 */
  batchId = '';

  /** 账单表别名,控制客户权限用 */
  billAlias = '';

  /** 账单年月 */
  billYm = '';

  /** 财务大类 */
  bizCategory = '';

  /** 业务类型,控制小合同权限用 */
  bizmanType = '';

  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias = '';

  /** clientOperation */
  clientOperation = undefined;

  /** flex是否行编号 */
  clientRowSeq = undefined;

  /** flex是否选择 */
  clientSelected = false;

  /** 合同表别名,控制合同权限用 */
  contractAlias = '';

  /** contractId */
  contractId = '';

  /** 创建人 */
  createBy = '';

  /** createBy2 */
  createBy2 = '';

  /** createByEx */
  createByEx = '';

  /** 创建日期 */
  createDt = '';

  /** 客户表别名,控制客户权限用 */
  customerAlias = '';

  /** dataFrom */
  dataFrom = '';

  /** del */
  del = false;

  /** 导入类型,扩充使用 */
  expType = '';

  /** filterByAuthNum */
  filterByAuthNum = '';

  /** 提供查询是做为排除条件使用 */
  filterId = '';

  /** 财务应收年月 */
  finReceiableYm = '';

  /** funBtnActiveStr */
  funBtnActiveStr = '';

  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch = '';

  /** 人数 */
  headCount = '';

  /** inId */
  inId = '';

  /** 是否账单查询 */
  isBillQuery = '';

  /** flex是否变化 */
  isChanged = false;

  /** 删除标记 */
  isDeleted = '';

  /** 是否包括存档费 0否 1是 */
  isIncludedArchive = '';

  /** 是否包括商保0否1是 */
  isIncludedBusiness = '';

  /** 是否包含客户一次性费用  0 否 1是 */
  isIncludedOnechares = '';

  /** 是否包含服务费(0 否，1 是) */
  isIncludedService = '';

  /** 是否薪资查询 */
  isWageQuery = '';

  /** 模拟人 */
  mimicBy = '';

  /** noChange */
  noChange = false;

  /** oldVat */
  oldVat = undefined;

  /** 金额 */
  oldamount = undefined;

  /** onecharesId */
  onecharesId = '';

  /** 0 客户一次性项目 1 外包一次性税费 */
  onechargesType = '';

  /** 流程审批角色名字 */
  processAprRoleName = '';

  /** 产品id */
  productId = '';

  /** 产品线 */
  productLine = '';

  /** 产品名称 */
  productName = '';

  /** 产品大类 */
  productTypeId = '';

  /** productTypeName */
  productTypeName = '';

  /** 供应商集团权限添加 */
  providerIdAlias = '';

  /** 代理人 */
  proxyBy = '';

  /** prvdGroupIdAlias */
  prvdGroupIdAlias = '';

  /** quotationId */
  quotationId = '';

  /** quotationItemId */
  quotationItemId = '';

  /** receivableId */
  receivableId = '';

  /** receivableTempltId */
  receivableTempltId = '';

  /** 帐套名称 */
  receivableTempltName = '';

  /** 注释 */
  remark = '';

  /** 卡纯代发人员,默认过滤 */
  restrictPure = '';

  /** 卡权限 */
  restrictType = '';

  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth = '';

  /** 获取前台勾选key的字符串 */
  selectKeyStr = '';

  /** serviceType */
  serviceType = '';

  /** serviceTypeText */
  serviceTypeText = '';

  /** signBranchTitle */
  signBranchTitle = '';

  /** signBranchTitleName */
  signBranchTitleName = '';

  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias = '';

  /** 总税率 */
  totalTax = undefined;

  /** 修改人 */
  updateBy = '';

  /** 修改日期 */
  updateDt = '';

  /** upt */
  upt = false;

  /** 用户id,控制小合同权限用 */
  userId = '';

  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias = '';

  /** vat */
  vat = undefined;

  /** vatr */
  vatr = undefined;
}

class Page {
  /** currentPage */
  currentPage = undefined;

  /** currentPageNo */
  currentPageNo = undefined;

  /** data */
  data = [];

  /** pageSize */
  pageSize = undefined;

  /** result */
  result = [];

  /** start */
  start = undefined;

  /** totalCount */
  totalCount = undefined;

  /** totalPage */
  totalPage = undefined;

  /** totalPageCount */
  totalPageCount = undefined;
}

export const exfinance = {
  CommonResponse,
  DelBillQuery,
  ExBillDTO,
  ExOneCharges,
  ExOneChargesDTO,
  ExportQuery,
  GenerateBillContionDTO,
  HashMap,
  Map,
  OneChargesDTO,
  Page,
};
