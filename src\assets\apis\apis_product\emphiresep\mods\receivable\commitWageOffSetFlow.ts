import * as defs from '../../baseClass';
import {
  fetch,
  RequestConfig,
  beforeFetch,
  afterFetch,
  checkResStatus,
  removeSpace,
} from '@/utils/request';

/**
     * @url /rhro-service-1.0/receivable/commitTemplateDelFlow
     * @desc 账单模板失效提交审核
账单模板失效提交审核
     * hasForm: false
     * hasBody: true
     */

export class Params {}

export const init = new defs.emphiresep.CommonResponse();
export const url = '/rhro-service-1.0/receivable/commitTemplateDelFlow:POST';
export const initialUrl = '/rhro-service-1.0/receivable/commitTemplateDelFlow';
export const cacheKey = '_receivable_commitTemplateDelFlow_POST';
export async function request(
  data: defs.emphiresep.ReceivableTemplate,
  options?: RequestConfig,
) {
  const reqUrl = `/rhro-service-1.0/receivable/commitTemplateDelFlow`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          reolve(res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
export async function requests(
  data: defs.emphiresep.ReceivableTemplate,
  options?: RequestConfig,
) {
  const reqUrl = `/rhro-service-1.0/receivable/commitTemplateDelFlow`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          if (checkResStatus(res)) return reolve(res ? res.data : res);
          reject(res ? res.message : res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
