import * as defs from '../../baseClass';
import {
  fetch,
  RequestConfig,
  beforeFetch,
  afterFetch,
  checkResStatus,
  removeSpace,
} from '@/utils/request';

/**
     * @url /rhro-service-1.0/api/ciReceivable/createCiReceivableBill
     * @desc 创建商保账单模板
创建商保账单模板
     * hasForm: false
     * hasBody: true
     */

export class Params {}

export const init = new defs.emphiresep.ReceivableBill();
export const url =
  '/rhro-service-1.0/api/ciReceivable/createCiReceivableBill:POST';
export const initialUrl =
  '/rhro-service-1.0/api/ciReceivable/createCiReceivableBill';
export const cacheKey = '_api_ciReceivable_createCiReceivableBill_POST';
export async function request(
  data: defs.emphiresep.ReceivableBill,
  options?: RequestConfig,
) {
  const reqUrl = `/rhro-service-1.0/api/ciReceivable/createCiReceivableBill`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          reolve(res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
export async function requests(
  data: defs.emphiresep.ReceivableBill,
  options?: RequestConfig,
) {
  const reqUrl = `/rhro-service-1.0/api/ciReceivable/createCiReceivableBill`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          if (checkResStatus(res)) return reolve(res ? res.data : res);
          reject(res ? res.message : res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
