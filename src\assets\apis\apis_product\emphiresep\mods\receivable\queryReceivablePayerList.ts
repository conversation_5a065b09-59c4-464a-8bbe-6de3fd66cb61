import * as defs from '../../baseClass';
import {
  fetch,
  RequestConfig,
  beforeFetch,
  afterFetch,
  checkResStatus,
  removeSpace,
} from '@/utils/request';

/**
     * @url /rhro-service-1.0/receivable/queryReceivablePayerList
     * @desc 根据大合同Id获取付款方列表
查询账单模板审批列表
     * hasForm: false
     * hasBody: true
     */

export class Params {}

export const init = new defs.emphiresep.Page();
export const url = '/rhro-service-1.0/receivable/queryReceivablePayerList:POST';
export const initialUrl =
  '/rhro-service-1.0/receivable/queryReceivablePayerList';
export const cacheKey = '_receivable_queryReceivablePayerList_POST';
export async function request(
  data: defs.emphiresep.Contract,
  options?: RequestConfig,
) {
  const reqUrl = `/rhro-service-1.0/receivable/queryReceivablePayerList`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          reolve(res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
export async function requests(
  data: defs.emphiresep.Contract,
  options?: RequestConfig,
) {
  const reqUrl = `/rhro-service-1.0/receivable/queryReceivablePayerList`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          if (checkResStatus(res)) return reolve(res ? res.data : res);
          reject(res ? res.message : res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
