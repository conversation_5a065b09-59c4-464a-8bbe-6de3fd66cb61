/*
 * @Author: 侯成
 * @Email: <EMAIL>
 * @Date: 2020-09-24 19:26:35
 * @LastAuthor: Veni_刘夏梅
 * @LastTime: 2022-05-31 19:47:40
 * @message: message
 * cathy.chen1
 * 01047727
 * ssGroupName: "北京社保"
 * ssPackageId: "111527569" #52c41a
 * empCode: 01003167, 01003485, 01012519, 01020944, 01030389, 01033951, 01044156, 01033955
 */
import React, { useEffect } from 'react';
import { Button, Form, Input, Modal, Switch, Typography, Upload } from 'antd';
import { WritableColumnProps } from '@/utils/writable/types';
import { CachedPage } from '@/components/CachedPage';
import { EditeFormProps } from '@/components/EditeForm';
import { useWritable, WritableInstance } from '@/components/Writable';
import {
  CustPayEntityExSelector,
  CustPayEntitySelector,
  ForPartyByDeptIdSelector,
  SsHandleTypeSelector,
  SsPackageNumberSelector,
  SsPackageSelector,
} from '@/components/Selectors/BaseDataSelectors';
import { SsGroupPop } from '@/components/StandardPop/SsGroupPop';
import { useState } from 'react';
import {
  getCurrentMenu,
  getCurrentUser,
  getCurrentUserCityId,
  getUserGoverningBranch,
  getUserId,
} from '@/utils/model';
import { CustomerPop } from '@/components/StandardPop/CustomerPop';
import { mapToSelectors } from '@/components/Selectors';
import { empStatus } from '@/utils/settings/welfaremanage/Socialmanage/socialApply';
import { InnerUserPop } from '@/components/StandardPop/InnerUserPop';
import { DateRange, MonthRange } from '@/components/DateRange4';
import { BooleanSelector } from '@/components/Selectors/BaseDropDown';
import { contractOldAreaTypeMap } from '@/utils/settings/sales/contract';
import { SocialApplyReceivable } from './SocialApplyReceivable';
import CustomerDetail from '@/pages/Sales/CustomerManage/CustomerQuery/components/CustomerDetail';
import { msgCall, msgErr, msgOk, msgWarn } from '@/utils/methods/message';
import { Store } from 'antd/lib/form/interface';
import ConfirmButton, { AsyncButton, ConfirmLoading } from '@/components/Forms/Confirm';
import { DownloadOutlined } from '@ant-design/icons';
import { downloadFileWithAlert, exportFilebyColumns } from '@/utils/methods/file';
import EmployeeFiles from '@/pages/emphiresep/emporder/QueryEmployeeOrder/Forms/EmployeeFiles';
import { isEmpty } from 'lodash';
import { GeneralInputRenderOption } from '@/components/Writable/libs/GeneralInput';
import { StrMonthPicker } from '@/components/DateComp/StrDatePicker';
import { stdMonthFormatMoDash } from '@/utils/methods/times';
import { Switchs } from '@/components/Selectors/Switch';
import { stdBoolFalseSrting, stdBoolTrueSrting } from '@/utils/settings';
import { AddTransferAndSubcontract } from '@/pages/emphiresep/sendorder/CustomerSubcontract/AddTransferAndSubcontract';
import { SsHandleTypeMap } from '@/utils/settings/empwelfare/social';
import { dataToHump } from '@/utils/methods/transfer';
import { BizUtil } from '@/utils/settings/bizUtil';
import { TablePage } from '@/utils/methods/pagenation';
import { customerSize } from '@/utils/settings/sysmanage/allocateService';
import { sendChannelMap } from '@/utils/settings/welfaremanage/Socialmanage';
import { modeOfOperationMap } from '@/utils/settings/emphiresep/sendorder/CustomerSubcontract';
import { CustPayEntityPop } from '@/components/StandardPop/CustPayEntityPop';
import { WelfareProcessorPop } from '@/components/StandardPop/WelfareProcessorPop';
import { tableIndexKey } from '@/utils/settings/forms';
import { WelfareProcessorBigPop } from '@/components/StandardPop/WelfareProcessorBigPop';
import EmpHireTemplate from '@/pages/emphiresep/hire/QueryEmpHireFromApp/components/empHireTemplate';
import { yesNoDataMap } from '@/utils/settings/basedata/comboManage';
import UploadForm, { FileSize } from '@/components/UploadForm';

type Twelfare = defs.welfaremanage.processInfoDTO;

interface SocialFundApplyProps {
  mode: 'social' | 'fund' | 'unclear';
}

let service = API.welfaremanage.socialManage.getSocialApply;
let serviceApply = API.welfaremanage.socialManage.applyProcess;
let ssGroupType: string | undefined = undefined;
let mianName = '社保公积金';

const FileTypeMaps: POJO = {
  '10': 'frontOfIDCard',
  '20': 'endOfIDCard',
  '25': 'identificationCard',
  '30': 'identificationPhoto',
  '40': 'firstPageOfHouseholdRegister',
  '50': 'ownPageOfHouseholdRegister',
  '60': 'changePageOfHouseholdRegister',
  '70': 'photoOfBankCard',
  '80': 'photoOfOtherDetails',
  '64': 'photoOfBusinessCard',
  '90': 'photoOfUnemploymentRegistration',
  '95': 'baseEndoInsCert_shaanxi',
};

// 303: 社保申请
// 304: 公积金

const SocialFundApply: React.FC<SocialFundApplyProps> = (props) => {
  const { mode } = props;
  if (mode === 'social') {
    mianName = '社保';
    ssGroupType = '1';
    service = {
      ...API.welfaremanage.socialManage.getSocialApply,
      cacheKey: API.welfaremanage.socialManage.getSocialApply.cacheKey + '303',
    };
    serviceApply = {
      ...API.welfaremanage.socialManage.applyProcess,
      cacheKey: API.welfaremanage.socialManage.applyProcess.cacheKey + '303',
    };
  } else if (mode === 'fund') {
    mianName = '公积金';
    ssGroupType = '2';
    service = {
      ...API.welfaremanage.socialManage.getSocialApply,
      cacheKey: API.welfaremanage.socialManage.getSocialApply.cacheKey + '304',
    };
    serviceApply = {
      ...API.welfaremanage.socialManage.applyProcess,
      cacheKey: API.welfaremanage.socialManage.applyProcess.cacheKey + '304',
    };
  }
  const [ssPackageId, setSsPackageId] = useState<string>();
  const [ISPRCNEEDAPPLY, setISPRCNEEDAPPLY] = useState<string>();
  const modalSocialApplyReceivable = useState(false);
  const modalEmployeeFiles = useState(false);
  const modalCustomerDetail = useState(false);
  const privatePopObject = getCurrentUser();
  const modalSubcontract = useState(false);
  const [count, setCount] = useState(0);
  const [uUIDData, setUUIDData] = useState<POJO>({});
  const [loading, setLoading] = useState(false);
  const [hireTemplate, setHireTemplate] = useState<POJO>({});
  const [visibleHireTemplate, setVisibleHireTemplate] = useState(false);

  const cityId = getCurrentUserCityId();
  const [form] = Form.useForm();

  // const writable = useWritable({ service: serviceSave });
  const afterRequest = (data: TablePage<any>) => {
    const newLi = new Array(data.list.length);
    data.list.forEach((line: any, i: number) => {
      const data = line.IS_ADD_PROCESS === 1 ? '0' : '1';
      newLi[i] = { ...line, IF_NOT_STOP: data };
    });
    return { pagination: data.pagination, list: newLi };
  };

  const writable = useWritable({
    service,
    // afterRequest,
  });
  const userGoverningBranch = getUserGoverningBranch();

  const handdleConfirmssGroupId = (ssGroup: POSO) => {
    // console.log('ssGroup in handdleConfirmssGroupId:', ssGroup)
    form.setFieldsValue({ status: '-1' });
    if (!ssGroup.ssGroupId || ssGroup.ISPRCNEEDAPPLY === undefined) {
      setISPRCNEEDAPPLY(undefined);
    } else {
      setISPRCNEEDAPPLY(ssGroup.ISPRCNEEDAPPLY.toString());
    }
  };

  const formColumns: EditeFormProps[] = [
    {
      label: '合并组',
      fieldName: 'ssPackageId',
      inputRender: () => (
        <SsPackageSelector
          params={cityId ? { cityId, typeId: ssGroupType } : { typeId: ssGroupType }}
          onChange={(value) => setSsPackageId(value)}
          skipEmptyParam
        />
      ),
    },
    {
      label: `${mianName}组`,
      fieldName: 'ssGroupId',
      inputRender: () => {
        return (
          <SsGroupPop
            rowValue="ssGroupId-ssGroupName"
            keyMap={{
              ssGroupId: 'SSGROUPID',
              ssGroupName: 'INSURANCENAME',
              ISPRCNEEDAPPLY: 'ISPRCNEEDAPPLY',
              cityId: 'CITYID',
            }}
            fixedValues={{ ssGroupType, cityId, ssPackageId }}
            handdleConfirm={handdleConfirmssGroupId}
          />
        );
      },
      formOptions: {
        shouldUpdate: true,
      },
    },
    { label: '唯一号', fieldName: 'empCode', inputRender: 'string' },
    { label: '雇员姓名', fieldName: 'empName', inputRender: 'string' },
    {
      label: '客户',
      fieldName: 'custId',
      inputRender: () => (
        <CustomerPop rowValue="custId-custName" fixedValues={{ userRoleType: 2 }} />
      ),
      colNumber: 2,
    },
    {
      label: '客户规模',
      fieldName: 'customerSize',
      inputRender: () => mapToSelectors(customerSize, { allowClear: true }),
    },
    {
      label: '入离职状态',
      fieldName: 'empStatus',
      inputRender: () => mapToSelectors(empStatus),
    },
    {
      label: '接单客服',
      fieldName: 'assigneeCsId',
      inputRender: () => (
        <InnerUserPop
          rowValue="assigneeCsId-assigneeCsName"
          keyMap={{ assigneeCsId: 'EMPID', assigneeCsName: 'REALNAME' }}
          fixedValues={{ roleCode: BizUtil.ROLE_CS, isDefault: 1 }}
        />
      ),
    },
    {
      label: '入职日期',
      fieldName: 'hireDtFrom',
      inputRender: () => (
        <DateRange
          fields={[
            {
              title: '入职日期>=',
              dataIndex: 'hireDtFrom',
            },
            {
              title: '入职日期<=',
              dataIndex: 'hireDtTo',
            },
          ]}
        />
      ),
    },
    {
      label: '<=',
      fieldName: 'rptHireDtTo',
      inputRender: () => (
        <DateRange
          fields={[
            {
              title: '报入职日期>=',
              dataIndex: 'rptHireDtFrom',
            },
            {
              title: '报入职日期<=',
              dataIndex: 'rptHireDtTo',
            },
          ]}
        />
      ),
    },

    { label: '小合同名称', fieldName: 'subcontractName', inputRender: 'string' },
    {
      label: '完善日期<=',
      fieldName: 'addPerfectDtTo',
      inputRender: () => (
        <DateRange
          fields={[
            {
              title: '完善日期>=',
              dataIndex: 'addPerfectDtFrom',
            },
            {
              title: '完善日期<=',
              dataIndex: 'addPerfectDtTo',
            },
          ]}
        />
      ),
    },
    {
      label: '福利起始月',
      fieldName: 'chargeStartDateTo',
      inputRender: () => (
        <MonthRange
          fields={[
            {
              title: '福利起始月>=',
              dataIndex: 'chargeStartDateFrom',
            },
            {
              title: '福利起始月<=',
              dataIndex: 'chargeStartDateTo',
            },
          ]}
        />
      ),
    },
    {
      label: '操作类型',
      fieldName: 'status',
      inputRender: () => mapToSelectors(SsHandleTypeMap),
      inputProps: { disabled: ISPRCNEEDAPPLY === undefined || ISPRCNEEDAPPLY !== '1' },
      rules: [{ required: true, message: '请先选择操作类型再进行查询' }],
    },
    { label: '证件号码', fieldName: 'idCardNum', inputRender: 'string' },
    {
      label: '服务区域类型',
      fieldName: 'areaType',
      inputRender: () => mapToSelectors(contractOldAreaTypeMap),
    },
    { label: '是否有拆分方', fieldName: 'splitPlace', inputRender: () => <BooleanSelector /> },
    {
      label: '发放通道',
      fieldName: 'sendChannel',
      inputRender: () => mapToSelectors(sendChannelMap),
      hidden: mode === 'fund',
    },
    {
      label: '是否单立户',
      fieldName: 'modeOfOperation',
      inputRender: () => mapToSelectors(modeOfOperationMap),
    },
    {
      label: '缴费实体',
      fieldName: 'custPayEntityId',
      inputRender: () => (
        <CustPayEntityPop
          rowValue="custPayEntityId-custPayEntityName"
          keyMap={{
            custPayEntityId: 'custPayEntityId',
            custPayEntityName: 'custPayEntityName',
          }}
        />
      ),
    },
    {
      label: '是否需要实做',
      fieldName: 'isAddProcess',
      inputRender: () => mapToSelectors(yesNoDataMap),
    },
  ];

  const applyProcess = async () => {
    const valiedated = await writable.validateFields({ validateSelected: true });
    if (valiedated.selectedCount === 0) return;
    // console.log('valiedated.selected in applyProcess:', valiedated.selected)
    for (const row of valiedated.selected) {
      if (row.IF_NOT_STOP === stdBoolTrueSrting && !row.COPY_MON) {
        return msgErr('请填写福利复制月');
      }
      if (row.reserveObj == '1' && !row.CUST_PAY_ENTITY_ID) {
        return msgErr('办理方为单立户，缴费实体必填');
      }
      // if (row.IF_NOT_STOP == '1') {
      //    msgWarn('福利不停勾选为是,不能继续办理');
      // }
      // row.IS_ADD_PROCESS = row.IF_NOT_STOP === '0' ? 1 : 0;
      if (!row.IF_NOT_STOP) {
        row.IF_NOT_STOP = '0';
      }
      if (!row.IS_ADD_PROCESS) {
        row.IS_ADD_PROCESS = '0';
      }
    }
    await serviceApply.requests({ applyProcessList: valiedated.selected });
    msgOk('申请办理成功');
    writable.request(writable.queries);
  };
  const noProcess = async () => {
    const applyProcessList: POJO[] = writable.getList().selected;
    for (const row of applyProcessList) {
      if (row.SVC_STATUS !== -1) {
        return msgErr('选中条目中包含不需办理记录');
      }
      if (!row.NEW_REMARK) {
        return msgErr('请填写备注');
      }
    }
    await API.welfaremanage.socialManage.noNeedDo.requests({ applyProcessList });
    msgOk('设置不需办理成功');
    writable.request(writable.queries);
  };

  const migrate = async () => {
    const _clicked = writable.selectedSingleRow;
    if (isEmpty(_clicked)) {
      return msgErr('未找到单选行。');
    }
    // if (_clicked.MODE_OF_OPERATION === 1) {
    //   return msgErr('单立户无法类似设定');
    // }
    const rows = writable.selectedRows;
    const selected = rows.filter((item) => item.RN !== _clicked.RN);

    if (selected.length === 0) {
      return msgErr('未找到复选行。');
    }
    // writable.migrateClickToSelected([
    //   'PROCESS_TYPE',
    //   'PROCESS_TYPE_NAME',
    //   'WELFARE_PROCESSOR',
    //   'WELFARE_PROCESSOR_NAME',
    //   'NEW_REMARK',
    //   'IF_NOT_STOP',
    //   'reserveObj',
    // ]);
    const clicked = writable.getRow(_clicked[tableIndexKey]);
    for (const row of selected) {
      row.PROCESS_TYPE = clicked.PROCESS_TYPE;
      row.PROCESS_TYPE_NAME = clicked.PROCESS_TYPE_NAME;

      row.NEW_REMARK = clicked.NEW_REMARK;
      row.IF_NOT_STOP = clicked.IF_NOT_STOP;
      row.IS_ADD_PROCESS = clicked.IS_ADD_PROCESS;
      if (row.MODE_OF_OPERATION === 2 && _clicked.MODE_OF_OPERATION === 2) {
        row.WELFARE_PROCESSOR = clicked.WELFARE_PROCESSOR;
        row.WELFARE_PROCESSOR_NAME = clicked.WELFARE_PROCESSOR_NAME;
        row.reserveObj = clicked.reserveObj;
        row.CUST_PAY_ENTITY_ID = undefined;
        row.CUST_PAY_ENTITY_NAME = undefined;
      }
      // if (!clicked.WELFARE_PROCESSOR || clicked.reserveObj === '0') {

      // }
    }

    writable.updateRows(selected);
    setTimeout(() => {
      setCount(count + 1);
    }, 500);
  };

  const viewEmp = (record: Twelfare) => {
    modalEmployeeFiles[1](true);
  };

  const viewCust = (record: Twelfare) => {
    modalCustomerDetail[1](true);
  };

  const viewRECEIVABLE_AMT = (record: Twelfare) => {
    modalSocialApplyReceivable[1](true);
  };

  const IF_NOT_STOPonChange = (
    options: GeneralInputRenderOption<any>,
    checked: string = stdBoolFalseSrting,
  ) => {
    // writable.setOtherFieldsValue(options.serial, { IF_NOT_STOP: checked })
    if (checked === stdBoolFalseSrting) {
      writable.setFieldsValue(options.serial, { COPY_MON: undefined });
    }
  };

  const red = { color: '#ff4d4f' };
  const columns: WritableColumnProps<any>[] = [
    {
      title: '雇员姓名',
      dataIndex: 'EMP_NAME',
      render: (text: string, record: Twelfare) => {
        return (
          <Typography.Link style={record.REMARK ? red : undefined} onClick={() => viewEmp(record)}>
            {text}
          </Typography.Link>
        );
      },
      fixed: 'left',
    },
    {
      title: '唯一号',
      dataIndex: 'EMP_CODE',
      // render: (text: string, record: Twelfare) => {
      //   return <Typography.Link style={record.REMARK ? red : undefined}>{text}</Typography.Link>;
      // },
      fixed: 'left',
    },
    {
      title: '社保组编号',
      dataIndex: 'SS_GROUP_ID',
      fixed: 'left',
    },
    { title: `${mianName}组`, dataIndex: 'INSURANCE_NAME', fixed: 'left' },
    { title: '申报工资', dataIndex: 'DEC_SALARY' },

    {
      title: '客户名称',
      dataIndex: 'CUST_NAME',
      render: (text: string, record: Twelfare) => {
        return <Typography.Link onClick={() => viewCust(record)}>{text}</Typography.Link>;
      },
      fixed: 'left',
    },
    { title: '客户规模', dataIndex: 'CUSTOMER_SIZE_NAME' },
    { title: '客户编号', dataIndex: 'CUST_CODE' },
    { title: '服务区域类型', dataIndex: 'AREA_TYPE_NAME' },
    {
      title: '小合同编号',
      dataIndex: 'SUBCONTRACT_ID',
    },
    {
      title: '小合同',
      dataIndex: 'SUBCONTRACT_NAME',
      render: (text: string, record: Twelfare) => {
        return <Typography.Link onClick={() => modalSubcontract[1](true)}>{text}</Typography.Link>;
      },
    },
    {
      title: '派遣单位名称',
      dataIndex: 'PAYER_NAME',
    },
    {
      title: '统一社会信用码',
      dataIndex: 'TAXPAYER_IDENTIFIER',
    },
    { title: '是否单立户', dataIndex: 'MODE_OF_OPERATION_NAME' },
    { title: '派单方', dataIndex: 'ASSIGNER_PROVIDER_NAME' },
    { title: '接单方', dataIndex: 'ASSIGNEE_PROVIDER_NAME' },
    { title: '派单方客服', dataIndex: 'ASSIGNER_CS_NAME' },
    { title: '接单方客服', dataIndex: 'ASSIGNEE_CS_NAME' },
    { title: '证件号码', dataIndex: 'ID_CARD_NUM' },
    { title: '手机号码', dataIndex: 'CONTACT_TEL2' },
    { title: '是否呼叫', dataIndex: 'IS_HIRE_CALL_NAME' },
    { title: '大分类', dataIndex: 'SUPER_TYPE_NAME' },
    { title: '小分类', dataIndex: 'SUBTYPE_NAME' },
    { title: '入职日期', dataIndex: 'HIRE_DT' },
    { title: '生成订单时间', dataIndex: 'RPT_HIRE_DT' },
    { title: '离职时间', dataIndex: 'SEP_DT' },
    { title: '完善日期', dataIndex: 'ADD_PERFECT_DT' },
    {
      title: '福利起始月',
      dataIndex: 'CHARGE_START_DATE',
    },
    {
      title: '账号',
      dataIndex: 'ACCT',
      inputRender: 'string',
    },
    {
      title: '新增方式',
      dataIndex: 'PROCESS_TYPE',
      inputRender: (options: GeneralInputRenderOption<any>) => (
        <SsHandleTypeSelector params={{ ssGroupId: options.record.SS_GROUP_ID }} keyType="number" />
      ),
    },
    {
      title: '办理方',
      dataIndex: 'WELFARE_PROCESSOR_NAME',
      inputRender: (options) => (
        <WelfareProcessorBigPop
          fixedValues={{ deptId: userGoverningBranch, isIndependent: 0 }}
          rowValue="WELFARE_PROCESSOR--reserveObj-WELFARE_PROCESSOR_NAME"
          keyMap={{
            WELFARE_PROCESSOR: 'key',
            reserveObj: 'reserveObj',
            WELFARE_PROCESSOR_NAME: 'shortName',
          }}
          disabled={options.record.MODE_OF_OPERATION === 1}
        />
        // <ForPartyByDeptIdSelector
        //   params={{ deptId: userGoverningBranch }}
        //   skipEmptyParam
        //   keyType="number"
        // />
      ),
      rules: [{ required: true, message: '请选择办理方' }],
      onGridChange: (value: Partial<any>, options: GeneralInputRenderOption<any>) => {
        if (!value.WELFARE_PROCESSOR || value.reserveObj === '0') {
          writable.setFieldsValue(options.serial, {
            CUST_PAY_ENTITY_ID: undefined,
          });
        } else {
          API.emphiresep.transfer.getCustPayEntityDropdownListEx
            .requests({
              custId: options.record.CUST_ID,
              welfareProcessor: value.WELFARE_PROCESSOR || options.record.WELFARE_PROCESSOR,
            })
            .then((res) => {
              // const result = res.list.filter(
              //   (item) => item.key === options.record.CUST_PAY_ENTITY_ID,
              // );
              if (!res.list.length) {
                writable.setFieldsValue(options.serial, {
                  CUST_PAY_ENTITY_ID: undefined,
                });
              } else {
                writable.setFieldsValue(options.serial, {
                  CUST_PAY_ENTITY_ID: res.list[0].key,
                });
              }
            });
        }
      },
    },
    {
      title: '缴费实体',
      dataIndex: 'CUST_PAY_ENTITY_ID', //

      inputRender: (options: GeneralInputRenderOption<any>) => {
        // ['1', '单立户'], ['2', '大户'],
        // const modeOfOperation = writable.getFieldValue(options.serial, 'MODE_OF_OPERATION');
        // const welfareProcessor = writable.getFieldValue(options.serial, 'WELFARE_PROCESSOR');
        const { WELFARE_PROCESSOR, reserveObj } = writable.getFieldsValue(options.serial, [
          'WELFARE_PROCESSOR',
          'reserveObj',
        ]);
        //  if (modeOfOperation !== '1') return null;

        const parmas = {
          custId: options.record.CUST_ID,
          welfareProcessor: WELFARE_PROCESSOR || options.record.WELFARE_PROCESSOR,
          refresh: true,
        };
        return (
          <CustPayEntityExSelector
            params={parmas}
            disabled={true}
            keyMap={{
              CUST_PAY_ENTITY_ID: 'key',
              CUST_PAY_ENTITY_NAME: 'shortName',
            }}
            //  skipEmptyParam
          />
        );
      },
    },
    { title: '新办备注', dataIndex: 'PROCESS_REMARK' },
    { title: '退回备注', dataIndex: 'REMARK' },
    { title: '备注', dataIndex: 'NEW_REMARK', inputRender: 'text' },
    {
      title: '是否福利不停',
      dataIndex: 'IF_NOT_STOP',
      inputRender: (options: GeneralInputRenderOption<any>) => (
        <Switchs onChange={(checked?: string) => IF_NOT_STOPonChange(options, checked)} />
      ),
    },
    {
      title: '是否需要实做',
      dataIndex: 'IS_ADD_PROCESS',
      inputRender: (options: GeneralInputRenderOption<any>) => (
        <Switchs onChange={(checked?: string) => IF_NOT_STOPonChange(options, checked)} />
      ),
    },
    {
      title: '福利复制月',
      dataIndex: 'COPY_MON',
      inputRender: (options: GeneralInputRenderOption<any>) => {
        const IF_NOT_STOP = writable.getFieldValue(options.serial, 'IF_NOT_STOP');
        return (
          <StrMonthPicker
            disabled={IF_NOT_STOP === stdBoolFalseSrting}
            format={stdMonthFormatMoDash}
          />
        );
      },
      // advanceRules: [
      //   {
      //     validator: (value, formData, options) => {
      //       console.log('value in validator:', value)
      //       console.log('formData in validator:', options)
      //     },
      //     message: '请选择福利复制月',
      //   },
      // ],
    },
    { title: '委托代发银行名称', dataIndex: 'ENTRUST_BANK_NAME' },
    { title: '委托代发银行帐户', dataIndex: 'ENTRUST_BANK_ACCOUNT' },
    {
      title: '应收总额',
      dataIndex: 'RECEIVABLE_AMT',
      render: (text: string, record: Twelfare) => {
        return <Typography.Link onClick={() => viewRECEIVABLE_AMT(record)}>{text}</Typography.Link>;
      },
    },
    { title: '欠款总额', dataIndex: 'ARREAR_AMOUNT' },
    { title: '资料收集情况', dataIndex: 'BEA_COUNT' },
    { title: '拆分方', dataIndex: 'SPLITPLACENAME' },
    { title: '签单分公司抬头', dataIndex: 'SIGNBRANCHTITLENAME' },
    { title: '发放通道', dataIndex: 'SEND_CHANNEL_NAME', hidden: mode === 'fund' },
    {
      title: '附件名称',
      dataIndex: 'FILE_NAME',
      hidden: mode === 'fund',
      render: (text: string, record: POJO) => {
        return (
          <Typography.Link onClick={() => exportImportFile(record.FILE_ID, record.FILE_NAME)}>
            {text}
          </Typography.Link>
        );
      },
    },
    {
      title: '附件上传',
      dataIndex: 'action',
      hidden: mode === 'fund',
      render: (text: string, record: Twelfare) => {
        return (
          <Typography.Text style={{ display: 'flex' }}>
            <Upload
              customRequest={(o) => customRequest(o, record)}
              action=""
              style={{ display: 'inline-block' }}
              showUploadList={false}
              maxCount={1}
            >
              <Typography.Link>上传</Typography.Link>
            </Upload>
            <Typography.Link onClick={() => onDeletFile(record)} style={{ marginLeft: '5px' }}>
              删除
            </Typography.Link>
          </Typography.Text>
        );
      },
    },
  ];

  const onDeletFile = async (record: POJO) => {
    Modal.confirm({
      content: '是否确认删除？如果点击确定，将文件物理删除。',
      onOk: async () => {
        await API.welfaremanage.socialManage.deleteSsFile.requests({
          empHireSepId: record.EMP_HIRE_SEP_ID,
          ssGroupId: record.SS_GROUP_ID,
        });
        msgOk('删除成功');
        writable.request(writable.queries);
      },
    });
  };

  const customRequest = async (option: any, record: POJO) => {
    if (record?.FILE_ID) {
      Modal.confirm({
        content: '该员工已经有附件，请确认是否继续上传？如果选择确定，则覆盖附件上传?',
        onOk: async () => {
          saveFile(option, record);
        },
      });
    } else {
      saveFile(option, record);
    }
  };

  const saveFile = async (option: any, record: POJO) => {
    const { file } = option;
    const maxSz = FileSize._5MB;
    const isTrue =
      file.name.includes('zip') || file.name.includes('rap') || file.name.includes('7z');
    if (file.size > maxSz || !isTrue) {
      const message = `上传失败，支持压缩包格式，大小限制5M以内，请核对！`;
      return msgErr(message);
    }
    const data = await API.commons.file.uploadFile.requests({
      file,
      bizType: '30302000',
      fileName: file.name,
    } as any);
    await API.welfaremanage.socialManage.saveSsFile.requests({
      empHireSepId: record.EMP_HIRE_SEP_ID,
      fileId: data,
      ssGroupId: record.SS_GROUP_ID,
    });
    msgOk('上传成功');
    writable.request(writable.queries);
  };

  const exportImportFile = (fileId: number | string, fileName: string, msg?: string) => {
    if (!fileId || !fileName) {
      return msgErr('无文件下载');
    }
    API.commons.file.downloadFile
      .request(
        {
          fileId: String(fileId),
        },
        { responseType: 'blob' },
      )
      .then((res) => {
        if (res && res.type != 'text/html') {
          return downloadFileWithAlert(res, fileName);
        }
        msgCall({ code: 404, msg: msg || '导出失败' });
      });
  };

  const exportColumns: WritableColumnProps<any>[] = [
    {
      title: '雇员姓名',
      dataIndex: 'EMP_NAME',
    },
    {
      title: '唯一号',
      dataIndex: 'EMP_CODE',
    },
    {
      title: '社保组编号',
      dataIndex: 'SS_GROUP_ID',
    },
    { title: `${mianName}组`, dataIndex: 'INSURANCE_NAME', fixed: 'left' },
    { title: '申报工资', dataIndex: 'DEC_SALARY' },
    {
      title: '客户名称',
      dataIndex: 'CUST_NAME',
    },
    { title: '客户规模', dataIndex: 'CUSTOMER_SIZE_NAME' },
    { title: '客户编号', dataIndex: 'CUST_CODE' },
    { title: '服务区域类型', dataIndex: 'AREA_TYPE_NAME' },
    {
      title: '小合同编号',
      dataIndex: 'SUBCONTRACT_ID',
    },
    {
      title: '小合同',
      dataIndex: 'SUBCONTRACT_NAME',
    },
    {
      title: '派遣单位名称',
      dataIndex: 'PAYER_NAME',
    },
    {
      title: '统一社会信用码',
      dataIndex: 'TAXPAYER_IDENTIFIER',
    },
    { title: '是否单立户', dataIndex: 'MODE_OF_OPERATION_NAME' },
    { title: '派单方', dataIndex: 'ASSIGNER_PROVIDER_NAME' },
    { title: '接单方', dataIndex: 'ASSIGNEE_PROVIDER_NAME' },
    { title: '派单方客服', dataIndex: 'ASSIGNER_CS_NAME' },
    { title: '接单方客服', dataIndex: 'ASSIGNEE_CS_NAME' },
    { title: '证件号码', dataIndex: 'ID_CARD_NUM' },
    { title: '手机号码', dataIndex: 'CONTACT_TEL2' },
    { title: '是否呼叫', dataIndex: 'IS_HIRE_CALL_NAME' },
    { title: '大分类', dataIndex: 'SUPER_TYPE_NAME' },
    { title: '小分类', dataIndex: 'SUBTYPE_NAME' },
    { title: '入职日期', dataIndex: 'HIRE_DT' },
    { title: '生成订单时间', dataIndex: 'RPT_HIRE_DT' },
    { title: '离职时间', dataIndex: 'SEP_DT' },
    { title: '完善日期', dataIndex: 'ADD_PERFECT_DT' },
    {
      title: '福利起始月',
      dataIndex: 'CHARGE_START_DATE',
    },
    {
      title: '账号',
      dataIndex: 'ACCT',
      inputRender: 'string',
    },
    {
      title: '新增方式',
      dataIndex: 'PROCESS_TYPE',
    },
    {
      title: '办理方',
      dataIndex: 'WELFARE_PROCESSOR',
    },
    {
      title: '缴费实体',
      dataIndex: 'CUST_PAY_ENTITY_NAME', // 因为存在可能没有办理方，但是有缴费实体，所以这里只能用name
    },
    { title: '新办备注', dataIndex: 'PROCESS_REMARK' },
    { title: '退回备注', dataIndex: 'REMARK' },
    { title: '备注', dataIndex: 'NEW_REMARK', inputRender: 'text' },
    {
      title: '是否福利不停',
      dataIndex: 'IF_NOT_STOP',
    },
    {
      title: '福利复制月',
      dataIndex: 'COPY_MON',
    },
    { title: '委托代发银行名称', dataIndex: 'ENTRUST_BANK_NAME' },
    { title: '委托代发银行帐户', dataIndex: 'ENTRUST_BANK_ACCOUNT' },
    {
      title: '应收总额',
      dataIndex: 'RECEIVABLE_AMT',
    },
    { title: '欠款总额', dataIndex: 'ARREAR_AMOUNT' },
    { title: '资料收集情况', dataIndex: 'BEA_COUNT' },
    { title: '拆分方', dataIndex: 'SPLITPLACENAME' },
    { title: '签单分公司抬头', dataIndex: 'SIGNBRANCHTITLENAME' },
    { title: '发放通道', dataIndex: 'SEND_CHANNEL_NAME' },
  ];

  const userId = getUserId();

  const handleQueries = (values: Store) => {
    const data: Store = {
      ...values,
      outPutType: 'process',
      subcontractAlias: 'hehs',
      userId,
    };
    if (!data.ssGroupId && !data.ssPackageId) {
      return msgErr('合并组与社保公积金组至少选择一个');
    }
    if (data.idCardNum) {
      data.idCardNum = data.idCardNum.toUpperCase();
    }
    if (data.chargeStartDateFrom || data.chargeStartDateTo) {
      data.changeStartDateMark = 1;
    } else {
      data.changeStartDateMark = undefined;
    }
    return data;
  };

  const downloadData = () => {
    const selectedSingleRow = writable.selectedSingleRow;
    if (isEmpty(selectedSingleRow)) {
      return msgErr('数据为空，请重新查询');
    }
    if (mode === 'fund') {
      exportColumns.pop();
    }

    exportFilebyColumns(
      API.welfaremanage.socialManage.toDownLoad,
      exportColumns,
      { ...writable.queries, expType: '1' },
      `${mianName}申请数据.xlsx`,
      null,
      null,
      { PROCESS_TYPE: 'PROCESS_TYPE_NAME', WELFARE_PROCESSOR: 'WELFARE_PROCESSOR_NAME' },
    );
  };

  // 修改个人信息
  const handleEditHireTemplate = async (type: string) => {
    const selectedSingleRow = writable.selectedSingleRow || {};
    const mainId = selectedSingleRow.EMP_HIRESEP_MAIN_ID;
    if (isEmpty(selectedSingleRow)) {
      return msgErr('您尚未选择任何数据');
    }
    if (selectedSingleRow.ASSOCIATIONSTATUS === '未关联') {
      return msgErr('没有对应的订单入职信息，不能修改，请知悉');
    }
    setUUIDData({});
    try {
      const res = await API.wechat.wechatHiresep.checkCouldEditEntry.request({ mainId });
      if (res.code === 200) {
        setLoading(true);
        handleHiresepMainData(false);
      } else {
        setLoading(false);
        msgErr(res.message);
      }
    } catch (e: any) {
      setLoading(false);
      msgErr(e.message || e);
    }
  };

  const handleHiresepMainData = async (type: boolean) => {
    const {
      EMP_HIRESEP_MAIN_ID: mainId,
      EMP_HIRE_SEP_ID: empHireSepId,
      CITYID: cityId,
    } = writable.selectedSingleRow || {};
    const fetchHiresepMain = async () => {
      const api = type
        ? API.wechat.wechatHiresep.getUniqueHiresepMain
        : API.wechat.wechatHiresep.editHiresepMain;
      const mainData = await api.request({ mainId, cityCode: cityId });
      return mainData.data;
    };
    const fetchAllPhotos = async (data: any) => {
      const allPhotosData: any = await API.wechat.wechatPhoto.allPhotos.request({
        mainId,
        uuid: data?.uuid,
      });
      const imgData: any = {};
      allPhotosData.data?.map((item: any) => {
        const FileTypeName = FileTypeMaps[item.imageType];
        imgData[FileTypeName] = item.imageUrl;
      });
      return imgData;
    };
    const fetchSocialCreditCode = async (data: any) => {
      if (!data?.unisocialcode_beijing && cityId === '10740' && empHireSepId) {
        const socialCreditCode = await API.wechat.wechatSepcailCity.fetchSocialCreditInfo.request({
          empHiresepId: empHireSepId,
        });
        return socialCreditCode.data;
      } else {
        return {};
      }
    };
    fetchHiresepMain().then((res) => {
      Promise.all([fetchAllPhotos(res), fetchSocialCreditCode(res)])
        .then((res1) => {
          setLoading(false);
          setVisibleHireTemplate(true);
          setHireTemplate({ ...res, ...res1[0], ...res1[1] });
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  const renderButtons = (_options: WritableInstance) => {
    const notSeleted = writable.selectedRows.length === 0;
    const noOne = isEmpty(writable.selectedSingleRow);
    return (
      <>
        <Button type="primary" htmlType="submit">
          查询
        </Button>
        <AsyncButton
          disabled={noOne}
          onClick={() => {
            window.open(
              `/singlePage/QueryOneEmployeeOrderDetail?empHireSepId=${writable.selectedSingleRow?.EMP_HIRE_SEP_ID}&userName=${privatePopObject?.profile?.userName}`,
            );
          }}
        >
          查看个人订单
        </AsyncButton>
        <AsyncButton disabled={notSeleted} onClick={applyProcess}>
          申请办理
        </AsyncButton>
        <AsyncButton disabled={notSeleted} onClick={noProcess}>
          不需办理
        </AsyncButton>
        <Button disabled={notSeleted} onClick={migrate}>
          类似设定
        </Button>
        <ConfirmButton
          icon={<DownloadOutlined />}
          service={API.welfaremanage.socialManage.toDownLoad}
          onClick={downloadData}
        >
          导出数据
        </ConfirmButton>

        {mode === 'social' && (
          <Button
            onClick={() => handleEditHireTemplate('newTemplate')}
            loading={loading}
            disabled={noOne}
          >
            修改入职信息
          </Button>
        )}
      </>
    );
  };

  return (
    <CachedPage
      editable
      service={service}
      formColumns={formColumns}
      form={form}
      columns={columns}
      renderButtons={renderButtons}
      wriTable={writable}
      handleQueries={handleQueries}
      initialValues={{
        status: '-1',
        // empCode: '01003485',
        // ssGroupId: 379750,
      }}
    >
      <>{false && count}</>
      <EmployeeFiles
        noShowList
        visible={modalEmployeeFiles[0]}
        hideHandle={() => modalEmployeeFiles[1](false)}
        employeeId={writable.selectedSingleRow?.EMP_ID}
      />
      <CustomerDetail
        action="COMMON"
        source="COMMON_USE"
        custId={writable.selectedSingleRow?.CUST_ID}
        hideHandle={() => modalCustomerDetail[1](false)}
        visible={modalCustomerDetail[0]}
      />
      <SocialApplyReceivable
        customer={writable.selectedSingleRow}
        modal={modalSocialApplyReceivable}
      />
      {/* 小合同详情 */}
      <AddTransferAndSubcontract
        title={'小合同查看'}
        currentState={'scView'}
        subContract={dataToHump(writable.selectedSingleRow)}
        modal={modalSubcontract}
      />
      <EmpHireTemplate
        visible={visibleHireTemplate}
        isPreview={false}
        uUIDData={uUIDData}
        hireTemplate={hireTemplate}
        onFinshed={(success: any) => {
          setVisibleHireTemplate(false);
          if (success) {
            writable.request();
          }
        }}
      />
    </CachedPage>
  );
};

export { SocialFundApply };
