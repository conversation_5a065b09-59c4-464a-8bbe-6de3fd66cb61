import * as defs from '../../baseClass';
import {
  fetch,
  RequestConfig,
  beforeFetch,
  afterFetch,
  checkResStatus,
  removeSpace,
} from '@/utils/request';

/**
     * @url /rhro-service-1.0/employeeBaseInfo/getAgentWageEmployeeBankInfo
     * @desc 根据条件得到纯代发雇员银行信息的分页查询结果
根据条件得到纯代发雇员银行信息的分页查询结果
     * hasForm: false
     * hasBody: true
     */

export class Params {
  /** accountEmployeeName */
  accountEmployeeName?: string;
  /** add */
  add?: boolean;
  /** 联系地址 */
  address?: string;
  /** 年龄 */
  age?: string;
  /** 养老金帐号 */
  annuityAccount?: string;
  /** appover */
  appover?: string;
  /** approveDT */
  approveDT?: string;
  /** 小合同接单方客服id */
  assigneeProviderId?: string;
  /** 小合同接单方客服Name */
  assigneeProviderName?: string;
  /** 小合同派单方客服id */
  assignerProviderId?: string;
  /** 小合同派单方客服Name */
  assignerProviderName?: string;
  /** attachmentName */
  attachmentName?: string;
  /** attachmentType */
  attachmentType?: string;
  /** attachmentstatus */
  attachmentstatus?: string;
  /** bankAcct */
  bankAcct?: string;
  /** bankName */
  bankName?: string;
  /** 批次号,用于备份 */
  batchId?: string;
  /** 首次参加工作日期 */
  beginWorkDate?: string;
  /** 账单表别名,控制客户权限用 */
  billAlias?: string;
  /** birthCountry */
  birthCountry?: number;
  /** 生日 */
  birthday?: string;
  /** 财务大类 */
  bizCategory?: string;
  /** 业务类型,控制小合同权限用 */
  bizmanType?: string;
  /** sys_branch_title别名,控制合同权限用 */
  branchTitleAlias?: string;
  /** busiType */
  busiType?: string;
  /** 子女情况： 1 无子女 2 独生子女 3 有子女但非独生子女。 */
  childrenStatus?: string;
  /** cityName */
  cityName?: string;
  /** clientOperation */
  clientOperation?: number;
  /** flex是否行编号 */
  clientRowSeq?: number;
  /** flex是否选择 */
  clientSelected?: boolean;
  /** condition */
  condition?: object;
  /** 联系电话1，电话 */
  contactTel1?: string;
  /** 联系电话2，手机 */
  contactTel2?: string;
  /** 合同表别名,控制合同权限用 */
  contractAlias?: string;
  /** countSqlName */
  countSqlName: string;
  /** 国家id */
  countryId?: string;
  /** 创建人 */
  createBy?: string;
  /** createBy2 */
  createBy2?: string;
  /** 创建日期 */
  createDt?: string;
  /** 客户编码 */
  custCode?: string;
  /** 客户id */
  custId?: string;
  /** 客户名称 */
  custName?: string;
  /** 客户表别名,控制客户权限用 */
  customerAlias?: string;
  /** dateOfIssue */
  dateOfIssue?: string;
  /** del */
  del?: boolean;
  /** disaCertValidUntil */
  disaCertValidUntil?: string;
  /** disabilityLevel */
  disabilityLevel?: string;
  /** 残疾类型 */
  disabilityType?: string;
  /** disabledId */
  disabledId?: string;
  /** disabledPeopleType */
  disabledPeopleType?: string;
  /** 职务 */
  duty?: string;
  /** 教育程度 */
  educationLevel?: string;
  /** email */
  email?: string;
  /** 紧急联系人 */
  emergencyContact?: string;
  /** 紧急联系人电话 */
  emergencyPhone?: string;
  /** empHireSepId */
  empHireSepId?: string;
  /** empHisId */
  empHisId?: string;
  /** 所在街道 */
  empStreet?: string;
  /** empattachmentid */
  empattachmentid?: string;
  /** 员工编号 */
  employeeCode?: string;
  /** 员工id */
  employeeId?: string;
  /** 员工姓名 */
  employeeName?: string;
  /** 上岗状态 */
  employeeStatus?: string;
  /** endIndex */
  endIndex?: number;
  /** 民族 */
  ethnic?: string;
  /** 查询使用排除的雇员id */
  exceptEmpId?: string;
  /** 导入类型,扩充使用 */
  expType?: string;
  /** 家属劳保卡号 */
  familyLaborCardNo?: string;
  /** fileCabCode */
  fileCabCode?: string;
  /** files */
  files?: number;
  /** filterByAuthNum */
  filterByAuthNum?: string;
  /** 提供查询是做为排除条件使用 */
  filterId?: string;
  /** filterNotPersonalOderEmp */
  filterNotPersonalOderEmp?: string;
  /** folderCode */
  folderCode?: string;
  /** 外语等级 */
  foreignLanguageLevel?: string;
  /** 外语语种 */
  foreignLanguageType?: string;
  /** funBtnActiveStr */
  funBtnActiveStr?: string;
  /** 性别 */
  gender?: string;
  /** 登录人所属分公司,控制小合同权限用 */
  governingBranch?: string;
  /** 毕业日期 */
  graduationDt?: string;
  /** guardianContact */
  guardianContact?: string;
  /** guardianName */
  guardianName?: string;
  /** 是否有不良记录 */
  haveBadnessRecord?: string;
  /** 健康状况 */
  healthStatus?: string;
  /** 入职日期 */
  hireDate?: string;
  /** hukouAddress */
  hukouAddress?: string;
  /** 户口类别 */
  hukouType?: string;
  /** 户籍地址邮编 */
  hukouZipCode?: string;
  /** idCardNum */
  idCardNum?: string;
  /** idCardType */
  idCardType?: string;
  /** 雇员证件类型名称 */
  idCardTypeName?: string;
  /** 15位证件号码 */
  idc15?: string;
  /** 18位证件号码 */
  idc18?: string;
  /** inId */
  inId?: string;
  /** 初始化类别 */
  initType?: string;
  /** 企业内部编号 */
  internalCode?: string;
  /** isArchive */
  isArchive?: string;
  /** 是否账单查询 */
  isBillQuery?: string;
  /** 是否市级保健对象 */
  isCareObject?: string;
  /** flex是否变化 */
  isChanged?: boolean;
  /** 删除标记 */
  isDeleted?: string;
  /** isDisabled */
  isDisabled?: number;
  /** 是否伤残军人 */
  isDisabledMilitary?: string;
  /** 是否为集体户口 */
  isGroupResident?: string;
  /** isLonelyOld */
  isLonelyOld?: number;
  /** isMartyFamily */
  isMartyFamily?: number;
  /** isSalaryImport */
  isSalaryImport?: number;
  /** isWageQuery */
  isWageQuery?: string;
  /** 参军日期 */
  joinMilitaryDate?: string;
  /** limit */
  limit: number;
  /** 婚姻状况 */
  marriageStatus?: string;
  /** martyFamilyId */
  martyFamilyId?: string;
  /** 医疗卡 */
  medicalCardId?: string;
  /** 医疗保障类别 */
  medicareType?: string;
  /** 模拟人 */
  mimicBy?: string;
  /** 兵役状况 */
  nationalServiceStatus?: string;
  /** noChange */
  noChange?: boolean;
  /** openBankName */
  openBankName?: string;
  /** 页数 */
  pageNum?: number;
  /** pageQuerySqlName */
  pageQuerySqlName: string;
  /** 每页记录数,默认65536条 */
  pageSize?: number;
  /** 密码 */
  password?: string;
  /** 薪资档案id */
  payPsnId?: string;
  /** 籍贯 */
  personalRegCity?: string;
  /** 政治面貌 */
  politicalStatus?: string;
  /** 职位 */
  position?: string;
  /** 流程审批角色名字 */
  processAprRoleName?: string;
  /** 职业工种发证日期 */
  professionAwardDate?: string;
  /** 职业工种发证单位 */
  professionAwardUnit?: string;
  /** 职业工种等级 */
  professionLevel?: string;
  /** 职业工种名称 */
  professionName?: string;
  /** 专业技术职称 */
  professionTitle?: string;
  /** 公积金账号 */
  providentFundAccount?: string;
  /** 供应商集团权限添加 */
  providerIdAlias?: string;
  /** 省份id */
  provinceId?: string;
  /** provinceName */
  provinceName?: string;
  /** 代理人 */
  proxyBy?: string;
  /** prvdGroupIdAlias */
  prvdGroupIdAlias?: string;
  /** 账单id */
  receivableTempltId?: string;
  /** 雇员信息备注 */
  remark?: string;
  /** 居住地址 */
  residentAddress?: string;
  /** 卡纯代发人员,默认过滤 */
  restrictPure?: string;
  /** 卡权限 */
  restrictType?: string;
  /** 转业日期 */
  retireFromMilitaryDate?: string;
  /** savepath */
  savepath?: string;
  /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
  selByAuth?: string;
  /** 获取前台勾选key的字符串 */
  selectKeyStr?: string;
  /** 离职提起 */
  sepDate?: string;
  /** start */
  start: number;
  /** startIndex */
  startIndex?: number;
  /** status */
  status?: string;
  /** 小合同表别名,控制小合同权限用 */
  subcontractAlias?: string;
  /** 小合同id */
  subcontractId?: string;
  /** subsidyAppStatus */
  subsidyAppStatus?: string;
  /** 补充公积金账号 */
  supProvidentFundAccount?: string;
  /** 职称发证日期 */
  techAwardDate?: string;
  /** 职称发证单位 */
  techAwardUnit?: string;
  /** 第二次导社保是否删除该雇员的社保，临时字段 */
  tmpDeleteSS?: string;
  /** type */
  type?: number;
  /** 记录修改身份证号时的信息 */
  upateIdcRemark?: string;
  /** 修改人 */
  updateBy?: string;
  /** 修改日期 */
  updateDt?: string;
  /** uploadDT */
  uploadDT?: string;
  /** upt */
  upt?: boolean;
  /** 用户id,控制小合同权限用 */
  userId?: string;
  /** 用户管理用户表别名,控制客户权限用 */
  userManageUserAlias?: string;
  /** 用户名 */
  userName?: string;
  /** 参加工作时间 */
  workDt?: string;
  /** 用工形式0:标准工时,1:综合共时,2:不定时 */
  workType?: string;
  /** 邮政编码 */
  zipCode?: string;
}

export const init = new defs.emphiresep.Page();
export const url =
  '/rhro-service-1.0/employeeBaseInfo/getAgentWageEmployeeBankInfo:POST';
export const initialUrl =
  '/rhro-service-1.0/employeeBaseInfo/getAgentWageEmployeeBankInfo';
export const cacheKey = '_employeeBaseInfo_getAgentWageEmployeeBankInfo_POST';
export async function request(data: Params, options?: RequestConfig) {
  const reqUrl = `/rhro-service-1.0/employeeBaseInfo/getAgentWageEmployeeBankInfo`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          reolve(res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
export async function requests(data: Params, options?: RequestConfig) {
  const reqUrl = `/rhro-service-1.0/employeeBaseInfo/getAgentWageEmployeeBankInfo`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          if (checkResStatus(res)) return reolve(res ? res.data : res);
          reject(res ? res.message : res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
