import * as defs from '../../baseClass';
import {
  fetch,
  RequestConfig,
  beforeFetch,
  afterFetch,
  checkResStatus,
  removeSpace,
} from '@/utils/request';

/**
     * @url /rhro-service-1.0/threeToOne/branch/getApplyTitleByPayAddress
     * @desc 社保公积金缴纳主体 记录对应分公司的城市，相同省下的所有有效的分公司下签约分公司抬头记录。
社保公积金缴纳主体 记录对应分公司的城市，相同省下的所有有效的分公司下签约分公司抬头记录。
     * hasForm: false
     * hasBody: true
     */

export class Params {
  /** payAddress */
  payAddress: string;
}

export const init = [];
export const url =
  '/rhro-service-1.0/threeToOne/branch/getApplyTitleByPayAddress:GET';
export const initialUrl =
  '/rhro-service-1.0/threeToOne/branch/getApplyTitleByPayAddress';
export const cacheKey = '_threeToOne_branch_getApplyTitleByPayAddress_GET';
export async function request(params: Params, options?: RequestConfig) {
  const reqUrl = `/rhro-service-1.0/threeToOne/branch/getApplyTitleByPayAddress`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',
    params: removeSpace(params),

    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .get(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          reolve(res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
export async function requests(params: Params, options?: RequestConfig) {
  const reqUrl = `/rhro-service-1.0/threeToOne/branch/getApplyTitleByPayAddress`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',
    params: removeSpace(params),

    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .get(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          if (checkResStatus(res)) return reolve(res ? res.data : res);
          reject(res ? res.message : res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
