import * as defs from '../../baseClass';
import {
  fetch,
  RequestConfig,
  beforeFetch,
  afterFetch,
  checkResStatus,
  removeSpace,
} from '@/utils/request';

/**
     * @url /rhro-service-1.0/socialBatch/rpac/getStampFileTypeRemind
     * @desc 获取rpac的签章提示信息
获取rpac的签章提示信息
     * hasForm: false
     * hasBody: true
     */

export class Params {
  /** ssBatchId */
  ssBatchId: string;
}

export const init = new defs.welfaremanage.FilterEntity();
export const url =
  '/rhro-service-1.0/socialBatch/rpac/getStampFileTypeRemind:GET';
export const initialUrl =
  '/rhro-service-1.0/socialBatch/rpac/getStampFileTypeRemind';
export const cacheKey = '_socialBatch_rpac_getStampFileTypeRemind_GET';
export async function request(params: Params, options?: RequestConfig) {
  const reqUrl = `/rhro-service-1.0/socialBatch/rpac/getStampFileTypeRemind`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',
    params: removeSpace(params),

    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .get(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          reolve(res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
export async function requests(params: Params, options?: RequestConfig) {
  const reqUrl = `/rhro-service-1.0/socialBatch/rpac/getStampFileTypeRemind`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',
    params: removeSpace(params),

    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .get(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          if (checkResStatus(res)) return reolve(res ? res.data : res);
          reject(res ? res.message : res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
