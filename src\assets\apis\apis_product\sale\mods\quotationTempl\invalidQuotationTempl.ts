import * as defs from '../../baseClass';
import {
  fetch,
  RequestConfig,
  beforeFetch,
  afterFetch,
  checkResStatus,
  removeSpace,
} from '@/utils/request';

/**
     * @url /rhro-service-1.0/quotationTempl/invalidQuotationTempl
     * @desc 失效
失效
     * hasForm: false
     * hasBody: true
     */

export class Params {}

export const init = new defs.sale.CommonResponse();
export const url =
  '/rhro-service-1.0/quotationTempl/invalidQuotationTempl:POST';
export const initialUrl =
  '/rhro-service-1.0/quotationTempl/invalidQuotationTempl';
export const cacheKey = '_quotationTempl_invalidQuotationTempl_POST';
export async function request(
  data: defs.sale.quotationTemplDTO,
  options?: RequestConfig,
) {
  const reqUrl = `/rhro-service-1.0/quotationTempl/invalidQuotationTempl`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          reolve(res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
export async function requests(
  data: defs.sale.quotationTemplDTO,
  options?: RequestConfig,
) {
  const reqUrl = `/rhro-service-1.0/quotationTempl/invalidQuotationTempl`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          if (checkResStatus(res)) return reolve(res ? res.data : res);
          reject(res ? res.message : res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
