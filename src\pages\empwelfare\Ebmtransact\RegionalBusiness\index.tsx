import React, { useState } from 'react';
import { CachedPage } from '@/components/CachedPage';
import { EditeFormProps } from '@/components/EditeForm';
import { Button, Form, FormInstance, Modal, message } from 'antd';
import { WritableColumnProps } from '@/utils/writable/types';
import { WritableInstance, useWritable } from '@/components/Writable';
import { AsyncButton } from '@/components/Forms/Confirm';
import { mapToSelectors } from '@/components/Selectors/FuncSelectors';
import { AuthButtons } from '@/components/Forms/RenderButtons';
import {
  BusinessBigByBusTypeSelector,
  CitySelector,
  GetBaseBusnameClassDropdownList,
  GetBusinessContentByBusTypeSelector,
} from '@/components/Selectors/BaseDataSelectors';
import BusinessContentForm from './components/BusinessContentForm';
import BusinessNodeForm from './components/BusinessNodeForm';
import BusinessNodeMaterialForm from './components/BusinessNodeMaterialForm';
import { isEmpty } from 'lodash';
import { getCurrentUserCityId } from '@/utils/model';
import {
  busTypeMap,
  transactObjectMap,
  transactPropertyMap,
  transactTypeMap,
} from '@/utils/settings/empwelfare/businessProcessing';
import { yesNoDataMap } from '@/utils/settings/basedata/comboManage';

const service = API.welfaremanage.ebmBusinessCityConfig.selectBusinessCityConfigPage;
const serviceNode = API.welfaremanage.ebmBusinessCityConfig.selectBusinessCityNodeConfigPage;
const serviceMaterial = API.welfaremanage.ebmBusinessCityConfig.selectBusinessCnMaterialPage;
const deleteService = API.welfaremanage.ebmBusinessCityConfig.deleteBusinessCityConfig;
const deleteNodeService = API.welfaremanage.ebmBusinessCityConfig.deleteBusinessCityNodeConfig;
const deleteMaterialService = API.welfaremanage.ebmBusinessCityConfig.deleteBusinessCnMaterial;

const updateStateService = API.welfaremanage.ebmBusinessCityConfig.updateCityState;

const RegionalBusiness = () => {
  const [form] = Form.useForm();

  const defaultCityId = getCurrentUserCityId() || '';

  // 业务内容弹窗状态
  const [businessCityContentVisible, setBusinessCityContentVisible] = useState(false);
  const [businessCityContentInfo, setBusinessCityContentInfo] = useState<any>({});

  // 业务节点弹窗状态
  const [businessCityNodeVisible, setBusinessCityNodeVisible] = useState(false);
  const [businessCityNodeInfo, setBusinessCityNodeInfo] = useState<any>({});

  const [businessCityNodeMaterialVisible, setBusinessCityNodeMaterialVisible] = useState(false);
  const [businessCityNodeMaterialInfo, setBusinessCityNodeMaterialInfo] = useState<any>({});

  // 选中的业务内容记录
  const [selectedBusinessCityContent, setSelectedBusinessCityContent] = useState<any>(null);
  const [selectedBusinessCityNode, setSelectedBusinessCityNode] = useState<any>(null);

  const writable = useWritable({
    service: service,
    onSuccess(data) {
      if (isEmpty(data?.list)) {
        setSelectedBusinessCityContent(null);
        wriTableNode.setNewData([]);
        return;
      }
      wriTableNode.request({ busCityConfigId: data?.list?.[0]?.busCityConfigId });
      setSelectedBusinessCityContent(data?.list?.[0]);
    },
  });

  const wriTableNode = useWritable({
    service: serviceNode,
    onSuccess(data) {
      if (isEmpty(data?.list)) {
        setSelectedBusinessCityNode(null);
        wriTableNode.setNewData([]);
        wriTableMaterial?.setNewData([]);
        return;
      }

      wriTableMaterial.request({ busCityNodeConfigId: data?.list?.[0]?.busCityNodeConfigId });
      setSelectedBusinessCityNode(data?.list?.[0]);
    },
  });

  const wriTableMaterial = useWritable({
    service: serviceMaterial,
  });

  // 处理业务内容选择
  const handleBusinessContentSelect = (record: any) => {
    setSelectedBusinessCityContent(record);
    if (record?.busCityConfigId === selectedBusinessCityContent?.busCityConfigId) return;
    if (record?.busCityConfigId) {
      wriTableNode.request({ busCityConfigId: record?.busCityConfigId });
    }
  };

  // 处理新增业务内容
  const handleAddBusinessCityContent = () => {
    setBusinessCityContentInfo({});
    setBusinessCityContentVisible(true);
  };

  // 处理修改业务内容
  const handleEditBusinessCityContent = (options: WritableInstance) => {
    const selectedRows = options.selectedRows;
    if (!selectedRows || selectedRows.length === 0) {
      message.error('请选择要修改的记录');
      return;
    }
    if (selectedRows.length > 1) {
      message.error('只能选择一条记录进行修改');
      return;
    }
    setBusinessCityContentInfo(selectedRows[0]);
    setBusinessCityContentVisible(true);
  };

  // 处理删除业务内容
  const handleDeleteBusinessCityContent = async (options: WritableInstance) => {
    if (!options.selectedRows || options.selectedRows.length === 0) {
      message.error('请选择要删除的记录');
      return;
    }

    // 检查是否有被引用的记录
    const hasReferencedRecord = options.selectedRows.some((item) => item.refStr === '1');
    if (hasReferencedRecord) {
      message.error('当前业务内容已被引用，无法删除，请知悉。');
      return;
    }

    const busCityConfigIds = options.selectedRows.map((item) => item.busCityConfigId.toString());
    Modal.confirm({
      content: `将删除所选业务内容，是否继续?`,
      okText: '是',
      cancelText: '否',
      onOk: async () => {
        await deleteService.requests(busCityConfigIds);
        message.success('删除成功');
        options.request();
      },
    });
  };

  // 处理发布业务内容
  const handlePublishBusinessContent = async (options: WritableInstance) => {
    if (!options.selectedRows || options.selectedRows.length === 0) {
      message.error('请选择要发布的记录');
      return;
    }

    const hasPublishedRecord = options.selectedRows.filter((item) => item.isValid === '0');
    if (hasPublishedRecord.length === 0) {
      message.error('所选记录中没有要发布的业务内容');
      return;
    }

    const publishData = hasPublishedRecord.map((item) => ({ ...item, isValid: '1' }));

    Modal.confirm({
      content: `将发布所选业务内容，是否继续?`,
      okText: '是',
      cancelText: '否',
      onOk: async () => {
        await updateStateService.requests(publishData);
        message.success('发布成功');
        options.request();
      },
    });
  };

  // 处理失效业务内容
  const handleInvalidateBusinessCityContent = async (options: WritableInstance) => {
    if (!options.selectedRows || options.selectedRows.length === 0) {
      message.error('请选择要失效的记录');
      return;
    }
    const validRecords = options.selectedRows.filter((item) => item.isValid === '1');

    if (validRecords.length === 0) {
      message.error('所选记录中没有要失效的业务内容');
      return;
    }

    const invalidateData = validRecords.map((item) => ({ ...item, isValid: '0' }));

    Modal.confirm({
      content: `将失效所选业务内容，是否继续?`,
      okText: '是',
      cancelText: '否',
      onOk: async () => {
        await updateStateService.requests(invalidateData);
        message.success('失效成功');
        options.request();
      },
    });
  };

  // 处理导出数据
  const handleExportData = (options: WritableInstance) => {
    options.handleExport(
      { service: API.welfaremanage.ebmBusinessCityConfig.exportExcel },
      {
        columns,
        condition: { ...options.queries },
        fileName: '各地业务维护.xlsx',
      },
    );
  };

  // 处理业务节点选择
  const handleBusinessCityNodeSelect = (record: any) => {
    setSelectedBusinessCityNode(record);
    if (record?.busCityNodeConfigId === selectedBusinessCityContent?.busCityNodeConfigId) return;
    if (record?.busCityNodeConfigId) {
      wriTableMaterial.request({ busCityNodeConfigId: record?.busCityNodeConfigId });
    }
  };

  // 处理新增业务节点
  const handleAddBusinessCityNode = () => {
    if (
      selectedBusinessCityContent?.transactProperty === '2' &&
      wriTableNode.data.list.length >= 1
    ) {
      message.error('当前业务内容属于“单次业务”，只能新增一个节点。');
      return;
    }
    if (!selectedBusinessCityContent) {
      message.error('请先选择业务内容，再新增业务节点');
      return;
    }
    setBusinessCityNodeInfo({
      busCityConfigId: selectedBusinessCityContent?.busCityConfigId,
      type: 'add',
    });
    setBusinessCityNodeVisible(true);
  };

  // 处理修改业务节点
  const handleEditBusinessCityNode = (options: WritableInstance) => {
    const selectedRows = options.selectedRows;
    if (!selectedRows || selectedRows.length === 0) {
      message.error('请选择要修改的记录');
      return;
    }
    if (selectedRows.length > 1) {
      message.error('只能选择一条记录进行修改');
      return;
    }
    setBusinessCityNodeInfo(selectedRows[0]);
    setBusinessCityNodeVisible(true);
  };

  // 处理删除业务节点
  const handleDeleteBusinessCityNode = async (options: WritableInstance) => {
    if (!options.selectedRows || options.selectedRows.length === 0) {
      message.error('请选择要删除的记录');
      return;
    }

    // 检查是否有被引用的记录
    const hasReferencedRecord = options.selectedRows.some((item) => item.refStr === '1');
    if (hasReferencedRecord) {
      message.error('所选记录中包含已被引用的业务节点，无法删除');
      return;
    }

    const busNodeConfigIds = options.selectedRows.map((item) => item.busNodeConfigId);
    Modal.confirm({
      content: `将删除所选业务节点，是否继续?`,
      okText: '是',
      cancelText: '否',
      onOk: async () => {
        await deleteNodeService.requests(busNodeConfigIds);
        message.success('删除成功');
        options.request();
      },
    });
  };

  // 处理新增业务节点材料
  const handleAddBusinessCityNodeMaterial = () => {
    setBusinessCityNodeMaterialInfo({
      type: 'add',
    });
    setBusinessCityNodeMaterialVisible(true);
  };

  // 处理修改业务节点材料
  const handleEditBusinessCityNodeMaterial = (options: WritableInstance) => {
    const selectedRows = options.selectedRows;
    if (!selectedRows || selectedRows.length === 0) {
      message.error('请选择要修改的记录');
      return;
    }
    if (selectedRows.length > 1) {
      message.error('只能选择一条记录进行修改');
      return;
    }
    setBusinessCityNodeMaterialInfo({
      ...selectedRows?.[0],
    });
    setBusinessCityNodeMaterialVisible(true);
  };

  // 处理删除业务节点材料
  const handleDeleteBusinessCityNodeMaterial = async (options: WritableInstance) => {
    if (!options.selectedRows || options.selectedRows.length === 0) {
      message.error('请选择要删除的记录');
      return;
    }

    const busCnMaterialIds = options.selectedRows.map((item) => item.busCnMaterialId);
    Modal.confirm({
      content: `将删除所选节点材料，是否继续?`,
      okText: '是',
      cancelText: '否',
      onOk: async () => {
        await deleteMaterialService.requests(busCnMaterialIds);
        message.success('删除成功');
        options.request();
      },
    });
  };

  const formColumns: EditeFormProps[] = [
    {
      label: '城市',
      fieldName: 'cityId',
      inputProps: {
        initialValue: defaultCityId,
      },
      inputRender: () => (
        <CitySelector allowClear keyMap={{ cityId: 'key', cityName: 'shortName' }} />
      ),
    },
    {
      label: '业务类型',
      fieldName: 'categoryId',
      inputRender: (outerForm: FormInstance) => {
        return mapToSelectors(busTypeMap, {
          allowClear: true,
          placeholder: '请选择业务类型',
          onChange: () => {
            outerForm.setFieldsValue({ busnameClassId: undefined, busContent: undefined });
          },
        });
      },
    },
    {
      label: '业务项目',
      fieldName: 'busnameClassId',
      inputRender: (outerForm: FormInstance) => (
        <GetBaseBusnameClassDropdownList
          allowClear
          params={{ categoryId: outerForm.getFieldValue('categoryId') ?? '' }}
        />
      ),
      shouldUpdate: (prevValues, curValues) => {
        return prevValues.categoryId !== curValues.categoryId;
      },
    },
    {
      label: '业务内容',
      fieldName: 'busContent',
      inputRender: (outerForm: FormInstance) => (
        <GetBusinessContentByBusTypeSelector
          params={{
            busnameClassId: outerForm.getFieldValue('busnameClassId') ?? '',
            categoryId: outerForm.getFieldValue('categoryId') ?? '',
          }}
          onChange={(value: any, option: any) => {
            // outerForm.setFieldsValue({ busContent: option?.title });
          }}
          allowClear
        />
      ),
      shouldUpdate: (prevValues, curValues) => {
        return (
          prevValues.categoryId !== curValues.categoryId ||
          prevValues.busnameClassId !== curValues.busnameClassId
        );
      },
    },
    {
      label: '办理属性',
      fieldName: 'transactProperty',
      inputRender: () => mapToSelectors(transactPropertyMap, { allowClear: true }),
    },
    {
      label: '办理对象',
      fieldName: 'transactObject',
      inputRender: () => mapToSelectors(transactObjectMap, { allowClear: true }),
    },
    {
      label: '办理方式',
      fieldName: 'transactType',
      inputRender: () => mapToSelectors(transactTypeMap, { allowClear: true }),
    },
    {
      label: '是否微信显示',
      fieldName: 'wxShowState',
      inputRender: () => mapToSelectors(yesNoDataMap, { allowClear: true }),
    },
    {
      label: '是否客户端显示',
      fieldName: 'clientShowState',
      inputRender: () => mapToSelectors(yesNoDataMap, { allowClear: true }),
    },
  ];

  const columns: WritableColumnProps<any>[] = [
    { title: '所属城市', dataIndex: 'cityName' },
    { title: '业务类型', dataIndex: 'categoryName' },
    { title: '业务项目', dataIndex: 'busnameClassName' },
    { title: '业务内容', dataIndex: 'busContent' },
    { title: '办理属性', dataIndex: 'transactPropertyName' },
    { title: '办理对象', dataIndex: 'transactObjectName' },
    { title: '办理方式', dataIndex: 'transactTypeName' },
    { title: '是否微信显示', dataIndex: 'wxShowStateName' },
    { title: '是否客户端显示', dataIndex: 'clientShowStateName' },
    { title: '业务内容说明', dataIndex: 'busContentDesc' },

    { title: '入离职状态', dataIndex: 'orderRequireName' },
    { title: '缴纳产品要求', dataIndex: 'productRequireName' },
    { title: '实做状态', dataIndex: 'doRequireName' },
    { title: '员工自主办理途径', dataIndex: 'empSelfContent' },
    { title: '业务内容状态', dataIndex: 'isValidName' },
    { title: '是否被引用', dataIndex: 'refStr' },
  ];

  const businessNodeColumns: WritableColumnProps<any>[] = [
    { title: '业务节点顺序', dataIndex: 'busCityNodeNum' },
    { title: '业务节点', dataIndex: 'busCityNodeConfigName' },
    { title: '是否收取资料', dataIndex: 'isCollectMaterialName' },
    { title: '是否支付津贴待遇', dataIndex: 'isAllowanceName' },
    { title: '办理周期', dataIndex: 'transactPeriod' },
    { title: '业务节点状态', dataIndex: 'isValidName' },
    { title: '是否被引用', dataIndex: 'refStr' },
  ];

  const businessMaterialColumns: WritableColumnProps<any>[] = [
    { title: '材料编号', dataIndex: 'busCnMaterialId' },
    { title: '材料名称', dataIndex: 'materialsName' },
    { title: '是否原件', dataIndex: 'isOriginalName' },
    { title: '材料数量', dataIndex: 'materialNum' },
    { title: '是否返还材料', dataIndex: 'isReturnMaterialName' },
    { title: '材料模板', dataIndex: 'materialFileId' },
    { title: '用印签字方', dataIndex: 'signatoryPartyName' },
  ];

  const renderButtons = (options: WritableInstance) => (
    <>
      <Button type="primary" htmlType="submit">
        查询
      </Button>
      <AuthButtons funcId="regional_business_btn">
        <Button onClick={handleAddBusinessCityContent}>新增</Button>
      </AuthButtons>
      <AuthButtons funcId="regional_business_btn">
        <Button
          onClick={() => handleEditBusinessCityContent(options)}
          disabled={options?.selectedRows?.length !== 1}
        >
          修改
        </Button>
      </AuthButtons>
      <AuthButtons funcId="regional_business_btn">
        <Button
          onClick={() => handleDeleteBusinessCityContent(options)}
          disabled={options?.selectedRows?.length === 0}
        >
          删除
        </Button>
      </AuthButtons>
      <AuthButtons funcId="regional_business_btn">
        <Button
          onClick={() => handlePublishBusinessContent(options)}
          disabled={options?.selectedRows?.length === 0}
        >
          发布
        </Button>
      </AuthButtons>
      <AuthButtons funcId="regional_business_btn">
        <Button
          onClick={() => handleInvalidateBusinessCityContent(options)}
          disabled={options?.selectedRows?.length === 0}
        >
          失效
        </Button>
      </AuthButtons>
      <AuthButtons funcId="regional_business_btn">
        <AsyncButton onClick={() => handleExportData(options)}>导出数据</AsyncButton>
      </AuthButtons>
      {/* 业务内容弹窗 */}
      <BusinessContentForm
        modal={[businessCityContentVisible, setBusinessCityContentVisible]}
        listOptions={options}
        initialInfo={businessCityContentInfo}
        defaultCityId={defaultCityId}
      />
    </>
  );

  const renderBusinessNodeButtons = (options: WritableInstance) => (
    <>
      <AuthButtons funcId="regional_business_btn">
        <Button disabled={!selectedBusinessCityContent} onClick={handleAddBusinessCityNode}>
          新增
        </Button>
      </AuthButtons>
      <AuthButtons funcId="regional_business_btn">
        <Button
          onClick={() => handleEditBusinessCityNode(options)}
          disabled={options?.selectedRows?.length !== 1}
        >
          修改
        </Button>
      </AuthButtons>
      <AuthButtons funcId="regional_business_btn">
        <Button
          onClick={() => handleDeleteBusinessCityNode(options)}
          disabled={options?.selectedRows?.length === 0}
        >
          删除
        </Button>
      </AuthButtons>
      {/* 业务节点弹窗 */}
      <BusinessNodeForm
        modal={[businessCityNodeVisible, setBusinessCityNodeVisible]}
        listOptions={options}
        initialInfo={{
          ...businessCityNodeInfo,
          busConfigId: selectedBusinessCityContent?.busConfigId,
          refStr: selectedBusinessCityContent?.refStr,
        }}
      />
    </>
  );

  const renderBusinessMaterialButtons = (options: WritableInstance) => (
    <>
      <AuthButtons funcId="regional_business_btn">
        <Button disabled={!selectedBusinessCityNode} onClick={handleAddBusinessCityNodeMaterial}>
          新增
        </Button>
      </AuthButtons>
      <AuthButtons funcId="regional_business_btn">
        <Button
          onClick={() => handleEditBusinessCityNodeMaterial(options)}
          disabled={options?.selectedRows?.length !== 1}
        >
          修改
        </Button>
      </AuthButtons>
      <AuthButtons funcId="regional_business_btn">
        <Button
          onClick={() => handleDeleteBusinessCityNodeMaterial(options)}
          disabled={options?.selectedRows?.length === 0}
        >
          删除
        </Button>
      </AuthButtons>
      {/* 节点材料弹窗 */}
      <BusinessNodeMaterialForm
        modal={[businessCityNodeMaterialVisible, setBusinessCityNodeMaterialVisible]}
        listOptions={options}
        initialInfo={{
          ...businessCityNodeMaterialInfo,
          busConfigId: selectedBusinessCityContent?.busConfigId,
          busCityNodeConfigId: selectedBusinessCityNode?.busCityNodeConfigId,
        }}
      />
    </>
  );

  return (
    <>
      <CachedPage
        service={service}
        formColumns={formColumns}
        columns={columns}
        renderButtons={renderButtons}
        form={form}
        rowKey="busCityConfigId"
        selectedSingleRow={selectedBusinessCityContent}
        onSelectSingleRow={handleBusinessContentSelect}
        wriTable={writable}
      />
      <CachedPage
        service={serviceNode}
        formColumns={[]}
        columns={businessNodeColumns}
        renderButtons={renderBusinessNodeButtons}
        rowKey="busCityNodeConfigId"
        selectedSingleRow={selectedBusinessCityNode}
        onSelectSingleRow={handleBusinessCityNodeSelect}
        wriTable={wriTableNode}
      />
      <CachedPage
        service={serviceMaterial}
        formColumns={[]}
        columns={businessMaterialColumns}
        renderButtons={renderBusinessMaterialButtons}
        rowKey="busCnMaterialId"
        wriTable={wriTableMaterial}
      />
    </>
  );
};

export default RegionalBusiness;
