import * as defs from '../../baseClass';
import {
  fetch,
  RequestConfig,
  beforeFetch,
  afterFetch,
  checkResStatus,
  removeSpace,
} from '@/utils/request';

/**
     * @url /rhro-service-1.0/payBatch/getpostscriptRmkList
     * @desc 根据type查询数据字典数据 附言的type为12335
根据type查询数据字典数据 附言的type为12335
     * hasForm: false
     * hasBody: true
     */

export class Params {}

export const init = new defs.payroll.CommonResponse();
export const url = '/rhro-service-1.0/payBatch/getpostscriptRmkList:GET';
export const initialUrl = '/rhro-service-1.0/payBatch/getpostscriptRmkList';
export const cacheKey = '_payBatch_getpostscriptRmkList_GET';
export async function request(options?: RequestConfig) {
  const reqUrl = `/rhro-service-1.0/payBatch/getpostscriptRmkList`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .get(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          reolve(res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
export async function requests(options?: RequestConfig) {
  const reqUrl = `/rhro-service-1.0/payBatch/getpostscriptRmkList`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .get(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          if (checkResStatus(res)) return reolve(res ? res.data : res);
          reject(res ? res.message : res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
