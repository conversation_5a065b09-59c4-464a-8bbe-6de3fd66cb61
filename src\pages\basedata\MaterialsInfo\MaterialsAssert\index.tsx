/*
 * @Author: 陈刚强
 * @Email: <EMAIL>
 * @Date: 2020-09-29 09:34:16
 * @LastAuthor: 陈刚强
 * @LastTime: 2020-12-21 13:45:41
 * @message:
 */
import React, { useState } from 'react';
import { Button } from 'antd';
import { CachedPage, CachedTableOptionsType } from '@/components/CachedPage';
import { EditeFormProps } from '@/components/EditeForm';
import { WritableColumnProps } from '@/utils/writable/types';
import { ConfirmButton } from '@/components/Forms/Confirm';
import { WritableInstance, useWritable } from '@/components/Writable';
import { pageInit } from '@/utils/methods/pagenation';
import EditModal from './components/editModal';
import { BooleanSelector } from '@/components/Selectors/BaseDropDown';
import { msgErr } from '@/utils/methods/message';
import { AuthButtons } from '@/components/Forms/RenderButtons';
import { DeptmentSelector } from '@/components/Selectors';
import { getCurrentMenu, getCurrentUser } from '@/utils/model';
const isOrgin = new Map<string, string>([
  ['0', '否'],
  ['1', '是'],
]);
let optionFunc: CachedTableOptionsType = {} as CachedTableOptionsType;
const formColumns: EditeFormProps[] = [
  {
    label: '分公司',
    fieldName: 'departmentId',
    inputRender: () => (
      <DeptmentSelector keyMap={{ branchId: 'key', branchName: 'shortName' }} allowClear />
    ),
  },
  {
    label: '材料名称',
    fieldName: 'materialsName',
    inputRender: 'string',
  },
];
const columns: WritableColumnProps<any>[] = [
  { title: '材料编号', dataIndex: 'materialsId' },
  {
    title: '材料名称',
    dataIndex: 'materialsName',
    inputRender: 'string',
    rules: [{ required: true, message: '请填写材料名称' }],
  },
  {
    title: '是否原件',
    dataIndex: 'isOriginal',
    inputRender: () => <BooleanSelector />,
    rules: [{ required: true, message: '请选择是否原件' }],
    render: (value: any) => <BooleanSelector code={value} />,
  },
  { title: '创建人', dataIndex: 'createName' },
  { title: '创建时间', dataIndex: 'createDt' },
  { title: '创建人分公司', dataIndex: 'branchName' },
  {
    title: '备注',
    dataIndex: 'remark',
    inputRender: 'string',
  },
  {
    title: '是否引用',
    dataIndex: 'isRef',
    // inputRender: () => <BooleanSelector />,
    render: (text) => isOrgin.get(text),
  },
];

interface AreaCodeAssertProps {
  [props: string]: any;
}

const materialsService = API.basedata.materialsInfo.listMaterialsInfoPage;
const materialsSaveService = API.basedata.materialsInfo.saveMaterialsInfo;
const materialsDelService = API.basedata.materialsInfo.delMaterialsInfo;

const AreaCode: React.FC<AreaCodeAssertProps> = () => {
  // 优化这个判断， 按钮权限也会用到不同菜单不同的funcCode，
  const currentMenu: any = getCurrentMenu();
  const service = currentMenu?.funcId === '80451000' ? materialsService : materialsService; // TODO: Add different service for 80453000
  const saveService = currentMenu?.funcId === '80451000' ? materialsSaveService : materialsSaveService; // TODO: Add different service for 80453000
  const delService = currentMenu?.funcId === '80451000' ? materialsDelService : materialsDelService; // TODO: Add different service for 80453000
  const [data, setData] = useState();
  const wriTable: WritableInstance = useWritable({ service });
  const wriTable2: WritableInstance = useWritable({ service: saveService });
  const [visible, setVisible] = useState(false);
  const handleUpdate = (_row: any) => {};
  const userInfo = getCurrentUser();
  const { governingBranch } = userInfo.profile;
  const editForm = () => {
    const { selectedRows } = optionFunc;
    if (selectedRows[0].governingBranch !== governingBranch) {
      msgErr('只能修改当前登录人对应分公司的数据，其他分公司的数据无法修改。');
      return;
    }
    wriTable2.setNewData({ list: selectedRows, pagination: pageInit });
    setData(selectedRows[0]);
    setVisible(true);
  };

  const handleSave = () => {
    wriTable.handleSave({
      service: saveService,
      data: (value) => {
        const arr: POJO = [];
        value.added.map((item) => {
          arr.push({
            isUsed: item.isUsed,
            clientOperation: item.clientOperation,
            aska: item.aska,
            materialsName: item.materialsName,
            createName: item.createName,
            isOriginal: item.isOriginal,
            remark: item.remark,
            isRef: item.isRef,
          });
        });
        return arr;
      },
    });
  };
  const handleDelete = () => {
    // wriTable.handleDelete({
    //   service: delService,
    //   data: (value) => value.map((item: any) => item.materialsId),
    // });
    const { selectedRows } = wriTable;
    if (!selectedRows.length) {
      msgErr('请先选择要删除的数据');
      return;
    } else {
      // 如果是新增的数据直接删除本地数据，如果是远程数据则请求接口删除
      const result = selectedRows.find((value) => value.materialsId);
      if (result) {
        wriTable.handleDelete({
          service: delService,
          data: (value) => value.map((item: any) => item.materialsId),
        });
      } else {
        wriTable.deleteRows(wriTable.selectedRows);
      }
    }
  };

  const renderButtons = (_options: WritableInstance) => {
    optionFunc = _options;
    const cantEdite = wriTable.selectedRows.length !== 1;
    return (
      <>
        <Button type="primary" htmlType="submit">
          查询
        </Button>
        <AuthButtons funcCode="materials_add">
          <Button onClick={() => wriTable.addRows({ isUsed: '是' })}>新增</Button>
        </AuthButtons>
        <AuthButtons funcCode="materials_save">
          <ConfirmButton service={saveService} onClick={() => handleSave()}>
            保存
          </ConfirmButton>
        </AuthButtons>
        <AuthButtons funcCode="materials_del">
          <Button onClick={() => handleDelete()}>删除</Button>
        </AuthButtons>
        <AuthButtons funcCode="materials_upt">
          <Button disabled={cantEdite} onClick={() => editForm()}>
            修改
          </Button>
        </AuthButtons>
      </>
    );
  };
  const cancel = () => {
    setVisible(false);
  };
  const ok = () => {
    optionFunc.request();
  };
  const editModalProps = { visible, cancel, data, ok };
  return (
    <CachedPage
      service={service}
      columns={columns}
      formColumns={formColumns}
      renderButtons={renderButtons}
      onSelectDoubleRow={handleUpdate}
      colNumber={4}
      editable="add"
      wriTable={wriTable as any}
      handleQueries={(values) => ({ ...values, governingBranch: values.departmentId })}
    >
      <EditModal {...editModalProps} />
    </CachedPage>
  );
};

export default AreaCode;
