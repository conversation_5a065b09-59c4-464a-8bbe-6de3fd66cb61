/*
 * @Author: Veni_刘夏梅
 * @Email: <EMAIL>
 * @Date: 2020-09-30 14:39:09
 * @LastAuthor: 侯成
 * @LastTime: 2021-06-11 10:55:00
 * @message: 社保、公积金查询
 */
import { Button, Form, Modal, Typography } from 'antd';
import React, { useEffect, useState } from 'react';
import { StandardTableColumnProps } from '@/components/StandardTable';
import { msgCall, msgErr, msgOk } from '@/utils/methods/message';
import { CachedPage } from '@/components/CachedPage';
import { EditeFormProps } from '@/components/EditeForm';
import { GroupPop } from '@/components/StandardPop/GroupPop';
import { CustomerPop } from '@/components/StandardPop/CustomerPop';
import { CsPop, InnerUserPop, SocialCsPop } from '@/components/StandardPop/InnerUserPop';
import { DateRange, MonthRange } from '@/components/DateRange4';
import { DeptmentSelector, ForPartyByDeptIdSelector, mapToSelectors } from '@/components/Selectors';
import {
  areaTypeDataMap,
  preStatusComboListMap,
  queryModeComboListMap,
  sufStatusComboListMap,
} from '@/utils/settings/welfaremanage/Socialmanage/querySocialSecurity';
import { mapToCheckbox } from '@/components/Selectors/FuncCheckbox';
import { isEmpty } from 'lodash';
import { downloadFile, downloadFileWithAlert } from '@/utils/methods/file';
import { getCurrentUser, getCurrentUserCityId } from '@/utils/model';
import { AsyncButton, ConfirmLoading } from '@/components/Forms/Confirm';
import { ButtonSocialFundDetail } from './DetailWin';
import { stdMonthFormatMoDash } from '@/utils/methods/times';
import { SelectValue } from 'antd/lib/select';
import { WritableInstance } from '@/components/Writable';
import { BizUtil } from '@/utils/settings/bizUtil';
import { customerSize } from '@/utils/settings/sysmanage/allocateService';
import { CustPayEntityPop } from '@/components/StandardPop/CustPayEntityPop';
import { sendChannelMap } from '@/utils/settings/welfaremanage/Socialmanage';
import { yesNoDataMap } from '@/utils/settings/basedata/comboManage';

interface SocialSecurityProps {
  type: string;
}

const queryBatch = API.welfaremanage.socialManage.getSocialSecurityList;
const exportService = API.welfaremanage.socialManage.toDownLoad;
const updatePayservice = API.welfaremanage.socialManage.updatePayFlags;
const verificationFileDownService = API.welfaremanage.socialBatch.downloadFile;

let formNewColumns: EditeFormProps[] = [];

const SocialSecurity: React.FC<SocialSecurityProps> = (props) => {
  const { type } = props;
  const service = { ...queryBatch, cacheKey: queryBatch.cacheKey + type };
  const { Text } = Typography;
  const [form] = Form.useForm();
  const cityId = getCurrentUserCityId();
  const privatePopObject = getCurrentUser();
  const [queryMode, setQueryMode] = useState<string>();
  const [deptId, setDeptId] = useState<string | undefined>();
  const [onShowDetailWin, setOnShowDetailWin] = useState<boolean>(false); // 查看

  let options: WritableInstance;

  const onVerificationFileDown = async (verificationFile) => {
    const res = await verificationFileDownService.request(
      { verificationFile },
      { responseType: 'blob' },
    );
    const arr = verificationFile.split('/');
    downloadFileWithAlert(res, arr[arr.length - 1]);
  };

  const exportImportFile = (fileId: number | string, fileName: string, msg?: string) => {
    if (!fileId || !fileName) {
      return msgErr('无文件下载');
    }
    API.commons.file.downloadFile
      .request(
        {
          fileId: String(fileId),
        },
        { responseType: 'blob' },
      )
      .then((res) => {
        if (res && res.type != 'text/html') {
          return downloadFileWithAlert(res, fileName);
        }
        msgCall({ code: 404, msg: msg || '导出失败' });
      });
  };

  const columns: StandardTableColumnProps<defs.workflow.RemindDTO>[] = [
    { title: '姓名', dataIndex: 'empName', fixed: true },
    { title: '唯一号', dataIndex: 'empCode', fixed: true },
    { title: '社保公积金组', dataIndex: 'insuranceName', fixed: true },
    { title: '申报工资', dataIndex: 'decSalary', fixed: true },
    { title: '客户名称', dataIndex: 'custName', fixed: true },
    { title: '客户规模', dataIndex: 'customerSizeName' },
    { title: '客户编号', dataIndex: 'custCode' },
    { title: '服务区域类型', dataIndex: 'areaTypeName' },
    {
      title: '是否需要实做',
      dataIndex: 'status',
      render: (text, record: POJO) => {
        let isProcessName: string = record?.isAddProcessName;
        if (text === 4) {
          isProcessName = record?.isReduceProcessName;
        }
        return <Text>{isProcessName}</Text>;
      },
    },
    {
      title: '附件名称',
      dataIndex: 'fileName',
      hidden: type !== '1',
      render: (text: string, record: POJO) => {
        return (
          <Typography.Link onClick={() => exportImportFile(record.fileId, record.fileName)}>
            {text}
          </Typography.Link>
        );
      },
    },
    { title: '小合同', dataIndex: 'subcontractName' },
    {
      title: '派遣单位名称',
      dataIndex: 'payerName',
    },
    {
      title: '统一社会信用码',
      dataIndex: 'taxpayerIdentifier',
    },
    { title: '证件号码', dataIndex: 'idCardNum' },
    { title: '状态', dataIndex: 'statusName' },
    { title: '申请时间', dataIndex: 'applyDt' },
    { title: '福利办理方', dataIndex: 'welfareProcessorName' },
    { title: '缴费实体', dataIndex: 'custPayEntityName' },
    { title: '办理时间', dataIndex: 'processDt' },
    { title: '办理人', dataIndex: 'processorName' },
    { title: '新增方式', dataIndex: 'processTypeName' },
    { title: '办理月', dataIndex: 'welfareProcessMon' },
    { title: '账号', dataIndex: 'acct' },
    { title: '序号', dataIndex: 'sNo' },
    // {
    //   title: '凭证下载',
    //   dataIndex: 'verificationFile',
    //   render: (text: string, record: POJO) => (
    //     <Link
    //       disabled={!text}
    //       onClick={() => {
    //         onVerificationFileDown(text);
    //       }}
    //     >
    //       下载{' '}
    //     </Link>
    //   ),
    //   hidden: type !== '1',
    // },
    { title: '办停人', dataIndex: 'stopByName' },
    { title: '办停时间', dataIndex: 'stopDt' },
    { title: '停办方式', dataIndex: 'stopTypeName' },
    { title: '福利起始月', dataIndex: 'welfareStartMon' },
    { title: '福利截至月', dataIndex: 'welfareEndMon' },
    { title: '办理过程备注', dataIndex: 'allRemark' },
    { title: '增员通道', dataIndex: 'sendChannelName' },
    { title: '减员通道', dataIndex: 'reduceSendChannelName' },
  ];

  const formColumns: EditeFormProps[] = [
    {
      label: '社保公积金组',
      fieldName: 'ssGroupName',
      inputRender: () => (
        <GroupPop
          rowValue="ssGroupId-ssGroupName"
          keyMap={{ ssGroupId: 'SSGROUPID', ssGroupName: 'INSURANCENAME', cityId: 'CITYID' }}
          fixedValues={{ ssGroupType: type }}
        />
      ),
    },
    { label: '唯一号', fieldName: 'empCode', inputRender: 'string' },
    { label: '员工姓名', fieldName: 'empName', inputRender: 'string' },
    { label: '证件号码', fieldName: 'idCardNum', inputRender: 'string' },
    {
      label: '客户',
      fieldName: 'custId',
      inputRender: () => <CustomerPop rowValue="custId-custName" />,
      colNumber: 2,
    },
    {
      label: '客户规模',
      fieldName: 'customerSize',
      inputRender: () => mapToSelectors(customerSize, { allowClear: true }),
    },
    { label: '小合同编号', fieldName: 'subcontractId', inputRender: 'string' },
    {
      label: '接单客服',
      fieldName: 'assigneeCs',
      inputRender: () => (
        <InnerUserPop
          rowValue="assigneeCs-assigneeCsName"
          keyMap={{ assigneeCs: 'EMPID', assigneeCsName: 'REALNAME' }}
          fixedValues={{ roleCode: BizUtil.ROLE_CS, isDefault: 1 }}
        />
      ),
    },
    {
      label: '办理日期',
      fieldName: 'processDt',
      inputRender: () => (
        <DateRange
          fields={[
            {
              title: '办理日期>=',
              dataIndex: 'processDt',
            },
            {
              title: '办理日期<=',
              dataIndex: 'processEndDt',
            },
          ]}
        />
      ),
    },
    {
      label: '办停时间',
      fieldName: 'stopDt',
      inputRender: () => (
        <DateRange
          fields={[
            {
              title: '办停时间>=',
              dataIndex: 'stopDt',
            },
            {
              title: '办停时间<=',
              dataIndex: 'stopEndDt',
            },
          ]}
        />
      ),
    },
    {
      label: '办理人',
      fieldName: 'processorId',
      inputRender: () => (
        <SocialCsPop
          rowValue="processorId-processorName"
          keyMap={{
            processorId: 'EMPID',
            processorName: 'REALNAME',
          }}
        />
      ),
    },
    {
      label: '办停人',
      fieldName: 'stopByName',
      inputRender: () => (
        <SocialCsPop
          rowValue="stopBy-stopByName"
          keyMap={{
            stopBy: 'EMPID',
            stopByName: 'REALNAME',
          }}
        />
      ),
    },
    {
      label: '办理月',
      fieldName: 'welfareProcessMon',
      inputRender: 'month',
      inputProps: { format: stdMonthFormatMoDash },
    },
    // { label: '', fieldName: '' },
    {
      label: '办理供应商',
      fieldName: 'processProvider',
      inputRender: () => <DeptmentSelector onChange={onProcessProviderChange} />,
    },
    {
      label: '福利办理方',
      fieldName: 'welfareProcessor',
      inputRender: () => {
        const processProvider = form.getFieldValue('processProvider');
        return (
          <ForPartyByDeptIdSelector
            params={{ deptId: processProvider }}
            disabled={!processProvider}
            skipEmptyParam
          />
        );
      },
    },
    {
      label: '福利月份',
      fieldName: 'welfareMon',
      inputRender: () => (
        <MonthRange
          fields={[
            {
              title: '福利起始月',
              dataIndex: 'welfareStartMon',
            },
            {
              title: '福利截至月',
              dataIndex: 'welfareEndMon',
            },
          ]}
          format={stdMonthFormatMoDash}
        />
      ),
    },
    {
      label: '查询方式',
      fieldName: 'queryMode',
      inputRender: () =>
        mapToSelectors(queryModeComboListMap, { onChange: onQueryModeChange, allowClear: false }),
    },
    {
      label: '服务区域类型',
      fieldName: 'areaType',
      inputRender: () => mapToSelectors(areaTypeDataMap),
    },
    {
      label: '缴费实体',
      fieldName: 'custPayEntityId',
      inputRender: () => (
        <CustPayEntityPop
          rowValue="custPayEntityId-custPayEntityName"
          keyMap={{
            custPayEntityId: 'custPayEntityId',
            custPayEntityName: 'custPayEntityName',
          }}
        />
      ),
    },
    {
      label: '发放通道',
      fieldName: 'sendChannelStatus',
      inputRender: () => mapToSelectors(sendChannelMap),
      hidden: type !== '1',
    },
    {
      label: '是否需要实做',
      fieldName: 'isAddProcess',
      inputRender: () => mapToSelectors(yesNoDataMap),
    },
  ];

  // 办理供应商变化
  const onProcessProviderChange = (value: SelectValue) => {
    setDeptId(value);
    form.setFieldsValue({ welfareProcessor: '' });
  };

  useEffect(() => {
    formNewColumns = formColumns;
    formNewColumns = formNewColumns.concat([
      {
        label: '办理后状态',
        fieldName: 'strStatusArr',
        inputRender: () => mapToCheckbox(sufStatusComboListMap),
        colNumber: 2,
      },
    ]);
    setQueryMode('办理后');
  }, []);

  // 动态切换办理状态
  const onQueryModeChange = async (values: string | undefined) => {
    const formValues = form.getFieldsValue();
    formNewColumns.pop();
    if (values === '1') {
      setQueryMode('办理后');
      form.setFieldsValue({ ...formValues, strStatusArr: ['3', '4', '5'] });
      formNewColumns.push({
        label: '办理后状态',
        fieldName: 'strStatusArr',
        inputRender: () => mapToCheckbox(sufStatusComboListMap),
        colNumber: 2,
      });
    } else {
      form.setFieldsValue({ ...formValues, strStatusArr: ['-1', '0', '3', '4'] });
      formNewColumns.push({
        label: '未办理状态',
        fieldName: 'strStatusArr',
        inputRender: () => mapToCheckbox(preStatusComboListMap),
        colNumber: 2,
      });
      setQueryMode('未办理');
    }
  };

  const onCheck = async () => {
    const selectedRows = options.selectedRows;
    const { strStatus } = options.queries;
    if (strStatus != '3' && strStatus != '4') {
      return msgErr('每次反查类型只能1种，请确认是在缴还是停缴');
    }
    if (selectedRows.length == options.queries.pageSize) {
      // 全选
      await API.welfaremanage.rpaPegging.fromSocial.requests({
        ...options.queries,
        pageSize: 9999,
      });
      msgOk('操作成功');
    } else {
      //部分
      await API.welfaremanage.rpaPegging.fromSocial.requests({ processList: selectedRows });
      msgOk('操作成功');
    }
    options.request(options.queries);
  };

  const onPrintEmployee = async (type: string) => {
    const rows: POJO[] = options.selectedRows;
    const values = options.queries;
    let sameResult = true;
    rows.forEach((item) => {
      delete item.allRemark;
      delete item.processRemark;
      delete item.subcontractName;

      if (
        item.ssGroupId !== rows[0]['ssGroupId'] ||
        item.welfareProcessor !== rows[0]['welfareProcessor'] ||
        item.custPayEntityId !== rows[0]['custPayEntityId']
      ) {
        sameResult = false;
      }
    });
    if (!sameResult) return msgErr('必须是同一社保组，同一福利办理方或缴费实体才能批量添加！');
    if (type === '1') {
      await API.welfaremanage.rpaCertificate.empAdd.requests({
        processList: rows,
      });
    } else {
      await API.welfaremanage.rpaCertificate.empBatchAdd.requests({
        processList: rows,
        ssGroupId: rows[0].ssGroupId,
        welfareProcessor: rows[0].welfareProcessor,
      });
    }
    msgOk('操作成功');
  };

  // 操作按钮
  const renderButtons = (_options: WritableInstance) => {
    options = _options;
    const clickedOne = isEmpty(_options.selectedSingleRow);
    const clickedTwo = _options.selectedRows.length;
    return (
      <React.Fragment>
        <AsyncButton type="primary" onClick={query_clickHandler}>
          查询
        </AsyncButton>
        <Button
          disabled={clickedOne}
          onClick={() => {
            window.open(
              `/singlePage/QueryOneEmployeeOrderDetail?empHireSepId=${options?.selectedSingleRow?.empHireSepId}&userName=${privatePopObject?.profile?.userName}&uuid=${options?.selectedSingleRow?.uuid}`,
            );
          }}
        >
          查看个人订单
        </Button>
        <ButtonSocialFundDetail processInfo={options.selectedSingleRow} disabled={clickedOne}>
          查看
        </ButtonSocialFundDetail>
        <AsyncButton disabled={clickedOne} onClick={() => exportData()}>
          导出数据
        </AsyncButton>
        <AsyncButton disabled={!clickedTwo} onClick={applyProcess_clickHandler}>
          更新支付标记
        </AsyncButton>
        {type === '1' && (
          <>
            <AsyncButton onClick={onCheck} disabled={!clickedTwo}>
              进入反查
            </AsyncButton>
            <AsyncButton onClick={() => onPrintEmployee('1')} disabled={!clickedTwo}>
              按员工单个打印凭证
            </AsyncButton>
            <AsyncButton onClick={() => onPrintEmployee('2')} disabled={!clickedTwo}>
              按员工批量打印凭证
            </AsyncButton>
          </>
        )}
      </React.Fragment>
    );
  };

  // 查询
  const query_clickHandler = async () => {
    const fieldsValue = await form.validateFields();
    const {
      strStatusArr,
      empName,
      empCode,
      idCardNum,
      ssGroupId,
      processProvider,
      subcontractCode,
      custId,
    } = fieldsValue;
    if (
      !empName &&
      !empCode &&
      !idCardNum &&
      !ssGroupId &&
      !processProvider &&
      !subcontractCode &&
      !custId
    ) {
      msgErr(
        '唯一号、证件号码、客户名称、小合同编号、员工姓名、公积金社保组、办理供应商至少填一个',
      );
      return;
    } else {
      fieldsValue['strStatus'] = strStatusArr.join(',');
      fieldsValue['ssGroupType'] = type;

      if (strStatusArr.includes('3')) {
        fieldsValue['sendChannel'] = fieldsValue.sendChannelStatus;
      } else {
        fieldsValue['sendChannel'] = undefined;
      }
      if (strStatusArr.includes('4')) {
        fieldsValue['reduceSendChannel'] = fieldsValue.sendChannelStatus;
      } else {
        fieldsValue['reduceSendChannel'] = undefined;
      }
      delete fieldsValue['strStatusArr'];
      delete fieldsValue['sendChannelStatus'];
      options.request(fieldsValue);
    }
  };

  // 导出数据
  const exportData = async () => {
    const queries = options?.queries || {};
    const fieldArr: any[] = [];
    const headArr: any[] = [];
    const typeArr: any[] = [];
    columns.forEach((column) => {
      headArr.push(column.title);
      fieldArr.push(column.dataIndex);
      typeArr.push('1');
    });
    const params = {
      condition: {
        ProcessInfo: { ...queries, pageSize: undefined, pageNum: undefined, expType: 'security' },
      },
      headStr: headArr.join(','),
      fieldArr,
      typeArr,
    };
    try {
      const res = await exportService.request(params, {
        responseType: 'blob',
      });
      Modal.confirm({
        content: '数据查询完毕是否导出',
        onOk: () => {
          downloadFile(res, 'ctg.xlsx');
          ConfirmLoading.clearLoading(exportService);
        },
        onCancel: () => {
          ConfirmLoading.clearLoading(exportService);
        },
      });
    } catch (e) {
      msgErr('导出数据失败');
    }
  };

  // 更新支付标记
  const applyProcess_clickHandler = async () => {
    const selectRows = options.selectedRows;
    if (selectRows.length === 0) {
      msgErr('请先选择一条数据');
      return;
    }
    await updatePayservice.requests({ applyProcessList: selectRows });
    msgOk('操作成功！');
    options.request(options.queries);
  };

  return (
    <CachedPage
      service={service as any}
      columns={columns}
      formColumns={formNewColumns}
      renderButtons={renderButtons}
      initialValues={{ queryMode: '1', strStatusArr: ['3', '4', '5'] }}
      form={form}
    >
      {/* 用于重新渲染页面 */}
      <span style={{ display: 'none' }}>
        `${queryMode}_${deptId}`
      </span>
    </CachedPage>
  );
};

export default SocialSecurity;
