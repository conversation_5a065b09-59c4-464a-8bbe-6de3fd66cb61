import * as defs from '../../baseClass';
import {
  fetch,
  RequestConfig,
  beforeFetch,
  afterFetch,
  checkResStatus,
  removeSpace,
} from '@/utils/request';

/**
     * @url /rhro-service-1.0/contractManage/delFileInContract
     * @desc 删除合同附件列表里的文件记录
删除合同附件列表里的文件记录
     * hasForm: false
     * hasBody: true
     */

export class Params {}

export const init = new defs.crm.CommonResponse();
export const url = '/rhro-service-1.0/contractManage/delFileInContract:POST';
export const initialUrl = '/rhro-service-1.0/contractManage/delFileInContract';
export const cacheKey = '_contractManage_delFileInContract_POST';
export async function request(
  data: defs.crm.ContractFile,
  options?: RequestConfig,
) {
  const reqUrl = `/rhro-service-1.0/contractManage/delFileInContract`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          reolve(res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
export async function requests(
  data: defs.crm.ContractFile,
  options?: RequestConfig,
) {
  const reqUrl = `/rhro-service-1.0/contractManage/delFileInContract`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          if (checkResStatus(res)) return reolve(res ? res.data : res);
          reject(res ? res.message : res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
