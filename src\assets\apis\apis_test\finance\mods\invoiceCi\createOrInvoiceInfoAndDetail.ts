import * as defs from '../../baseClass';
import {
  fetch,
  RequestConfig,
  beforeFetch,
  afterFetch,
  checkResStatus,
  removeSpace,
} from '@/utils/request';

/**
     * @url /rhro-service-1.0/api/ci/createOrInvoiceInfoAndDetail
     * @desc 新增开票记录
新增开票记录
     * hasForm: false
     * hasBody: true
     */

export class Params {}

export const init = new defs.finance.CommonResponse();
export const url = '/rhro-service-1.0/api/ci/createOrInvoiceInfoAndDetail:POST';
export const initialUrl =
  '/rhro-service-1.0/api/ci/createOrInvoiceInfoAndDetail';
export const cacheKey = '_api_ci_createOrInvoiceInfoAndDetail_POST';
export async function request(
  data: defs.finance.InvoiceDTO,
  options?: RequestConfig,
) {
  const reqUrl = `/rhro-service-1.0/api/ci/createOrInvoiceInfoAndDetail`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          reolve(res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
export async function requests(
  data: defs.finance.InvoiceDTO,
  options?: RequestConfig,
) {
  const reqUrl = `/rhro-service-1.0/api/ci/createOrInvoiceInfoAndDetail`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          if (checkResStatus(res)) return reolve(res ? res.data : res);
          reject(res ? res.message : res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
