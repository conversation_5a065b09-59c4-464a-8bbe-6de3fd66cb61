import * as defs from '../../baseClass';
import {
  fetch,
  RequestConfig,
  beforeFetch,
  afterFetch,
  checkResStatus,
  removeSpace,
} from '@/utils/request';

/**
     * @url /rhro-service-1.0/empmaintainPayEntity/exportEntity
     * @desc 导出单立户缴费实体
导出集团公司
     * hasForm: false
     * hasBody: true
     */

export class Params {}

export const init = undefined;
export const url = '/rhro-service-1.0/empmaintainPayEntity/exportEntity:POST';
export const initialUrl = '/rhro-service-1.0/empmaintainPayEntity/exportEntity';
export const cacheKey = '_empmaintainPayEntity_exportEntity_POST';
export async function request(
  data: defs.basedata.ExportQuery,
  options?: RequestConfig,
) {
  const reqUrl = `/rhro-service-1.0/empmaintainPayEntity/exportEntity`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          reolve(res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
export async function requests(
  data: defs.basedata.ExportQuery,
  options?: RequestConfig,
) {
  const reqUrl = `/rhro-service-1.0/empmaintainPayEntity/exportEntity`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          if (checkResStatus(res)) return reolve(res ? res.data : res);
          reject(res ? res.message : res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
