import React from 'react';
import { CachedPage } from '@/components/CachedPage';
import { EditeFormProps } from '@/components/EditeForm';
import { stdMonthFormatMoDash, today } from '@/utils/methods/times';
import { CustomerPop } from '@/components/StandardPop/CustomerPop';
import { mapToSelectors } from '@/components/Selectors';
import { WritableColumnProps } from '@/utils/writable/types';
import { formatAmount } from '@/utils/methods/format';
import { Button, Modal } from 'antd';
import { AsyncButton } from '@/components/Forms/Confirm';
import { WritableInstance } from '@/components/Writable';
import { isEmpty } from 'lodash';
import { msgErr, msgOk } from '@/utils/methods/message';
import SelectSendIdom from '@/components/StandardPop/SelectSendIdom';

let _options: WritableInstance | undefined = undefined;
const SENDMAIL = () => {
  const service = API.payroll.send.postGetSendMailListPage;

  const isSendList = new Map<string, string>([
    ['1', '是'],
    ['0', '否'],
  ]);

  const formColumns: EditeFormProps[] = [
    {
      label: '工资所属年月起',
      fieldName: 'sendMonthStart',
      inputRender: 'month',
      inputProps: { format: stdMonthFormatMoDash },
    },
    {
      label: '工资所属年月止',
      fieldName: 'sendMonthEnd',
      inputRender: 'month',
      inputProps: { format: stdMonthFormatMoDash },
    },
    {
      label: '客户',
      fieldName: 'custId',
      inputRender: () => <CustomerPop />,
    },
    {
      label: '薪资类别编号',
      fieldName: 'wageClassId',
      inputRender: 'string',
      rules: [{ required: false, pattern: /^[0-9]+$/, message: '请输入有效的薪资类别编号' }],
    },
    {
      label: '薪资类别名称',
      fieldName: 'wageClassName',
      inputRender: 'string',
    },
    {
      label: '发放名称',
      fieldName: 'wageSendId',
      inputRender: () => (
        <SelectSendIdom
          rowValue="wageSendId-disburseName"
          keyMap={{ wageSendId: 'WAGESENDID', disburseName: 'DISBURSENAME' }}
          modalTitle="发放名称"
        />
      ),
    },
    {
      label: '雇员姓名',
      fieldName: 'empName',
      inputRender: 'string',
    },
    {
      label: '唯一号',
      fieldName: 'empCode',
      inputRender: 'string',
    },
    {
      label: '证件号码',
      fieldName: 'idCardNum',
      inputRender: 'string',
    },
    {
      label: '电子邮件',
      fieldName: 'eMail',
      inputRender: 'string',
    },
    {
      label: '证件是否发送成功',
      fieldName: 'isSendEmail',
      inputRender: () => mapToSelectors(isSendList, { allowClear: true }),
    },
  ];

  const columns: WritableColumnProps<any>[] = [
    { title: '客户编号', dataIndex: 'custCode' },
    { title: '薪资类别', dataIndex: 'wageClassName' },
    { title: '发放名称', dataIndex: 'disburseName' },
    { title: '雇员姓名', dataIndex: 'empName' },
    { title: '唯一号', dataIndex: 'empCode' },
    { title: '工资单格式', dataIndex: 'payrollFormatName' },
    { title: '语言', dataIndex: 'languageName' },
    { title: '薪资单抄送人', dataIndex: 'cc' },
    { title: '电子邮件', dataIndex: 'eMail' },
    { title: '工资所属年月', dataIndex: 'sendMonth' },
    { title: '客户账单年月', dataIndex: 'billMonth' },
    { title: '工资计税年月', dataIndex: 'taxMonth' },
    { title: '是否已发送邮件', dataIndex: 'isSendEmail' },
    { title: '发送邮件次数', dataIndex: 'sendEmailTimes' },
    { title: '收入合计', dataIndex: 'f1', render: formatAmount },
    { title: '扣款合计', dataIndex: 'f2', render: formatAmount },
    { title: '实发合计', dataIndex: 'f3', render: formatAmount },
    { title: '税前总额', dataIndex: 'f4', render: formatAmount },
    { title: '应税工资(含个税基数)', dataIndex: 'f9', render: formatAmount },
    { title: '本次扣税', dataIndex: 'f10', render: formatAmount },
    { title: '工资服务费', dataIndex: 'f11', render: formatAmount },
    { title: '税率', dataIndex: 'f12', render: formatAmount },
    { title: '速算扣除数', dataIndex: 'f13', render: formatAmount },
    { title: '营业税', dataIndex: 'f14', render: formatAmount },
    { title: '工会费', dataIndex: 'f15', render: formatAmount },
  ];

  const now = today(stdMonthFormatMoDash);

  const onSend = () => {
    const rows = _options?.selectedRows;
    if (isEmpty(rows)) return msgErr('请选择数据');
    const noPayrollFormat = rows?.find((e) => !e.payrollFormat);
    if (noPayrollFormat) return msgErr('选中的数据有误，请联系管理员!');
    Modal.confirm({
      content: `选中${rows?.length}条数据,确定继续操作吗?`,
      onOk: async () => {
        const r = await API.payroll.send.sendMailList.request(rows!);
        if (r.bizCode == 1)
          msgOk(
            '已经提交到邮件发送等待队列(但存在EMAIL为空或工资单格式非邮件方式的记录),请稍后查收',
          );
        else msgOk('已经提交到邮件发送等待队列,请稍后查收');
      },
    });
  };

  const renderButtons = (options: WritableInstance) => {
    _options = options;
    return (
      <React.Fragment>
        <Button type="primary" htmlType="submit">
          查询
        </Button>
        <AsyncButton onClick={onSend}>发送邮件</AsyncButton>
      </React.Fragment>
    );
  };

  return (
    <CachedPage
      service={service}
      formColumns={formColumns}
      columns={columns}
      editable={false}
      initialValues={{ sendMonthStart: now, sendMonthEnd: now }}
      renderButtons={renderButtons}
      initPageInfo={{ pageSize: 100 }}
    />
  );
};

export default SENDMAIL;
