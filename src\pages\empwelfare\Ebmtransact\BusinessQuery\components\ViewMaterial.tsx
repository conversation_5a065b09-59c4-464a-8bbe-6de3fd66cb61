import React, { useEffect, useState } from 'react';
import { Button, Modal } from 'antd';
import Codal from '@/components/Codal';
import { CachedPage } from '@/components/CachedPage';
import { WritableColumnProps } from '@/utils/writable/types';
import { downloadFile } from '@/utils/methods/file';
import { msgErr } from '@/utils/methods/message';

interface MaterialProps {
  [props: string]: any;
  visible: boolean;
  hideHandle: CallableFunction;
  title: string;
  initValues?: any;
}

const service = API.welfaremanage.ebmBusinessQuery.getMaterialsByNodeId;

const ViewMaterial: React.FC<MaterialProps> = (props) => {
  const { visible, hideHandle, title } = props;

  const columns: WritableColumnProps<any>[] = [
    { title: '材料编号', dataIndex: 'materialsId' },
    { title: '材料名称', dataIndex: 'materialName' },
    { title: '是否原件', dataIndex: 'isOriginal' },
    { title: '材料数量', dataIndex: 'materialCount' },
    { title: '是否返还材料', dataIndex: 'isReturnMaterial' },
    { title: '材料模板', dataIndex: 'materialFileId' },
    { title: '材料上传情况', dataIndex: 'materialUploadStatus' },
    { title: '材料确认情况', dataIndex: 'materialConfirmStatus' },
    {
      title: '操作',
      dataIndex: 'operate',
      render: (_, record) => (
        <Button onClick={() => onDownload(record?.materialFileId)}>下载</Button>
      ),
    },
  ];

  const onDownload = (fileId: string | undefined) => {
    if (!fileId) return msgErr('文件不存在');

    API.commons.file.generalDownloadFile
      .request(
        {
          fileId: fileId,
        },
        { responseType: 'blob' },
      )
      .then((res) => {
        if (!res) {
          msgErr('文件不存在');
        } else {
          Modal.confirm({
            content: '文件已下载，是否保存？',
            onOk: () => {
              downloadFile(res);
            },
          });
        }
      });
  };

  const CodalButtons = () => {
    return (
      <>
        <Button onClick={() => hideHandle()}>关闭</Button>
      </>
    );
  };

  return (
    <Codal
      title={title}
      width="70vw"
      visible={visible}
      onCancel={() => hideHandle()}
      footer={CodalButtons}
    >
      <CachedPage service={service} columns={columns} formColumns={[]} />
    </Codal>
  );
};

export default ViewMaterial;
