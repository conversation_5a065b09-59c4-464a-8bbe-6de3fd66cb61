import * as defs from '../../baseClass';
import {
  fetch,
  RequestConfig,
  beforeFetch,
  afterFetch,
  checkResStatus,
  removeSpace,
} from '@/utils/request';

/**
     * @url /rhro-service-1.0/contractManage/queryContracViewtById
     * @desc 获取大合同详情数据
获取大合同详情数据
     * hasForm: false
     * hasBody: true
     */

export class Params {
  /** contractId */
  contractId: string;
}

export const init = new defs.crm.contractVO();
export const url = '/rhro-service-1.0/contractManage/queryContracViewtById:GET';
export const initialUrl =
  '/rhro-service-1.0/contractManage/queryContracViewtById';
export const cacheKey = '_contractManage_queryContracViewtById_GET';
export async function request(params: Params, options?: RequestConfig) {
  const reqUrl = `/rhro-service-1.0/contractManage/queryContracViewtById`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',
    params: removeSpace(params),

    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .get(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          reolve(res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
export async function requests(params: Params, options?: RequestConfig) {
  const reqUrl = `/rhro-service-1.0/contractManage/queryContracViewtById`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',
    params: removeSpace(params),

    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .get(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          if (checkResStatus(res)) return reolve(res ? res.data : res);
          reject(res ? res.message : res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
