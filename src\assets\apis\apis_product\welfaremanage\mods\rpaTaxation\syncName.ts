import * as defs from '../../baseClass';
import {
  fetch,
  RequestConfig,
  beforeFetch,
  afterFetch,
  checkResStatus,
  removeSpace,
} from '@/utils/request';

/**
     * @url /rhro-service-1.0/rpac/tax/sync/name
     * @desc 同步单立户和大户名称
同步单立户和大户名称
     * hasForm: false
     * hasBody: true
     */

export class Params {}

export const init = new defs.welfaremanage.RpaTaxation();
export const url = '/rhro-service-1.0/rpac/tax/sync/name:POST';
export const initialUrl = '/rhro-service-1.0/rpac/tax/sync/name';
export const cacheKey = '_rpac_tax_sync_name_POST';
export async function request(
  data: Array<defs.welfaremanage.BaseEntity>,
  options?: RequestConfig,
) {
  const reqUrl = `/rhro-service-1.0/rpac/tax/sync/name`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          reolve(res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
export async function requests(
  data: Array<defs.welfaremanage.BaseEntity>,
  options?: RequestConfig,
) {
  const reqUrl = `/rhro-service-1.0/rpac/tax/sync/name`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          if (checkResStatus(res)) return reolve(res ? res.data : res);
          reject(res ? res.message : res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
