import React, { useEffect } from 'react';
import { WritableColumnProps } from '@/utils/writable/types';
import Codal from '@/components/Codal';
import StandardTable from '@/components/StandardTable';

const columns: WritableColumnProps<any>[] = [
  { title: '产品大类', dataIndex: 'productCategoryName' },
  { title: '产品金额', dataIndex: 'amount' },
  { title: '产品企业金额', dataIndex: 'emoney' },
  { title: '产品个人金额', dataIndex: 'pmoney' },
  { title: '产品增值税率', dataIndex: 'vatr' },
  { title: '产品增值税费', dataIndex: 'vat' },
  { title: '人数', dataIndex: 'headcount' },
  { title: '签单分公司抬头', dataIndex: 'signBranchTitleName' },
];

interface BillDetailModalProps {
  modal: [boolean, CallableFunction];
  currentRow?: any;
}

const BillDetailModal: React.FC<BillDetailModalProps> = ({ modal, currentRow }) => {
  const [visible, setVisible] = modal;
  if (!visible) return null;
  const [table, tableOptions] = StandardTable.useStandardTable({
    service: API.emphiresep.ciReceivable.queryCiReceivableStatisticsPage,
  });

  useEffect(() => {
    if (visible) tableOptions.request({ receivableId: currentRow.receivableId });
  }, [visible]);

  return (
    <Codal
      title="商保账单应收明细"
      visible={visible}
      onCancel={() => setVisible(false)}
      cancelText="关闭"
      width="80%"
    >
      <StandardTable columns={columns} notShowPagination {...(table as any)} />
    </Codal>
  );
};

export { BillDetailModal };
