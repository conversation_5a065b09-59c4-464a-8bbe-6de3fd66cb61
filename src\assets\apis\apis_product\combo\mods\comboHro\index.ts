/**
 * @description 套餐相关
 */
import * as addComboRatioByCombo from './addComboRatioByCombo';
import * as checkComboCust from './checkComboCust';
import * as checkComboCustInSpec from './checkComboCustInSpec';
import * as comboHROService from './comboHROService';
import * as deleteSpecSuppPayment from './deleteSpecSuppPayment';
import * as toDownLoad from './toDownLoad';
import * as enableCombo from './enableCombo';
import * as getSSsubject from './getSSsubject';
import * as queryComboCust from './queryComboCust';
import * as queryComboGroupList from './queryComboGroupList';
import * as queryComboList from './queryComboList';
import * as queryComboRatio from './queryComboRatio';
import * as queryComboRatioByRatioId from './queryComboRatioByRatioId';
import * as queryComboRatioProductListForView from './queryComboRatioProductListForView';
import * as queryComboSepcBaseBound from './queryComboSepcBaseBound';
import * as queryCommonRatio from './queryCommonRatio';
import * as queryIncludCity from './queryIncludCity';
import * as queryPersonCategoryList from './queryPersonCategoryList';
import * as queryProductBySsGroupId from './queryProductBySsGroupId';
import * as queryRatioProductList from './queryRatioProductList';
import * as querySpecSuppPayment from './querySpecSuppPayment';
import * as querySsGroupByCityId from './querySsGroupByCityId';
import * as querySsGroupRatio from './querySsGroupRatio';
import * as replaceSubContractCombo from './replaceSubContractCombo';
import * as saveCombo from './saveCombo';
import * as saveComboCust from './saveComboCust';
import * as saveSpecSuppPayment from './saveSpecSuppPayment';
import * as stopCombo from './stopCombo';
import * as updateComboRaitoList from './updateComboRaitoList';
import * as updateComboRatio from './updateComboRatio';
import * as updateComboRatioByCombo from './updateComboRatioByCombo';
import * as updateReduceComboRatio from './updateReduceComboRatio';
import * as updateSpecBaseBoundList from './updateSpecBaseBoundList';
import * as updateStandard from './updateStandard';

export {
  addComboRatioByCombo,
  checkComboCust,
  checkComboCustInSpec,
  comboHROService,
  deleteSpecSuppPayment,
  toDownLoad,
  enableCombo,
  getSSsubject,
  queryComboCust,
  queryComboGroupList,
  queryComboList,
  queryComboRatio,
  queryComboRatioByRatioId,
  queryComboRatioProductListForView,
  queryComboSepcBaseBound,
  queryCommonRatio,
  queryIncludCity,
  queryPersonCategoryList,
  queryProductBySsGroupId,
  queryRatioProductList,
  querySpecSuppPayment,
  querySsGroupByCityId,
  querySsGroupRatio,
  replaceSubContractCombo,
  saveCombo,
  saveComboCust,
  saveSpecSuppPayment,
  stopCombo,
  updateComboRaitoList,
  updateComboRatio,
  updateComboRatioByCombo,
  updateReduceComboRatio,
  updateSpecBaseBoundList,
  updateStandard,
};
