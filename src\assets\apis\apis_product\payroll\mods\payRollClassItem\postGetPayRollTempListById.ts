import * as defs from '../../baseClass';
import {
  fetch,
  RequestConfig,
  beforeFetch,
  afterFetch,
  checkResStatus,
  removeSpace,
} from '@/utils/request';

/**
     * @url /rhro-service-1.0/payRollClassItem/getPayRollTempListById
     * @desc 根据类别id条件得到薪资项目的查询结果
根据类别id条件得到薪资项目的查询结果
     * hasForm: false
     * hasBody: true
     */

export class Params {}

export const init = new defs.payroll.PayRollClassItem();
export const url =
  '/rhro-service-1.0/payRollClassItem/getPayRollTempListById:POST';
export const initialUrl =
  '/rhro-service-1.0/payRollClassItem/getPayRollTempListById';
export const cacheKey = '_payRollClassItem_getPayRollTempListById_POST';
export async function request(data: object, options?: RequestConfig) {
  const reqUrl = `/rhro-service-1.0/payRollClassItem/getPayRollTempListById`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          reolve(res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
export async function requests(data: object, options?: RequestConfig) {
  const reqUrl = `/rhro-service-1.0/payRollClassItem/getPayRollTempListById`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          if (checkResStatus(res)) return reolve(res ? res.data : res);
          reject(res ? res.message : res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
