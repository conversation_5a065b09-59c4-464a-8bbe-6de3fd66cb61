import * as defs from '../../baseClass';
import {
  fetch,
  RequestConfig,
  beforeFetch,
  afterFetch,
  checkResStatus,
  removeSpace,
} from '@/utils/request';

/**
     * @url /rhro-service-1.0/rpa/job/list
     * @desc 查询Rpa定时任务
查询Rpa定时任务
     * hasForm: false
     * hasBody: true
     */

export class Params {}

export const init = new defs.rpa.FilterEntity();
export const url = '/rhro-service-1.0/rpa/job/list:POST';
export const initialUrl = '/rhro-service-1.0/rpa/job/list';
export const cacheKey = '_rpa_job_list_POST';
export async function request(
  data: defs.rpa.FilterEntity,
  options?: RequestConfig,
) {
  const reqUrl = `/rhro-service-1.0/rpa/job/list`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          reolve(res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
export async function requests(
  data: defs.rpa.FilterEntity,
  options?: RequestConfig,
) {
  const reqUrl = `/rhro-service-1.0/rpa/job/list`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          if (checkResStatus(res)) return reolve(res ? res.data : res);
          reject(res ? res.message : res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
