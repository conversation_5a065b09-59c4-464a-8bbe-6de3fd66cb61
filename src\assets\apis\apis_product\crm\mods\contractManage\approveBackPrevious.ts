import * as defs from '../../baseClass';
import {
  fetch,
  RequestConfig,
  beforeFetch,
  afterFetch,
  checkResStatus,
  removeSpace,
} from '@/utils/request';

/**
     * @url /rhro-service-1.0/contractManage/approveBackPrevious
     * @desc 销售补充数据提交再审批
销售补充数据提交再审批
     * hasForm: false
     * hasBody: true
     */

export class Params {}

export const init = new defs.crm.CommonResponse();
export const url = '/rhro-service-1.0/contractManage/approveBackPrevious:POST';
export const initialUrl =
  '/rhro-service-1.0/contractManage/approveBackPrevious';
export const cacheKey = '_contractManage_approveBackPrevious_POST';
export async function request(
  data: defs.crm.ContractDTO,
  options?: RequestConfig,
) {
  const reqUrl = `/rhro-service-1.0/contractManage/approveBackPrevious`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          reolve(res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
export async function requests(
  data: defs.crm.ContractDTO,
  options?: RequestConfig,
) {
  const reqUrl = `/rhro-service-1.0/contractManage/approveBackPrevious`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          if (checkResStatus(res)) return reolve(res ? res.data : res);
          reject(res ? res.message : res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
