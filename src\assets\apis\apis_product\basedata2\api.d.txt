declare namespace defs {
  export namespace basedata2 {
    export class AreaCodeVO {
      /** 地区码 */
      areaCode: number;

      /** 地区id */
      areaId: number;

      /** 地区名称 */
      areaName: string;

      /** 地区类型 */
      areaType: number;

      /** 版本 */
      version: number;
    }

    export class BaseEntity {
      /** add */
      add: boolean;

      /** 年度社平工资 */
      averageWageSociety: number;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** 残障金收取定值 */
      disabilityCollectFix: number;

      /** 残障金收取比例% */
      disabilityCollectRatio: number;

      /** 导入类型,扩充使用 */
      expType: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** 公积金上限 */
      housingFundToplimit: number;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** 是否薪资查询 */
      isWageQuery: string;

      /** 模拟人 */
      mimicBy: string;

      /** 最低工资 */
      minimunWage: number;

      /** noChange */
      noChange: boolean;

      /** 工会经费收取定值 */
      outLayCollectFix: number;

      /** 工会经费收取比例% */
      outLayCollectRatio: number;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;

      /** 扣缴义务人ID */
      withholdAgentId: number;

      /** 扣缴义务人名称 */
      withholdAgentName: string;

      /** 扣缴义务人限额ID */
      withholdAgentNormId: number;

      /** 扣缴义务人类型 1：大户 2：外部供应商 3:单立户 */
      withholdAgentType: number;

      /** 扣缴义务人类型 1：大户 2：外部供应商 3:单立户（显示） */
      withholdAgentTypeName: string;
    }

    export class BatchSaveOrUpdateAreaCodeVO {
      /** 新增地区码集合 */
      insertList: Array<defs.basedata2.AreaCodeVO>;

      /** 更新地区码集合 */
      updateList: Array<defs.basedata2.AreaCodeVO>;
    }

    export class BdPolicyBusnameClass {
      /** add */
      add: boolean;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** 编号 */
      busnameClassCode: string;

      /** 主键 */
      busnameClassId: string;

      /** 业务项目名称 */
      busnameClassName: string;

      /** 所属类型 */
      categoryId: string;

      /** 所属类型名称 */
      categoryName: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** 导入类型,扩充使用 */
      expType: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** 是否薪资查询 */
      isWageQuery: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;
    }

    export class BdPolicyBusnameSubtype {
      /** add */
      add: boolean;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** 小类编号 */
      busnameSubtypeCode: string;

      /** 主键 */
      busnameSubtypeId: string;

      /** 小类名称 */
      busnameSubtypeName: string;

      /** 大类名称ID */
      busnameTypeId: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** 屏蔽客户数 */
      exCustNum: string;

      /** 导入类型,扩充使用 */
      expType: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 客户端是否显示0 不显示 1显示 */
      isClientShow: string;

      /** 删除标记 */
      isDeleted: string;

      /** 是否薪资查询 */
      isWageQuery: string;

      /** 手机端是否显示0 不显示 1显示 */
      isWechartShow: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;
    }

    export class BdPolicyBusnameType {
      /** add */
      add: boolean;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** 项目名称ID */
      busnameClassId: string;

      /** 大类编号 */
      busnameTypeCode: string;

      /** 主键 */
      busnameTypeId: string;

      /** 大类名称 */
      busnameTypeName: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** 导入类型,扩充使用 */
      expType: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** 是否薪资查询 */
      isWageQuery: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;
    }

    export class BdTaxRuleQuery {
      /** 调基任务号 */
      adjTaskId: string;

      /** 基础数据编码 */
      baseDataCode: string;

      /** 批次id */
      batchId: string;

      /** 列名 */
      columns: string;

      /** 薪资批次id */
      currentRecordId: string;

      /** 客户id */
      custId: string;

      /** 结束时间 */
      endDt: string;

      /** endIndex */
      endIndex: number;

      /** 错误类型 */
      errorType: string;

      /** 导入结果 0：成功 1：失败 */
      impTag: string;

      /** 导入人姓名 */
      impUserName: string;

      /** 是否有效 */
      isEffective: string;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** 导入批次中做详细筛选的id值 */
      recordId: string;

      /** 规则id */
      ruleId: string;

      /** 规则名称 */
      ruleName: string;

      /** 前端传入的参数，判断不同的情况查询语句 */
      selByAuth: string;

      /** 开始时间 */
      startDt: string;

      /** startIndex */
      startIndex: number;

      /** 表名 */
      tableName: string;

      /** 类型id */
      typeId: string;

      /** 薪资类别ID */
      wageClassId: string;

      /** withholdAgentId */
      withholdAgentId: string;

      /** withholdAgentType */
      withholdAgentType: string;
    }

    export class BusinessSubType {
      /** add */
      add: boolean;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** busSubtypeId */
      busSubtypeId: string;

      /** busSubtypeName */
      busSubtypeName: string;

      /** busTypeId */
      busTypeId: string;

      /** busTypeName */
      busTypeName: string;

      /** 项目名称id */
      busnameClassId: string;

      /** 小类名称id */
      busnameSubtypeId: string;

      /** 大类名称id */
      busnameTypeId: string;

      /** 所属类型 */
      category: string;

      /** 人员类型列表 */
      categoryNames: string;

      /** cityId */
      cityId: string;

      /** cityName */
      cityName: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** 企业空表模板 */
      eBlankTemplatePath: string;

      /** 企业空表模板名 */
      eBlankTemplatePathName: string;

      /** 企业样本模板 */
      eSampleTemplatePath: string;

      /** 企业样本模板名 */
      eSampleTemplatePathName: string;

      /** endIndex */
      endIndex: number;

      /** 导入类型,扩充使用 */
      expType: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** 是否可预约 */
      isBooked: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** isRef */
      isRef: string;

      /** 是否有效 */
      isValid: string;

      /** 是否薪资查询 */
      isWageQuery: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** 个人空表模板 */
      pBlankTemplatePath: string;

      /** 个人空表模板名 */
      pBlankTemplatePathName: string;

      /** 个人样本模板 */
      pSampleTemplatePath: string;

      /** 个人样本模板名 */
      pSampleTemplatePathName: string;

      /** 页数 */
      pageNum: number;

      /** 每页记录数,默认65536条 */
      pageSize: number;

      /** 人员类型列表id */
      personCategoryIds: string;

      /** 人员类型列表名称 */
      personCategoryNames: string;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** remark */
      remark: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** startIndex */
      startIndex: number;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;

      /** whetherPay */
      whetherPay: string;
    }

    export class BusinessSubTypeQuery {
      /** busSubtypeId */
      busSubtypeId: string;

      /** busSubtypeName */
      busSubtypeName: string;

      /** busTypeId */
      busTypeId: string;

      /** busTypeName */
      busTypeName: string;

      /** 业务项目名称id */
      busnameClassId: string;

      /** 大类名称id */
      busnameTypeId: string;

      /** cityId */
      cityId: string;

      /** cityName */
      cityName: string;

      /** endIndex */
      endIndex: number;

      /** isRef */
      isRef: string;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** remark */
      remark: string;

      /** startIndex */
      startIndex: number;

      /** whetherPay */
      whetherPay: string;
    }

    export class BusinessType {
      /** add */
      add: boolean;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** busTypeId */
      busTypeId: string;

      /** busTypeName */
      busTypeName: string;

      /** 业务项目名称id */
      busnameClassId: string;

      /** 业务项目名称 */
      busnameClassName: string;

      /** 业务大类名称id */
      busnameTypeId: string;

      /** categoryId */
      categoryId: string;

      /** categoryName */
      categoryName: string;

      /** cityId */
      cityId: string;

      /** cityName */
      cityName: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** doRequire */
      doRequire: string;

      /** endIndex */
      endIndex: number;

      /** 导入类型,扩充使用 */
      expType: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** 是否有效 */
      isValid: string;

      /** 是否薪资查询 */
      isWageQuery: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** orderRequire */
      orderRequire: string;

      /** 页数 */
      pageNum: number;

      /** 每页记录数,默认65536条 */
      pageSize: number;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** remark */
      remark: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** ssGroupId */
      ssGroupId: string;

      /** ssGroupName */
      ssGroupName: string;

      /** startIndex */
      startIndex: number;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** type */
      type: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;
    }

    export class BusinessTypeQuery {
      /** busTypeId */
      busTypeId: string;

      /** busTypeName */
      busTypeName: string;

      /** 业务项目名称id */
      busnameClassId: string;

      /** 业务大类名称id */
      busnameTypeId: string;

      /** categoryId */
      categoryId: string;

      /** categoryName */
      categoryName: string;

      /** cityId */
      cityId: string;

      /** cityName */
      cityName: string;

      /** doRequire */
      doRequire: string;

      /** endIndex */
      endIndex: number;

      /** orderRequire */
      orderRequire: string;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** remark */
      remark: string;

      /** ssGroupId */
      ssGroupId: string;

      /** ssGroupName */
      ssGroupName: string;

      /** startIndex */
      startIndex: number;

      /** type */
      type: string;
    }

    export class BusinessTypeVO {
      /** businessType */
      businessType: defs.basedata2.BusinessType;

      /** subList */
      subList: Array<defs.basedata2.BusinessSubType>;
    }

    export class CommonQuery {
      /** 城市id */
      cityId: string;

      /** 客户id */
      custId: string;

      /** 部门级别 */
      departmentGrade: string;

      /** 部门内部编号 */
      departmentInternalCode: string;

      /** 部门名称 */
      departmentName: string;

      /** endIndex */
      endIndex: number;

      /** 城市id */
      fileProviderName: string;

      /** 大区id */
      governingArea: string;

      /** 大区id */
      governingAreaId: string;

      /** 分公司id */
      governingBranch: string;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** 供应商类型 */
      providerType: string;

      /** 供应商集团id */
      prvdGroupId: string;

      /** startIndex */
      startIndex: number;
    }

    export class CommonResponse<T0 = any> {
      /** bizCode */
      bizCode: number;

      /** code */
      code: number;

      /** data */
      data: T0;

      /** message */
      message: string;

      /** t */
      t: T0;
    }

    export class DisableBenefitRatio {
      /** add */
      add: boolean;

      /** 批次号,用于备份 */
      batchId: string;

      /** benefitRatio */
      benefitRatio: string;

      /** benefitRatioId */
      benefitRatioId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** cityId */
      cityId: string;

      /** cityName */
      cityName: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** 创建日期 */
      createDt: string;

      /** ceateTime */
      createTime: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** 导入类型,扩充使用 */
      expType: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** ids */
      ids: Array<number>;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** 是否薪资查询 */
      isWageQuery: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** realName */
      realName: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;
    }

    export class DropdownList {
      /** 业务大类类型 */
      btType: string;

      /** chargeRate */
      chargeRate: string;

      /** cityId */
      cityId: string;

      /** cityIdForParty */
      cityIdForParty: string;

      /** cityName */
      cityName: string;

      /** contractAvgAmt */
      contractAvgAmt: string;

      /** contractHeadcount */
      contractHeadcount: string;

      /** contractName */
      contractName: string;

      /** contractSubType */
      contractSubType: string;

      /** contractSubTypeName */
      contractSubTypeName: string;

      /** contractType */
      contractType: string;

      /** contractTypeName */
      contractTypeName: string;

      /** currentSalesName */
      currentSalesName: string;

      /** departmentName */
      departmentName: string;

      /** englishTermName */
      englishTermName: string;

      /** exFeeMonth */
      exFeeMonth: string;

      /** 供应商收费模板 */
      exFeeTempltId: string;

      /** governingArea */
      governingArea: string;

      /** 所属大区 */
      governingAreaId: string;

      /** governingBranch */
      governingBranch: string;

      /** 所属分公司 */
      governingBranchId: string;

      /** groupType */
      groupType: string;

      /** 主键 */
      key: string;

      /** liabilityCsName */
      liabilityCsName: string;

      /** 全称 */
      name: string;

      /** 拼音码 */
      pinYinCode: string;

      /** productLineId */
      productLineId: string;

      /** 供应商类型1内部2外部 */
      providerType: string;

      /** 保留名字1 */
      reserveName1: string;

      /** 保留名字2 */
      reserveName2: string;

      /** 储备对象,用于查询下拉框的时候,可能还需要查询其他列的值 */
      reserveObj: string;

      /** 缩写名 */
      shortName: string;

      /** signBrachTitleId */
      signBrachTitleId: string;

      /** signBranchTitleName */
      signBranchTitleName: string;

      /** 社保组ID */
      ssGroupId: string;

      /** svcSubtypeName */
      svcSubtypeName: string;

      /** svcTypeName */
      svcTypeName: string;
    }

    export class EmpMaintainPayEntity {
      /** add */
      add: boolean;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** companyCode */
      companyCode: string;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** custCode */
      custCode: string;

      /** custId */
      custId: string;

      /** custName */
      custName: string;

      /** custPayEntityId */
      custPayEntityId: string;

      /** custPayEntityName */
      custPayEntityName: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** departmentName */
      departmentName: string;

      /** employerMaintainId */
      employerMaintainId: string;

      /** employerName */
      employerName: string;

      /** 用工方简称 */
      employerShortName: string;

      /** 导入类型,扩充使用 */
      expType: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** hasStamp */
      hasStamp: number;

      /** hasStampStr */
      hasStampStr: string;

      /** ids */
      ids: Array<number>;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** 是否薪资查询 */
      isWageQuery: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;
    }

    export class ExportQuery {
      /** 查询条件 */
      condition: object;

      /** 表头字段列表 */
      fieldArr: Array<string>;

      /** 表头字段中文拼接 */
      headStr: string;

      /** 表头字段类型列表 */
      typeArr: Array<string>;
    }

    export class FileProviderQuery {
      /** 所属分公司ID */
      departmentId: number;

      /** endIndex */
      endIndex: number;

      /** 档案供应商名称 */
      fileProviderName: string;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** startIndex */
      startIndex: number;
    }

    export class FilterEntity {
      /** add */
      add: boolean;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** categoryId */
      categoryId: string;

      /** categoryName */
      categoryName: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** companyName */
      companyName: string;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** custName */
      custName: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** endIndex */
      endIndex: number;

      /** 导入类型,扩充使用 */
      expType: string;

      /** fileType */
      fileType: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** groupName */
      groupName: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** 是否薪资查询 */
      isWageQuery: string;

      /** materialType */
      materialType: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** 页数 */
      pageNum: number;

      /** 每页记录数,默认65536条 */
      pageSize: number;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** sealDes */
      sealDes: string;

      /** sealType */
      sealType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** startIndex */
      startIndex: number;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;

      /** versionType */
      versionType: string;
    }

    export class GlobalResult<T0 = any> {
      /** code */
      code: string;

      /** data */
      data: Array<defs.basedata2.ObjectMap<string, ObjectMap>>;

      /** message */
      message: string;
    }

    export class ImMedicalDTO {
      /** batchId */
      batchId: string;

      /** cityId */
      cityId: string;

      /** cityName */
      cityName: string;

      /** 创建人 */
      createByName: string;

      /** effectiveDt */
      effectiveDt: string;

      /** effectiveStatus */
      effectiveStatus: string;

      /** fileId */
      fileId: string;

      /** fileName */
      fileName: string;

      /** importId */
      importId: string;

      /** importTaskName */
      importTaskName: string;

      /** remark */
      remark: string;
    }

    export class ImMedicalQuery {
      /** batchId */
      batchId: string;

      /** cityId */
      cityId: string;

      /** cityName */
      cityName: string;

      /** 创建人 */
      createByName: string;

      /** effectiveDt */
      effectiveDt: string;

      /** effectiveStatus */
      effectiveStatus: string;

      /** endIndex */
      endIndex: number;

      /** fileId */
      fileId: string;

      /** fileName */
      fileName: string;

      /** importId */
      importId: string;

      /** importTaskName */
      importTaskName: string;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** remark */
      remark: string;

      /** startIndex */
      startIndex: number;
    }

    export class JindieOrg {
      /** add */
      add: boolean;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** cfCompanyLevel */
      cfCompanyLevel: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** endIndex */
      endIndex: number;

      /** 导入类型,扩充使用 */
      expType: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** 金蝶同步标记 0未同步 1已同步 */
      isSyncJindie: string;

      /** isValid */
      isValid: string;

      /** 是否薪资查询 */
      isWageQuery: string;

      /** jindieOrgId */
      jindieOrgId: string;

      /** jindieOrgName */
      jindieOrgName: string;

      /** jindieOrgType */
      jindieOrgType: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** 页数 */
      pageNum: number;

      /** 每页记录数,默认65536条 */
      pageSize: number;

      /** 不含税单价 */
      priceNoTax: string;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** startIndex */
      startIndex: number;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** superOrgId */
      superOrgId: string;

      /** 纳税资质 1一般纳税人2简易计税3小规模纳税人 */
      taxAptitude: string;

      /** 纳税资质（显示） */
      taxAptitudeName: string;

      /** 税率 bd_base_data type=614 对应数据 */
      taxRate: string;

      /** 税率 （显示） */
      taxRateName: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;
    }

    export class JindieOrgQuery {
      /** endIndex */
      endIndex: number;

      /** isValid */
      isValid: string;

      /** jindieOrgId */
      jindieOrgId: string;

      /** jindieOrgName */
      jindieOrgName: string;

      /** jindieOrgType */
      jindieOrgType: string;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** startIndex */
      startIndex: number;

      /** superOrgId */
      superOrgId: string;
    }

    export class Map<T0 = any, T1 = any> {}

    export class Materials {
      /** add */
      add: boolean;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** branchName */
      branchName: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** createName */
      createName: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** endIndex */
      endIndex: number;

      /** 导入类型,扩充使用 */
      expType: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** isOriginal */
      isOriginal: string;

      /** isRef */
      isRef: string;

      /** 是否薪资查询 */
      isWageQuery: string;

      /** materialsId */
      materialsId: string;

      /** materialsName */
      materialsName: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** nremark */
      nremark: string;

      /** 页数 */
      pageNum: number;

      /** 每页记录数,默认65536条 */
      pageSize: number;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** remark */
      remark: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** startIndex */
      startIndex: number;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;
    }

    export class MaterialsPackage {
      /** add */
      add: boolean;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** busSubtypeId */
      busSubtypeId: string;

      /** busTypeId */
      busTypeId: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** endIndex */
      endIndex: number;

      /** 导入类型,扩充使用 */
      expType: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** 是否是单位办理材料1 是 0否 */
      isEMaterial: string;

      /** isOriginal */
      isOriginal: string;

      /** 是否是个人办理材料1 是 0否 */
      isPMaterial: string;

      /** isReturn */
      isReturn: string;

      /** 是否薪资查询 */
      isWageQuery: string;

      /** materialsAccount */
      materialsAccount: string;

      /** materialsId */
      materialsId: string;

      /** materialsName */
      materialsName: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** packageId */
      packageId: string;

      /** 页数 */
      pageNum: number;

      /** 每页记录数,默认65536条 */
      pageSize: number;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** startIndex */
      startIndex: number;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;
    }

    export class MaterialsPackageQuery {
      /** busSubtypeId */
      busSubtypeId: string;

      /** busTypeId */
      busTypeId: string;

      /** endIndex */
      endIndex: number;

      /** isOriginal */
      isOriginal: string;

      /** isReturn */
      isReturn: string;

      /** materialsAccount */
      materialsAccount: string;

      /** materialsId */
      materialsId: string;

      /** materialsName */
      materialsName: string;

      /** packageId */
      packageId: string;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** startIndex */
      startIndex: number;
    }

    export class MaterialsQuery {
      /** branchName */
      branchName: string;

      /** createName */
      createName: string;

      /** endIndex */
      endIndex: number;

      /** isOriginal */
      isOriginal: string;

      /** isRef */
      isRef: string;

      /** materialsId */
      materialsId: string;

      /** materialsName */
      materialsName: string;

      /** nremark */
      nremark: string;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** remark */
      remark: string;

      /** startIndex */
      startIndex: number;
    }

    export class MedicalIns {
      /** add */
      add: boolean;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** cityId */
      cityId: string;

      /** cityName */
      cityName: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** countyName */
      countyName: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** endIndex */
      endIndex: number;

      /** errorInfo */
      errorInfo: string;

      /** errorType */
      errorType: string;

      /** 导入类型,扩充使用 */
      expType: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** hosCode */
      hosCode: string;

      /** hosId */
      hosId: string;

      /** hosLevel */
      hosLevel: string;

      /** hosName */
      hosName: string;

      /** hosType */
      hosType: string;

      /** impTag */
      impTag: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** 是否薪资查询 */
      isWageQuery: string;

      /** lineNum */
      lineNum: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** 页数 */
      pageNum: number;

      /** 每页记录数,默认65536条 */
      pageSize: number;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** startIndex */
      startIndex: number;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;
    }

    export class MedicalInsDTO {
      /** cityId */
      cityId: string;

      /** cityName */
      cityName: string;

      /** countyName */
      countyName: string;

      /** errorInfo */
      errorInfo: string;

      /** errorType */
      errorType: string;

      /** hosCode */
      hosCode: string;

      /** hosId */
      hosId: string;

      /** hosLevel */
      hosLevel: string;

      /** hosName */
      hosName: string;

      /** hosType */
      hosType: string;

      /** impTag */
      impTag: string;

      /** lineNum */
      lineNum: string;
    }

    export class MedicalInsVO {
      /** ids */
      ids: Array<number>;

      /** 医疗机构维护实体 */
      medicalInsDTO: defs.basedata2.MedicalInsDTO;

      /** 医疗机构维护集合 */
      medicalInsDTOList: Array<defs.basedata2.MedicalInsDTO>;
    }

    export class Page {
      /** currentPage */
      currentPage: number;

      /** currentPageNo */
      currentPageNo: number;

      /** data */
      data: Array<object>;

      /** pageSize */
      pageSize: number;

      /** result */
      result: Array<object>;

      /** start */
      start: number;

      /** totalCount */
      totalCount: number;

      /** totalPage */
      totalPage: number;

      /** totalPageCount */
      totalPageCount: number;
    }

    export class PayAccountSyncQuery {
      /** 分行名 */
      bankBranch: string;

      /** 所属银行id */
      bankBranchName: string;

      /** 所属银行id */
      bankId: number;

      /** 开户名 */
      bankName: string;

      /** 是否启用,0 否 1是 */
      enabledState: number;

      /** endIndex */
      endIndex: number;

      /** 是否已同步,0否1 是 */
      isSync: number;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** 支付地id */
      payPlace: number;

      /** 支付地抬头 */
      payTitleStr: string;

      /** startIndex */
      startIndex: number;
    }

    export class ServicePointCustDTO {
      /** 增员材料 */
      addEmpMaterial: string;

      /** 大区 */
      area: string;

      /** 接单方id */
      assigneeProviderId: string;

      /** 账单收费规则:1 预付;2每月付 */
      billFeeRule: string;

      /** 城市ID */
      cityId: string;

      /** cityName */
      cityName: string;

      /** 联系人(客服) */
      contactName: string;

      /** 联系电话 */
      contactTel: string;

      /** 客户编号 */
      custCode: string;

      /** 英文名称 */
      custEnglishName: string;

      /** 客户Id */
      custId: string;

      /** 客户名称 */
      custName: string;

      /** 客户简称 */
      custShortName: string;

      /** 离职补差规则 */
      dismissMakeupRule: string;

      /** 离职补差起始月 */
      dismissMakeupSatartMon: string;

      /** 是否有效 */
      isDeleted: string;

      /** 是否离职补差 */
      isDismissMakeup: string;

      /** 大户所在区 */
      largeAccountArea: string;

      /** 机构类别:1 自营分公司; 2 供应商 */
      organizationType: string;

      /** 补交材料 */
      payInbackMaterial: string;

      /** provinceName */
      provinceName: string;

      /** 减员材料 */
      reduceEmpMaterial: string;

      /** 备注 */
      remark: string;

      /** 网点服务名称(接单方) */
      serviceAssigneeName: string;

      /** 服务网点标识: 1 优选,2 备选,3 只减不增,4 仅操作指定客户,5 终止合作 */
      serviceBranchFlag: string;

      /** serviceCustId */
      serviceCustId: string;

      /** 网点服务名称(集团) */
      serviceGroupName: string;

      /** 主键 */
      servicePointId: string;

      /** 公积金申报频率 */
      sfApplicationFrequency: string;

      /** 公积基金中心当月可操作时间段 */
      sfOperateTime: string;

      /** 当地单立户可操作区县 */
      singleAccountArea: string;

      /** 社保申报频率 */
      ssApplicationFrequency: string;

      /** 社保当月可操作时间段 */
      ssOperateTime: string;
    }

    export class ServicePointDTO {
      /** 增员材料 */
      addEmpMaterial: string;

      /** 大区 */
      area: string;

      /** 接单方id */
      assigneeProviderId: string;

      /** 账单收费规则:1 预付;2每月付 */
      billFeeRule: string;

      /** 城市ID */
      cityId: string;

      /** cityLevel */
      cityLevel: string;

      /** cityLevelCN */
      cityLevelCN: string;

      /** cityName */
      cityName: string;

      /** 联系人(客服) */
      contactName: string;

      /** 联系电话 */
      contactTel: string;

      /** 离职补差规则 */
      dismissMakeupRule: string;

      /** 离职补差起始月 */
      dismissMakeupSatartMon: string;

      /** 是否有效 */
      isDeleted: string;

      /** 是否离职补差 */
      isDismissMakeup: string;

      /** 大户所在区 */
      largeAccountArea: string;

      /** 机构类别:1 自营分公司; 2 供应商 */
      organizationType: string;

      /** 补交材料 */
      payInbackMaterial: string;

      /** provinceName */
      provinceName: string;

      /** 减员材料 */
      reduceEmpMaterial: string;

      /** 备注 */
      remark: string;

      /** 网点服务名称(接单方) */
      serviceAssigneeName: string;

      /** 服务网点标识: 1 优选,2 备选,3 只减不增,4 仅操作指定客户,5 终止合作 */
      serviceBranchFlag: string;

      /** 网点服务名称(集团) */
      serviceGroupName: string;

      /** 主键 */
      servicePointId: string;

      /** 公积金申报频率 */
      sfApplicationFrequency: string;

      /** 公积基金中心当月可操作时间段 */
      sfOperateTime: string;

      /** 当地单立户可操作区县 */
      singleAccountArea: string;

      /** 社保申报频率 */
      ssApplicationFrequency: string;

      /** 社保当月可操作时间段 */
      ssOperateTime: string;
    }

    export class SpecialBranchTitle {
      /** add */
      add: boolean;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** branchId */
      branchId: number;

      /** branchName */
      branchName: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** 导入类型,扩充使用 */
      expType: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** isValid */
      isValid: string;

      /** 是否薪资查询 */
      isWageQuery: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** signBranchInnerId */
      signBranchInnerId: string;

      /** signBranchName */
      signBranchName: string;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** titleId */
      titleId: number;

      /** titleName */
      titleName: string;

      /** titleSpecialId */
      titleSpecialId: number;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;
    }

    export class SpecialBranchTitleQuery {
      /** add */
      add: boolean;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** branchId */
      branchId: number;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** endIndex */
      endIndex: number;

      /** 导入类型,扩充使用 */
      expType: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** isValid */
      isValid: number;

      /** 是否薪资查询 */
      isWageQuery: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** 页数 */
      pageNum: number;

      /** 每页记录数,默认65536条 */
      pageSize: number;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** signBranchId */
      signBranchId: number;

      /** startIndex */
      startIndex: number;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** titleName */
      titleName: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;
    }

    export class SysBranchTitle {
      /** add */
      add: boolean;

      /** bankNameAcct */
      bankNameAcct: string;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** branchId */
      branchId: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** checkTitle */
      checkTitle: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** contactAddress */
      contactAddress: string;

      /** contactTel */
      contactTel: string;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** departName */
      departName: string;

      /** endIndex */
      endIndex: number;

      /** 导入类型,扩充使用 */
      expType: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** isValid */
      isValid: string;

      /** isValidStr */
      isValidStr: string;

      /** 是否薪资查询 */
      isWageQuery: string;

      /** jindieOrgId */
      jindieOrgId: string;

      /** memo */
      memo: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** 页数 */
      pageNum: number;

      /** 每页记录数,默认65536条 */
      pageSize: number;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** startIndex */
      startIndex: number;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** taxpayerIdentifie */
      taxpayerIdentifie: string;

      /** titleCode */
      titleCode: string;

      /** titleId */
      titleId: string;

      /** titleName */
      titleName: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;
    }

    export class SysBranchTitleBilling {
      /** add */
      add: boolean;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 金税盘号 */
      billingGold: string;

      /** billingId */
      billingId: string;

      /** 开票机号 */
      billingNum: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** endIndex */
      endIndex: number;

      /** 导入类型,扩充使用 */
      expType: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** 是否薪资查询 */
      isWageQuery: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** 页数 */
      pageNum: number;

      /** 每页记录数,默认65536条 */
      pageSize: number;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** remark */
      remark: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** startIndex */
      startIndex: number;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** titleId */
      titleId: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;
    }

    export class SysBranchTitleParItem {
      /** add */
      add: boolean;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** endIndex */
      endIndex: number;

      /** 导入类型,扩充使用 */
      expType: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** 是否薪资查询 */
      isWageQuery: string;

      /** 摘要名称 */
      itemName: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** 页数 */
      pageNum: number;

      /** 每页记录数,默认65536条 */
      pageSize: number;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** startIndex */
      startIndex: number;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** titleId */
      titleId: string;

      /** titleParItemId */
      titleParItemId: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;
    }

    export class SysBranchTitleQuery {
      /** add */
      add: boolean;

      /** areaId */
      areaId: string;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** branchId */
      branchId: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** departName */
      departName: string;

      /** endIndex */
      endIndex: number;

      /** 导入类型,扩充使用 */
      expType: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** isValid */
      isValid: string;

      /** 是否薪资查询 */
      isWageQuery: string;

      /** jindieOrgId */
      jindieOrgId: string;

      /** memo */
      memo: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** 页数 */
      pageNum: number;

      /** 每页记录数,默认65536条 */
      pageSize: number;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** startIndex */
      startIndex: number;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** titleCode */
      titleCode: string;

      /** titleId */
      titleId: string;

      /** titleName */
      titleName: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;
    }

    export class SysBranchTitleVO {
      /** addList */
      addList: Array<defs.basedata2.SysBranchTitleBilling>;

      /** addListPar */
      addListPar: Array<defs.basedata2.SysBranchTitleParItem>;

      /** uptList */
      uptList: Array<defs.basedata2.SysBranchTitleBilling>;

      /** uptListPar */
      uptListPar: Array<defs.basedata2.SysBranchTitleParItem>;
    }

    export class TaxPayerBenefit {
      /** add */
      add: boolean;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** 唯一号 */
      empCode: string;

      /** 姓名 */
      empName: string;

      /** 导入类型,扩充使用 */
      expType: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** 证照号码 */
      idCardNum: string;

      /** 身份证件类型：1.身份证2.护照3.军官证4.香港身份证5.双派 */
      idCardType: number;

      /** 身份证件类型：1.身份证2.护照3.军官证4.香港身份证5.双派 */
      idCardTypeStr: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** 是否有效 */
      isValid: number;

      /** 是否有效 1:是 0:否 */
      isValidStr: string;

      /** 是否薪资查询 */
      isWageQuery: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** 主键 */
      taxPayerBenefitId: number;

      /** 年度 */
      taxYear: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;

      /** 扣缴义务人编号 */
      withholdAgentId: number;

      /** 扣缴义务人名称 */
      withholdAgentName: string;

      /** 扣缴义务人类型 1：大户 2：外部供应商 3:单立户 */
      withholdAgentType: number;

      /** 扣缴义务人类型 1：大户 2：外部供应商 3:单立户 */
      withholdAgentTypeStr: string;
    }

    export class TaxPayerBenefitQuery {
      /** endIndex */
      endIndex: number;

      /** 证照号码 */
      idCardNum: string;

      /** 是否有效:0无效，1有效 */
      isValid: number;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** startIndex */
      startIndex: number;

      /** 年度 */
      taxYear: string;

      /** 扣缴义务人ID */
      withholdAgentId: number;
    }

    export class ThreeToOneRule {
      /** add */
      add: boolean;

      /** 适用的人员类别(1：代理，2：派遣，3：岗位外包，4：BPO) */
      applyEmpType: string;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** 分公司id */
      branchId: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** 签约方公司抬头要求(公司下所有有效的签单方抬头记录) */
      branchTitleId: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** endIndex */
      endIndex: number;

      /** 导入类型,扩充使用 */
      expType: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 是否默认缴纳主体(0否1是) */
      isDefaultSubject: string;

      /** 删除标记 */
      isDeleted: string;

      /** 签署劳动合同是否支持电子签(0否1是) */
      isLaborSign: string;

      /** 是否强制缴纳公积金(0否1是) */
      isPayFund: string;

      /** 签署离职材料是否支持电子签(0否1是) */
      isQuitMaterialsSign: string;

      /** 是否强制缴纳残障金(工资)(0否1是) */
      isSalaryDisabilityAllowance: string;

      /** 是否强制缴纳工会费(工资)(0否1是) */
      isSalaryUnionFee: string;

      /** 是否强制缴纳残障金(社保)(0否1是) */
      isSsDisabilityAllowance: string;

      /** 是否强制缴纳工会费(社保)(0否1是) */
      isSsUnionFee: string;

      /** 参保或劳动用工备案是否需要上传劳动合同扫描件(0否1是) */
      isUploadLaborFile: string;

      /** 是否薪资查询 */
      isWageQuery: string;

      /** 劳动合同主体上年度职工月平均工资(不定时工时-最新) */
      laborLastYearAvgSalary: string;

      /** 劳动合同主体月度最低工资标准(标准工时、综合工时-最新) */
      laborMonMinAvgSalary: string;

      /** 劳动合同签订主体 */
      laborSubjectId: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** 外包执行【综合工时】是否有资质(1.没有资质/有资质，2.客户可以使用/有资质，3.客户不能使用) */
      outerCompIsQualifications: string;

      /** 外包执行【不定时工时】是否有资质(1.没有资质/有资质，2.客户可以使用/有资质，3.客户不能使用) */
      outerFlexIsQualifications: string;

      /** 外包其他说明 */
      outerOtherContent: string;

      /** 页数 */
      pageNum: number;

      /** 每页记录数,默认65536条 */
      pageSize: number;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 工资扣缴义务人名称 */
      salaryWithholdingId: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** 派造执行【综合工时】是否有资质(1.没有资质/有资质，2.客户可以使用/有资质，3.客户不能使用) */
      sendCompIsQualifications: string;

      /** 派遣执行【不定时工时】是否有资质(1.没有资质/有资质，2.客户可以使用/有资质，3.客户不能使用) */
      sendFlexIsQualifications: string;

      /** 派遣其他说明 */
      sendOtherContent: string;

      /**  社保公积金缴纳主体 有效签约方抬头 */
      ssSubjectId: string;

      /** startIndex */
      startIndex: number;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** 缴纳主体适用范围说明 */
      subjectContent: string;

      /** 主键 */
      threeToOneRuleId: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;
    }

    export class ThreeToOneRuleQuery {
      /** add */
      add: boolean;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** 分公司id */
      branchId: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** endIndex */
      endIndex: number;

      /** 导入类型,扩充使用 */
      expType: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 是否默认缴纳主体(0否1是) */
      isDefaultSubject: string;

      /** 删除标记 */
      isDeleted: string;

      /** 签署劳动合同是否支持电子签(0否1是) */
      isLaborSign: string;

      /** 签署离职材料是否支持电子签(0否1是) */
      isQuitMaterialsSign: string;

      /** 是否薪资查询 */
      isWageQuery: string;

      /** 劳动合同签订主体 */
      laborSubjectId: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** 页数 */
      pageNum: number;

      /** 每页记录数,默认65536条 */
      pageSize: number;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 工资扣缴义务人名称 */
      salaryWithholdingId: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** 社保公积金缴纳主体 有效签约方抬头 */
      ssSubjectId: string;

      /** startIndex */
      startIndex: number;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;
    }

    export class ThreeToOneRuleVo {
      /** 是否强制缴纳残障金(工资)(0否1是) */
      isSalaryDisabilityAllowance: string;

      /** 是否强制缴纳工会费(工资)(0否1是) */
      isSalaryUnionFee: string;
    }

    export class UsualTemplate {
      /** add */
      add: boolean;

      /** 适用合同类型 */
      applyContractType: string;

      /** 适用合同类型文本 */
      applyContractTypeName: string;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** 分公司id */
      branchId: string;

      /** 分公司名称 */
      branchName: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** 导入类型,扩充使用 */
      expType: string;

      /** 文件ID */
      fileId: string;

      /** 文件名称 */
      fileName: string;

      /** 文件类型 */
      fileType: string;

      /** 文件类型文本 */
      fileTypeName: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** 是否薪资查询 */
      isWageQuery: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** 历史上传说明 */
      oldUploadIllustrate: string;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** 模板ID */
      templateId: number;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** 上传说明 */
      uploadIllustrate: string;

      /** 上传材料名称 */
      uploadMaterialName: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;

      /** 版本类型 */
      versionType: string;

      /** 版本类型文本 */
      versionTypeName: string;
    }

    export class UsualTemplateQuery {
      /** add */
      add: boolean;

      /** 适用合同类型 */
      applyContractType: string;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** 分公司id */
      branchId: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** endIndex */
      endIndex: number;

      /** 导入类型,扩充使用 */
      expType: string;

      /** 文件类型 */
      fileType: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** 是否薪资查询 */
      isWageQuery: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** 页数 */
      pageNum: number;

      /** 每页记录数,默认65536条 */
      pageSize: number;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** startIndex */
      startIndex: number;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** 上传材料名称 */
      uploadMaterialName: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;

      /** 版本类型 */
      versionType: string;
    }

    export class areaCodeDTO {
      /** 地区编码 */
      areaCode: string;

      /** 地区id */
      areaId: number;

      /** 地区名称 */
      areaName: string;

      /** 版本 */
      version: number;
    }

    export class areaCodeQuery {
      /** 地区码 */
      areaCode: number;

      /** 地区名称 */
      areaName: string;

      /** endIndex */
      endIndex: number;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** startIndex */
      startIndex: number;

      /** 版本 */
      version: number;
    }

    export class baseData {
      /** 编码 */
      baseDataCode: string;

      /** 基础数据ID */
      baseDataId: string;

      /** 名称 */
      baseDataName: string;

      /** 英文名称 */
      englishTermName: string;

      /** 是否有效：0无效 1有效 */
      isValid: string;

      /** 拼音码 */
      pinYinCode: string;

      /** 说明/备注 */
      remark: string;

      /** 父类型编码 */
      type: string;
    }

    export class baseDataInterDTO {
      /** 基础数据小类编码 */
      baseDataCode: string;

      /** 名称 */
      baseDataName: string;

      /** 创建人 */
      createBy: string;

      /** 创建时间 */
      createDt: string;

      /** 国际化ID */
      id: string;

      /** 语种 */
      languageType: string;

      /** 基础数据大类编码 */
      typeId: string;
    }

    export class baseDataInterQuery {
      /** 基础数据小类编码 */
      baseDataCode: string;

      /** 基础数据大类编码 */
      typeId: string;
    }

    export class baseDataInterVO {
      /** 基础数据小类编码 */
      baseDataCode: string;

      /** 名称 */
      baseDataName: string;

      /** 创建人 */
      createBy: string;

      /** 语种 */
      languageType: string;

      /** 基础数据大类编码 */
      typeId: string;
    }

    export class baseDataQuery {
      /** 类型名称 */
      baseDataName: string;

      /** endIndex */
      endIndex: number;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** startIndex */
      startIndex: number;

      /** 类型编码 */
      type: string;
    }

    export class baseDataVO {
      /** 编码 */
      baseDataCode: string;

      /** 基础数据ID */
      baseDataId: string;

      /** 名称 */
      baseDataName: string;

      /** 英文名称 */
      englishTermName: string;

      /** 是否有效：0无效 1有效 */
      isValid: string;

      /** 修改前编码，修改时不能为空 */
      oldBaseDataCode: string;

      /** 拼音码 */
      pinYinCode: string;

      /** 说明/备注 */
      remark: string;

      /** 父类型编码 */
      type: string;
    }

    export class batchSaveOrUpdateCityVO {
      /** 新增城市集合 */
      insertList: Array<defs.basedata2.cityVO>;

      /** 更新城市集合 */
      updateList: Array<defs.basedata2.cityVO>;
    }

    export class batchSaveOrUpdateCompetitorVO {
      /** 新增竞争对手集合 */
      insertList: Array<defs.basedata2.competitorVO>;

      /** 更新竞争对手集合 */
      updateList: Array<defs.basedata2.competitorVO>;
    }

    export class batchSaveOrUpdateCountryVO {
      /** 新增国家集合 */
      insertList: Array<defs.basedata2.countryVO>;

      /** 更新国家集合 */
      updateList: Array<defs.basedata2.countryVO>;
    }

    export class batchSaveOrUpdateEmpMaintainAcctVO {
      /** 新增list */
      insertList: Array<defs.basedata2.empMaintainAcctVO>;

      /** 更新list */
      updateList: Array<defs.basedata2.empMaintainAcctVO>;
    }

    export class batchSaveOrUpdateProductSubVO {
      /** 新增福利产品小类集合 */
      insertList: Array<defs.basedata2.productSubVO>;

      /** 更新福利产品小类集合 */
      updateList: Array<defs.basedata2.productSubVO>;
    }

    export class batchSaveOrUpdateProductSuperVO {
      /** 新增福利产品大类集合 */
      insertList: Array<defs.basedata2.productSuperVO>;

      /** 更新福利产品大类集合 */
      updateList: Array<defs.basedata2.productSuperVO>;
    }

    export class batchSaveOrUpdateProvinceVO {
      /** 新增省份集合 */
      insertList: Array<defs.basedata2.provinceVO>;

      /** 更新省份集合 */
      updateList: Array<defs.basedata2.provinceVO>;
    }

    export class batchSaveOrUpdateServiceSubTypeVO {
      /** 新增合同小类集合 */
      insertList: Array<defs.basedata2.serviceSubTypeDTO>;

      /** 更新合同小类集合 */
      updateList: Array<defs.basedata2.serviceSubTypeDTO>;
    }

    export class batchSaveOrUpdateServiceTypeVO {
      /** 新增合同大类集合 */
      insertList: Array<defs.basedata2.serviceTypeDTO>;

      /** 更新合同大类集合 */
      updateList: Array<defs.basedata2.serviceTypeDTO>;
    }

    export class branchInvoiceDTO {
      /** 分公司id */
      branchId: string;

      /** 主键id */
      branchInvoiceId: string;

      /** 分公司名称 */
      branchName: string;

      /** 普票开票方式:1电子发票、2纸制发票 */
      geneInvoiceType: string;

      /** 普票开票方式名称 */
      geneInvoiceTypeName: string;

      /** 专票开票方式:1电子发票、2纸制发票 */
      specInvoiceType: string;

      /** 专票开票方式名称 */
      specInvoiceTypeName: string;
    }

    export class branchInvoiceQuery {
      /** 分公司id */
      branchId: string;

      /** endIndex */
      endIndex: number;

      /** 普票开票方式:1电子发票、2纸制发票 */
      geneInvoiceType: string;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** 专票开票方式:1电子发票、2纸制发票 */
      specInvoiceType: string;

      /** startIndex */
      startIndex: number;
    }

    export class branchInvoiceVO {
      /** 分公司id */
      branchId: string;

      /** 主键id */
      branchInvoiceId: string;

      /** 普票开票方式:1电子发票、2纸制发票 */
      geneInvoiceType: string;

      /** 专票开票方式:1电子发票、2纸制发票 */
      specInvoiceType: string;
    }

    export class cityDTO {
      /** 代理城市打折系数 */
      agentDiscountFactor: string;

      /** 代理级次 */
      agentLevel: string;

      /** 所属大区 */
      area: string;

      /** 城市编码 */
      cityCode: string;

      /** 城市英文名 */
      cityEnglishName: string;

      /** 城市系数 */
      cityFactor: string;

      /** 城市ID */
      cityId: string;

      /** 城市抵扣级别 */
      cityLevel: number;

      /** 城市名称 */
      cityName: string;

      /** 所属上级城市 */
      governingCity: string;

      /** 是否被使用 */
      isUsed: string;

      /** 拼音码 */
      pinyinCode: string;

      /** 省ID */
      provinceId: string;

      /** 省名 */
      provinceName: string;
    }

    export class cityQuery {
      /** 大区 */
      area: number;

      /** 城市区号 */
      cityCode: string;

      /** 城市名称 */
      cityName: string;

      /** endIndex */
      endIndex: number;

      /** 是否被使用 0:否，1:是 */
      isUsed: string;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** 省份ID */
      provinceId: string;

      /** startIndex */
      startIndex: number;
    }

    export class cityVO {
      /** 代理城市打折系数 */
      agentDiscountFactor: string;

      /** 代理级次 */
      agentLevel: string;

      /** 所属大区 */
      area: string;

      /** 城市编码 */
      cityCode: string;

      /** 城市英文名 */
      cityEnglishName: string;

      /** 城市系数 */
      cityFactor: string;

      /** 城市ID */
      cityId: string;

      /** 城市抵扣级别 */
      cityLevel: number;

      /** 城市名称 */
      cityName: string;

      /** 所属上级城市 */
      governingCity: string;

      /** 是否被使用 */
      isUsed: string;

      /** 拼音码 */
      pinyinCode: string;

      /** 省ID */
      provinceId: string;

      /** 省名 */
      provinceName: string;
    }

    export class competitorDTO {
      /** 公司名称 */
      companyName: string;

      /** 竞争对手ID */
      competitorId: number;

      /** 创建人 */
      createBy: string;

      /** 创建时间 */
      createDt: string;

      /** 逻辑删除标识 0：正常 1：已删除，默认值为0 */
      isDeleted: string;

      /** 模拟人 */
      mimicBy: string;

      /** 创建人 */
      realName: string;

      /** 备注 */
      remark: string;

      /** 是否有效 */
      status: number;

      /** 更新人 */
      updateBy: string;

      /** 更新时间 */
      updateDt: string;
    }

    export class competitorQuery {
      /** 公司名称 */
      companyName: string;

      /** endIndex */
      endIndex: number;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** startIndex */
      startIndex: number;
    }

    export class competitorVO {
      /** 公司名称 */
      companyName: string;

      /** 竞争对手ID */
      competitorId: number;

      /** 创建人 */
      realName: string;

      /** 备注 */
      remark: string;

      /** 是否有效 */
      status: number;
    }

    export class countryDTO {
      /** 国家区位码 */
      countryAreaCode: string;

      /** 国家英文名称 */
      countryEnglishName: string;

      /** 国家ID */
      countryId: string;

      /** 国家名称 */
      countryName: string;

      /** 国家简称 */
      countryShortName: string;

      /** 创建人 */
      createBy: string;

      /** 创建时间 */
      createDt: string;

      /** 逻辑删除标识 0：正常 1：已删除，默认值为0 */
      isDeleted: string;

      /** 模拟人 */
      mimicBy: string;

      /** 更新人 */
      updateBy: string;

      /** 更新时间 */
      updateDt: string;
    }

    export class countryQuery {
      /** 国家区位码 */
      countryAreaCode: string;

      /** 国家名称 */
      countryName: string;

      /** 国家简称 */
      countryShortName: string;

      /** endIndex */
      endIndex: number;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** startIndex */
      startIndex: number;
    }

    export class countryVO {
      /** 国家区位码 */
      countryAreaCode: string;

      /** 国家英文名称 */
      countryEnglishName: string;

      /** 国家ID */
      countryId: string;

      /** 国家名称 */
      countryName: string;

      /** 国家简称 */
      countryShortName: string;

      /** 创建人 */
      createBy: string;

      /** 创建时间 */
      createDt: string;

      /** 逻辑删除标识 0：正常 1：已删除，默认值为0 */
      isDeleted: string;

      /** 模拟人 */
      mimicBy: string;

      /** 更新人 */
      updateBy: string;

      /** 更新时间 */
      updateDt: string;
    }

    export class dropdownListDTO {
      /** 业务大类类型 */
      btType: string;

      /** chargeRate */
      chargeRate: string;

      /** cityId */
      cityId: string;

      /** cityIdForParty */
      cityIdForParty: string;

      /** contractAvgAmt */
      contractAvgAmt: string;

      /** contractHeadcount */
      contractHeadcount: string;

      /** contractName */
      contractName: string;

      /** currentSalesName */
      currentSalesName: string;

      /** departmentName */
      departmentName: string;

      /** exFeeMonth */
      exFeeMonth: string;

      /** 供应商收费模板 */
      exFeeTempltId: string;

      /** governingArea */
      governingArea: string;

      /** 所属大区 */
      governingAreaId: string;

      /** governingBranch */
      governingBranch: string;

      /** 所属分公司 */
      governingBranchId: string;

      /** groupType */
      groupType: string;

      /** 主键 */
      key: string;

      /** liabilityCsName */
      liabilityCsName: string;

      /** 全称 */
      name: string;

      /** 拼音码 */
      pinYinCode: string;

      /** productLineId */
      productLineId: string;

      /** 供应商类型1内部2外部 */
      providerType: string;

      /** 保留名字1 */
      reserveName1: string;

      /** 保留名字2 */
      reserveName2: string;

      /** 储备对象,用于查询下拉框的时候,可能还需要查询其他列的值 */
      reserveObj: string;

      /** 缩写名 */
      shortName: string;

      /** 社保组ID */
      ssGroupId: string;

      /** svcSubtypeName */
      svcSubtypeName: string;

      /** svcTypeName */
      svcTypeName: string;
    }

    export class empMaintainAcctDTO {
      /** 开户行名称 */
      acctBankName: string;

      /** 主键 */
      acctId: string;

      /** 开户名 */
      acctName: string;

      /** 开户账号 */
      acctNum: string;

      /** 是否默认(默认0) */
      isDefault: string;

      /** 金蝶编号 */
      jindieId: string;

      /** 用工方id */
      maintainId: string;

      /** 社保组id */
      ssGroupId: string;
    }

    export class empMaintainAcctVO {
      /** companyCode */
      companyCode: string;

      /** custCode */
      custCode: string;

      /** custId */
      custId: string;

      /** custPayEntityId */
      custPayEntityId: string;

      /** custPayEntityName */
      custPayEntityName: string;

      /** employerMaintainId */
      employerMaintainId: string;

      /** endIndex */
      endIndex: number;

      /** ids */
      ids: Array<number>;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** startIndex */
      startIndex: number;
    }

    export class empMaintainDTO {
      /** 城市id */
      cityId: string;

      /** companyCode */
      companyCode: string;

      /** 分公司 */
      departmentId: string;

      /** 用工方维护ID */
      employerMaintainId: string;

      /** 用工方 */
      employerName: string;

      /** 用工方简称 */
      employerShortName: string;

      /** 是否有电子章，1：是，0：否 */
      hasStamp: number;

      /** hasStampName */
      hasStampName: string;

      /** 是否独立户，1：是，0：否 */
      isIndependent: string;

      /** 金碟组织编号 */
      jindieOrgId: string;

      /** 支付分公司 */
      payProviderName: string;
    }

    export class empMaintainQuery {
      /** 分公司 */
      departmentId: string;

      /** 用工方 */
      employerName: string;

      /** endIndex */
      endIndex: number;

      /** 是否有电子章，1：是，0：否 */
      hasStamp: number;

      /** 单立户 */
      isIndependent: string;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** startIndex */
      startIndex: number;
    }

    export class empMaintainVO {
      /** companyCode */
      companyCode: string;

      /** 分公司 */
      departmentId: string;

      /** 用工方维护ID */
      employerMaintainId: string;

      /** 用工方 */
      employerName: string;

      /** 用工方简称 */
      employerShortName: string;

      /** 是否有电子章，1：是，0：否 */
      hasStamp: number;

      /** 是否独立户，1：是，0：否 */
      isIndependent: string;

      /** 金碟组织编号 */
      jindieOrgId: string;
    }

    export class fileProviderDTO {
      /** 收费频率  1:月缴,12:年缴 */
      chargeRate: number;

      /** 所属城市ID */
      cityId: number;

      /** 成本 */
      cost: number;

      /** 创建人 */
      createBy: string;

      /** 创建时间 */
      createDt: string;

      /** 所属分公司ID */
      departmentId: number;

      /** 档案供应商编码 */
      fileProviderCode: string;

      /** 档案供应商ID */
      fileProviderId: number;

      /** 档案供应商名称 */
      fileProviderName: string;

      /** 逻辑删除标识 0：正常 1：已删除，默认值为0 */
      isDeleted: string;

      /** 模拟人 */
      mimicBy: string;

      /** 价格 */
      price: number;

      /** 更新人 */
      updateBy: string;

      /** 更新时间 */
      updateDt: string;
    }

    export class fileProviderVO {
      /** 收费频率  1:月缴,12:年缴 */
      chargeRate: number;

      /** 所属城市ID */
      cityId: number;

      /** 成本 */
      cost: number;

      /** 所属分公司ID */
      departmentId: number;

      /** 档案供应商编码 */
      fileProviderCode: string;

      /** 档案供应商ID */
      fileProviderId: number;

      /** 档案供应商名称 */
      fileProviderName: string;

      /** 价格 */
      price: number;
    }

    export class medicalInsQuery {
      /** cityId */
      cityId: string;

      /** cityName */
      cityName: string;

      /** countyName */
      countyName: string;

      /** endIndex */
      endIndex: number;

      /** errorInfo */
      errorInfo: string;

      /** errorType */
      errorType: string;

      /** hosCode */
      hosCode: string;

      /** hosId */
      hosId: string;

      /** hosLevel */
      hosLevel: string;

      /** hosName */
      hosName: string;

      /** hosType */
      hosType: string;

      /** impTag */
      impTag: string;

      /** lineNum */
      lineNum: string;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** startIndex */
      startIndex: number;
    }

    export class personCategoryDTO {
      /** 人员分类名称 */
      categoryName: string;

      /** 城市id */
      cityId: string;

      /** 城市名称 */
      cityName: string;

      /** 部门Id */
      departmentId: string;

      /** 是否删除 */
      isDeleted: string;

      /** 人员分类Id */
      personCategoryId: string;

      /** 产品Id */
      productId: string;

      /** 产品名称 */
      productName: string;

      /** 社保组名称 */
      ssGroupName: string;

      /** 社保组类型 */
      ssGroupType: string;
    }

    export class personCategoryQuery {
      /** 人员分类名称 */
      categoryName: string;

      /** 城市Id */
      cityId: string;

      /** endIndex */
      endIndex: number;

      /** isDeleted */
      isDeleted: string;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** 人员分类编号 */
      personCategoryId: string;

      /** startIndex */
      startIndex: number;
    }

    export class productSubDTO {
      /** 创建人 */
      createBy: string;

      /** 创建时间 */
      createDt: string;

      /** 逻辑删除标识 0：正常 1：已删除，默认值为0 */
      isDeleted: string;

      /** 模拟人 */
      mimicBy: string;

      /** 福利产品大类编号 */
      productSubtypeCode: string;

      /** 福利产品小类ID */
      productSubtypeId: number;

      /** 福利产品大类名称 */
      productSubtypeName: string;

      /** 福利产品大类ID */
      productSuperTypeId: number;

      /** 备注 */
      remark: string;

      /** 更新人 */
      updateBy: string;

      /** 更新时间 */
      updateDt: string;
    }

    export class productSubQuery {
      /** endIndex */
      endIndex: number;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** 福利产品大类ID */
      productSuperTypeId: number;

      /** startIndex */
      startIndex: number;
    }

    export class productSubVO {
      /** 福利产品小类编号 */
      productSubtypeCode: string;

      /** 福利产品小类ID */
      productSubtypeId: number;

      /** 福利产品小类名称 */
      productSubtypeName: string;

      /** 福利产品大类ID */
      productSuperTypeId: number;

      /** 备注 */
      remark: string;
    }

    export class productSuperDTO {
      /** 创建人 */
      createBy: string;

      /** 创建时间 */
      createDt: string;

      /** 逻辑删除标识 0：正常 1：已删除，默认值为0 */
      isDeleted: string;

      /** 模拟人 */
      mimicBy: string;

      /** 福利产品大类编号 */
      productSuperTypeCode: string;

      /** 福利产品大类ID */
      productSuperTypeId: number;

      /** 福利产品大类名称 */
      productSuperTypeName: string;

      /** 备注 */
      remark: string;

      /** 更新人 */
      updateBy: string;

      /** 更新时间 */
      updateDt: string;
    }

    export class productSuperQuery {
      /** endIndex */
      endIndex: number;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** 福利产品大类名称 */
      productSuperTypeName: string;

      /** startIndex */
      startIndex: number;
    }

    export class productSuperVO {
      /** 福利产品大类编号 */
      productSuperTypeCode: string;

      /** 福利产品大类ID */
      productSuperTypeId: number;

      /** 福利产品大类名称 */
      productSuperTypeName: string;

      /** 备注 */
      remark: string;
    }

    export class provinceDTO {
      /** 国别 */
      countryId: string;

      /** 创建人 */
      createBy: string;

      /** 创建时间 */
      createDt: string;

      /** 逻辑删除标识 0：正常 1：已删除，默认值为0 */
      isDeleted: string;

      /** 模拟人 */
      mimicBy: string;

      /** 拼音码 */
      pinyinCode: string;

      /** 省份英文名 */
      provinceEnglishName: string;

      /** 省份ID */
      provinceId: string;

      /** 省份名称 */
      provinceName: string;

      /** 省名简称 */
      provinceShortName: string;

      /** provincialCityId */
      provincialCityId: string;

      /** provincialCityName */
      provincialCityName: string;

      /** 更新人 */
      updateBy: string;

      /** 更新时间 */
      updateDt: string;
    }

    export class provinceQuery {
      /** 国家 */
      countryId: string;

      /** endIndex */
      endIndex: number;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** 省份英文名称 */
      provinceEnglishName: string;

      /** 省份名称 */
      provinceName: string;

      /** 省份简称 */
      provinceShortName: string;

      /** startIndex */
      startIndex: number;
    }

    export class provinceVO {
      /** 国别 */
      countryId: string;

      /** 创建人 */
      createBy: string;

      /** 创建时间 */
      createDt: string;

      /** 逻辑删除标识 0：正常 1：已删除，默认值为0 */
      isDeleted: string;

      /** 模拟人 */
      mimicBy: string;

      /** 拼音码 */
      pinyinCode: string;

      /** 省份英文名 */
      provinceEnglishName: string;

      /** 省份ID */
      provinceId: string;

      /** 省份名称 */
      provinceName: string;

      /** 省名简称 */
      provinceShortName: string;

      /** 省份城市 */
      provincialCityId: string;

      /** 更新人 */
      updateBy: string;

      /** 更新时间 */
      updateDt: string;
    }

    export class servicePointCustQuery {
      /** 大区 */
      area: string;

      /** 城市 */
      cityId: string;

      /** 城市名称 */
      cityName: string;

      /** 联系人(客服) */
      contactName: string;

      /** 联系电话 */
      contactTel: string;

      /** 客户编号 */
      custCode: string;

      /** 英文名称 */
      custEnglishName: string;

      /** 客户Id */
      custId: string;

      /** 客户名称 */
      custName: string;

      /** 客户简称 */
      custShortName: string;

      /** endIndex */
      endIndex: number;

      /** 是否有效 */
      isDeleted: string;

      /** 机构类别:1 自营分公司; 2 供应商 */
      organizationType: string;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** 省份 */
      provinceName: string;

      /** 网点服务名称(接单方) */
      serviceAssigneeName: string;

      /** 服务网点标识: 1 优选,2 备选,3 只减不增,4 仅操作指定客户,5 终止合作 */
      serviceBranchFlag: string;

      /** serviceCustId */
      serviceCustId: string;

      /** 网点服务名称(集团) */
      serviceGroupName: string;

      /** 主键 */
      servicePointId: string;

      /** startIndex */
      startIndex: number;
    }

    export class servicePointQuery {
      /** 大区 */
      area: string;

      /** 接单方id */
      assigneeProviderId: string;

      /** 城市 */
      cityId: string;

      /** cityLevel */
      cityLevel: string;

      /** cityLevelCN */
      cityLevelCN: string;

      /** 城市名称 */
      cityName: string;

      /** 联系人(客服) */
      contactName: string;

      /** 联系电话 */
      contactTel: string;

      /** 网点分公司id */
      departmentId: string;

      /** 网点分公司id */
      departmentName: string;

      /** endIndex */
      endIndex: number;

      /** 是否有效 */
      isDeleted: string;

      /** 机构类别:1 自营分公司; 2 供应商 */
      organizationType: string;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** 省份 */
      provinceName: string;

      /** 网点服务名称(接单方) */
      serviceAssigneeName: string;

      /** 服务网点标识: 1 优选,2 备选,3 只减不增,4 仅操作指定客户,5 终止合作 */
      serviceBranchFlag: string;

      /** 网点服务名称(集团) */
      serviceGroupName: string;

      /** startIndex */
      startIndex: number;
    }

    export class serviceSubTypeDTO {
      /** 创建人 */
      createBy: string;

      /** 创建时间 */
      createDt: string;

      /** 逻辑删除标识 0：正常 1：已删除，默认值为0 */
      isDeleted: string;

      /** 说明 */
      memo: string;

      /** 模拟人 */
      mimicBy: string;

      /** 拼音码 */
      pinYinCode: string;

      /** 合同小类ID */
      svcSubtypeId: number;

      /** 合同小类名称 */
      svcSubtypeName: string;

      /** 合同大类ID */
      svcTypeId: number;

      /** 合同小类编码 */
      svcTypeSubCode: string;

      /** 更新人 */
      updateBy: string;

      /** 更新时间 */
      updateDt: string;
    }

    export class serviceSubTypeQuery {
      /** endIndex */
      endIndex: number;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** startIndex */
      startIndex: number;

      /** 合同大类ID */
      svcTypeId: number;
    }

    export class serviceTypeDTO {
      /** 创建人 */
      createBy: string;

      /** 创建时间 */
      createDt: string;

      /** 逻辑删除标识 0：正常 1：已删除，默认值为0 */
      isDeleted: string;

      /** 说明 */
      memo: string;

      /** 模拟人 */
      mimicBy: string;

      /** 合同大类编码 */
      svcTypeCode: string;

      /** 合同大类ID */
      svcTypeId: number;

      /** 合同大类名称 */
      svcTypeName: string;

      /** 更新人 */
      updateBy: string;

      /** 更新时间 */
      updateDt: string;
    }

    export class serviceTypeQuery {
      /** endIndex */
      endIndex: number;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** startIndex */
      startIndex: number;

      /** 合同大类名称 */
      svcTypeName: string;
    }

    export class taxRateDTO {
      /** deductAmount */
      deductAmount: number;

      /** fixRate */
      fixRate: number;

      /** remark */
      remark: string;

      /** wageTaxbaseId */
      wageTaxbaseId: number;

      /** wageTaxbaseName */
      wageTaxbaseName: string;
    }

    export class taxRateDetailDTO {
      /** maxAmount */
      maxAmount: number;

      /** minAmount */
      minAmount: number;

      /** quickDeduct */
      quickDeduct: number;

      /** taxLevel */
      taxLevel: number;

      /** taxRate */
      taxRate: number;

      /** wageDetailId */
      wageDetailId: number;

      /** wageTaxbaseId */
      wageTaxbaseId: number;
    }

    export class taxRateDetailVO {
      /** maxAmount */
      maxAmount: number;

      /** minAmount */
      minAmount: number;

      /** quickDeduct */
      quickDeduct: number;

      /** taxLevel */
      taxLevel: number;

      /** taxRate */
      taxRate: number;
    }

    export class taxRateQuery {
      /** endIndex */
      endIndex: number;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** startIndex */
      startIndex: number;

      /** wageTaxbaseName */
      wageTaxbaseName: string;
    }

    export class taxRateVO {
      /** 起征点 */
      deductAmount: number;

      /** 固定税率 */
      fixRate: number;

      /** 说明 */
      remark: string;

      /** 薪资税率明细 */
      taxRateDetailVOList: Array<defs.basedata2.taxRateDetailVO>;

      /** 薪资税率表id */
      wageTaxbaseId: number;

      /** 薪资税率表名称 */
      wageTaxbaseName: string;
    }

    export class withholdAgentNormQuery {
      /** add */
      add: boolean;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** endIndex */
      endIndex: number;

      /** 导入类型,扩充使用 */
      expType: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** 是否薪资查询 */
      isWageQuery: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** 页数 */
      pageNum: number;

      /** 每页记录数,默认65536条 */
      pageSize: number;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** startIndex */
      startIndex: number;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;

      /** 扣缴义务人ID */
      withholdAgentId: string;
    }
  }
}

declare namespace API {
  export namespace basedata2 {
    /**
     * 地区码维护
     */
    export namespace areaCode {
      /**
        * 批量删除地区码
批量删除地区码
        * /areacode/delete
        */
      export namespace remove {
        export class Params {
          /** 地区id */
          areaIds: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: boolean;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询地区码信息
查询地区码信息
        * /areacode/list
        */
      export namespace list {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.areaCodeDTO;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.areaCodeQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.areaCodeQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 批量新增或修改地区码信息
批量新增或修改地区码信息
        * /areacode/saveOrUpdate
        */
      export namespace saveOrUpdate {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: boolean;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.BatchSaveOrUpdateAreaCodeVO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.BatchSaveOrUpdateAreaCodeVO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 基本信息
     */
    export namespace baseDataCls {
      /**
        * 批量删除基础数据
批量删除基础数据
        * /basedata/batchDelete
        */
      export namespace batchDelete {
        export class Params {
          /** 基础数据ID，逗号分隔 */
          baseDataIds: string;
        }

        export type Response<T> = defs.basedata2.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 下拉数据
下拉数据
        * /basedata/getBaseDataByTypeAndIsValid
        */
      export namespace getDropDownList {
        export class Params {
          /** isValid */
          isValid?: string;
          /** type */
          type: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.DropdownList;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 下拉数据
下拉数据
        * /basedata/getDropDownList
        */
      export namespace getBasedataGetDropDownList {
        export class Params {
          /** type */
          type: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.DropdownList;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 特殊类型下拉数据
特殊类型下拉数据
        * /basedata/getSpecialTypeDropDownList
        */
      export namespace getSpecialTypeDropDownList {
        export class Params {
          /** type */
          type?: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.DropdownList;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 批量删除基础数据国际化
批量删除基础数据国际化
        * /basedata/inter/batchDelete
        */
      export namespace postBasedataInterBatchDelete {
        export class Params {
          /** 基础数据国际化ID */
          ids: Array<string>;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: boolean;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 批量新增基础数据国际化数据
批量新增基础数据国际化数据
        * /basedata/inter/batchSave
        */
      export namespace batchSave {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: boolean;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Array<defs.basedata2.baseDataInterVO>,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Array<defs.basedata2.baseDataInterVO>,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 语种下拉数据
查询语种下拉数据
        * /basedata/inter/languageDropdownList
        */
      export namespace languageDropdownList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: boolean;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 基础数据国际化列表
查询基础数据国际化列表
        * /basedata/inter/list
        */
      export namespace interList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.baseDataInterDTO;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.baseDataInterQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.baseDataInterQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 基础数据列表
查询基础数据列表
        * /basedata/list
        */
      export namespace list {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.baseData;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.baseDataQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.baseDataQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 新增基础数据
新增基础数据
        * /basedata/save
        */
      export namespace save {
        export class Params {}

        export type Response<T> = defs.basedata2.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.basedata2.baseDataVO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.baseDataVO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 修改基础数据
修改基础数据
        * /basedata/update
        */
      export namespace update {
        export class Params {}

        export type Response<T> = defs.basedata2.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.basedata2.baseDataVO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.baseDataVO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 下拉数据
下拉数据
        * /basedataCls/getDropDownList
        */
      export namespace getDorpDownList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.DropdownList;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: object,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: object,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 残障金比例
     */
    export namespace benefitRatio {
      /**
        * 删除
删除
        * /benefitRatio/batchDelete
        */
      export namespace batchDelete {
        export class Params {
          /** ID */
          ids: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: boolean;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
       * remove
       * /benefitRatio/delete/{ids}
       */
      export namespace remove {
        export class Params {
          /** ids */
          ids: string;
        }

        export type Response<T> = defs.basedata2.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 导出
导出
        * /benefitRatio/downloadfile
        */
      export namespace toDownLoad {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: any;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 导出
导出
        * /benefitRatio/export
        */
      export namespace exportCust {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: any;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 新增残障金比例
新增残障金比例
        * /benefitRatio/insert
        */
      export namespace insertBenefitRatio {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.DisableBenefitRatio;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.DisableBenefitRatio,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.DisableBenefitRatio,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询残障金比例
查询残障金比例
        * /benefitRatio/list
        */
      export namespace getBenefitRatio {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.DisableBenefitRatio;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.DisableBenefitRatio,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.DisableBenefitRatio,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询残障金比例
查询残障金比例
        * /benefitRatio/listPage
        */
      export namespace queryBenefitRatio {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.DisableBenefitRatio;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.cityQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.cityQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 分公司开票方式维护
     */
    export namespace branchInvoice {
      /**
       * delete
       * /branchInvoice/delete
       */
      export namespace remove {
        export class Params {
          /** 主键id */
          ids: string;
        }

        export type Response<T> = defs.basedata2.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询分公司开票方式
查询分公司开票方式
        * /branchInvoice/list
        */
      export namespace list {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.branchInvoiceDTO;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.branchInvoiceQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.branchInvoiceQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 保存分公司开票方式
保存分公司开票方式
        * /branchInvoice/saveOrUpdate
        */
      export namespace saveOrUpdate {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: boolean;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.branchInvoiceVO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.branchInvoiceVO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 业务大小类维护
     */
    export namespace businessType {
      /**
        * 单个新增小类信息
页面路径：基础数据管理->业务大小类维护->新增小类
        * /businessType/addBizSubType
        */
      export namespace addBizSubType {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.CommonResponse<number>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.BusinessSubType,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.BusinessSubType,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 单个新增大类信息
页面路径：基础数据管理->业务大小类维护->新增大类 -> 新增大类
        * /businessType/addBizType
        */
      export namespace addBizType {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.CommonResponse<number>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.BusinessType,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.BusinessType,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 新增小类名称小类名称下挂客户
新增小类名称下挂客户
        * /businessType/addExCust
        */
      export namespace addExCust {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.CommonResponse<number>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.BaseEntity,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.BaseEntity,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 删除小类名称小类名称下挂客户
删除小类名称小类名称下挂客户
        * /businessType/delExCust
        */
      export namespace delExCust {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.CommonResponse<number>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.BaseEntity,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.BaseEntity,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 删除小类
页面路径：基础数据管理->业务大小类维护->删除
请求参数：数组类型 [busSubtypeId]

        * /businessType/delSub
        */
      export namespace delSub {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.CommonResponse<number>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Array<number>,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Array<number>,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 删除上传材料
删除上传材料
        * /businessType/delSubTypeFile
        */
      export namespace delSubTypeFile {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.CommonResponse<number>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: object,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: object,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 删除大类
页面路径：基础数据管理->业务大小类维护->删除
请求参数：数组[busTypeId]

        * /businessType/delSup
        */
      export namespace delSup {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.CommonResponse<number>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Array<number>,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Array<number>,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 导出下挂客户
导出下挂客户
        * /businessType/downloadExCust
        */
      export namespace downloadExCust {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: any;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 导出人员小类名称
导出人员小类名称
        * /businessType/expBusnameSubtype
        */
      export namespace expBusnameSubtype {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: any;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.BdPolicyBusnameSubtype,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.BdPolicyBusnameSubtype,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询项目名称DropdownList
请求参数：{$busnameClassName$: 项目名称 }

        * /businessType/geBusnameClassDropdownList
        */
      export namespace geBusnameClassDropdownList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.CommonResponse<
            Array<defs.basedata2.DropdownList>
          >;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: object,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: object,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询大类DropdownList
请求参数：{cityId: 城市id
ssGroupId：社保组id
busnameClassId：业务项目名称id}
        * /businessType/getBusTypeDropdownList
        */
      export namespace getBusTypeDropdownList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.CommonResponse<
            Array<defs.basedata2.DropdownList>
          >;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: object,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: object,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询项目名称
查询项目名称
        * /businessType/getBusnameClassList
        */
      export namespace getBusnameClassList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.BdPolicyBusnameClass,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.BdPolicyBusnameClass,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询小类名称
查询小类名称
        * /businessType/getBusnameSubtypeList
        */
      export namespace getBusnameSubtypeList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.BdPolicyBusnameSubtype,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.BdPolicyBusnameSubtype,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询大类名称
查询大类名称
        * /businessType/getBusnameTypeList
        */
      export namespace getBusnameTypeList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.BdPolicyBusnameType,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.BdPolicyBusnameType,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询城市列表（供其他页面调用）
页面路径：供其他页面调用
请求参数：略

        * /businessType/getCityDropdownList
        */
      export namespace getCityDropdownList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.CommonResponse<
            Array<defs.basedata2.DropdownList>
          >;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: object,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: object,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询小类名称小类名称下挂客户
查询小类名称下挂客户
        * /businessType/getExCustList
        */
      export namespace getExCustList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.BaseEntity,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.BaseEntity,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询社保组下拉框
页面路径：基础数据管理->业务大小类维护-> 查询条件-> 所属类型
请求参数：{cityId: 城市ID }
响应结果列：key，shortName
        * /businessType/getSsGroupDropdownList
        */
      export namespace getSsGroupDropdownList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.CommonResponse<
            Array<defs.basedata2.DropdownList>
          >;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: object,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: object,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询小类列表（供其他页面调用）
页面路径：供其他页面调用
请求参数：略

        * /businessType/getSubTypeDropdownList
        */
      export namespace getSubTypeDropdownList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.CommonResponse<
            Array<defs.basedata2.DropdownList>
          >;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: object,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: object,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询大类(供其他页面调用)
页面路径：供其他页面调用
请求参数：略

        * /businessType/getSupTypeDropdownList
        */
      export namespace getSupTypeDropdownList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.CommonResponse<
            Array<defs.basedata2.DropdownList>
          >;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: object,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: object,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 新增项目名称
新增项目名称
        * /businessType/insertBusnameClass
        */
      export namespace insertBusnameClass {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.CommonResponse<number>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.BdPolicyBusnameClass,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.BdPolicyBusnameClass,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 新增小类名称
新增小类名称
        * /businessType/insertBusnameSubtype
        */
      export namespace insertBusnameSubtype {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.CommonResponse<number>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.BdPolicyBusnameSubtype,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.BdPolicyBusnameSubtype,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 新增大类名称
新增大类名称
        * /businessType/insertBusnameType
        */
      export namespace insertBusnameType {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.CommonResponse<number>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.BdPolicyBusnameType,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.BdPolicyBusnameType,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据大类id查询小类表格
页面路径：基础数据管理->业务大小类维护->小类表格查询
请求参数：
{
    "busTypeId": "",
    "pageNum": "",
    "pageSize": ""
}
        * /businessType/listBusSubType
        */
      export namespace listBusSubType {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.BusinessSubTypeQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.BusinessSubTypeQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 分页查询大类表格
页面路径：基础数据管理->业务大小类维护-> 大类表格查询
请求参数：
{
 "categoryId":"所属类型",
 "cityId":"所属城市",
 "busTypeName":"业务大类"
 "pageNum": "",
 "pageSize": ""
}
响应结果列：
categoryName 所属类型 , cityId 所属城市, busTypeName 业务大类名称, ssGroupId 关联社保组, orderRequire 订单要求, doRequire 实做要求,remark  说明
        * /businessType/listBusTypePage
        */
      export namespace listBusTypePage {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.BusinessTypeQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.BusinessTypeQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 保存小类信息
页面路径：基础数据管理->业务大小类维护->新增小类-> 保存小类
请求参数：
{
    "subList": [
        {
            "busSubtypeId": "",
            "busSubtypeName": "",
            "busTypeId": "",
            "whetherPay": "",
            "remark": "",
            "cityId": "",
            "cityName": "",
            "busTypeName": "",
            "isRef": ""
        }
    ],
    "businessType": {
        "categoryId": "取父表（位于上方表的字段categoryId）"
    }
}
        * /businessType/saveSub
        */
      export namespace saveSub {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.CommonResponse<number>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.BusinessTypeVO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.BusinessTypeVO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 保存大类信息
页面路径：基础数据管理->业务大小类维护->新增大类 -> 保存大类
请求参数：
[{
    "busTypeId": "",
    "categoryId": "",
    "cityId": "",
    "busTypeName": "",
    "ssGroupId": "",
    "remark": "",
    "cityName": "",
    "categoryName": "",
    "type": "",
    "ssGroupName": "",
    "orderRequire": "",
    "doRequire": ""
}]
        * /businessType/saveSup
        */
      export namespace saveSup {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.CommonResponse<number>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Array<defs.basedata2.BusinessType>,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Array<defs.basedata2.BusinessType>,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 单个修改大类信息
页面路径：基础数据管理->业务大小类维护->新增大类 -> 修改大类
        * /businessType/updateBizType
        */
      export namespace updateBizType {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.CommonResponse<number>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.BusinessType,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.BusinessType,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 单个修改小类信息
页面路径：基础数据管理->业务大小类维护->新增小类 -> 修改小类
        * /businessType/updateBusSubType
        */
      export namespace updateBusSubType {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.CommonResponse<number>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.BusinessSubType,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.BusinessSubType,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 修改项目名称
修改项目名称
        * /businessType/updateBusnameClass
        */
      export namespace updateBusnameClass {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.CommonResponse<number>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.BdPolicyBusnameClass,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.BdPolicyBusnameClass,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 修改小类名称
修改小类名称
        * /businessType/updateBusnameSubtype
        */
      export namespace updateBusnameSubtype {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.CommonResponse<number>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.BdPolicyBusnameSubtype,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.BdPolicyBusnameSubtype,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 修改大类名称
修改大类名称
        * /businessType/updateBusnameType
        */
      export namespace updateBusnameType {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.CommonResponse<number>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.BdPolicyBusnameType,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.BdPolicyBusnameType,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * minio上传文件接口
minio上传文件接口
        * /businessType/uploadBusSubTypeFile
        */
      export namespace uploadBusSubTypeFile {
        export class Params {
          /** file */
          file: File;
          /** subTypeId */
          subTypeId: any;
          /** subTypeType */
          subTypeType: any;
        }
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: string;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 城市信息
     */
    export namespace city {
      /**
        * 批量删除城市
批量删除城市
        * /city/batchDelete
        */
      export namespace batchDelete {
        export class Params {
          /** 城市ID */
          cityIds: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: boolean;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 批量新增或更新城市
批量新增或更新城市
        * /city/batchSaveOrUpdate
        */
      export namespace batchSaveOrUpdate {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: boolean;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.batchSaveOrUpdateCityVO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.batchSaveOrUpdateCityVO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 城市下拉数据
城市下拉数据
        * /city/cityDropDownList
        */
      export namespace cityDropDownList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.DropdownList;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 导出城市信息
导出城市信息
        * /city/downloadfile
        */
      export namespace toDownLoad {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: any;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取联动的城市下拉数据
获取联动的城市下拉数据
        * /city/getDropDownList
        */
      export namespace getDropDownList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.DropdownList;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询城市列表
查询城市
        * /city/list
        */
      export namespace queryCityList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.cityDTO;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.cityQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.cityQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 供应商网点城市下拉数据
供应商网点城市下拉数据
        * /city/providerCityList
        */
      export namespace providerCityList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.DropdownList;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 竞争对手信息
     */
    export namespace competitor {
      /**
        * 批量删除竞争对手
批量删除竞争对手
        * /competitor/batchDelete
        */
      export namespace batchDelete {
        export class Params {
          /** 竞争对手ID */
          competitorIds: Array<string>;
        }

        export type Response<T> = defs.basedata2.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 批量新增或更新竞争对手
批量新增或更新竞争对手
        * /competitor/batchSaveOrUpdate
        */
      export namespace batchSaveOrUpdate {
        export class Params {}

        export type Response<T> = defs.basedata2.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.basedata2.batchSaveOrUpdateCompetitorVO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.batchSaveOrUpdateCompetitorVO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取竞争对手下拉菜单数据
获取竞争对手下拉菜单数据
        * /competitor/getCompetitorDropdownList
        */
      export namespace getCompetitorDropdownList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.CommonResponse<
            Array<defs.basedata2.DropdownList>
          >;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: object,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: object,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 竞争对手列表
查询竞争对手列表
        * /competitor/list
        */
      export namespace list {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.competitorDTO;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.competitorQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.competitorQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 国家信息
     */
    export namespace country {
      /**
        * 批量删除国家
批量删除国家
        * /country/batchDelete
        */
      export namespace batchDelete {
        export class Params {
          /** 国家ID，逗号分隔 */
          countryIds: string;
        }

        export type Response<T> = defs.basedata2.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 批量新增或更新国家
批量新增或更新国家
        * /country/batchSaveOrUpdate
        */
      export namespace batchSaveOrUpdate {
        export class Params {}

        export type Response<T> = defs.basedata2.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.basedata2.batchSaveOrUpdateCountryVO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.batchSaveOrUpdateCountryVO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 国家下拉数据
国家下拉数据
        * /country/countryDropDownList
        */
      export namespace countryDropDownList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.DropdownList;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询国家列表
查询国家列表
        * /country/list
        */
      export namespace list {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.countryDTO;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.countryQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.countryQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 分公司用工方维护
     */
    export namespace empMaintain {
      /**
        * 批量删除用工方
批量删除用工方
        * /empmaintain/delete
        */
      export namespace remove {
        export class Params {
          /** 用工方id */
          ids: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: boolean;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 用工方下拉
用工方下拉
        * /empmaintain/dropdownList
        */
      export namespace getDropdownList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.DropdownList;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询分公司用工方
查询分公司用工方
        * /empmaintain/list
        */
      export namespace list {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.empMaintainDTO;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.empMaintainQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.empMaintainQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 维护支付分公司
维护支付分公司
        * /empmaintain/payProvider/{employerMaintainId}/{payProviderId}
        */
      export namespace maintainPayProvider {
        export class Params {
          /** employerMaintainId */
          employerMaintainId: string;
          /** payProviderId */
          payProviderId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: boolean;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 新增或修改分公司用工方
新增或修改分公司用工方
        * /empmaintain/saveOrUpdate
        */
      export namespace saveOrUpdate {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: boolean;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.empMaintainVO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.empMaintainVO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 用工方详情
用工方详情
        * /empmaintain/{id}
        */
      export namespace get {
        export class Params {
          /** id */
          id: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.empMaintainDTO;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 用工方银行卡维护
     */
    export namespace empMaintainAcct {
      /**
        * 批量删除银行卡信息
批量删除银行卡信息
        * /empmaintainacct/delete
        */
      export namespace remove {
        export class Params {
          /** 银行卡id */
          ids: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: boolean;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询银行卡信息
查询银行卡信息
        * /empmaintainacct/list/{maintainId}
        */
      export namespace list {
        export class Params {
          /** maintainId */
          maintainId: string;
          /** maintainId */
          '用工方id': string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.empMaintainAcctDTO;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 批量保存或更新用工方银行卡信息
批量保存或更新用工方银行卡信息
        * /empmaintainacct/saveOrUpdate
        */
      export namespace saveOrUpdate {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: boolean;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.batchSaveOrUpdateEmpMaintainAcctVO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.batchSaveOrUpdateEmpMaintainAcctVO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 单立户缴费实体维护
     */
    export namespace empMaintainPayEntity {
      /**
        * 删除单立户缴费实体
删除单立户缴费实体
        * /empmaintainPayEntity/delete/{ids}
        */
      export namespace remove {
        export class Params {
          /** ids */
          ids: string;
        }

        export type Response<T> = defs.basedata2.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 导出
导出
        * /empmaintainPayEntity/export
        */
      export namespace exporting {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: any;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.empMaintainAcctVO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.empMaintainAcctVO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 导出单立户缴费实体
导出集团公司
        * /empmaintainPayEntity/exportEntity
        */
      export namespace exportCompany {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: any;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 新增单立户缴费实体
新增单立户缴费实体
        * /empmaintainPayEntity/insert
        */
      export namespace insert {
        export class Params {}

        export type Response<T> = defs.basedata2.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.basedata2.EmpMaintainPayEntity,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.EmpMaintainPayEntity,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询单立户缴费实体
查询单立户缴费实体
        * /empmaintainPayEntity/list/{maintainId}
        */
      export namespace list {
        export class Params {
          /** maintainId */
          maintainId: string;
          /** maintainId */
          '用工方id': string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.EmpMaintainPayEntity;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 分页查询单立户缴费实体
分页查询单立户缴费实体
        * /empmaintainPayEntity/listPage
        */
      export namespace listPage {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.EmpMaintainPayEntity;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.empMaintainAcctVO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.empMaintainAcctVO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 分页查询单立户缴费实体
分页查询单立户缴费实体
        * /empmaintainPayEntity/listPageAll
        */
      export namespace listPageAllCondition {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.EmpMaintainPayEntity;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.empMaintainAcctVO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.empMaintainAcctVO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 修改单立户缴费实体
修改单立户缴费实体
        * /empmaintainPayEntity/update
        */
      export namespace update {
        export class Params {}

        export type Response<T> = defs.basedata2.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.basedata2.EmpMaintainPayEntity,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.EmpMaintainPayEntity,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 档案供应商
     */
    export namespace fileProvider {
      /**
        * 批量删除档案供应商
批量删除档案供应商
        * /fileprovider/batchDelete
        */
      export namespace batchDelete {
        export class Params {
          /** 档案供应商ID */
          fileProviderIds: Array<string>;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: boolean;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 导出档案供应商
导出档案供应商
        * /fileprovider/downloadfile
        */
      export namespace toDownLoad {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: any;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 档案供应商列表
查询档案供应商列表
        * /fileprovider/list
        */
      export namespace list {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.fileProviderDTO;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.FileProviderQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.FileProviderQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 重载模板附加页
重载模板附加页
        * /fileprovider/reloadtemplate
        */
      export namespace reloadTemplate {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: any;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 新增档案供应商
新增档案供应商
        * /fileprovider/save
        */
      export namespace save {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: boolean;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.fileProviderVO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.fileProviderVO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 修改档案供应商
修改档案供应商
        * /fileprovider/update
        */
      export namespace update {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: boolean;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.fileProviderVO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.fileProviderVO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 入离职基础数据
     */
    export namespace hireBase {
      /**
        * 《员工分类维护》查询
页面路径：tab 员工分类维护 - 查询 
请求参数：
{
    "cityId": "城市",
    "remark": "备注",
    "superTypeId": "大分类ID",
    "superTypeName": "大分类名称",
    "type":"所属名称"
}
响应结果列：
superTypeId 大分类ID , superTypeName 大分类名称 ，cityId 城市 ，remark 备注
        * /hireBase/findBaseAllType
        */
      export namespace findBaseAllType {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.CommonResponse<Array>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: object,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: object,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 《分类-服务-材料关联》 下左表 查询 
页面路径：tab 分类-服务-材料关联  下左表查询 
请求参数：{subtypeId: 小分类ID }
响应结果列 ： categorySvcId 主键 , remark 备注 

        * /hireBase/findSvcMaterial
        */
      export namespace findSvcMaterial {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.CommonResponse<Array>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: object,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: object,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 《材料维护》 分页查询
页面路径：tab 材料维护 - 查询 
请求参数：{materialName: 材料名称 ,pageNum : 1 , pageSize: 100}
响应结果列：materialId 主键 ,materialName 材料名称 , remark 备注 

        * /hireBase/queryMaterial
        */
      export namespace queryMaterial {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.CommonResponse<defs.basedata2.Page>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: object,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: object,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 《服务项目维护》 分页查询
页面路径：tab 服务项目维护 - 查询 
请求参数：{cityId: 城市ID, svcName: 服务名称,pageNum:,pageSize: } 
响应结果列：svcId 主键 ,cityId 城市ID， svcType 服务类型，svcName  服务名称 ， remark 备注 

        * /hireBase/queryServiceItem
        */
      export namespace queryServiceItem {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: object,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: object,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 《分类-服务-材料关联》上表查询
页面路径：tab 分类-服务-材料关联 - 上表查询 
请求参数：
{
    "cityId": "城市",
    "remark": "备注",
    "superTypeId": "大分类ID",
    "superTypeName": "大分类名称",
    "type":"所属名称"
}
响应结果列：
superTypeId 大类主键ID , superTypeName 大分类名称 ，cityId 城市 ，remark 备注
        * /hireBase/querySuperTypeTree
        */
      export namespace querySuperTypeTree {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.CommonResponse<Array>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: object,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: object,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 《员工分类维护》保存
页面路径：tab 员工分类维护 - 保存  
请求参数：属性+集合
{
    "userId": "",
    "hireEmpTypeList": [
        {
            "cityId": "城市",
            "remark": "备注",
            "superTypeId": "大分类ID",
            "superTypeName": "大分类名称",
            "type": "所属名称"
            "clientOperation": "记录类型"
            "children": [clientOperation:'',subtypeName:'小分类名称',remark:'备注',subtypeId:'小类ID',SuperTypeId:'大类Id']
        }
    ]
}


        * /hireBase/saveBaseAllType
        */
      export namespace saveBaseAllType {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: object,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: object,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 《服务项目维护》 批量保存
页面路径：tab 服务项目维护 - 保存 
请求参数：3个属性都是集合
{addList:[{svcId:主键 ,cityId :城市ID, svcType: 服务类型, svcName : 服务名称 , remark :备注 }],
uptList:[{svcId:主键 ,cityId :城市ID, svcType: 服务类型, svcName : 服务名称 , remark :备注 }],
delList:[{svcId:主键}]
}
        * /hireBase/saveHireService
        */
      export namespace saveHireService {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: object,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: object,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 《材料维护》 批量保存
页面路径：tab 材料维护 - 保存
请求参数：3个属性都是集合
{addList:[{materialId:主键 , materialName : 材料名称 , remark :备注 }],
uptList:[{materialId:主键 , materialName : 材料名称 , remark :备注 }],
delList:[{materialId:主键}]
}
        * /hireBase/saveMaterial
        */
      export namespace saveMaterial {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: object,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: object,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 《分类-服务-材料关联》 保存
页面路径：tab 分类-服务-材料关联  保存 
请求参数：
{属性+集合
    "userId": "当前用户ID",
    "hireSubtypeServiceList": [集合
        {
            "clientOperation": "记录状态：1 追加 ， 2 修改 ，-1 删除",
            "categorySvcId": "左下表关系表主键，如果是修改，",
            "svcId": "左下表的服务ID",
            "subtypeId": "位于页面上表的小类主键",
            "children": [子集合
                {
                    "clientOperation": "记录状态：1 追加 ， 2 修改 ，-1 删除",
                    "materialId": "材料ID"
                }
            ]
        }
    ]
}

        * /hireBase/saveRelation
        */
      export namespace saveRelation {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: object,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: object,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 金蝶供应商维护
     */
    export namespace jindieOrg {
      /**
        * 部门下拉列表
页面按钮路径：【新增】-【所属母公司】文本框下拉列表；
参数：{jindieOrgName: 金碟分公司名称}；
说明：直接调用无需传参，jindieOrgName 选填
        * /jindieOrg/getSuperOrgDropDownList
        */
      export namespace getSuperOrgDropDownList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.CommonResponse<
            Array<defs.basedata2.DropdownList>
          >;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: object,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: object,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 新增金蝶分公司
页面按钮路径：【新增】-【提交】;
 请求参数：
{
 "jindieOrgId":"金碟分公司编号",
 "jindieOrgName":"金碟分公司名称",
 "jindieOrgType":"子母公司",
 "superOrgId":"所属母公司ID",
}说明：新增的时候已经把 Flex 前端的部分校验逻辑实现掉，判断供应商名称和编号是否重复 

        * /jindieOrg/insertJindieOrg
        */
      export namespace insertJindieOrg {
        export class Params {}

        export type Response<T> = defs.basedata2.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.basedata2.JindieOrg,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.JindieOrg,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 金蝶供应商分页查询
请求参数：
{
 "jindieOrgId":"金碟分公司编号",
 "jindieOrgName":"金碟分公司名称",
 "jindieOrgType":"子母公司",
 "isValid": "是否生效",
 "pageNum": "1",
 "pageSize": ""
}
响应结果列： jindieOrgId 金蝶分公司编号 ,jindieOrgName  金蝶分公司名称 ,isSuperOrgList  子/母公司 ,superOrgId  所属母公司, isValid 是否有效,
 
        * /jindieOrg/listPage
        */
      export namespace listPage {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.JindieOrgQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.JindieOrgQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 同步金碟分公司数据接口
同步金碟分公司数据接口
        * /jindieOrg/sysncJindieOrg
        */
      export namespace sysncJindieOrg {
        export class Params {}

        export type Response<T> = defs.basedata2.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 修改金蝶分公司
页面路径：【修改】-【提交】；
请求参数："{\n" +
            " \"jindieOrgId\":\"金碟分公司编号\",\n" +
            " \"jindieOrgName\":\"金碟分公司名称\",\n" +
            " \"jindieOrgType\":\"子母公司\",\n" +
            " \"superOrgId\":\"所属母公司ID\",\n" +
            "}" +说明：修改的时候已经把 Flex 前端的部分校验逻辑实现掉，判断供应商名称和编号是否重复

        * /jindieOrg/updateJindieOrg
        */
      export namespace updateJindieOrg {
        export class Params {}

        export type Response<T> = defs.basedata2.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.basedata2.JindieOrg,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.JindieOrg,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 失效
页面按钮路径：功能主页面的【失效】；
请求参数：如下
{
 "jindieOrgId":"金碟分公司编号",
 "isValid": "是否生效"
}说明：jindieOrgId 必传，isValid 必传
        * /jindieOrg/updateJindieOrgisValid
        */
      export namespace updateJindieOrgisValid {
        export class Params {}

        export type Response<T> = defs.basedata2.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.basedata2.JindieOrg,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.JindieOrg,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 材料维护
     */
    export namespace materialsInfo {
      /**
        * 《材料维护》选择删除
页面路径：基础数据管理->业务材料数据->材料维护-》删除；
请求参数：
数组类型 int[materialsId] 
        * /materialsInfo/delMaterialsInfo
        */
      export namespace delMaterialsInfo {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.CommonResponse<number>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Array<number>,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Array<number>,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 《材料包维护》删除材料包下材料信息
页面路径：基础数据管理->业务材料数据->材料包维护-》位于下方的子表格查询-》删除 ；
请求参数：数组int[svcPackageId] 

        * /materialsInfo/delPackage
        */
      export namespace delPackage {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.CommonResponse<number>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Array<number>,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Array<number>,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 《材料包维护》主表查询
页面路径：基础数据管理->业务材料数据->材料包维护-》位于上方的主表格查询
请求参数：
{
 "cityName":"城市",
 "busTypeName":"业务大类",
 "busSubtypeName":"业务小类",
 "pageNum": "",
 "pageSize": ""
}
响应结果列： cityName 城市， busTypeName 业务大类， busSubtypeName 业务小类
        * /materialsInfo/listBusType
        */
      export namespace listBusType {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.BusinessSubTypeQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.BusinessSubTypeQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 《材料维护》分页查询
页面路径：基础数据管理->业务材料数据->材料维护-》查询
请求参数：
{
 "materialsName":"材料名称",
 "pageNum": "",
 "pageSize": ""
}
响应结果列：materialsId 材料编号 ，materialsName 材料名称，isOriginal 是否原件 ，createName 创建人 ，createDt 创建时间，branchName 创建人分公司 ，remark 备注，isRef 是否引用

        * /materialsInfo/listMaterialsInfoPage
        */
      export namespace listMaterialsInfoPage {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.MaterialsQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.MaterialsQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 新增材料查询不在材料包的材料信息
页面路径：基础数据管理->业务材料数据->材料包维护-》位于下方的子表格查询-》新增弹窗-》筛选
请求参数：
{busSubtypeId:,materialsName：材料名称 , pageNum ,pageSize}
响应结果列：materialsId 材料编号 ， materialsName 材料名称 ， isOriginal 是否原件
        * /materialsInfo/listMaterialsNotInPackage
        */
      export namespace listMaterialsNotInPackage {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.MaterialsPackageQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.MaterialsPackageQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 《材料包维护》子表查询
页面路径：基础数据管理->业务材料数据->材料包维护-》位于下方的子表格查询
请求参数：
{busSubtypeId:业务小类 , pageNum ,pageSize}
响应结果列：  materialsId 材料编号， materialsName 材料名称， isOriginal 是否原件，  materialsAccount 份数，  isReturn 返还属性
        * /materialsInfo/listPackge
        */
      export namespace listPackge {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.MaterialsPackageQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.MaterialsPackageQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 《材料维护》批量保存
页面路径：基础数据管理->业务材料数据->材料维护-》保存；
请求参数：
数组对象 list [{materialsName 材料名称，isOriginal 是否原件，remark 备注}]
        * /materialsInfo/saveMaterialsInfo
        */
      export namespace saveMaterialsInfo {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.CommonResponse<number>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Array<defs.basedata2.Materials>,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Array<defs.basedata2.Materials>,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 保存材料包
页面路径：基础数据管理->业务材料数据->材料包维护-》位于下方的子表格查询-》保存; 
请求参数：
数组对象：[{
    "packageId": "材料编号",
    "busTypeId": "业务大类",
    "busSubtypeId": "业务小类",
    "materialsId": "材料编号",
    "materialsAccount": "份数",
    "isReturn": "返还属性"
}]
        * /materialsInfo/savePackage
        */
      export namespace savePackage {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.CommonResponse<number>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Array<defs.basedata2.MaterialsPackage>,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Array<defs.basedata2.MaterialsPackage>,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 《材料维护》更新材料信息
页面路径：基础数据管理->业务材料数据->材料维护-》修改-》保存
请求参数：
{
    "materialsId": "材料编号（必填）",
    "materialsName": "材料名称",
    "isOriginal": "是否原件",
    "nremark": "新备注"
}
        * /materialsInfo/updateMaterialsInfo
        */
      export namespace updateMaterialsInfo {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.CommonResponse<number>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.Materials,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.Materials,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 医疗机构维护
     */
    export namespace medicalIns {
      /**
        * 添加导入任务
添加导入任务
        * /medicalIns/addImpTask
        */
      export namespace addImpTask {
        export class Params {}

        export type Response<T> = defs.basedata2.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.basedata2.ImMedicalDTO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.ImMedicalDTO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 删除医疗机构信息
删除医疗机构信息
        * /medicalIns/del
        */
      export namespace del {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.CommonResponse<number>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.MedicalInsVO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.MedicalInsVO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 新增
新增
        * /medicalIns/save
        */
      export namespace save {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.CommonResponse<number>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Array<defs.basedata2.MedicalIns>,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Array<defs.basedata2.MedicalIns>,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 医疗机构查询
医疗机构查询
        * /medicalIns/sel
        */
      export namespace sel {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.medicalInsQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.medicalInsQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询导入结果
查询导入结果
        * /medicalIns/selImpResult
        */
      export namespace selImpResult {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.ImMedicalQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.ImMedicalQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 医疗机构查询
医疗机构查询
        * /medicalIns/selImportTask
        */
      export namespace selImportTask {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.ImMedicalQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.ImMedicalQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 作废任务
作废任务
        * /medicalIns/uptImpTask
        */
      export namespace uptImpTask {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.CommonResponse<number>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.MedicalInsVO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.MedicalInsVO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * ⽀付地与客户同步
     */
    export namespace payAccountCust {
      /**
        * 新增⽀付地客户账号同步
新增⽀付地客户账号同步
        * /payAccountCust/insertPayAccountCust
        */
      export namespace insertPayAccountCust {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.FilterEntity,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.FilterEntity,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * ⽀付地与客户同步查询
⽀付地与客户同步查询
        * /payAccountCust/queryPayAccountCust
        */
      export namespace queryPayAccountCust {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.FilterEntity,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.FilterEntity,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 停用⽀付地客户账号
修改⽀⽀付地客户账号
        * /payAccountCust/stopPayAccountCust
        */
      export namespace stopPayAccountCust {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.FilterEntity,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.FilterEntity,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 同步信息同步到沐融系统
⽀付地账号同步信息同步到沐融系统
        * /payAccountCust/synAccountCust
        */
      export namespace synAccountCust {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.FilterEntity,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.FilterEntity,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * ⽀付地账号同步
     */
    export namespace payAccountSync {
      /**
        * ⽀付地账号同步信息同步到沐融系统
⽀付地账号同步信息同步到沐融系统
        * /PayAccountSync/executeAccountSync
        */
      export namespace executeAccountSync {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.BaseEntity,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.BaseEntity,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 新增⽀付地账号同步
新增⽀付地账号同步
        * /PayAccountSync/insertPayAccountSync
        */
      export namespace insertPayAccountSync {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.BaseEntity,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.BaseEntity,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        *  查询银行相关信息
 查询银行相关信息
        * /PayAccountSync/queryBankInfo
        */
      export namespace queryBankInfo {
        export class Params {}

        export type Response<T> = defs.basedata2.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.basedata2.PayAccountSyncQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.PayAccountSyncQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取支付地抬头
获取支付地抬头
        * /PayAccountSync/queryBranchTitleByBranchId
        */
      export namespace getCustPayerDorpDownList {
        export class Params {
          /** branchId */
          branchId: string;
        }

        export type Response<T> = defs.basedata2.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * ⽀付地账号同步查询
⽀付地账号同步查询
        * /PayAccountSync/queryPayAccountSync
        */
      export namespace queryPayAccountSync {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.PayAccountSyncQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.PayAccountSyncQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 修改⽀付地账号同步信息
修改⽀付地账号同步信息
        * /PayAccountSync/updatePayAccountSync
        */
      export namespace updatePayAccountSync {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.BaseEntity,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.BaseEntity,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 人员分类
     */
    export namespace personCategory {
      /**
        * 删除
删除
        * /personCategory/deletePersonCategoryById
        */
      export namespace deletePersonCategoryById {
        export class Params {
          /** personCategoryId */
          personCategoryId: string;
        }

        export type Response<T> = defs.basedata2.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 导出信息
导出信息
        * /personCategory/downloadfile
        */
      export namespace toDownLoad {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: any;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取人员分类初始化下拉框
获取城市和人品分类初始化下拉框
        * /personCategory/getPersonCategoryDropdownList
        */
      export namespace getPersonCategoryDropdownList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.dropdownListDTO;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 通过城市id查询人员分类名称
通过城市id查询人员分类名称
        * /personCategory/queryCategoryNameByCityId
        */
      export namespace queryCategoryNameByCityId {
        export class Params {
          /** cityId */
          cityId: string;
        }
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.dropdownListDTO;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 分页查询人员分类信息
分页查询人员分类信息
        * /personCategory/queryPersonCategoryGroupList
        */
      export namespace queryPersonCategoryGroupList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.personCategoryDTO;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.personCategoryQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.personCategoryQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 保存
保存
        * /personCategory/savePersonCategoryInfo
        */
      export namespace savePersonCategoryInfo {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.CommonResponse<number>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.personCategoryQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.personCategoryQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 福利产品大类小类
     */
    export namespace productType {
      /**
        * 批量删除福利产品小类
批量删除福利产品小类
        * /producttype/sub/batchDelete
        */
      export namespace batchDeleteSub {
        export class Params {
          /** 福利产品小类ID */
          ids: Array<string>;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: boolean;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 批量新增或更新福利产品小类
批量新增或更新福利产品小类
        * /producttype/sub/batchSaveOrUpdate
        */
      export namespace batchSaveOrUpdateSub {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: boolean;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.batchSaveOrUpdateProductSubVO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.batchSaveOrUpdateProductSubVO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 福利产品小类列表
查询福利产品小类列表
        * /producttype/sub/list
        */
      export namespace listSub {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.productSubDTO;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.productSubQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.productSubQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 批量删除福利产品大类
批量删除福利产品大类
        * /producttype/super/batchDelete
        */
      export namespace batchDelete {
        export class Params {
          /** 福利产品大类ID */
          ids: Array<string>;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: boolean;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 批量新增或更新福利产品大类
批量新增或更新福利产品大类
        * /producttype/super/batchSaveOrUpdate
        */
      export namespace batchSaveOrUpdate {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: boolean;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.batchSaveOrUpdateProductSuperVO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.batchSaveOrUpdateProductSuperVO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 检查福利产品大类下是否包含福利产品小类
检查福利产品大类下是否包含福利产品小类
        * /producttype/super/checkHasSub
        */
      export namespace checkHasSub {
        export class Params {
          /** 福利产品大类ID */
          ids: Array<string>;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: number;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 福利产品大类列表
查询福利产品大类列表
        * /producttype/super/list
        */
      export namespace list {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.productSuperDTO;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.productSuperQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.productSuperQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 省份信息
     */
    export namespace province {
      /**
        * 批量删除省份
批量删除省份
        * /province/batchDelete
        */
      export namespace batchDelete {
        export class Params {
          /** 省份ID，逗号分隔 */
          provinceIds: string;
        }

        export type Response<T> = defs.basedata2.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 批量新增或更新省份
批量新增或更新省份
        * /province/batchSaveOrUpdate
        */
      export namespace insertOrUpdateProvinces {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: boolean;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.batchSaveOrUpdateProvinceVO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.batchSaveOrUpdateProvinceVO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询省份列表
查询省份列表
        * /province/list
        */
      export namespace queryProvinceList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.provinceDTO;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.provinceQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.provinceQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 省份下拉数据
省份下拉数据
        * /province/provinceDropDownList
        */
      export namespace provinceDropDownList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.DropdownList;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 省市信息
     */
    export namespace region {
      /**
       * 根据code获取下拉列表(新老code都支持)
       * /region/getByCode
       */
      export namespace getByCode {
        export class Params {
          /** 行政区域编码 */
          code: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.GlobalResult<
            Array<defs.basedata2.ObjectMap<string, ObjectMap>>
          >;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询省市列表，不传参数查所有省份列表
查询省市列表，不传参数查所有省份列表
        * /region/list
        */
      export namespace getRegionList {
        export class Params {
          /** code */
          code?: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.provinceDTO;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 服务网点维护
     */
    export namespace servicePoint {
      /**
        * 城市下拉数据
城市下拉数据
        * /servicePoint/cityDropDownListEx
        */
      export namespace cityDropDownList {
        export class Params {
          /** area */
          area: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.DropdownList;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 导出数据
导出数据
        * /servicePoint/createServicePoint
        */
      export namespace createServicePoint {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: any;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.ServicePointDTO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.ServicePointDTO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 导出历史表数据
导出历史表数据
        * /servicePoint/createServicePointHis
        */
      export namespace createServicePointHis {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: any;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.ServicePointDTO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.ServicePointDTO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 区域
区域
        * /servicePoint/getAreaDropDownList
        */
      export namespace getAreaDropDownList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.CommonResponse<
            Array<defs.basedata2.DropdownList>
          >;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.servicePointQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.servicePointQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 联系人（客服）
联系人（客服）
        * /servicePoint/getContactName
        */
      export namespace getContactName {
        export class Params {}

        export type Response<T> = defs.basedata2.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.basedata2.servicePointQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.servicePointQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 联系电话
联系电话
        * /servicePoint/getContactTel
        */
      export namespace getContactTel {
        export class Params {}

        export type Response<T> = defs.basedata2.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.basedata2.servicePointQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.servicePointQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 服务网点名称（接单方）
服务网点名称（接单方）
        * /servicePoint/getServiceAssigneeName
        */
      export namespace getServiceAssigneeName {
        export class Params {}

        export type Response<T> = defs.basedata2.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.basedata2.servicePointQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.servicePointQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 服务网点名称（集团全称）
服务网点名称（集团全称）
        * /servicePoint/getServiceGroupName
        */
      export namespace getServiceGroupName {
        export class Params {
          /** assigneeProviderId */
          assigneeProviderId: string;
        }

        export type Response<T> = defs.basedata2.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 服务网点查询
服务网点查询
        * /servicePoint/getServicePointPage
        */
      export namespace getServicePointPage {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.ServicePointDTO;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.servicePointQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.servicePointQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 服务网点维护-新增-选择按钮-保存动作
字符串格式传入servicePointDTO
        * /servicePoint/insertServicePoint
        */
      export namespace insertServicePoint {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.ServicePointDTO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.ServicePointDTO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 服务网点维护-新增-选择按钮-网点信息添加校验接口
字符串格式传入assigneeProviderId
        * /servicePoint/isDeletedServicePoint
        */
      export namespace isDeletedServicePoint {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.CommonResponse<number>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.ServicePointDTO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.ServicePointDTO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 省份
省份
        * /servicePoint/provinceDropDownList
        */
      export namespace provinceDropDownList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.CommonResponse<
            Array<defs.basedata2.DropdownList>
          >;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.servicePointQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.servicePointQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 网点分公司
网点分公司
        * /servicePoint/queryBranch
        */
      export namespace queryBranch {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.CommonQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.CommonQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 城市
城市
        * /servicePoint/queryCityList
        */
      export namespace queryCityList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.CommonResponse<
            Array<defs.basedata2.DropdownList>
          >;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.servicePointQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.servicePointQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查看历史
查看历史
        * /servicePoint/queryServicePointDetail
        */
      export namespace queryServicePointDetail {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.CommonResponse<
            Array<defs.basedata2.ServicePointDTO>
          >;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.ServicePointDTO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.ServicePointDTO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 服务网点维护-新增-选择按钮-城市优选网点校验接口
字符串格式传入cityId
        * /servicePoint/selectServicePointCityCount
        */
      export namespace selectServicePointCityCount {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.CommonResponse<number>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.ServicePointDTO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.ServicePointDTO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 服务网点维护-修改-选择按钮-保存动作
字符串格式传入servicePointDTO
        * /servicePoint/updateServicePoint
        */
      export namespace updateServicePoint {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.ServicePointDTO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.ServicePointDTO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 服务网点维护-失效-选择按钮-保存动作
字符串格式传入servicePointDTO
        * /servicePoint/updateServicePointisValid
        */
      export namespace updateServicePointisValid {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.ServicePointDTO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.ServicePointDTO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 服务网点特定客户备案
     */
    export namespace servicePointCust {
      /**
        * 服务网点特定客户备案-客户是否生效校验接口
字符串格式传入custId
        * /servicePointCust/checkOnlyValidCustomer
        */
      export namespace checkOnlyValidCustomer {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.ServicePointCustDTO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.ServicePointCustDTO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 服务网点特定客户备案-删除
服务网点特定客户备案-删除
        * /servicePointCust/delCust
        */
      export namespace delCust {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.CommonResponse<number>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.ServicePointCustDTO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.ServicePointCustDTO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 删除
字符串格式传入servicePointCustDTO
        * /servicePointCust/delCustomerRecord
        */
      export namespace delCustomerRecord {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.ServicePointCustDTO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.ServicePointCustDTO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 选择客户查询
选择客户查询
        * /servicePointCust/getCustomer
        */
      export namespace getCustomer {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.ServicePointCustDTO;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.servicePointCustQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.servicePointCustQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 服务网点特定客户备案-新增-选择按钮-保存动作
字符串格式传入servicePointCustDTO
        * /servicePointCust/save
        */
      export namespace save {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.ServicePointCustDTO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.ServicePointCustDTO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 特定客户备案客户查询
特定客户备案客户查询
        * /servicePointCust/sel
        */
      export namespace sel {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.ServicePointCustDTO;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.servicePointCustQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.servicePointCustQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 服务网点特定客户备案-客户是否已添加校验接口
字符串格式传入custId
        * /servicePointCust/selectServicePointCustCount
        */
      export namespace selectServicePointCustCount {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.CommonResponse<number>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.ServicePointCustDTO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.ServicePointCustDTO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 合同大小类
     */
    export namespace serviceType {
      /**
        * 批量删除合同大类
批量删除合同大类
        * /servicetype/batchDelete
        */
      export namespace batchDelete {
        export class Params {
          /** 合同大类ID */
          ids: Array<string>;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: boolean;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 批量新增或更新合同大类
批量新增或更新合同大类
        * /servicetype/batchSaveOrUpdate
        */
      export namespace batchSaveOrUpdate {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: boolean;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.batchSaveOrUpdateServiceTypeVO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.batchSaveOrUpdateServiceTypeVO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 合同大类下拉列表
合同大类下拉列表
        * /servicetype/getContractTypeDropdownList
        */
      export namespace getContractTypeDropdownList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.DropdownList;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 合同大类列表
查询合同大类列表
        * /servicetype/list
        */
      export namespace list {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.serviceTypeDTO;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.serviceTypeQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.serviceTypeQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 批量删除合同小类
批量删除合同小类
        * /servicetype/sub/batchDelete
        */
      export namespace batchDeleteSub {
        export class Params {
          /** 合同小类ID */
          ids: Array<string>;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: boolean;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 批量新增或更新合同小类
批量新增或更新合同小类
        * /servicetype/sub/batchSaveOrUpdate
        */
      export namespace batchSaveOrUpdateSub {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: boolean;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.batchSaveOrUpdateServiceSubTypeVO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.batchSaveOrUpdateServiceSubTypeVO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 合同小类列表
查询合同小类列表
        * /servicetype/sub/list
        */
      export namespace listSub {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.serviceSubTypeDTO;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.serviceSubTypeQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.serviceSubTypeQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 特殊签单方抬头
     */
    export namespace specialBranchTitle {
      /**
        * 新增特殊分公司抬头
新增特殊分公司抬头
        * /specialBranchTitle/insertSpecialBranchTitle
        */
      export namespace insertSpecialBranchTitle {
        export class Params {}

        export type Response<T> = defs.basedata2.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.basedata2.SpecialBranchTitle,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.SpecialBranchTitle,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 特殊签单方抬头分页查询
特殊签单方抬头分页查询
        * /specialBranchTitle/list
        */
      export namespace list {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.SpecialBranchTitleQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.SpecialBranchTitleQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 分公司抬头失效
分公司抬头失效
        * /specialBranchTitle/updateSpecialBranchTitleDisable
        */
      export namespace updateSpecialBranchTitleDisable {
        export class Params {}

        export type Response<T> = defs.basedata2.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.basedata2.SpecialBranchTitle,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.SpecialBranchTitle,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 用印流程查询
     */
    export namespace stamp {
      /**
        * 导出
导出使用印章流程查询
请求参数：
{  } 


        * /stamp/expSelectStamp
        */
      export namespace expSelectStamp {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: any;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 使用印章流程查询
使用印章流程查询
        * /stamp/selectStamp
        */
      export namespace selectStamp {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.FilterEntity,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.FilterEntity,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 维护用印流程说明
维护用印流程说明
        * /stamp/updateSealDes
        */
      export namespace updateSealDes {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.FilterEntity,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.FilterEntity,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 签单方抬头
     */
    export namespace sysBranchTitle {
      /**
        * 删除摘要名称
页面路径：【签单方抬头】-【维护摘要名称】-【删除】 ；
 请求参数：数组 int[titleParItemId]    
        * /sysBranchTitle/delBranchTitleAbstract
        */
      export namespace delBranchTitleAbstract {
        export class Params {}

        export type Response<T> = defs.basedata2.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: Array<number>,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Array<number>,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 删除开票机
页面路径：【签单方抬头】-【维护开票机】-【删除】 ；
 请求参数：数组 int[billingId]    
        * /sysBranchTitle/delBranchTitleBilling
        */
      export namespace delBranchTitleBilling {
        export class Params {}

        export type Response<T> = defs.basedata2.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: Array<number>,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Array<number>,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 导出
导出
        * /sysBranchTitle/downloadfile
        */
      export namespace toDownLoad {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: any;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询摘要名称
页面路径：【签单方抬头】-【维护摘要名称】-【默认查询】 ；
请求参数：{"titleId":"必传"} 
响应结果列：titleParItemId 主键， titleId 父表ID， itemName 摘要名称
        * /sysBranchTitle/getBranchTitleAbstract
        */
      export namespace getBranchTitleAbstract {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.SysBranchTitleParItem,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.SysBranchTitleParItem,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询开票机
页面路径：【签单方抬头】-【维护开票机】-【默认查询】 ；
请求参数：{"titleId":"必传"} 
响应结果列：billingGold 金税盘号， billingNum 开票机号， remark 备注
        * /sysBranchTitle/getBranchTitleBillingPage
        */
      export namespace getBranchTitleBillingPage {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.SysBranchTitleBilling,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.SysBranchTitleBilling,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 分公司下拉列表
路径：功能页面-》查询条件-》分公司下拉列表, 条件已卡，直接调用，无需传参数
响应返回列：key ， shortName
        * /sysBranchTitle/getDepartmentDropdownList
        */
      export namespace getDepartmentDropdownList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.CommonResponse<
            Array<defs.basedata2.DropdownList>
          >;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: object,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: object,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 金蝶编号下拉列表
路径：【新增】-》【金蝶分公司编号】下拉列表，直接调用，无需传参数；
返回值：key , shortName ;
        * /sysBranchTitle/getJindieOrgDropDownList
        */
      export namespace getJindieOrgDropDownList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.CommonResponse<
            Array<defs.basedata2.DropdownList>
          >;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 签单方抬头分页查询
页面路径： 功能页面的主查询
请求参数：
{
	"branchId": "分公司Id",
	"isValid": "是否生效",
	"titleCode": "签约方内部编号",
	"titleName": "分公司抬头",
 "pageNum": "",
 "pageSize": ""
}
响应结果列：
titleId 编号, titleCode 签约方内部编号,titleName 分公司抬头,isValid 是否生效,departName 分公司，jindieOrgId 金蝶组织编号
        * /sysBranchTitle/listPage
        */
      export namespace listPage {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.SysBranchTitleQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.SysBranchTitleQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 新增分公司抬头(保存)
页面路径： 【新增】-》【保存】
请求参数：
{
	"branchId": "分公司Id",
	"departName": "部门名称",
	"memo": "备注",
	"titleId": "主键",
	"titleCode": "签约方内部编号",
	"titleName": "分公司抬头",
	"jindieOrgId": "金蝶组织编号",
}
        * /sysBranchTitle/saveBranchTitle
        */
      export namespace saveBranchTitle {
        export class Params {}

        export type Response<T> = defs.basedata2.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.basedata2.SysBranchTitle,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.SysBranchTitle,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 摘要名称,批量新增修改
页面按钮路径：【签单方抬头】-【维护摘要名称】-【保存】 ；
请求参数：addList 新增集合，uptList 修改集合 
格式：
{
    "addListPar": [
        {
            "titleParItemId": "主键",
            "titleId": "父表ID",
            "itemName": "摘要名称"
        }
    ],
    "uptListPar": [
        {
            "titleParItemId": "主键",
            "titleId": "父表ID",
            "itemName": "摘要名称"
        }
    ]
}
        * /sysBranchTitle/saveBranchTitleAbstract
        */
      export namespace saveBranchTitleAbstract {
        export class Params {}

        export type Response<T> = defs.basedata2.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.basedata2.SysBranchTitleVO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.SysBranchTitleVO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 开票机,批量新增修改
页面按钮路径：【签单方抬头】-【维护开票机】-【保存】 ；
请求参数：addList 新增集合，uptList 修改集合 
格式：
{
    "addList": [
        {
            "billingGold": "金税盘号",
            "billingNum": "开票机号",
            "remark": "备注"
        }
    ],
    "uptList": [
        {
            "billingId": "主键",
            "billingGold": "",
            "billingNum": "",
            "remark": ""
        }
    ]
}
        * /sysBranchTitle/saveBranchTitleBilling
        */
      export namespace saveBranchTitleBilling {
        export class Params {}

        export type Response<T> = defs.basedata2.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.basedata2.SysBranchTitleVO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.SysBranchTitleVO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 新增分公司抬头（生效）
页面按钮路径： 【新增】-》【生效】
请求参数：
{
	"branchId": "分公司Id",
	"departName": "部门名称",
	"memo": "备注",
	"titleId": "主键",
	"titleCode": "签约方内部编号",
	"titleName": "分公司抬头",
	"jindieOrgId": "金蝶组织编号",
}
        * /sysBranchTitle/saveBranchTitleEnable
        */
      export namespace saveBranchTitleEnable {
        export class Params {}

        export type Response<T> = defs.basedata2.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.basedata2.SysBranchTitle,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.SysBranchTitle,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询分公司抬头数量
方法中的子接口
请求参数：
{
	"branchId": "分公司Id",
	"titleName": "分公司抬头",
}
        * /sysBranchTitle/selectBranchTitleCount
        */
      export namespace selectBranchTitleCount {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.CommonResponse<number>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.SysBranchTitle,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.SysBranchTitle,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 更新分公司抬头
页面按钮路径：【生效】|【失效】|【删除】
请求参数：
{"titleId":"主键","isValid":"1生效 0失效","isDeleted":"1删除", }
 注意：一个 api 多用途，生效 失效 删除 都调它
        * /sysBranchTitle/updateBranchTitle
        */
      export namespace updateBranchTitle {
        export class Params {}

        export type Response<T> = defs.basedata2.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.basedata2.SysBranchTitle,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.SysBranchTitle,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 纳税优惠人员信息维护（6W）
     */
    export namespace taxPayerBenefit {
      /**
        * 新增
新增
        * /taxPayerBenefit/add
        */
      export namespace add {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: boolean;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.TaxPayerBenefit,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.TaxPayerBenefit,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 删除
删除
        * /taxPayerBenefit/del
        */
      export namespace del {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: boolean;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.TaxPayerBenefit,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.TaxPayerBenefit,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 导出
导出
        * /taxPayerBenefit/export
        */
      export namespace toDownLoad {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: any;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询
查询
        * /taxPayerBenefit/list
        */
      export namespace list {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.TaxPayerBenefitQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.TaxPayerBenefitQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 税率表维护
     */
    export namespace taxRate {
      /**
       * delete
       * /taxrate/delete
       */
      export namespace remove {
        export class Params {
          /** flag=1删除主表信息，flag=2删除子表信息 */
          flag: string;
          /** 主键id */
          ids: string;
        }

        export type Response<T> = defs.basedata2.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据id获取税率表信息
根据id获取税率表信息
        * /taxrate/detail/{id}
        */
      export namespace getTaxDetailList {
        export class Params {
          /** id */
          id: number;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.taxRateDetailDTO;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 保存税率表信息
保存税率表信息
        * /taxrate/list
        */
      export namespace getTaxBaseList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.taxRateDTO;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.taxRateQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.taxRateQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 保存税率表信息
保存税率表信息
        * /taxrate/saveOrUpdate
        */
      export namespace saveOrUpdate {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: boolean;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.taxRateVO,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.taxRateVO,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据id获取税率表信息
根据id获取税率表信息
        * /taxrate/{id}
        */
      export namespace getTaxRateByTaxId {
        export class Params {
          /** id */
          id: number;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.taxRateDTO;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 税局要求接口类
     */
    export namespace taxRuleService {
      /**
        * 导出
导出
        * /taxRule/export
        */
      export namespace exporting {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: any;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.BaseEntity,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.BaseEntity,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取导入批次信息
获取导入批次信息
        * /taxRule/getBatchInfo
        */
      export namespace getBatchInfo {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.BdTaxRuleQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.BdTaxRuleQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 三流合一规则-分公司规则
     */
    export namespace threeToOneRule {
      /**
        * 导出三流合一规则
导出三流合一规则
        * /threeToOne/branch/export
        */
      export namespace exportThreeToOneRule {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: any;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.ThreeToOneRuleQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.ThreeToOneRuleQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 社保公积金缴纳主体 记录对应分公司的城市，相同省下的所有有效的分公司下签约分公司抬头记录。
社保公积金缴纳主体 记录对应分公司的城市，相同省下的所有有效的分公司下签约分公司抬头记录。
        * /threeToOne/branch/getApplyTitleByPayAddress
        */
      export namespace getPolicyDropdownList {
        export class Params {
          /** payAddress */
          payAddress: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: Array<object>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 是否强制缴纳残障金（工资）和  是否强制缴纳工会费（工资）
是否强制缴纳残障金（工资）和  是否强制缴纳工会费（工资）  入参 type:1-是否强制缴纳残障金，2-是否强制缴纳工会费 返回值：0否1是
        * /threeToOne/branch/getDisabilityAndFee
        */
      export namespace getDisabilityAndFee {
        export class Params {
          /** salaryWithholdingId */
          salaryWithholdingId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.CommonResponse<defs.basedata2.ThreeToOneRuleVo>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 新增三流合一规则
新增三流合一规则
        * /threeToOne/branch/insert
        */
      export namespace insertThreeToOneRule {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.ThreeToOneRule,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.ThreeToOneRule,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据条件分页查询结果
根据条件分页查询结果
        * /threeToOne/branch/query
        */
      export namespace queryThreeToOneRule {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.ThreeToOneRuleQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.ThreeToOneRuleQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 修改三流合一规则
修改三流合一规则
        * /threeToOne/branch/update
        */
      export namespace updateThreeToOneRule {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.ThreeToOneRule,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.ThreeToOneRule,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 常用模板管理
     */
    export namespace usualTemplate {
      /**
       * 新增常用模板
       * /usualTemplates/add
       */
      export namespace addUsualTemplate {
        export class Params {}

        export type Response<T> = defs.basedata2.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.basedata2.UsualTemplate,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.UsualTemplate,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 删除
删除
        * /usualTemplates/delete
        */
      export namespace remove {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: boolean;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Array<defs.basedata2.UsualTemplate>,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Array<defs.basedata2.UsualTemplate>,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
       * 查询所有常用模板
       * /usualTemplates/getUsualTemplates
       */
      export namespace getUsualTemplates {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.basedata2.UsualTemplate;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.UsualTemplateQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.UsualTemplateQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
       * 修改常用模板
       * /usualTemplates/modify
       */
      export namespace modifyUsualTemplate {
        export class Params {}

        export type Response<T> = defs.basedata2.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.basedata2.UsualTemplate,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.UsualTemplate,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 扣缴义务人限额表
     */
    export namespace withholdAgentNorm {
      /**
        * 导出
导出
        * /withholdAgentNorm/downloadfile
        */
      export namespace toDownLoad {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: any;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.basedata2.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 新增扣缴义务人限额表
新增扣缴义务人限额表
        * /withholdAgentNorm/insert
        */
      export namespace add {
        export class Params {}

        export type Response<T> = defs.basedata2.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.basedata2.BaseEntity,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.BaseEntity,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询扣缴义务人限额表
查询扣缴义务人限额表
        * /withholdAgentNorm/list
        */
      export namespace getlist {
        export class Params {}

        export type Response<T> = defs.basedata2.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.basedata2.withholdAgentNormQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.withholdAgentNormQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 更新扣缴义务人限额表
更新扣缴义务人限额表
        * /withholdAgentNorm/update
        */
      export namespace update {
        export class Params {}

        export type Response<T> = defs.basedata2.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.basedata2.BaseEntity,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.basedata2.BaseEntity,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }
  }
}
