import React, { useState } from 'react';
import { WritableColumnProps } from '@/utils/writable/types';
import { CachedPage, CachedTableOptionsType } from '@/components/CachedPage';
import { EditeFormProps, FormInstance } from '@/components/EditeForm';
import { mapToSelectors, CommonBaseDataSelector } from '@/components/Selectors';
import { Button, Form, message } from 'antd';
import {
  statusList,
  yesNoDataProvider,
  areaTypeList,
  associationStatusList,
  pendingReasonList,
  calculateMethodList,
  customerSizeList,
  smsSendStatusList,
} from '@/utils/settings/emphiresep/emporder/QueryEmpOrderListForPer';
import { EmployeePop } from '@/components/StandardPop/EmployeePop';
import { InnerBranchPop, BranchPop } from '@/components/StandardPop/BranchPop';
import { msgOk, msgErr } from '@/utils/methods/message';
import CustomerDetail from '@/pages/Sales/CustomerManage/CustomerQuery/components/CustomerDetail.tsx';
import { Typography } from 'antd';
import { stdDateFormat } from '@/utils/methods/times';
import { column4 } from '@/utils/forms/typography';
import { DateRange } from '@/components/DateRange4';
import { AsyncButton } from '@/components/Forms/Confirm';
import AddOrderForm from './Forms/AddOrderForm';
import QueryEmpHirePopUp from './Forms/QueryEmpHirePopUp';
import dict from '@/locales/zh-CN/locale';
import { useWritable } from '@/components/Writable';
import { tableIndexKey } from '@/utils/settings/forms';
import { downloadFileWithAlert } from '@/utils/methods/file';
import PerfectSearchQuery from './Forms/PerfectSearchQuery';
import { generateReportURL } from '../QueryEmpOrderListForGen/Forms/BizUtil';
import { getCurrentUser, getCurrentMenu } from '@/utils/model';
import AddForm from '@/components/EditeForm/AddForm';
import { RadioItem } from '@/components/EditeForm/Item/CheckboxItem';
import { AddTransferAndSubcontract } from '@/pages/emphiresep/sendorder/CustomerSubcontract/AddTransferAndSubcontract';
import EditEmpHireFromApp from './Forms/EditEmpHireFromApp';
import { AuthButtons } from '@/components/Forms/RenderButtons';
import { isEmpty } from 'lodash';
import EmployeeFiles from '../QueryEmployeeOrder/Forms/EmployeeFiles';
import { CustPayEntityPop } from '@/components/StandardPop/CustPayEntityPop';
import EmpHireTemplate from '../../hire/QueryEmpHireFromApp/components/empHireTemplate';
import { yesornoList } from '@/utils/settings/emphiresep/emporder/queryClientOrderForAdd';
const { Link } = Typography;
export const FileTypeMaps: POJO = {
  '10': 'frontOfIDCard',
  '20': 'endOfIDCard',
  '25': 'identificationCard',
  '30': 'identificationPhoto',
  '40': 'firstPageOfHouseholdRegister',
  '50': 'ownPageOfHouseholdRegister',
  '60': 'changePageOfHouseholdRegister',
  '70': 'photoOfBankCard',
  '80': 'photoOfOtherDetails',
  '64': 'photoOfBusinessCard',
  '90': 'photoOfUnemploymentRegistration',
  '95': 'baseEndoInsCert_shaanxi',
};
const service = API.emphiresep.employeeHireSep.queryEmpOrderListForPer;
const serviceQueryEmpFeeHisList = API.emphiresep.employeeHireSep.queryEmpFeeHisList;
const serviceUpdateBatchPending = API.emphiresep.employeeHireSep.updateBatchPending;
const serviceUpdateEmpHireSepReject = API.emphiresep.employeeHireSep.updateEmpHireSepReject;
const serviceQuerySubcontractLiteList = API.emphiresep.subcontract.querySubcontractLiteList;
const serviceSetBatchConfirm = API.emphiresep.employeeHireSep.setBatchConfirm;
const serviceExportFile = API.emphiresep.employeeHireSep.exportFile;

const queryParams = [
  { propName: 'governingBranch', needAnd: false, aliasPropName: 'governingBranch' },
  { propName: 'governingBranch1', needAnd: true, aliasPropName: 'governingBranch1' },
  { propName: 'providerType', needAnd: true },
  { propName: 'addConfirmStatus', needAnd: true, aliasPropName: 'addConfirmStat' },
  { propName: 'idCardType', needAnd: true },
  { propName: 'idCardNum', needAnd: true },
  { propName: 'custCode', needAnd: true },
  { propName: 'custName', needAnd: true, needEncode: true },
  { propName: 'employeeCode', needAnd: true },
  { propName: 'employeeName', needAnd: true, needEncode: true },
  { propName: 'assignerProviderId', needAnd: true },
  { propName: 'assigneeProviderId', needAnd: true },
  { propName: 'assigneeCsId', needAnd: true },
  { propName: 'subcontractName', needAnd: true, needEncode: true },
  { propName: 'rptHireStartDtNoLine', needAnd: true, aliasPropName: 'rptHireStartDt' },
  { propName: 'rptHireEndDtNoLine', needAnd: true, aliasPropName: 'rptHireEndDt' },
  { propName: 'alterStatus', needAnd: true, aliasPropName: 'alterStat' },
  { propName: 'sepConfirmStatus', needAnd: true, aliasPropName: 'sepConfirmStat' },
  { propName: 'custId', needAnd: true },
  { propName: 'subcontractId', needAnd: true },
  { propName: 'subcontractCode', needAnd: true },
  { propName: 'chargeStartDateNoLine', needAnd: true, aliasPropName: 'chargeStartDate' },
  { propName: 'chargeEndDateNoLine', needAnd: true, aliasPropName: 'chargeEndDate' },
  { propName: 'typeClass', needAnd: true },
  { propName: 'productId', needAnd: true },
  { propName: 'userId', needAnd: true },
];

const transferKeys = ['pendingReason', 'confirmRemark'];
const formColumns: EditeFormProps[] = [
  { label: '客户编号', fieldName: 'custCode', inputRender: 'string' },
  { label: '客户名称', fieldName: 'custName', inputRender: 'string' },
  {
    label: '客户规模',
    fieldName: 'customerSize',
    inputRender: () => {
      return mapToSelectors(customerSizeList);
    },
  },
  { label: '唯一号', fieldName: 'employeeCode', inputRender: 'string' },
  { label: '雇员姓名', fieldName: 'employeeName', inputRender: 'string' },
  {
    label: '证件类型',
    fieldName: 'idCardType',
    inputRender: () => {
      return <CommonBaseDataSelector params={{ type: '045' }} />;
    },
  },
  { label: '证件号码', fieldName: 'idCardNum', inputRender: 'string' },
  { label: '小合同', fieldName: 'subcontractName', inputRender: 'string' },
  {
    label: '接单方客服',
    fieldName: 'assigneeCsId',
    inputRender: () => {
      return (
        <EmployeePop
          rowValue="assigneeCsId-assigneeCsName"
          keyMap={{
            assigneeCsId: 'EMPID',
            assigneeCsName: 'REALNAME',
          }}
        />
      );
    },
  },
  {
    label: '派单方',
    fieldName: 'assignerProviderId',
    inputRender: () => {
      return (
        <InnerBranchPop
          rowValue="assignerProviderId-assignerProviderName"
          keyMap={{
            assignerProviderId: 'departmentId',
            assignerProviderName: 'departmentName',
          }}
        />
      );
    },
  },
  {
    label: '接单方',
    fieldName: 'assigneeProviderId',
    inputRender: () => {
      return (
        <BranchPop
          rowValue="assigneeProviderId-assigneeProviderName"
          keyMap={{
            assigneeProviderId: 'departmentId',
            assigneeProviderName: 'departmentName',
          }}
        />
      );
    },
  },
  {
    label: '订单确认状态',
    fieldName: 'addConfirmStatus',
    inputRender: () => {
      return mapToSelectors(statusList);
    },
  },

  {
    label: '',
    fieldName: '',
    inputRender: () => (
      <DateRange
        fields={[
          {
            title: '订单创建时间(起)',
            dataIndex: 'rptHireStartDt',
          },
          {
            title: '订单创建时间(止)',
            dataIndex: 'rptHireEndDt',
          },
        ]}
        format={stdDateFormat}
      />
    ),
  },
  {
    label: '服务区域类型',
    fieldName: 'areaType',
    inputRender: () => {
      return mapToSelectors(areaTypeList);
    },
  },
  {
    label: '是否需入职呼叫',
    fieldName: 'isHireCall',
    inputRender: () => {
      return mapToSelectors(yesNoDataProvider);
    },
  },
  {
    label: '关联入职状态',
    fieldName: 'associationStatus',
    inputRender: () => {
      return mapToSelectors(associationStatusList);
    },
  },
  {
    label: '短信发送状态',
    fieldName: 'smsSendStatus',
    inputRender: () => {
      return mapToSelectors(smsSendStatusList);
    },
  },
  {
    label: '',
    fieldName: '',
    inputRender: () => (
      <DateRange
        fields={[
          {
            title: '短信发送日期起',
            dataIndex: 'smsSendStartDt',
          },
          {
            title: '短信发送日期止',
            dataIndex: 'smsSendEndDt',
          },
        ]}
        format={stdDateFormat}
      />
    ),
  },
  {
    label: '缴费实体',
    fieldName: 'custPayEntityId',
    inputRender: () => (
      <CustPayEntityPop
        rowValue="custPayEntityId-custPayEntityName"
        keyMap={{
          custPayEntityId: 'custPayEntityId',
          custPayEntityName: 'custPayEntityName',
        }}
      />
    ),
  },
  {
    label: '是否需要实做',
    fieldName: 'isAddProcess',
    inputRender: () => {
      return mapToSelectors(yesornoList);
    },
  },
];
const QueryEmpOrderListForPer: React.FC = () => {
  const [onShowPerfect, setOnShowPerfect] = useState(false);
  const [updateitem, setUpdateitem] = useState({});
  const [onShowAssociateOrder, setOnShowAssociateOrder] = useState(false);
  const [custDetailVisible, setCustDetailVisible] = useState(false);
  const [subcontractDetailVisible, setSubcontractDetailVisible] = useState(false);
  const [onShowBatchPerOrder, setOnShowBatchPerOrder] = useState(false);
  const [onShowEditEmpHire, setOnShowEditEmpHire] = useState(false);
  const [detailData, setDetailData] = useState<POJO>({});
  const [onShowEmployeeFiles, setOnShowEmployeeFiles] = useState<boolean>(false);
  const [disabledFields, setDisabledFields] = useState({
    billStartMonth: true,
  } as any);
  const [loading, setLoading] = useState(false);
  const [visibleHireTemplate, setVisibleHireTemplate] = useState(false);
  const [hireTemplate, setHireTemplate] = useState<POJO>({});
  const wriTable = useWritable({ service });
  const [form] = Form.useForm();
  const privateObject: any = getCurrentUser();
  const currentMenu: any = getCurrentMenu();
  const columns: WritableColumnProps<any>[] = [
    {
      title: '雇员姓名',
      dataIndex: 'employeeName',
      render: (text, record) => {
        return (
          <Link
            onClick={() => {
              setOnShowEmployeeFiles(!onShowEmployeeFiles);
              setDetailData({
                empId: record.empId,
              });
            }}
            style={record.hasAssociations > 0 ? { color: 'red' } : {}}
          >
            {text}
          </Link>
        );
      },
    },
    { title: '唯一号', dataIndex: 'employeeCode' },
    { title: '证件类型', dataIndex: 'idCardTypeName' },
    { title: '证件号码', dataIndex: 'idCardNum' },
    { title: '关联入职状态', dataIndex: 'associationStatus' },
    { title: '客户编号', dataIndex: 'custCode' },
    { title: '客户类型', dataIndex: 'custType' },
    {
      title: '客户名称',
      dataIndex: 'custName',
      render: (text, record) => {
        return <Link onClick={() => taggleShowCustDetail(record)}>{text}</Link>;
      },
    },
    { title: '客户规模', dataIndex: 'customerSize' },
    { title: '服务区域类型', dataIndex: 'areaTypeName' },
    { title: '派单类型', dataIndex: 'assignmentType' },
    {
      title: '小合同名称',
      dataIndex: 'subcontractName',
      render: (text, record) => {
        return <Link onClick={() => taggleShowSubcontractDetail(record)}>{text}</Link>;
      },
    },
    { title: '派遣单位名称', dataIndex: 'payerName' },
    { title: '统一社会信用码', dataIndex: 'taxpayerIdentifier' },
    { title: '员工类别', dataIndex: 'empTypeName' },
    { title: '大分类', dataIndex: 'superTypeName' },
    { title: '小分类', dataIndex: 'subTypeName' },
    { title: '申报入职时间', dataIndex: 'rptHireDt' },
    { title: '申报入职人', dataIndex: 'rptHireByName' },
    {
      title: '入职备注',
      dataIndex: 'hireRemark',
      //  render: (text) => <EllipsisGrid text={text} />
    },
    {
      title: '入职过程',
      dataIndex: 'addConfirmPro',
      // render: (text) => <EllipsisGrid text={text} />,
    },
    {
      title: '挂起原因',
      dataIndex: 'pendingReason',
      inputRender: () => {
        return mapToSelectors(pendingReasonList);
      },
    },
    { title: '填写备注', dataIndex: 'confirmRemark', inputRender: 'text' },
    { title: '是否需入职呼叫', dataIndex: 'isHireCallName' },
    { title: '短信发送状态', dataIndex: 'smsSendStatusStr' },
    { title: '短信发送日期', dataIndex: 'smsSendDt' },
    { title: '电话', dataIndex: 'contactTel1' },
    { title: '手机', dataIndex: 'contactTel2' },
    { title: '派单方', dataIndex: 'assignerProvider' },
    { title: '申报工资', dataIndex: 'decSalary' },
    { title: '派单方客服', dataIndex: 'assignerCs' },
    { title: '接单方', dataIndex: 'assigneeProvider' },
    { title: '缴费实体', dataIndex: 'custPayEntityName' },
    { title: '是否需要实做', dataIndex: 'isAddProcessName' },
    { title: '是否集中一地投保', dataIndex: 'isSameInsurName' },
    { title: '是否增强型代理', dataIndex: 'enhancedAgentName' },
    { title: '开户人姓名', dataIndex: 'accountEmployeeName' },
    { title: '银行名称', dataIndex: 'openBankName' },
    { title: '银行卡号', dataIndex: 'bankAcct' },
    { title: '银行卡更新时间', dataIndex: 'bankCardUpdateDt' },
    { title: '银行卡更新人', dataIndex: 'bankCardUpdateBy' },
  ];
  const formColumnsNotice: EditeFormProps[] = [
    {
      label: '账单起始月计算方式',
      fieldName: 'calculateMethod',
      inputRender: (outerForm: FormInstance) => {
        return mapToSelectors(calculateMethodList, {
          onChange: (val) => changeCalculateMethod(val, outerForm),
        });
      },
    },
    {
      label: '没有下一账期则填写账单起始月',
      fieldName: 'isInputNextBillMonth',
      inputRender: (outerForm: FormInstance) => {
        return (
          <RadioItem values={[false, true]} onChange={(val) => changeIsInput(val, outerForm)} />
        );
      },
    },
    {
      label: '账单起始月',
      fieldName: 'billStartMonth',
      inputRender: 'month',
    },
  ];
  const renderButtons = (_options: CachedTableOptionsType) => {
    const cantEdite = isEmpty(_options.selectedSingleRow);
    const cantEdite2 = _options.selectedRows.length === 0;
    return (
      <div>
        <Button type="primary" htmlType="submit">
          查询
        </Button>
        <AsyncButton disabled={cantEdite} onClick={perfect_clickHandler}>
          完善订单
        </AsyncButton>
        <AsyncButton disabled={cantEdite} onClick={associateOrder_clickHandler}>
          关联入职信息
        </AsyncButton>
        <AsyncButton disabled={cantEdite2} onClick={pending_clickHandler}>
          挂起
        </AsyncButton>
        <AuthButtons funcId="20201301">
          <AsyncButton disabled={cantEdite} onClick={reject_clickHandler}>
            驳回
          </AsyncButton>
        </AuthButtons>
        {/* <Button
          disabled={cantEdite || _options.selectedSingleRow?.associationStatus === '未关联'}
          onClick={() => {
            setOnShowEditEmpHire(!onShowEditEmpHire);
            setDetailData({
              empHiresepMainId: _options.selectedSingleRow?.empHiresepMainId,
            });
          }}
        >
          查看入职信息
        </Button> */}
        <Button
          loading={loading}
          disabled={cantEdite || _options.selectedSingleRow?.associationStatus === '未关联'}
          onClick={() => {
            handleHiresepMainData(_options.selectedSingleRow);
          }}
        >
          查看入职信息
        </Button>
        <AsyncButton onClick={exportExcel}>导出数据</AsyncButton>
        <AsyncButton disabled={cantEdite2} onClick={generateBatch_clickHandler}>
          批量员工完善订单
        </AsyncButton>
        <AsyncButton onClick={() => reportLink_clickHandler('EmpFeeByDate.jsp')}>
          生成普通横表
        </AsyncButton>
        <AsyncButton onClick={() => reportLink_clickHandler('EmpFeeByRowNum.jsp')}>
          生成紧凑横表
        </AsyncButton>
        <Button disabled={cantEdite2} onClick={similarSetting_clickHandler}>
          类似设定
        </Button>
        <AsyncButton disabled={cantEdite2} onClick={sendSms}>
          入职通知
        </AsyncButton>
      </div>
    );
  };

  // 入职通知
  const sendSms = async () => {
    const { selectedRows } = wriTable;
    if (selectedRows.length === 0) {
      return msgErr('请先选择数据');
    }
    const ids = selectedRows.map((i) => i.empHireSepId);
    await API.emphiresep.employeeHireSep.sendSms.requests(ids);
    msgOk('已处理');
  };

  const refresh = () => {
    form.validateFields().then((value: any) => {
      wriTable.request(value);
    });
  };

  // 完善订单
  const perfect_clickHandler = async () => {
    const { selectedSingleRow } = wriTable;
    const { nonSsGroupList, ssGroupList } = await serviceQueryEmpFeeHisList.requests(
      selectedSingleRow,
    );
    const { list: subcontract } = await serviceQuerySubcontractLiteList.requests({
      subcontractId: selectedSingleRow.subcontractId,
      pageNum: 1,
      pageSize: 1,
    });
    setOnShowPerfect(!onShowPerfect);
    setUpdateitem({
      ...selectedSingleRow,
      nonSsGroupList,
      ssGroupList,
      subcontract: subcontract && subcontract[0],
    });
  };

  const hidePerfectOrder = (bol?: boolean) => {
    setOnShowPerfect(!onShowPerfect);
    setUpdateitem({});
    if (bol) refresh();
  };

  // 关联入职信息
  const associateOrder_clickHandler = () => {
    const { selectedSingleRow } = wriTable;
    setOnShowAssociateOrder(!onShowAssociateOrder);
    setUpdateitem({ ...selectedSingleRow });
  };

  const hideAssociateOrder = (bol?: boolean) => {
    setOnShowAssociateOrder(!onShowAssociateOrder);
    setUpdateitem({});
    if (bol) refresh();
  };

  // 挂起
  const pending_clickHandler = async () => {
    const { selectedRows } = wriTable;
    const { all: allList } = wriTable.getList();
    const keys = selectedRows.map((item) => item[tableIndexKey]);
    const list: any[] = allList.filter((item) => keys.includes(item[tableIndexKey]));
    const newArr = [];
    for (let index = 0; index < list.length; index++) {
      const obj = list[index];
      if (!obj.pendingReason) {
        return msgErr('请选择挂起原因');
      } else if (obj.pendingReason == '8' && !obj.confirmRemark) {
        return msgErr(dict.plzInputInputRemark);
      } else {
        const newObj = {} as any;
        newObj.empHireSepId = obj.empHireSepId;
        newObj.addConfirmStatus = '22';
        newObj.type = 22;
        newObj.empId = obj.empId;
        newObj.idCardNum = obj.idCardNum;
        newObj.empAddId = obj.empAddId;
        newObj.pendingReason = obj.pendingReason;
        newObj.confirmRemark = obj.confirmRemark;
        newArr.push(newObj);
      }
    }
    await serviceUpdateBatchPending.requests(newArr);
    msgOk(dict.hintMsgOk);
    refresh();
  };

  const reject_clickHandler = async () => {
    const { selectedSingleRow } = wriTable;
    const confirmRemark = wriTable.getFieldValue(selectedSingleRow[tableIndexKey], 'confirmRemark');
    if (confirmRemark) {
      const obj = { ...selectedSingleRow, confirmRemark } as any;
      obj.addConfirmStatus = '1';
      obj.type = 1; //增员接单驳回
      await serviceUpdateEmpHireSepReject.requests(obj);
      msgOk(dict.hintMsgOk);
      refresh();
    } else {
      return msgErr(dict.plzInputInputRemark);
    }
  };

  // 批量员工完善订单
  const generateBatch_clickHandler = async () => {
    const { selectedRows } = wriTable;
    let isAddProcess = '';
    for (let index = 0; index < selectedRows.length; index++) {
      const obj = selectedRows[index];
      if (isAddProcess && isAddProcess != '' && isAddProcess != obj.isAddProcess) {
        return msgErr('请检查数据‘是否需要实做’的值必填相同');
      } else {
        isAddProcess = obj.isAddProcess;
      }
    }
    // const hasUnRelated = selectedRows.some(
    //   (item: any) => item.isRelated === '1' && item.associationStatus === '未关联',
    // );
    // if (hasUnRelated) {
    //   return msgErr('选择的订单中存在关联入职状态为"未关联"，请关联后再操作完善订单');
    // }
    const flag = selectedRows.every((item) => item.assigneeCsId !== item.assignerCsId);
    if (flag) {
      const newList = selectedRows.map((item) => {
        if (item.assigneeCsId == item.assignerCsId) {
          return { ...item, addConfirmStatus: '40' }; // 派单确认或者接单确认本地单增员完成
        } else {
          return { ...item, addConfirmStatus: '30' }; // 等待派单方确认
        }
      });
      await serviceSetBatchConfirm.requests({ employeeHireSepList: newList });
      msgOk('操作成功');
      refresh();
    } else {
      setOnShowBatchPerOrder(!onShowBatchPerOrder);
    }
  };

  const changeCalculateMethod = (calculateMethod: string, outerForm: FormInstance) => {
    if (calculateMethod == '1') {
      setDisabledFields({
        isInputNextBillMonth: false,
        billStartMonth: true,
      });
    } else {
      setDisabledFields({
        isInputNextBillMonth: true,
        billStartMonth: false,
      });
      outerForm.setFieldsValue({
        isInputNextBillMonth: false,
      });
    }
    outerForm.setFieldsValue({
      billStartMonth: null,
    });
  };

  const changeIsInput = (isInputNextBillMonth: boolean, outerForm: FormInstance) => {
    if (!isInputNextBillMonth) {
      outerForm.setFieldsValue({
        billStartMonth: null,
      });
      setDisabledFields({
        billStartMonth: true,
      });
    } else {
      setDisabledFields({
        billStartMonth: false,
      });
    }
  };

  const generateBatch_submit = async (data: any) => {
    const { calculateMethod, isInputNextBillMonth = false, billStartMonth = null } = data;
    const { selectedRows } = wriTable;
    if (
      (calculateMethod == '1' && isInputNextBillMonth && !billStartMonth) ||
      (calculateMethod == '2' && !billStartMonth)
    ) {
      return msgErr(dict.inputBillStartMonth);
    }
    const employeeHireSepList = selectedRows.map((item) => {
      const newItem = item;
      if (newItem.assigneeCsId == newItem.assignerCsId) {
        newItem.addConfirmStatus = '40'; // 派单确认或者接单确认本地单增员完成
      } else {
        newItem.addConfirmStatus = '30'; // 等待派单方确认
      }
      return { ...newItem };
    });
    await serviceSetBatchConfirm.requests({
      employeeHireSepList,
      calculateMethod,
      inputNextBillMonth: isInputNextBillMonth,
      billStartMonth,
    });
    msgOk(dict.hintMsgOk);
    setOnShowBatchPerOrder(!onShowBatchPerOrder);
    refresh();
  };

  // 生成普通横表
  const reportLink_clickHandler = async (jspName: string) => {
    const employeeHireSep = await form.validateFields();
    let tmpQueryMapping: any = new PerfectSearchQuery(employeeHireSep);
    tmpQueryMapping = initialQuery(tmpQueryMapping, jspName);
    const servURL: string = generateReportURL(tmpQueryMapping, queryParams, privateObject);
    window.open(servURL, '_blank');
  };

  const initialQuery = (rptQuery: any, jspName: string) => {
    rptQuery.baseRptURL = privateObject.reportDir; //报表系统入口地址
    rptQuery.modulePath = currentMenu.reportUrl + jspName; //模块路径
    rptQuery.userId = privateObject.profile.userId; //当前登录人Id
    //				rptQuery.userId1 = privateObject.user.userId; //当前登录人Id
    //								rptQuery.baseRptURL = "http://localhost:8080";  //报表系统入口地址
    //								rptQuery.modulePath = "/reporttest/qa/"+jspName; //模块路径
    if (privateObject.profile && privateObject.profile.governingBranch) {
      if (privateObject.profile.governingBranch == '0') {
        rptQuery.providerType = '2';
      } else {
        rptQuery.providerType = '1';
        rptQuery.governingBranch = privateObject.profile.governingBranch;
      }
    } else {
      rptQuery.providerType = '2';
    }

    if (!rptQuery.addConfirmStatus) {
      rptQuery.addConfirmStatus = '20|22';
    }

    if (!rptQuery.assigneeCsId) {
      rptQuery.assigneeCsId = privateObject.profile.userId;
    }
    return rptQuery;
  };

  // 类似设定
  const similarSetting_clickHandler = () => {
    wriTable.migrateClickToSelected(transferKeys);
  };

  // 导出
  const exportExcel = async () => {
    const condition = await form.validateFields();
    const fieldArr: any[] = [];
    const headStr: any[] = [];
    const typeArr: any[] = [];
    columns.forEach((column) => {
      headStr.push(column.title);
      if (column.dataIndex === 'pendingReason') {
        fieldArr.push('pendingReasonName');
        return;
      }
      fieldArr.push(column.dataIndex);
    });
    await serviceExportFile
      .request(
        {
          condition: { ...condition, type: 1 },
          fieldArr,
          headStr: String(headStr),
          typeArr,
        },
        { responseType: 'blob' },
      )
      .then((res) => {
        if (res) {
          downloadFileWithAlert(res, '完善个人订单.xlsx');
        }
      });
  };

  const taggleShowCustDetail = (record?: POJO) => {
    if (!custDetailVisible) {
      setDetailData(record);
    } else {
      setDetailData({});
    }
    setCustDetailVisible(!custDetailVisible);
  };

  const taggleShowSubcontractDetail = (record?: POJO) => {
    if (!subcontractDetailVisible) {
      API.emphiresep.subcontract.querySubcontractList
        .requests({ subcontractId: record.subcontractId, pageSize: 1, pageNum: 1 })
        .then((res) => {
          if (res.list && res.list[0]) setDetailData({ ...record, ...res.list[0] });
        });
    } else {
      setDetailData({});
    }
    setSubcontractDetailVisible(!custDetailVisible);
  };

  const handleHiresepMainData = async (record?: any) => {
    const { empHiresepMainId: mainId, empHireSepId, cityId } = record || {};
    const fetchHiresepMain = async () => {
      const mainData = await API.wechat.wechatHiresep.getUniqueHiresepMain.request({
        mainId,
        cityCode: cityId,
      });
      return mainData.data;
    };
    const fetchAllPhotos = async (data: any) => {
      const allPhotosData: any = await API.wechat.wechatPhoto.allPhotos.request({
        mainId,
        uuid: data?.uuid,
      });
      const imgData: any = {};
      allPhotosData.data?.map((item: any) => {
        const FileTypeName = FileTypeMaps[item.imageType];
        imgData[FileTypeName] = item.imageUrl;
      });
      return imgData;
    };
    const fetchSocialCreditCode = async (data: any) => {
      if (!data?.unisocialcode_beijing && cityId === '10740' && empHireSepId) {
        const socialCreditCode = await API.wechat.wechatSepcailCity.fetchSocialCreditInfo.request({
          empHiresepId: empHireSepId,
        });
        return socialCreditCode.data;
      } else {
        return {};
      }
    };
    fetchHiresepMain().then((res) => {
      Promise.all([fetchAllPhotos(res), fetchSocialCreditCode(res)])
        .then((res1) => {
          setLoading(false);
          setVisibleHireTemplate(true);
          setHireTemplate({ ...res, ...res1[0], ...res1[1] });
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };
  return (
    <div>
      <CachedPage
        service={service}
        formColumns={formColumns}
        columns={columns}
        renderButtons={renderButtons}
        wriTable={wriTable}
        form={form}
        editable
      />
      <AddOrderForm
        title="完善订单"
        visible={onShowPerfect}
        hideHandle={hidePerfectOrder}
        updateitem={updateitem}
      />
      <QueryEmpHirePopUp
        visible={onShowAssociateOrder}
        hideHandle={hideAssociateOrder}
        updateitem={updateitem}
      />
      <AddForm
        title="提示"
        visible={onShowBatchPerOrder}
        formColumns={formColumnsNotice}
        hideHandle={() => setOnShowBatchPerOrder(!onShowBatchPerOrder)}
        submitHandle={generateBatch_submit}
        disabledFields={disabledFields}
      />
      {/* 客户详情超链接 */}
      <CustomerDetail
        visible={custDetailVisible}
        hideHandle={taggleShowCustDetail}
        extraObj={{ custId: detailData.custId }}
        action="COMMON"
        source="COMMON_USE"
      />
      {/* 小合同详情 */}
      <AddTransferAndSubcontract
        title={'小合同查看'}
        currentState={'scView'}
        subContract={detailData}
        modal={[subcontractDetailVisible, setSubcontractDetailVisible]}
        onSuccess={taggleShowSubcontractDetail}
      />
      <EditEmpHireFromApp
        visible={onShowEditEmpHire}
        hideHandle={() => setOnShowEditEmpHire(!onShowEditEmpHire)}
        empHiresepMainId={detailData.empHiresepMainId}
        currentState="view"
      />
      {/* 雇员超链接 */}
      <EmployeeFiles
        visible={onShowEmployeeFiles}
        hideHandle={() => {
          setOnShowEmployeeFiles(!onShowEmployeeFiles);
          setDetailData({});
        }}
        noShowList
        employeeId={detailData.empId}
      />
      {/*新版入职查询*/}
      <EmpHireTemplate
        visible={visibleHireTemplate}
        isPreview={true}
        hireTemplate={{ ...hireTemplate }}
        onFinshed={() => {
          setVisibleHireTemplate(false);
        }}
      />
    </div>
  );
};

export default QueryEmpOrderListForPer;
