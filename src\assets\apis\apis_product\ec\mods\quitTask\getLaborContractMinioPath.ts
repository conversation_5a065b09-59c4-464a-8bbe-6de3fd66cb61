import * as defs from '../../baseClass';
import {
  fetch,
  RequestConfig,
  beforeFetch,
  afterFetch,
  checkResStatus,
  removeSpace,
} from '@/utils/request';

/**
     * @url /rhro-service-1.0/quitTask/getLaborContractMinioPath
     * @desc 电子业务根据上下岗下载材料
电子业务根据上下岗下载材料
     * hasForm: false
     * hasBody: true
     */

export class Params {}

export const init = new defs.ec.CommonResponse();
export const url = '/rhro-service-1.0/quitTask/getLaborContractMinioPath:POST';
export const initialUrl =
  '/rhro-service-1.0/quitTask/getLaborContractMinioPath';
export const cacheKey = '_quitTask_getLaborContractMinioPath_POST';
export async function request(
  data: defs.ec.EmployeeHireSep,
  options?: RequestConfig,
) {
  const reqUrl = `/rhro-service-1.0/quitTask/getLaborContractMinioPath`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          reolve(res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
export async function requests(
  data: defs.ec.EmployeeHireSep,
  options?: RequestConfig,
) {
  const reqUrl = `/rhro-service-1.0/quitTask/getLaborContractMinioPath`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          if (checkResStatus(res)) return reolve(res ? res.data : res);
          reject(res ? res.message : res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
