import * as defs from '../../baseClass';
import {
  fetch,
  RequestConfig,
  beforeFetch,
  afterFetch,
  checkResStatus,
  removeSpace,
} from '@/utils/request';

/**
     * @url /rhro-service-1.0/api/ciReceivable/TestHttpCiReceivableAgain
     * @desc 测试重连外部接口
查询商保账单模板列表
     * hasForm: false
     * hasBody: true
     */

export class Params {}

export const init = new defs.emphiresep.Page();
export const url =
  '/rhro-service-1.0/api/ciReceivable/TestHttpCiReceivableAgain:POST';
export const initialUrl =
  '/rhro-service-1.0/api/ciReceivable/TestHttpCiReceivableAgain';
export const cacheKey = '_api_ciReceivable_TestHttpCiReceivableAgain_POST';
export async function request(
  data: defs.emphiresep.SysCiLog,
  options?: RequestConfig,
) {
  const reqUrl = `/rhro-service-1.0/api/ciReceivable/TestHttpCiReceivableAgain`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          reolve(res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
export async function requests(
  data: defs.emphiresep.SysCiLog,
  options?: RequestConfig,
) {
  const reqUrl = `/rhro-service-1.0/api/ciReceivable/TestHttpCiReceivableAgain`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          if (checkResStatus(res)) return reolve(res ? res.data : res);
          reject(res ? res.message : res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
