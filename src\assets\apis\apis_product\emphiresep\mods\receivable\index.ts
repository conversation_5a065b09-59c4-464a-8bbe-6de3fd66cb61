/**
 * @description 账单模板管理
 */
import * as approveReceivableTemplate from './approveReceivableTemplate';
import * as backReceivableTemplate from './backReceivableTemplate';
import * as checkSameReceivableNameByDiffentId from './checkSameReceivableNameByDiffentId';
import * as commitWageOffSetFlow from './commitWageOffSetFlow';
import * as delReceivableTemplateInterList from './delReceivableTemplateInterList';
import * as deleteReceivableTemplate from './deleteReceivableTemplate';
import * as deleteReceivableTemplateApp from './deleteReceivableTemplateApp';
import * as doApproveTemplateDelFlow from './doApproveTemplateDelFlow';
import * as exportFile from './exportFile';
import * as exportImpHis from './exportImpHis';
import * as getBillQuotedBySubCount from './getBillQuotedBySubCount';
import * as getContractByCustWithState from './getContractByCustWithState';
import * as getImpDetail from './getImpDetail';
import * as getMaxBillLockingDay from './getMaxBillLockingDay';
import * as getProductDropdownListBySSGroup from './getProductDropdownListBySSGroup';
import * as getReceivableDropdownList from './getReceivableDropdownList';
import * as getReceivableFrequencyById from './getReceivableFrequencyById';
import * as getReceivableFrequencyDropdownList from './getReceivableFrequencyDropdownList';
import * as getReceivableTemplateById from './getReceivableTemplateById';
import * as getReceivableTemplateDropdownList from './getReceivableTemplateDropdownList';
import * as getReceivableTemplateInterList from './getReceivableTemplateInterList';
import * as getReceivableUpdateDropdownList from './getReceivableUpdateDropdownList';
import * as getTempltImpInfo from './getTempltImpInfo';
import * as insertAndUpdateOneTimeFrequency from './insertAndUpdateOneTimeFrequency';
import * as insertReceivableDisplay from './insertReceivableDisplay';
import * as insertReceivableFrequency from './insertReceivableFrequency';
import * as insertReceivablePrecision from './insertReceivablePrecision';
import * as insertReceivableTemplate from './insertReceivableTemplate';
import * as insertReceivableTemplateApprove from './insertReceivableTemplateApprove';
import * as modifyDisplayAndPrecision from './modifyDisplayAndPrecision';
import * as queryOneTimeFrequencyList from './queryOneTimeFrequencyList';
import * as queryReceivableDisplayList from './queryReceivableDisplayList';
import * as queryReceivableFrequencyList from './queryReceivableFrequencyList';
import * as queryReceivablePayerList from './queryReceivablePayerList';
import * as queryReceivablePrecisionAndDisplayList from './queryReceivablePrecisionAndDisplayList';
import * as queryReceivablePrecisionList from './queryReceivablePrecisionList';
import * as queryReceivableTemplateAppList from './queryReceivableTemplateAppList';
import * as queryReceivableTemplateApprovalList from './queryReceivableTemplateApprovalList';
import * as queryReceivableTemplateHisList from './queryReceivableTemplateHisList';
import * as queryReceivableTemplateList from './queryReceivableTemplateList';
import * as queryTemplateDelPage from './queryTemplateDelPage';
import * as saveReceivableTemplateInterList from './saveReceivableTemplateInterList';
import * as setReceivableTemplate from './setReceivableTemplate';
import * as terminalTemplateDelFlow from './terminalTemplateDelFlow';
import * as toolsDataByEntity from './toolsDataByEntity';
import * as updateReceivableDisplay from './updateReceivableDisplay';
import * as updateReceivableFrequency from './updateReceivableFrequency';
import * as updateReceivablePrecision from './updateReceivablePrecision';
import * as updateReceivableTemplate from './updateReceivableTemplate';
import * as updateReceivableTemplateApp from './updateReceivableTemplateApp';
import * as updateReceivableTemplateApprove from './updateReceivableTemplateApprove';
import * as updateReceivableTemplateRecreateProcess from './updateReceivableTemplateRecreateProcess';

export {
  approveReceivableTemplate,
  backReceivableTemplate,
  checkSameReceivableNameByDiffentId,
  commitWageOffSetFlow,
  delReceivableTemplateInterList,
  deleteReceivableTemplate,
  deleteReceivableTemplateApp,
  doApproveTemplateDelFlow,
  exportFile,
  exportImpHis,
  getBillQuotedBySubCount,
  getContractByCustWithState,
  getImpDetail,
  getMaxBillLockingDay,
  getProductDropdownListBySSGroup,
  getReceivableDropdownList,
  getReceivableFrequencyById,
  getReceivableFrequencyDropdownList,
  getReceivableTemplateById,
  getReceivableTemplateDropdownList,
  getReceivableTemplateInterList,
  getReceivableUpdateDropdownList,
  getTempltImpInfo,
  insertAndUpdateOneTimeFrequency,
  insertReceivableDisplay,
  insertReceivableFrequency,
  insertReceivablePrecision,
  insertReceivableTemplate,
  insertReceivableTemplateApprove,
  modifyDisplayAndPrecision,
  queryOneTimeFrequencyList,
  queryReceivableDisplayList,
  queryReceivableFrequencyList,
  queryReceivablePayerList,
  queryReceivablePrecisionAndDisplayList,
  queryReceivablePrecisionList,
  queryReceivableTemplateAppList,
  queryReceivableTemplateApprovalList,
  queryReceivableTemplateHisList,
  queryReceivableTemplateList,
  queryTemplateDelPage,
  saveReceivableTemplateInterList,
  setReceivableTemplate,
  terminalTemplateDelFlow,
  toolsDataByEntity,
  updateReceivableDisplay,
  updateReceivableFrequency,
  updateReceivablePrecision,
  updateReceivableTemplate,
  updateReceivableTemplateApp,
  updateReceivableTemplateApprove,
  updateReceivableTemplateRecreateProcess,
};
