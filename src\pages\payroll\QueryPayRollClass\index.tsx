import React, { useEffect, useState } from 'react';
import { Button, Form, Modal, Typography } from 'antd';
import { WritableColumnProps } from '@/utils/writable/types';
import { CachedPage } from '@/components/CachedPage';
import { EditeFormProps } from '@/components/EditeForm';
import { WritableInstance } from '@/components/Writable';
import { CustomerPop } from '@/components/StandardPop/CustomerPop';
import {
  GetApplyTitleByPayAddress,
  mapToSelectors,
  renderListToSelectors,
} from '@/components/Selectors';
import {
  createMethodList,
  defaultFreeTaxList,
  defaultTaxTypeList,
  getDataTypeList,
  isGetSsList,
  isOrNotMap,
  isValidStatusComboList,
  templateTypeList,
  witchMonthList,
} from '@/utils/settings/payroll/salaryItem';
import { SelectInnerEmpAmbPop } from '@/components/StandardPop/EmpAmbPop';
import { BizUtil } from '@/utils/settings/bizUtil';
import { msgErr, msgOk } from '@/utils/methods/message';
import dict from '@/locales/zh-CN/locale';
import { isEmpty } from '@/utils/methods/checker';
import { ChoosePublicPayRollPop } from '@/components/StandardPop/ChoosePublicPayRollPop';
import QuerySelectedSubcontract from './components/QuerySelectedSubcontract';
import QueryPayRollClassItemWin from './components/QueryPayRollClassItemWin';
import { AsyncButton } from '@/components/Forms/Confirm';
import styles from './index.less';
import { TablePage } from '@/utils/methods/pagenation';
import { Calculator } from '@/utils/methods/calculator';
import { PureContractPop } from '@/components/StandardPop/PureContractPop';
import {
  CommonBaseDataSelector,
  ReceivableTemplateComboListSelector,
} from '@/components/Selectors/BaseDataSelectors';

interface QueryPayRollClassProps {
  [props: string]: any;
}

const service = API.payroll.payRollClass.getPayRollClassListByCustId;
const allDropDownService = API.payroll.payRollClass.getAllDropDownList;
const vatrService = API.payroll.payRollClass.initData;
const baseDropService = API.basedata.baseDataCls.getDorpDownList;
const departService = API.admin.department.getDepartmentDropdownList;
const getAssigneeCsIdService = API.payroll.payRollClass.getAssigneeCsIdByContractIdInHsSubcontract;
const verifyService = API.payroll.payRollClass.verify;
const saveService = API.payroll.payRollClass.save;
const hasCalculateService = API.payroll.payRollClass.isHasCalculatedPayRollClass;
const delService = API.payroll.payRollClass.delPayRollClass;
const receivableTemplateComboListService =
  API.payroll.payRollClass.getReceivableTemplateComboListById;

const QueryPayRollClass: React.FC<QueryPayRollClassProps> = () => {
  const [custId, setCustId] = useState();
  const [applyTitle, setApplyTitle] = useState([]);
  const [defaultTaxRateList, setDefaultTaxRateList] = useState([]);
  const [receivableTemplateList, setReceivableTemplateList] = useState([]);
  const [contractComboList, setContractComboList] = useState<POJO[]>([]);
  const [payRollFormatList, setPayRollFormatList] = useState([]);
  const [departmentComboList, setDepartmentComboList] = useState([]);
  const [vatrList, setVatrList] = useState([]);
  const [createMethod, setCreateMethod] = useState('1');
  const [contractVisible, setContractVisible] = useState(false);
  const [salaryVisible, setSalaryVisible] = useState(false);
  const [signBranchTitleId, setSignBranchTitleId] = useState('');
  const [contractId, setContractId] = useState('');

  const [form] = Form.useForm();
  const [addForm] = Form.useForm();
  useEffect(() => {
    (async () => {
      const data = await vatrService.requests({});
      setVatrList(data.vatrList);
    })();
  }, []);
  useEffect(() => {
    (async () => {
      if (custId) {
        const result = await allDropDownService.requests({ custId });
        setDefaultTaxRateList(result['taxRateComboList']);
        // setReceivableTemplateList(result['receivableTemplateComboList']);
        setContractComboList(result['contractComboList']);
        setPayRollFormatList(result['payRollFormatList']);
        const data = await departService.requests({ departmentGrade: '3', providerType: '1' });
        setDepartmentComboList(data.list);
      }
    })();
  }, [custId]);

  useEffect(() => {
    (async () => {
      if (custId) {
        const formData = await form.validateFields();
        const listData = await opt.request(formData);
        if (listData?.list?.length > 0) {
          getApplyTitle();
        }
      }
    })();
  }, [contractComboList]);

  // useEffect(() => {
  //   receivableTemplateComboListService
  //     .requests({ custId, signBranchTitleId })
  //     .then((comboList) => setReceivableTemplateList(comboList?.receivableTemplateComboList || []));
  // }, [signBranchTitleId, custId]);

  let opt: WritableInstance;
  // 选择客户
  const handleCustConfirm = async (value: any) => {
    setCustId(value.custId);
    // const formData = await form.validateFields();
    // const listData = await opt.request(formData);
    // if (listData?.list?.length > 0) {
    //   getApplyTitle();
    // }
  };
  // 默认支付地抬头
  const getApplyTitle = async (payAddress?: string) => {
    const data = await baseDropService.requests({
      statementName: 'wgSendBatch.getApplyTitleByPayAddress',
      payAddress: payAddress || '',
    });
    setApplyTitle(data.list);
  };

  const formColumns: EditeFormProps[] = [
    {
      label: '客户',
      fieldName: 'custId',
      rules: [{ required: true, message: '请输入客户' }],
      inputRender() {
        return (
          <CustomerPop
            rowValue="custId-wageCalculateType-custName"
            handdleConfirm={handleCustConfirm}
            keyMap={{
              custName: 'custName',
              wageCalculateType: 'wageCalculateType',
              custId: 'custId',
            }}
            fixedValues={{ isWageQuery: '1' }}
            initPageInfo={{ pageSize: 20 }}
          ></CustomerPop>
        );
      },
    },
    {
      label: '薪资类别名称 ',
      fieldName: 'wageClassName',
      inputRender: 'string',
    },
    { label: '薪资类别编号', fieldName: 'wageClassId', inputRender: 'string' },
    {
      label: '是否有效',
      fieldName: 'isValid',
      inputRender: () => mapToSelectors(isValidStatusComboList),
    },
    {
      label: '是否删除',
      fieldName: 'isDeleted',
      inputRender: () => mapToSelectors(isOrNotMap),
    },
    {
      label: '创建人 ',
      fieldName: 'createByName',
      inputRender: 'string',
    },
  ];
  const addFormColumns: EditeFormProps[] = [
    {
      label: '生成方式 ',
      fieldName: 'createMethodNum',
      colNumber: 2,
      inputRender: () =>
        mapToSelectors(createMethodList, {
          onChange: (value) => setCreateMethod(value),
          allowClear: false,
        }),
    },

    {
      label: '薪资类别名称',
      fieldName: 'wageClassOtherId',
      colNumber: 2,
      hidden: createMethod === '1' || createMethod === '4',
      rules: [{ required: true, message: '请输入薪资类别名称' }],
      inputRender: () => (
        <ChoosePublicPayRollPop
          rowValue="wageClassOtherId-wageClassOtherName"
          keyMap={{ wageClassOtherId: 'wageClassId', wageClassOtherName: 'wageClassName' }}
          fixedValues={{ addWay: '3' }}
        />
      ),
    },
    {
      label: '生成方式 ',
      fieldName: 'templateType',
      rules: [{ required: true, message: '请选择生成方式' }],
      colNumber: 2,
      hidden: createMethod === '1' || createMethod === '3',
      inputRender: () => mapToSelectors(templateTypeList),
    },
  ];
  const columns: WritableColumnProps<any>[] = [
    { title: '薪资类别编号', dataIndex: 'wageClassId', inputProps: { disabled: true } },
    {
      title: '薪资类别名称 ',
      dataIndex: 'wageClassName',
      inputRender: 'string',
      rules: [{ required: true, message: '请输入薪资类别名称 ' }],
    },
    {
      title: '默认税率表',
      dataIndex: 'defaultTaxRate',
      inputRender: ({ text }) =>
        renderListToSelectors(defaultTaxRateList, ['key', 'shortName'], {
          value: text,
          allowClear: false,
        }),
      rules: [{ required: true, message: '请输入默认税率表' }],
    },
    {
      title: '是否拉社保数据',
      dataIndex: 'isGetSs',
      inputRender: ({ text }) => mapToSelectors(isGetSsList, { value: text, allowClear: false }),
    },
    {
      title: '拉哪月数据',
      dataIndex: 'witchMonth',
      inputRender: ({ text }) => mapToSelectors(witchMonthList, { value: text, allowClear: false }),
    },
    {
      title: '社保依据',
      dataIndex: 'getDataType',
      inputRender: ({ text }) =>
        mapToSelectors(getDataTypeList, { value: text, allowClear: false }),
    },
    {
      title: '工资单格式',
      dataIndex: 'payRollFormat',
      rules: [{ required: true, message: '请输入工资单格式' }],
      inputRender: ({ text }) =>
        renderListToSelectors(payRollFormatList, ['key', 'shortName'], {
          value: text || '1',
          allowClear: false,
        }),
    },
    {
      title: '默认免税政策',
      dataIndex: 'defaultFreeTax',
      inputRender: ({ text }) =>
        mapToSelectors(defaultFreeTaxList, { value: text, allowClear: false }),
    },
    {
      title: '小合同',
      dataIndex: 'subContractList',
      inputRender: () => (
        <Typography.Link onClick={() => setContractVisible(true)}>查看</Typography.Link>
      ),
    },
    {
      title: '纯代发默认接单客服',
      dataIndex: 'assigneeCsId',
      inputRender: () => (
        <SelectInnerEmpAmbPop
          modalTitle={'雇员查询'}
          rowValue="assigneeCsId-assigneeCs"
          keyMap={{ assigneeCsId: 'EMPID', assigneeCs: 'REALNAME' }}
          params={{ roleCode: BizUtil.ROLE_CS }}
          requestOnMounted
          fromQueryBranch
          noClear
          rowValueOnly
          handdlePopConfirm={handleAssigneeCsChange}
        />
      ),
      rules: [{ required: true, message: '请输入纯代发默认接单客服' }],
    },
    {
      title: '纯代发派单客服',
      dataIndex: 'assignerCsId',
      rules: [{ required: true, message: '请输入纯代发派单客服' }],
      inputRender: (options) => {
        return (
          <SelectInnerEmpAmbPop
            modalTitle={'雇员查询'}
            rowValue="assignerCsId-assignerCs"
            keyMap={{ assignerCsId: 'EMPID', assignerCs: 'REALNAME' }}
            params={{ roleCode: BizUtil.ROLE_CS, branchId: options.record.deptId }}
            requestOnMounted
            fromQueryBranch
            noClear
            rowValueOnly
            handdlePopConfirm={handleAssignerCsChange}
          />
        );
      },
    },
    {
      title: '是否有效',
      dataIndex: 'isValidName',
    },
    {
      title: '是否删除',
      dataIndex: 'isDeletedName',
    },
    {
      title: '纯代发小合同对应大合同',
      dataIndex: 'contractId',
      // inputRender: ({ text }) =>
      //   renderListToSelectors(contractComboList, ['key', 'shortName'], {
      //     value: text,
      //     allowClear: false,
      //     onChange: handleContractChange,
      //   }),
      rules: [{ required: true, message: '请选择纯代发小合同对应大合同' }],
      inputRender: ({ record, text }) => {
        return (
          <PureContractPop
            modalTitle={'合同'}
            rowValue="contractId-contractName"
            keyMap={{ contractId: 'key', contractName: 'shortName' }}
            noClear
            initData={{
              contractId: text,
              contractName: contractComboList.find((item) => item.key == text)?.shortName,
            }}
            handdlePopConfirm={handleContractChange}
            staticData={contractComboList}
          />
        );
      },
    },
    {
      title: '纯代发小合同对应签单方抬头',
      dataIndex: 'signBranchTitle',
      inputProps: { disabled: true },
    },
    {
      title: '薪资项目',
      dataIndex: 'salaryItem',
      inputRender: () => (
        <Typography.Link onClick={() => setSalaryVisible(true)}>查看</Typography.Link>
      ),
    },

    {
      title: '账单模板名称',
      dataIndex: 'defaultTemplateId',
      // inputRender: ({ text }) =>
      //   renderListToSelectors(receivableTemplateList, ['key', 'shortName'], {
      //     value: text,
      //     allowClear: false,
      //   }),
      inputRender: ({ record, text }) => (
        <ReceivableTemplateComboListSelector
          params={{
            custId: record.custId || custId,
            signBranchTitleId: record.signBranchTitleId,
          }}
          allowClear={false}
          keyMap={{
            wageTemplate: 'key',
            wageTemplateName: 'shortName',
          }}
        />
      ),
      rules: [{ required: true, message: '请输入账单模板名称' }],
    },
    {
      title: '薪资计算方式',
      dataIndex: 'wageCalculateType',
      inputRender: ({ record, text }) => <CommonBaseDataSelector params={{ type: '9431' }} />,
      rules: [{ required: true, message: '请选择薪资计算方式' }],
    },
    {
      title: '创建人',
      dataIndex: 'createByName',
    },
    {
      title: '创建时间',
      dataIndex: 'createDt',
    },
    {
      title: '更新人',
      dataIndex: 'updateByName',
    },
    {
      title: '更新时间',
      dataIndex: 'updateDt',
    },
    {
      title: '增值税率%默认值',
      dataIndex: 'vatr',
      rules: [{ required: true, message: '请输入增值税率%默认值' }],
      inputRender: ({ text }) =>
        renderListToSelectors(vatrList, ['key', 'shortName'], {
          value: text,
          disabledKey: ['0'],
          allowClear: false,
        }),
    },
    {
      title: '附加税率%默认值',
      dataIndex: 'atr',
      // inputRender: ({ text = '0' }) => (
      //   <InputNumber
      //     defaultValue={Number(text) || 0}
      //     min={0}
      //     max={1}
      //     formatter={(value) => `${value}%`}
      //     parser={(value) => Number(value?.replace('%', '') || '0')}
      //   />
      // ),
      // inputRender: ({ text = 0 }) => (
      //   <span>
      //     <Input style={{ width: '50%' }} value={text * 100} />%
      //   </span>
      // ),
      inputRender: 'string',
      inputProps: { addonAfter: '%', validateFirst: true },
      rules: [
        { required: true, message: '' },
        // { type: 'number', max: 1, min: 0, message: '附加税率%默认值格式错误或超出限制' },
        {
          validator: (_, value) => {
            if (!value && value != 0) {
              return Promise.reject('请输入附加税率%默认值');
            }
            if (value >= 0 && value <= 1) {
              return Promise.resolve();
            } else {
              return Promise.reject('附加税率%默认值格式错误');
            }
          },
        },
      ],
    },
    {
      title: '默认计税方式',
      dataIndex: 'taxType',
      inputRender: ({ text }) =>
        mapToSelectors(defaultTaxTypeList, { value: text, allowClear: false }),
      rules: [{ required: true, message: '请输入默认计税方式' }],
    },
    {
      title: '默认支付地',
      dataIndex: 'payAddress',
      inputRender: ({ text, serial }) =>
        renderListToSelectors(departmentComboList, ['key', 'shortName'], {
          value: text,
          onChange: (value) => {
            opt.setFieldsValue(serial, { deptTitleId: '', payAddress: value });
            // setPayAddress(value);
          },
          allowClear: false,
        }),
    },
    {
      title: '默认支付地抬头',
      dataIndex: 'deptTitleId',
      // inputRender: ({ text }) =>
      //   renderListToSelectors(applyTitle, ['key', 'shortName'], { value: text }),
      inputRender: ({ record, text }) => (
        <GetApplyTitleByPayAddress
          params={{ payAddress: record.payAddress || '' }}
          key={record.payAddress}
          allowClear={false}
        />
      ),
    },
  ];

  const handleAssignerCsChange = (item: POJO) => {
    opt.setFieldsValue(opt.selectedSingleRow.aska, {
      assignerCsId: item.EMPID,
      assignerCs: item.REALNAME,
    });
  };

  const handleAssigneeCsChange = (item: POJO) => {
    opt.setFieldsValue(opt.selectedSingleRow.aska, {
      assigneeCsId: item.EMPID,
      assigneeCs: item.REALNAME,
    });
  };

  // 纯代发小合同对应大合同change
  const handleContractChange = async (item: POJO) => {
    const pClass: POJO = {};
    pClass.contractId = item.key;
    setContractId(item.key);
    const curRow: POJO = { contractId: item.key, contractName: item.shortName };
    curRow.signBranchTitle = item.reserveName1;
    curRow.signBranchTitleId = item.reserveObj;

    const titleObj: POJO = {};
    titleObj.custId = opt.selectedSingleRow.custId;
    titleObj.signBranchTitleId = item.reserveObj;
    setSignBranchTitleId(item.reserveObj);

    const comboList = await receivableTemplateComboListService.requests(titleObj);
    setReceivableTemplateList(comboList?.receivableTemplateComboList || []);

    const result: POJO = await getAssigneeCsIdService.requests(pClass);
    if (result) {
      curRow.assigneeCsId = result.assigneeCsId;
      curRow.assigneeCs = result.assigneeCs;
      curRow.assignerCsId = result.assigneeCsId;
      curRow.assignerCs = result.assigneeCs;
      curRow.deptId = result.deptId;
      curRow.assigneeCsDeptId = result.deptId;
      curRow.assignerCsDeptId = result.deptId;
    } else {
      curRow.assigneeCsId = '';
      curRow.assigneeCs = '';
      curRow.assignerCsId = '';
      curRow.assignerCs = '';
      curRow.deptId = '';
      curRow.assigneeCsDeptId = '';
      curRow.assignerCsDeptId = '';
    }
    curRow.defaultTemplateId = undefined;
    // console.log(1111, curRow);
    opt.setFieldsValue(opt.selectedSingleRow.aska, curRow);
  };

  // 添加
  const handleAdd = async (options: WritableInstance) => {
    // options.addRows({});
    const formData = await form.validateFields();
    const prClass: POJO = {};
    prClass.custId = formData.custId;
    prClass.wageCalculateType = formData.wageCalculateType;
    prClass.isGetSs = '0';
    prClass.witchMonth = '-1';
    prClass.getDataType = '0';
    prClass.payRollFormat = '1';

    options.addRows(prClass);

    // 移除默认 RHRO-3281
    // if (contractComboList.length > 0) {
    //   prClass.contractId = contractComboList[0].key;
    //   prClass.contractName = contractComboList[0].shortName;
    //   prClass.signBranchTitle = contractComboList[0].reserveName1;
    //   prClass.signBranchTitleId = contractComboList[0].reserveObj;

    //   const result = await getAssigneeCsIdService.requests({
    //     contractId: contractComboList[0].key,
    //   });
    //   if (!isEmpty(result)) {
    //     prClass.assigneeCsId = result.assigneeCsId;
    //     prClass.assigneeCs = result.assigneeCs;
    //     prClass.assignerCsId = result.assigneeCsId;
    //     prClass.assignerCs = result.assigneeCs;
    //     prClass.deptId = result.deptId;
    //     prClass.assigneeCsDeptId = result.deptId;
    //     prClass.assignerCsDeptId = result.deptId;
    //   }
    //   options.addRows(prClass);
    // } else {
    //   msgErr(dict.thisClientNotContract);
    // }
  };
  // 保存
  const handleSave = async (options: WritableInstance) => {
    const formData = await form.validateFields();
    const addFormData = await addForm.validateFields();
    const tableData = await options.validateFields();
    const { updated, added } = tableData;
    for (let i = 0; i < added.length; i++) {
      const pRollClass = added[i];
      if (pRollClass.payAddress && !pRollClass.deptTitleId) {
        msgErr('请填写【默认支付地抬头】');
        return;
      }
      pRollClass.custId = formData.custId;
      pRollClass.custName = formData.custName;
      // pRollClass.createWay = addFormData.createMethodNum;
      pRollClass.wageClassOtherId = addFormData.wageClassOtherId;
      pRollClass.atr = Calculator.divide(pRollClass.atr, 100);
    }
    for (let i = 0; i < updated.length; i++) {
      const pRollClass = updated[i];
      if (pRollClass.payAddress && !pRollClass.deptTitleId) {
        msgErr('请填写【默认支付地抬头】');
        return;
      }
      pRollClass.custId = formData.custId;
      pRollClass.custName = formData.custName;
      // updated[i].createWay = addFormData.createMethodNum;
      pRollClass.wageClassOtherId = addFormData.wageClassOtherId;
      pRollClass.atr = Calculator.divide(pRollClass.atr, 100);
    }

    // 验证
    const verifyRes = await verifyService.requests({ insertList: added, updateList: updated });
    if (verifyRes) {
      msgErr(verifyRes);
      return;
    }
    // 保存
    await saveService.requests({
      insertList: added,
      updateList: updated,
      createWay: addFormData.createMethodNum,
      templateType: addFormData.templateType,
      // ids: addFormData.createMethodNum,
    });
    msgOk(dict.saveSucc);
    options.request(formData);
    // options.handleSave();
  };
  // 删除
  const handleDel = (options: WritableInstance) => {
    Modal.confirm({
      content: dict.isConfirmDel,
      onOk: async () => {
        const ids = options.selectedSingleRow.wageClassId;
        if (!ids) {
          msgOk(dict.recordHaveDeleted);
          options.request(options.queries);
          return;
        }
        const checkRes = await hasCalculateService.requests({ ids });
        if (checkRes == true) {
          msgErr(dict.countYet);
        } else {
          await delService.requests(
            {
              ids,
            },
            { params: { ids } },
          );
          msgOk(dict.recordHaveDeleted);
          options.request(options.queries);
        }
      },
    });
  };
  const renderButtons = (options: WritableInstance) => {
    opt = options;
    return (
      <>
        <Button type="primary" htmlType="submit">
          查询
        </Button>
        <Button onClick={() => handleAdd(options)}>添加</Button>
        <AsyncButton
          onClick={() => handleSave(options)}
          disabled={isEmpty(options.selectedSingleRow)}
        >
          保存
        </AsyncButton>
        <Button onClick={() => handleDel(options)} disabled={isEmpty(options.selectedSingleRow)}>
          删除
        </Button>
        <Button
          onClick={async () => {
            await API.payroll.payRollClass.setPayRollClassValidStatus.requests(
              {},
              {
                params: { ids: options.selectedSingleRow?.wageClassId },
              },
            );
            options.request();
          }}
          disabled={
            !(options.selectedSingleRow?.isDeleted == 0 && options.selectedSingleRow?.isValid == 0)
          }
        >
          设为有效
        </Button>
        <Button
          onClick={() =>
            Modal.confirm({
              content: '将把有效的薪资类别设置为无效，是否继续',
              onOk: async () => {
                await API.payroll.payRollClass.setPayRollClassInValidStatus.requests(
                  {},
                  {
                    params: { ids: options.selectedSingleRow?.wageClassId },
                  },
                );
                options.request();
              },
            })
          }
          disabled={
            !(options.selectedSingleRow?.isDeleted == 0 && options.selectedSingleRow?.isValid == 1)
          }
        >
          设为失效
        </Button>
        {/* 大小和同 */}
        <QuerySelectedSubcontract
          modal={[contractVisible, setContractVisible]}
          detail={options.selectedSingleRow}
          listOpt={options}
        />
        {/* 薪资类别 */}
        <QueryPayRollClassItemWin
          modal={[salaryVisible, setSalaryVisible]}
          detail={options.selectedSingleRow}
          listOpt={options}
        />
        {/* 生成方式 */}
        <div className={styles.addForm}>
          <CachedPage
            service={service}
            formColumns={addFormColumns}
            columns={[]}
            initialValues={{ createMethodNum: '1' }}
            form={addForm}
          />
        </div>
      </>
    );
  };

  const afterRequest = (data: TablePage<any>) => {
    const { list, pagination } = data;
    list.forEach((item) => {
      // 接口没有返回contractName, 需要遍历去查找
      const contractName = contractComboList.find(
        (contract) => contract.key == item.contractId,
      )?.shortName;
      item.contractName = contractName;
      if (item.atr) {
        item.atr = Calculator.multiply(item.atr, 100);
      }
    });
    return { list, pagination };
  };

  return (
    <>
      <CachedPage
        service={service}
        formColumns={formColumns}
        columns={columns}
        renderButtons={renderButtons}
        initialValues={{ isValid: '1', isDeleted: '0' }}
        editable
        form={form}
        notShowRowSelection
        className={styles.payrollClass}
        afterRequest={afterRequest}
        notShowPagination
        forceEnterVirtualWithEditable
        showLineSize={20}
      />
    </>
  );
};

export default QueryPayRollClass;
