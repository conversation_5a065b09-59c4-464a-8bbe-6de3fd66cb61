import * as defs from '../../baseClass';
import {
  fetch,
  RequestConfig,
  beforeFetch,
  afterFetch,
  checkResStatus,
  removeSpace,
} from '@/utils/request';

/**
     * @url /rhro-service-1.0/rpac/certificate/download
     * @desc 下载（单个或批量）并压缩成zip
下载（单个或批量）并压缩成zip
     * hasForm: false
     * hasBody: true
     */

export class Params {
  /** certificateIdList */
  certificateIdList: Array<number>;
  /** certificateType */
  certificateType: number;
}

export const init = '';
export const url = '/rhro-service-1.0/rpac/certificate/download:POST';
export const initialUrl = '/rhro-service-1.0/rpac/certificate/download';
export const cacheKey = '_rpac_certificate_download_POST';
export async function request(data: Params, options?: RequestConfig) {
  const reqUrl = `/rhro-service-1.0/rpac/certificate/download`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          reolve(res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
export async function requests(data: Params, options?: RequestConfig) {
  const reqUrl = `/rhro-service-1.0/rpac/certificate/download`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          if (checkResStatus(res)) return reolve(res ? res.data : res);
          reject(res ? res.message : res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
