import * as defs from '../../baseClass';
import {
  fetch,
  RequestConfig,
  beforeFetch,
  afterFetch,
  checkResStatus,
  removeSpace,
} from '@/utils/request';

/**
     * @url /rhro-service-1.0/rpac/certificate/cust/approve
     * @desc 下载审批申请
下载审批申请
     * hasForm: false
     * hasBody: true
     */

export class Params {}

export const init = '';
export const url = '/rhro-service-1.0/rpac/certificate/cust/approve:POST';
export const initialUrl = '/rhro-service-1.0/rpac/certificate/cust/approve';
export const cacheKey = '_rpac_certificate_cust_approve_POST';
export async function request(
  data: defs.welfaremanage.RpaSsCustCertificate,
  options?: RequestConfig,
) {
  const reqUrl = `/rhro-service-1.0/rpac/certificate/cust/approve`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          reolve(res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
export async function requests(
  data: defs.welfaremanage.RpaSsCustCertificate,
  options?: RequestConfig,
) {
  const reqUrl = `/rhro-service-1.0/rpac/certificate/cust/approve`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          if (checkResStatus(res)) return reolve(res ? res.data : res);
          reject(res ? res.message : res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
