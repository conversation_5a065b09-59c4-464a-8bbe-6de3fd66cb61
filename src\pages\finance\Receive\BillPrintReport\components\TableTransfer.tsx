import React, { useState } from 'react';
import { Transfer, Table } from 'antd';
import difference from 'lodash/difference';

const columns = [
  {
    dataIndex: 'receivableTempltId',
    title: '账单模板账号',
  },
  {
    dataIndex: 'receivableTempltName',
    title: '账单模板名称',
  },
];

interface IP {
  data: POSO[];
  handleChange: (target: any) => void;
  handlefilteredItemsData: (items: any) => void;
}

// Customize Table Transfer
const TableTransfer: React.FC<IP> = ({ data, handleChange, handlefilteredItemsData }) => {
  const [target, setTarget] = useState([]);
  data.forEach((item) => (item.key = item.receivableTempltId));
  return (
    <Transfer
      style={{ padding: 20, background: '#fff' }}
      showSelectAll={false}
      showSearch={true}
      dataSource={data}
      onChange={(target: any) => {
        setTarget(target);
        handleChange(target);
      }}
      targetKeys={target}
      operations={['选择', '删除']}
      filterOption={(inputValue, item) =>
        item.receivableTempltId.indexOf(inputValue) !== -1 ||
        item.receivableTempltName.indexOf(inputValue) !== -1
      }
    >
      {({ filteredItems, onItemSelectAll, onItemSelect, selectedKeys: listSelectedKeys }) => {
        handlefilteredItemsData(filteredItems);
        const rowSelection = {
          onSelectAll(selected, selectedRows) {
            const treeSelectedKeys = selectedRows
              .filter((item) => !item.disabled)
              .map(({ key }) => key);
            const diffKeys = selected
              ? difference(treeSelectedKeys, listSelectedKeys)
              : difference(listSelectedKeys, treeSelectedKeys);
            onItemSelectAll(diffKeys, selected);
          },
          onSelect({ key }, selected) {

            console.log('key', selected);
            onItemSelect(key, selected);
          },
          selectedRowKeys: listSelectedKeys,
        };

        return (
          <Table
            rowSelection={rowSelection}
            columns={columns}
            dataSource={filteredItems}
            size="small"
            onRow={({ key, disabled: itemDisabled }) => ({
              onClick: () => {

                if (itemDisabled) return;
                onItemSelect(key, !listSelectedKeys.includes(key));
              },
            })}
          />
        );
      }}
    </Transfer>
  );
};

export default TableTransfer;
