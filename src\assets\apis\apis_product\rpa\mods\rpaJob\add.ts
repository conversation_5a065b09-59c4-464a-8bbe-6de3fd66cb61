import * as defs from '../../baseClass';
import {
  fetch,
  RequestConfig,
  beforeFetch,
  afterFetch,
  checkResStatus,
  removeSpace,
} from '@/utils/request';

/**
     * @url /rhro-service-1.0/rpa/job/add
     * @desc 新增Rpa定时任务
新增Rpa定时任务
     * hasForm: false
     * hasBody: true
     */

export class Params {}

export const init = new defs.rpa.BaseEntity();
export const url = '/rhro-service-1.0/rpa/job/add:POST';
export const initialUrl = '/rhro-service-1.0/rpa/job/add';
export const cacheKey = '_rpa_job_add_POST';
export async function request(
  data: Array<defs.rpa.BaseEntity>,
  options?: RequestConfig,
) {
  const reqUrl = `/rhro-service-1.0/rpa/job/add`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          reolve(res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
export async function requests(
  data: Array<defs.rpa.BaseEntity>,
  options?: RequestConfig,
) {
  const reqUrl = `/rhro-service-1.0/rpa/job/add`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          if (checkResStatus(res)) return reolve(res ? res.data : res);
          reject(res ? res.message : res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
