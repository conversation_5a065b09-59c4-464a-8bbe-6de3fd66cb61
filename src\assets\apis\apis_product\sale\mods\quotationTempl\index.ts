/**
 * @description 报价单模板
 */
import * as del from './del';
import * as delQuotationTemplTrdCost from './delQuotationTemplTrdCost';
import * as delQuotationTemplateAreas from './delQuotationTemplateAreas';
import * as delQuotationTemplateInfect from './delQuotationTemplateInfect';
import * as delSocialSecurityTempl from './delSocialSecurityTempl';
import * as getAllDropDownList from './getAllDropDownList';
import * as getBatchInfo from './getBatchInfo';
import * as getCustomer from './getCustomer';
import * as getOuotationTemplateById from './getOuotationTemplateById';
import * as getProductTypeDropDownList from './getProductTypeDropDownList';
import * as getProviderDropDownList from './getProviderDropDownList';
import * as getQuoTemplSsecrityList from './getQuoTemplSsecrityList';
import * as getQuoTemplSsecrityListWithCustId from './getQuoTemplSsecrityListWithCustId';
import * as getQuoTemplateInsuranceDataList from './getQuoTemplateInsuranceDataList';
import * as getQuotationTemplCFG from './getQuotationTemplCFG';
import * as getQuotationTemplInsuranceTypeList from './getQuotationTemplInsuranceTypeList';
import * as getQuotationTemplItem from './getQuotationTemplItem';
import * as getQuotationTemplItemList from './getQuotationTemplItemList';
import * as getQuotationTemplTrdCost from './getQuotationTemplTrdCost';
import * as getQuotationTemplate from './getQuotationTemplate';
import * as getQuotationTemplate1700ById from './getQuotationTemplate1700ById';
import * as getQuotationTemplateAreas from './getQuotationTemplateAreas';
import * as getQuotationTemplateById from './getQuotationTemplateById';
import * as getQuotationTemplateInfect from './getQuotationTemplateInfect';
import * as getSocialSecurityTemplProduct from './getSocialSecurityTemplProduct';
import * as insert from './insert';
import * as insertQuotationTempl from './insertQuotationTempl';
import * as insertQuotationTempl1700 from './insertQuotationTempl1700';
import * as invalidQuotationTempl from './invalidQuotationTempl';
import * as publishQuotationTempl from './publishQuotationTempl';
import * as saveQuotationProductLine from './saveQuotationProductLine';
import * as saveQuotationTemplTrdCost from './saveQuotationTemplTrdCost';
import * as saveQuotationTemplateAreas from './saveQuotationTemplateAreas';
import * as saveQuotationTemplateCFG from './saveQuotationTemplateCFG';
import * as saveQuotationTemplateInfect from './saveQuotationTemplateInfect';
import * as saveQuotationTemplateInsuranceType from './saveQuotationTemplateInsuranceType';
import * as saveQuotationTemplateItem from './saveQuotationTemplateItem';
import * as saveSsecurityTempl from './saveSsecurityTempl';
import * as update from './update';
import * as updateQuotationTempl from './updateQuotationTempl';
import * as updateQuotationTempl1700 from './updateQuotationTempl1700';

export {
  del,
  delQuotationTemplTrdCost,
  delQuotationTemplateAreas,
  delQuotationTemplateInfect,
  delSocialSecurityTempl,
  getAllDropDownList,
  getBatchInfo,
  getCustomer,
  getOuotationTemplateById,
  getProductTypeDropDownList,
  getProviderDropDownList,
  getQuoTemplSsecrityList,
  getQuoTemplSsecrityListWithCustId,
  getQuoTemplateInsuranceDataList,
  getQuotationTemplCFG,
  getQuotationTemplInsuranceTypeList,
  getQuotationTemplItem,
  getQuotationTemplItemList,
  getQuotationTemplTrdCost,
  getQuotationTemplate,
  getQuotationTemplate1700ById,
  getQuotationTemplateAreas,
  getQuotationTemplateById,
  getQuotationTemplateInfect,
  getSocialSecurityTemplProduct,
  insert,
  insertQuotationTempl,
  insertQuotationTempl1700,
  invalidQuotationTempl,
  publishQuotationTempl,
  saveQuotationProductLine,
  saveQuotationTemplTrdCost,
  saveQuotationTemplateAreas,
  saveQuotationTemplateCFG,
  saveQuotationTemplateInfect,
  saveQuotationTemplateInsuranceType,
  saveQuotationTemplateItem,
  saveSsecurityTempl,
  update,
  updateQuotationTempl,
  updateQuotationTempl1700,
};
