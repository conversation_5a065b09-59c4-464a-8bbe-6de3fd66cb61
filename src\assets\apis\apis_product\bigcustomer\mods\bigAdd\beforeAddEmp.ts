import * as defs from '../../baseClass';
import {
  fetch,
  RequestConfig,
  beforeFetch,
  afterFetch,
  checkResStatus,
  removeSpace,
} from '@/utils/request';

/**
     * @url /rhro-service-1.0/bigAdd/beforeAddEmp
     * @desc 手动提交HRO增员
手动提交HRO增员
     * hasForm: false
     * hasBody: true
     */

export class Params {}

export const init = [];
export const url = '/rhro-service-1.0/bigAdd/beforeAddEmp:POST';
export const initialUrl = '/rhro-service-1.0/bigAdd/beforeAddEmp';
export const cacheKey = '_bigAdd_beforeAddEmp_POST';
export async function request(data: Array<number>, options?: RequestConfig) {
  const reqUrl = `/rhro-service-1.0/bigAdd/beforeAddEmp`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          reolve(res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
export async function requests(data: Array<number>, options?: RequestConfig) {
  const reqUrl = `/rhro-service-1.0/bigAdd/beforeAddEmp`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          if (checkResStatus(res)) return reolve(res ? res.data : res);
          reject(res ? res.message : res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
