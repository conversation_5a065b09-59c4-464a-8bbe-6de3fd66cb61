import * as defs from '../../baseClass';
import {
  fetch,
  RequestConfig,
  beforeFetch,
  afterFetch,
  checkResStatus,
  removeSpace,
} from '@/utils/request';

/**
     * @url /rhro-service-1.0/employeeHireSep/getEmpOrderApproveList
     * @desc 查询个人订单修改审批记录
查询个人订单修改审批记录
     * hasForm: false
     * hasBody: true
     */

export class Params {}

export const init = new defs.emphiresep.FilterEntity();
export const url =
  '/rhro-service-1.0/employeeHireSep/getEmpOrderApproveList:POST';
export const initialUrl =
  '/rhro-service-1.0/employeeHireSep/getEmpOrderApproveList';
export const cacheKey = '_employeeHireSep_getEmpOrderApproveList_POST';
export async function request(
  data: defs.emphiresep.FilterEntity,
  options?: RequestConfig,
) {
  const reqUrl = `/rhro-service-1.0/employeeHireSep/getEmpOrderApproveList`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          reolve(res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
export async function requests(
  data: defs.emphiresep.FilterEntity,
  options?: RequestConfig,
) {
  const reqUrl = `/rhro-service-1.0/employeeHireSep/getEmpOrderApproveList`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          if (checkResStatus(res)) return reolve(res ? res.data : res);
          reject(res ? res.message : res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
