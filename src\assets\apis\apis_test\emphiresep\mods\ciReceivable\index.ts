/**
 * @description 商保账单模板管理
 */
import * as TestHttpCiReceivable from './TestHttpCiReceivable';
import * as TestHttpCiReceivableAgain from './TestHttpCiReceivableAgain';
import * as createCiReceivableBill from './createCiReceivableBill';
import * as exportFile from './exportFile';
import * as queryCiReceivableList from './queryCiReceivableList';
import * as queryCiReceivablePage from './queryCiReceivablePage';
import * as queryCiReceivableStatisticsPage from './queryCiReceivableStatisticsPage';

export {
  TestHttpCiReceivable,
  TestHttpCiReceivableAgain,
  createCiReceivableBill,
  exportFile,
  queryCiReceivableList,
  queryCiReceivablePage,
  queryCiReceivableStatisticsPage,
};
