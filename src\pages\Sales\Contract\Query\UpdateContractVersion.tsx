import React, { useEffect, useState } from 'react';
import { Button, Form } from 'antd';
import { WritableColumnProps } from '@/utils/writable/types';
import { CachedPage } from '@/components/CachedPage';
import { EditeFormProps } from '@/components/EditeForm';
import Codal from '@/components/Codal';
import { WritableInstance } from '@/components/Writable';
import {
  FormElement2,
  FormElement3,
  RowElement,
  ColElementButton,
} from '@/components/Forms/FormLayouts';
import { EnumerateFields } from '@/components/CachedPage/EnumerateFields';
import { msgOk } from '@/utils/methods/message';
import { mapToSelectors, GetSignFlagManualSelect } from '@/components/Selectors';
import {
  contractEndDateTypeMap,
  contractEndDateTypeNameMap,
} from '@/utils/settings/sales/contract';
import { RuleObject } from 'antd/lib/form';
import { StoreValue } from 'antd/lib/form/interface';
import { ConfirmLoading } from '@/utils/request';

const columns: WritableColumnProps<any>[] = [];
const renderButtons = (options: WritableInstance) => {
  return (
    <>
      <Button>保存</Button>
      <Button>取消</Button>
    </>
  );
};

interface UpdateContractVersionProps {
  [props: string]: any;
  modal: [boolean, CallableFunction];
  contract?: Partial<defs.sale.ContractDTO>;
  onConfirm: () => void;
}

const service = API.sale.contract.saveContractVersion;
const apis = [
  'contractService,saveContractVersion',
  'contractService,saveContractVersion',
  'contractVersionService,isDeletedContractVersion',
  'contractVersionService,setValidCustomerRecord',
  'serviceTypeService,getSub',
  'quotationService,getQuotation',
];
const codalNames = [];
const UpdateContractVersion: React.FC<UpdateContractVersionProps> = (props) => {
  const { contract, onConfirm, modal } = props;
  const [visible, setVisible] = modal;
  if (!visible) return null;
  if (!contract) return null;
  const [form] = Form.useForm();
  const { contractId } = contract;
  // 这个contract.contractEndDateType可能是中文
  const propsContractEndDateType =
    contractEndDateTypeNameMap.get(contract.contractEndDateType) || contract.contractEndDateType;
  const [contractEndDateType, setcontractEndDateType] = useState(propsContractEndDateType);

  useEffect(() => {
    if (!contractId) return;
    const contractVersion = {
      contractEndDateType: propsContractEndDateType,
      contractEndDate: contract.contractEndDate,
      contractVersion: contract.contractVersion,
      contractEndDateTypeOld: propsContractEndDateType,
      contractEndDateOld: contract.contractEndDate,
      contractVersionOld: contract.contractVersion,
      nonStandardLabelOld: contract?.nonStandardLabelList,
      contractRelationLabelOld: contract?.contractRelationLabelList,
      paymentDateOld: contract?.paymentDate,
      paymentDateApproverOld: contract?.paymentDateApprover,
      outSourcingMarginOld: contract?.outSourcingMargin,
      outSourcingMarginApproverOld: contract?.outSourcingMarginApprover,
      oldContractCodeOld: contract?.oldContractCode,
      labelRemarkOld: contract?.labelRemark,
    };
    form.setFieldsValue(contractVersion);
  }, [contractId]);

  const onOk = () => {
    form.validateFields().then((values) => {
      service.requests({ ...values, contractId }).then((data) => {
        msgOk('更新合同信息（质控）成功。');
        setVisible(false);
        onConfirm();
      });
    });
  };

  const oncontractEndDateTypeChange = (value: string) => {
    setcontractEndDateType(value);
  };

  const formColumns: EditeFormProps[] = [
    {
      label: '原合同最终结束日期类型',
      fieldName: 'contractEndDateTypeOld',
      inputProps: { disabled: true },
      inputRender: () => mapToSelectors(contractEndDateTypeMap),
    },
    {
      label: '新合同最终结束日期类型',
      fieldName: 'contractEndDateType',
      inputRender: () =>
        mapToSelectors(contractEndDateTypeMap, { onChange: oncontractEndDateTypeChange }),
    },
    {
      label: '原最终结束日期',
      fieldName: 'contractEndDateOld',
      inputProps: { disabled: true },
      inputRender: 'date',
    },
    {
      label: '新最终结束日期',
      fieldName: 'contractEndDate',
      inputRender: 'date',
      rules: [
        {
          required: contractEndDateType === '3',
          message: '请填写最终结束日期',
        },
      ],
    },
    {
      label: '原合同版本',
      fieldName: 'contractVersionOld',
      inputProps: { disabled: true },
      inputRender: 'string',
    },
    {
      label: '新合同版本',
      fieldName: 'contractVersion',
      rules: [
        { required: true, message: '请输入合同版本' },
        {
          validator: (_: RuleObject, value: StoreValue) => {
            if (!value) return Promise.resolve();
            if (value === '未匹配') return Promise.reject('不允许填写未匹配字样。');
            return Promise.resolve();
          },
        },
      ],
      inputRender: 'string',
    },
    {
      label: '原回款日',
      fieldName: 'paymentDateOld',
      inputProps: { disabled: true },
      inputRender: () => 'string',
    },
    {
      label: '新回款日',
      fieldName: 'paymentDate',
      inputRender: () => 'string',
    },
    {
      label: '原回款日审核人',
      fieldName: 'paymentDateApproverOld',
      inputProps: { disabled: true },
      inputRender: () => 'string',
    },
    {
      label: '新回款日审核人',
      fieldName: 'paymentDateApprover',
      inputRender: () => 'string',
    },
    {
      label: '原原合同编号',
      inputProps: { disabled: true },
      fieldName: 'oldContractCodeOld',
      inputRender: () => 'string',
    },
    {
      label: '新原合同编号',
      fieldName: 'oldContractCode',
      inputRender: () => 'string',
    },
    {
      label: '原外包毛利率',
      fieldName: 'outSourcingMarginOld',
      inputProps: { disabled: true },
      inputRender: () => 'string',
    },
    {
      label: '新外包毛利率',
      fieldName: 'outSourcingMargin',
      inputRender: () => 'string',
    },
    {
      label: '原外包毛利率审核人',
      inputProps: { disabled: true },
      fieldName: 'outSourcingMarginApproverOld',
      inputRender: () => 'string',
    },
    {
      label: '新外包毛利率审核人',
      fieldName: 'outSourcingMarginApprover',
      inputRender: () => 'string',
    },
    {
      label: '原非标标签',
      fieldName: 'nonStandardLabelOld',
      inputProps: { disabled: true },
      inputRender: () => {
        return (
          <GetSignFlagManualSelect
            params={{
              type: '9142',
            }}
            mode="multiple"
            onMultiConfirm={() => {}}
          />
        );
      },
    },
    {
      label: '新非标标签',
      fieldName: 'nonStandardLabelList',
      inputRender: () => {
        return (
          <GetSignFlagManualSelect
            params={{
              type: '9142',
            }}
            mode="multiple"
            onMultiConfirm={() => {}}
          />
        );
      },
    },
    {
      label: '原合同关系标签',
      fieldName: 'contractRelationLabelOld',
      inputProps: { disabled: true },
      inputRender: () => {
        return (
          <GetSignFlagManualSelect
            params={{
              type: '9143',
            }}
            mode="multiple"
            onMultiConfirm={() => {}}
          />
        );
      },
    },
    {
      label: '新合同关系标签',
      fieldName: 'contractRelationLabelList',
      inputRender: () => {
        return (
          <GetSignFlagManualSelect
            params={{
              type: '9143',
            }}
            mode="multiple"
            onMultiConfirm={() => {}}
          />
        );
      },
    },
    {
      label: '原标签备注',
      inputProps: { disabled: true },
      fieldName: 'labelRemarkOld',
      inputRender: () => 'text',
    },
    {
      label: '新标签备注',
      fieldName: 'labelRemark',
      inputRender: () => 'text',
    },
  ];
  const renderFooter = () => {
    return (
      <RowElement>
        <ColElementButton
          style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}
        >
          <Button key="ok" onClick={() => onOk()}>
            确认
          </Button>
          <Button key="cancel" type="primary" onClick={() => setVisible(false)}>
            取消
          </Button>
        </ColElementButton>
      </RowElement>
    );
  };
  return (
    <Codal
      title="维护合同信息（质控）"
      visible={visible}
      service={service}
      // onCancel={() => setVisible(false)}
      // onOk={onOk}
      footer={renderFooter()}
      width={'70%'}
    >
      <FormElement2 form={form} onFinishFailed={() => ConfirmLoading.clearLoading(service)}>
        <EnumerateFields colNumber={2} outerForm={form} formColumns={formColumns} />
      </FormElement2>
    </Codal>
  );
};

export default UpdateContractVersion;
