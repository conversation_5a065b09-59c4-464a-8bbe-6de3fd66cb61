/*
 * @Author: “liuxiamei” “<EMAIL>”
 * @Date: 2025-09-01 16:43:58
 * @LastEditors: “liuxiamei” “<EMAIL>”
 * @LastEditTime: 2025-09-05 14:19:44
 * @FilePath: \rhro_web2\src\pages\empwelfare\Ebmtransact\BusinessProcessingProject\components\main.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import React, { useEffect, useState } from 'react';
import { Button, Form } from 'antd';
import { CachedPage } from '@/components/CachedPage';
import { EditeFormProps } from '@/components/EditeForm';
import { WritableColumnProps } from '@/utils/writable/types';
import { ConfirmButton } from '@/components/Forms/Confirm';
import { WritableInstance } from '@/components/Writable';
import AddCodal from './AddCodal';
import SubmitCodal from './SubmitCodal';
import { GetBaseBusnameClassDropdownList, mapToSelectors } from '@/components/Selectors';
import {
  busSourceMap,
  busTypeMap,
  processObjectMap,
  resultMap,
  statusMap,
  transactPropertyMap,
  transactTypeMap,
} from '@/utils/settings/empwelfare/businessProcessing';
import { getCurrentUserCityId } from '@/utils/model';
import { CustomerPop } from '@/components/StandardPop/CustomerPop';
import { customerSize } from '@/utils/settings/sysmanage/allocateService';
import {
  BaseDataAreaSelector,
  CitySelector,
  GetBusContentSelector,
} from '@/components/Selectors/BaseDataSelectors';
import { useEditable } from '@/components/Editable/hooks/useEditable';

const columns: WritableColumnProps<any>[] = [
  { title: '城市', dataIndex: 'cityName' },
  { title: '业务类型', dataIndex: 'categoryName' },
  { title: '业务项目', dataIndex: 'busnameClassName' },
  { title: '业务内容', dataIndex: 'busContent' },
  { title: '办理属性', dataIndex: 'transPropertyName' },
  { title: '办理对象', dataIndex: 'transObjectName' },
  { title: '姓名', dataIndex: 'empName' },
  { title: '证件号码', dataIndex: 'idCardNum' },
  { title: '入离职状态', dataIndex: 'sepStatus' },
  { title: '实做状态', dataIndex: 'ssStatus' },
  { title: '是否收费业务', dataIndex: 'isChargeBusinessName' },
  { title: '是否收取客户费用', dataIndex: 'isChargeCustName' },

  { title: '收取金额', dataIndex: 'chargeAmount' },
  { title: '业务来源', dataIndex: 'sourceTypeName' },
  { title: '业务状态', dataIndex: 'statusName' },
  { title: '业务进度', dataIndex: 'progress' },
  { title: '办理结果', dataIndex: 'resultName' },
  { title: '创建人', dataIndex: 'createByName' },
  { title: '创建时间', dataIndex: 'createDt' },
  { title: '派单地', dataIndex: 'projectCsCity' },
  { title: '项目客服', dataIndex: 'projectCsName' },
  { title: '接单地', dataIndex: 'assigneeCsCity' },
  { title: '接单客服', dataIndex: 'assigneeCsName' },
  { title: '后道客服', dataIndex: 'bankendCsName' },
];

interface BusinessProcessingProps {
  type: string;
  [props: string]: any;
}

const service = API.welfaremanage.ebmBusiness.searchApplications;
const delService = API.basedata.areaCode.remove;
const BusinessProcessing: React.FC<BusinessProcessingProps> = (props) => {
  const { type } = props;
  const [form] = Form.useForm();
  let options: WritableInstance;
  const [addVisible, setAddVisible] = useState<string | undefined>();
  const [submitVisible, setSubmitVisible] = useState<string | undefined>();
  const [singleRow, setSingleRow] = useState<POJO | undefined>({});

  const currentUserCityId = getCurrentUserCityId() || '';

  useEffect(() => {
    if (type === '2') {
      form.setFieldsValue({ cityId: currentUserCityId });
    } else {
      form.resetFields();
    }
  }, []);

  const formColumns: EditeFormProps[] = [
    {
      label: '城市',
      fieldName: 'cityId',
      hidden: type !== '2',
      inputRender: () => <CitySelector />,
    },

    {
      label: '业务类型',
      fieldName: 'categoryId',
      inputRender: (outerForm) =>
        mapToSelectors(busTypeMap, {
          onChange: (value) => {
            outerForm.setFieldsValue({ busnameClassId: undefined, busContent: undefined });
          },
        }),
      //  rules: [{ required: true, message: '请选择' }],
    },
    {
      label: '业务项目',
      fieldName: 'busnameClassId',
      shouldUpdate: (prevValues, curValues) => {
        return prevValues.categoryId !== curValues.categoryId;
      },
      inputRender: (outerForm) => {
        const { categoryId } = outerForm.getFieldsValue();
        return (
          <GetBaseBusnameClassDropdownList
            allowClear
            // keyMap={{ busNameClassName: 'busnameClassName', busNameClassId: 'busnameClassId' }}
            params={{
              pageNum: 1,
              pageSize: 2147483647,
              categoryId,
            }}
            onChange={(value) => {
              outerForm.setFieldsValue({ busContent: undefined });
            }}
          />
        );
      },
      // rules: [{ required: true, message: '请选择' }],
    },
    {
      label: '业务内容',
      fieldName: 'busContent',
      shouldUpdate: (prevValues, curValues) => {
        return (
          prevValues.cityId !== curValues.cityId ||
          prevValues.categoryId !== curValues.categoryId ||
          prevValues.busnameClassId !== curValues.busnameClassId
        );
      },
      inputRender: (outerForm) => {
        const { categoryId, busnameClassId, cityId } = outerForm.getFieldsValue();
        return (
          <GetBusContentSelector params={{ categoryId, busnameClassId, cityId }} skipEmptyParam />
        );
      },
    },
    {
      label: '办理属性',
      fieldName: 'transactProperty',
      inputRender: () => mapToSelectors(transactPropertyMap),
    },
    {
      label: '办理对象',
      fieldName: 'transactObject',
      inputRender: () => mapToSelectors(processObjectMap),
    },
    {
      label: '办理方式',
      fieldName: 'transactType',
      inputRender: () => mapToSelectors(transactTypeMap),
    },
    {
      label: '客户名称',
      fieldName: 'custId',
      inputRender: () => {
        return <CustomerPop rowValue="custId-custName" />;
      },
    },
    {
      label: '客户规模',
      fieldName: 'custSize',
      inputRender: () => mapToSelectors(customerSize, { allowClear: true }),
    },
    { label: '唯一号', fieldName: 'empCode', inputRender: 'string' },
    { label: '姓名', fieldName: 'empName', inputRender: 'string' },
    { label: '证件号码', fieldName: 'idCardNum', inputRender: 'string' },
    { label: '业务来源', fieldName: 'sourceType', inputRender: () => mapToSelectors(busSourceMap) },
    { label: '业务状态', fieldName: 'status', inputRender: () => mapToSelectors(statusMap) },
    {
      label: '业务进度',
      fieldName: 'progress',
      inputRender: () => <BaseDataAreaSelector params={{ type: 12336 }} />,
    },
    { label: '办理结果', fieldName: 'result', inputRender: () => mapToSelectors(resultMap) },
  ];

  // 删除
  const handleDelete = (table: WritableInstance) => {
    table.handleDelete({
      service: delService,
      data: (value) => ({ applicationId: value.map((item: any) => item.applicationId).join(',') }),
    });
  };

  const renderButtons = (_options: WritableInstance) => {
    options = _options;
    const singleRow = options.selectedSingleRow;
    return (
      <>
        <Button type="primary" htmlType="submit">
          查询
        </Button>
        <Button onClick={() => setAddVisible(type)}>新增办理</Button>
        <Button
          onClick={() => {
            setSubmitVisible('submit');
            setSingleRow(singleRow);
          }}
        >
          进入办理
        </Button>
        <Button onClick={() => handleDelete(options)}>删除</Button>
        <Button onClick={() => handleDelete(options)}>导出数据</Button>
      </>
    );
  };
  return (
    <CachedPage
      service={service}
      columns={columns}
      formColumns={formColumns}
      renderButtons={renderButtons}
      form={form}
      handleQueries={(values) => {
        return { ...values, queryMenu: type };
      }}
    >
      <AddCodal
        title="新增办理"
        visible={addVisible}
        hideHandle={(value: string) => {
          if (value) {
            options?.request(options.queries);
          }
          setAddVisible(undefined);
        }}
      />
      <SubmitCodal
        title="进入办理"
        visible={submitVisible}
        hideHandle={(value: string) => {
          if (value) {
            options?.request(options.queries);
          }
          setSubmitVisible(undefined);
          setSingleRow({});
        }}
        initialValues={singleRow}
      />
    </CachedPage>
  );
};

export default BusinessProcessing;
