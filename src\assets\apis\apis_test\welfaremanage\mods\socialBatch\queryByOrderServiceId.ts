import * as defs from '../../baseClass';
import {
  fetch,
  RequestConfig,
  beforeFetch,
  afterFetch,
  checkResStatus,
  removeSpace,
} from '@/utils/request';

/**
     * @url /rhro-service-1.0/socialDetail/list/orderServiceId
     * @desc 根据OrderServiceId查询社保反馈的所有批次
根据OrderServiceId查询社保反馈的所有批次
     * hasForm: false
     * hasBody: true
     */

export class Params {
  /** orderServiceId */
  orderServiceId: number;
}

export const init = new defs.welfaremanage.RpaSsDetail();
export const url = '/rhro-service-1.0/socialDetail/list/orderServiceId:POST';
export const initialUrl = '/rhro-service-1.0/socialDetail/list/orderServiceId';
export const cacheKey = '_socialDetail_list_orderServiceId_POST';
export async function request(data: Params, options?: RequestConfig) {
  const reqUrl = `/rhro-service-1.0/socialDetail/list/orderServiceId`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          reolve(res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
export async function requests(data: Params, options?: RequestConfig) {
  const reqUrl = `/rhro-service-1.0/socialDetail/list/orderServiceId`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          if (checkResStatus(res)) return reolve(res ? res.data : res);
          reject(res ? res.message : res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
