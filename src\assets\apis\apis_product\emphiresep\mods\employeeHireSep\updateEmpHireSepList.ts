import * as defs from '../../baseClass';
import {
  fetch,
  RequestConfig,
  beforeFetch,
  afterFetch,
  checkResStatus,
  removeSpace,
} from '@/utils/request';

/**
     * @url /rhro-service-1.0/employeeHireSep/updateEmpHireSepList
     * @desc 批量更新个人订单()
批量更新个人订单()
     * hasForm: false
     * hasBody: true
     */

export class Params {}

export const init = new defs.emphiresep.CommonResponse();
export const url =
  '/rhro-service-1.0/employeeHireSep/updateEmpHireSepList:POST';
export const initialUrl =
  '/rhro-service-1.0/employeeHireSep/updateEmpHireSepList';
export const cacheKey = '_employeeHireSep_updateEmpHireSepList_POST';
export async function request(
  data: Array<defs.emphiresep.EmployeeHireSep>,
  options?: RequestConfig,
) {
  const reqUrl = `/rhro-service-1.0/employeeHireSep/updateEmpHireSepList`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          reolve(res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
export async function requests(
  data: Array<defs.emphiresep.EmployeeHireSep>,
  options?: RequestConfig,
) {
  const reqUrl = `/rhro-service-1.0/employeeHireSep/updateEmpHireSepList`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          if (checkResStatus(res)) return reolve(res ? res.data : res);
          reject(res ? res.message : res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
