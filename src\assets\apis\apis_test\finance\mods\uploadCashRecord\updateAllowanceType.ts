import * as defs from '../../baseClass';
import {
  fetch,
  RequestConfig,
  beforeFetch,
  afterFetch,
  checkResStatus,
  removeSpace,
} from '@/utils/request';

/**
     * @url /rhro-service-1.0/uploadCashRecord/updateAllowanceType
     * @desc 上传网银到款修改费用属性
上传网银到款修改费用属性
     * hasForm: false
     * hasBody: true
     */

export class Params {}

export const init = new defs.finance.CommonResponse();
export const url =
  '/rhro-service-1.0/uploadCashRecord/updateAllowanceType:POST';
export const initialUrl =
  '/rhro-service-1.0/uploadCashRecord/updateAllowanceType';
export const cacheKey = '_uploadCashRecord_updateAllowanceType_POST';
export async function request(
  data: defs.finance.UploadCashRecordDTO,
  options?: RequestConfig,
) {
  const reqUrl = `/rhro-service-1.0/uploadCashRecord/updateAllowanceType`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          reolve(res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
export async function requests(
  data: defs.finance.UploadCashRecordDTO,
  options?: RequestConfig,
) {
  const reqUrl = `/rhro-service-1.0/uploadCashRecord/updateAllowanceType`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          if (checkResStatus(res)) return reolve(res ? res.data : res);
          reject(res ? res.message : res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
