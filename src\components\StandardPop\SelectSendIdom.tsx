/*
 * @Author: Veni_刘夏梅
 * @Email: <EMAIL>
 * @Date: 2021-10-13 09:38:28
 * @LastAuthor: Veni_刘夏梅
 * @LastTime: 2022-03-02 17:28:26
 * @message:
 */
import React, { useState } from 'react';
import { Button, Form, Typography } from 'antd';
import { WritableColumnProps } from '@/utils/writable/types';
import { CachedPage } from '@/components/CachedPage';
import { EditeFormProps } from '@/components/EditeForm';
import Codal from '@/components/Codal';
import { WritableInstance } from '@/components/Writable';
import { StandardPop } from './libs/StdPop';
import { CustomerPop } from './CustomerPop';
import { ChoosePublicPayRollPop } from './ChoosePublicPayRollPop';
import { msgErr } from '@/utils/methods/message';
import PaySendDetail from '@/pages/payroll/XZFFPCXN/XJFFPCXN/components/PaySendDetail';
import { mapToSelectView } from '../Selectors';

const formColumns: EditeFormProps[] = [
  {
    label: '客户编号',
    fieldName: 'custCode',
    inputRender: () => (
      <CustomerPop rowValue="custId-custCode" fixedValues={{ isWageQuery: '1' }} />
    ),
  },
  {
    label: '薪资类别名称 ',
    fieldName: 'wageClassId',
    inputRender: () => (
      <ChoosePublicPayRollPop
        rowValue="wageClassId-custId-wageClassName"
        keyMap={{ wageClassId: 'wageClassId', wageClassName: 'wageClassName', custId: 'custId' }}
      />
    ),
  },
  { label: '发放名称', fieldName: 'sendName', inputRender: 'string' },
  { label: '工资所属年月起', fieldName: 'sendMonth', inputRender: 'string' },
  { label: '工资所属年月止', fieldName: 'sendMonthTo', inputRender: 'string' },
];
const formColumns2: EditeFormProps[] = [
  {
    label: '客户编号',
    fieldName: 'custCode',
    inputRender: () => (
      <CustomerPop rowValue="custId-custCode" fixedValues={{ isWageQuery: '1' }} />
    ),
  },
  {
    label: '薪资类别 ',
    fieldName: 'wageClassId',
    inputRender: () => (
      <ChoosePublicPayRollPop
        rowValue="wageClassId-custId-wageClassName"
        keyMap={{ wageClassId: 'wageClassId', wageClassName: 'wageClassName', custId: 'custId' }}
      />
    ),
  },
  { label: '薪资发放名称', fieldName: 'disburseName', inputRender: 'string' },
  { label: '薪资计税月从', fieldName: 'taxMonthFrom', inputRender: 'month' },
  { label: '薪资计税月到', fieldName: 'taxMonthTo', inputRender: 'month' },
];
const columns: WritableColumnProps<any>[] = [
  { title: '客户编号', dataIndex: 'CUST_CODE', inputRender: 'string' },
  { title: '客户名称', dataIndex: 'CUST_NAME', inputRender: 'string' },
  { title: '薪资类别名称', dataIndex: 'WAGE_CLASS_NAME', inputRender: 'string' },
  { title: '发放名称', dataIndex: 'DISBURSENAME', inputRender: 'string' },
  { title: '薪资所属年月', dataIndex: 'SEND_MONTH', inputRender: 'string' },
  { title: '账单月', dataIndex: 'BILL_MONTH', inputRender: 'string' },
  { title: '计税年月', dataIndex: 'TAX_MONTH', inputRender: 'string' },
];

interface SelectSendIdomProps {
  [props: string]: any;
}

const service = API.payroll.send.getsendIds;
const service2 = API.payroll.wgOffsetTask.queryPaySend;

const SelectSendIdom: React.FC<SelectSendIdomProps> = (props) => {
  const { Link } = Typography;
  const [form] = Form.useForm();

  const [singleRow, setSingleRow] = useState<POJO>({});
  const [detailVisible, setDetailVisible] = useState<boolean>(false);

  const onView = (record: POJO) => {
    setSingleRow(record);
    setDetailVisible(true);
  };

  const statusList = new Map<string, string>([
    ['0', '未发放'],
    ['1', '发放中'],
    ['2', '部分发放'],
    ['3', '发放完成'],
  ]);

  const columns2: WritableColumnProps<any>[] = [
    { title: '发放名称', dataIndex: 'disburseName' },
    { title: '薪资所属年月', dataIndex: 'sendMonth' },
    { title: '客户账单年月', dataIndex: 'billMonth' },
    { title: '薪资计税年月', dataIndex: 'taxMonth' },
    { title: '客户编号', dataIndex: 'custCode' },
    { title: '客户名称', dataIndex: 'custName' },
    { title: '薪资类别', dataIndex: 'wageClassName' },
    { title: '数据确认日期', dataIndex: 'confirmDt' },
    { title: '确认人', dataIndex: 'confirmBy' },
    {
      title: '发放状态',
      dataIndex: 'disburseBatchStatus',
      render: (text) => mapToSelectView(statusList, text),
    },
    {
      title: '查看明细',
      dataIndex: 'auction',
      render: (text: string, record) => {
        return <Typography.Link onClick={() => onView(record)}>查看</Typography.Link>;
      },
    },
  ];

  const beforeQuery = (params: POJO) => {
    if (!props.formColumnsType && !params.custCode && !params.wageClassId && !params.sendName) {
      msgErr('客户编号,薪资类别名称,发放名称至少选择一个');
      return false;
    } else if (
      props.formColumnsType &&
      !params.custCode &&
      !params.wageClassId &&
      !params.disburseName
    ) {
      msgErr('客户编号,薪资类别名称,薪资发放名称至少选择一个');
      return false;
    } else {
      return true;
    }
  };
  return (
    <>
      <StandardPop
        columns={props.formColumnsType ? columns2 : columns}
        service={props.formColumnsType ? service2 : service}
        formColumns={props.formColumnsType ? formColumns2 : formColumns}
        rowValue={props.rowValue || 'WAGESENDID-DISBURSENAME'}
        modalwidth={1200}
        form={form}
        beforeQuery={beforeQuery}
        {...(props as any)}
      />
      <PaySendDetail
        visible={detailVisible}
        hideHandle={() => setDetailVisible(false)}
        data={singleRow}
      />
    </>
  );
};

export default SelectSendIdom;
