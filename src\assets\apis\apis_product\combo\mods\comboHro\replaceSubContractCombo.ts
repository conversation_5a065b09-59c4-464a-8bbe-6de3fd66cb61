import * as defs from '../../baseClass';
import {
  fetch,
  RequestConfig,
  beforeFetch,
  afterFetch,
  checkResStatus,
  removeSpace,
} from '@/utils/request';

/**
     * @url /rhro-service-1.0/comboHRO/replaceSubContractCombo
     * @desc 替换社保套餐
替换社保套餐
     * hasForm: false
     * hasBody: true
     */

export class Params {}

export const init = new defs.combo.CommonResponse();
export const url = '/rhro-service-1.0/comboHRO/replaceSubContractCombo:POST';
export const initialUrl = '/rhro-service-1.0/comboHRO/replaceSubContractCombo';
export const cacheKey = '_comboHRO_replaceSubContractCombo_POST';
export async function request(
  data: Array<defs.combo.Subcontract>,
  options?: RequestConfig,
) {
  const reqUrl = `/rhro-service-1.0/comboHRO/replaceSubContractCombo`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          reolve(res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
export async function requests(
  data: Array<defs.combo.Subcontract>,
  options?: RequestConfig,
) {
  const reqUrl = `/rhro-service-1.0/comboHRO/replaceSubContractCombo`;
  const fetchOption = {
    reqUrl,
    requestType: 'json',

    data: removeSpace(data),
    ...options,
  } as RequestConfig;

  return new Promise((reolve, reject) => {
    beforeFetch(cacheKey, fetchOption).then(() => {
      fetch
        .post(reqUrl, fetchOption)
        .then((res: StdRes | undefined) => {
          if (checkResStatus(res)) return reolve(res ? res.data : res);
          reject(res ? res.message : res);
        })
        .catch((e) => reject(e))
        .finally(() => afterFetch(cacheKey));
    });
  });
}
