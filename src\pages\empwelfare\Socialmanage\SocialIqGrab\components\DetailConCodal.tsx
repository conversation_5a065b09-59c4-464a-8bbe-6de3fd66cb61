/*
 * @Author: Veni_刘夏梅
 * @Email: <EMAIL>
 * @Date: 2022-10-11 14:01:30
 * @LastAuthor: Veni_刘夏梅
 * @LastTime: 2022-10-14 10:19:31
 * @message: 明细详情
 */

import React, { useEffect } from 'react';
import Codal from '@/components/Codal';
import { CachedPage } from '@/components/CachedPage';
import { useWritable } from '@/components/Writable';

interface Props {
  //锁定类型
  visible: boolean | undefined;
  [props: string]: any;
}
const service = API.welfaremanage.socialBatch.queryByOrderServiceId;

const DetailConCodal: React.FC<Props> = (props: Props) => {
  const { visible, hideHandle, data } = props;
  const wriTable = useWritable({ service });

  useEffect(() => {
    if (!visible) {
      wriTable.setNewData([]);
      return;
    }
    service
      .requests(
        { orderServiceId: data.ORDER_SS_GROUP_SVC_ID },
        { params: { orderServiceId: data.ORDER_SS_GROUP_SVC_ID } },
      )
      .then((res) => {
        wriTable.setNewData(res);
      });
  }, [visible]);

  const columns: POJO[] = [
    { title: '批次号', dataIndex: 'ssBatchId' },
    { title: '批次明细号', dataIndex: 'detailId' },
    { title: '办理类型', dataIndex: 'empType' },
    { title: '批次发送时间', dataIndex: 'startDate' },
    { title: '批次处理反馈时间', dataIndex: 'endDate' },
    { title: '批次明细状态', dataIndex: 'detailStatus' },
    { title: '办理状态', dataIndex: 'ssStatus' },
    { title: '社保反馈结果', dataIndex: 'ssResult' },
    { title: '失败原因', dataIndex: 'resultRemark' },
  ];

  return (
    <Codal
      title="明细详情"
      visible={visible}
      onCancel={() => hideHandle()}
      footer={null}
      width="80vw"
    >
      <CachedPage
        service={service}
        formColumns={[]}
        columns={columns}
        notShowRowSelection
        notShowPagination
        wriTable={wriTable}
      />
    </Codal>
  );
};

export default DetailConCodal;
