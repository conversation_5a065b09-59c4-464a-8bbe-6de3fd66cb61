declare namespace defs {
  export namespace ec {
    export class BaseEntity {
      /** 实际离职日期 */
      actualSepDate: string;

      /** add */
      add: boolean;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** 离职证明法人单位id */
      certificateCorporationId: string;

      /** 离职证明法人单位名称 */
      certificateCorporationName: string;

      /** certificateDate */
      certificateDate: string;

      /** certificateEleId */
      certificateEleId: string;

      /** 离职证明电子版本 */
      certificateSpId: string;

      /** 离职证明电子合同状态:1未发起、5已完成、6已过期、9已作废 */
      certificateStatus: number;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 经济补偿金收款账号 */
      compensationAccount: string;

      /** 经济补偿金收款银行 */
      compensationBank: string;

      /** 经济补偿金收款开户行全称 */
      compensationBankFull: string;

      /** 经济补偿金支付日期 */
      compensationDate: string;

      /** 经济补偿金（小写） */
      compensationLower: string;

      /** 经济补偿金（大写） */
      compensationUpper: string;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** corpId */
      corpId: number;

      /** 法人单位ID */
      corporationId: string;

      /** 法人单位名称 */
      corporationName: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** empHireSepId */
      empHireSepId: string;

      /** 导入类型,扩充使用 */
      expType: string;

      /** 上传附件的fileId */
      fileId: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** 是否内部 */
      isInner: string;

      /** isLast */
      isLast: number;

      /** 是否离职外呼1 呼叫中心通知  2 客服自行通知  3 不需通知  客户代通知 */
      isSepCall: string;

      /** 是否薪资查询 */
      isWageQuery: string;

      /** 离职材料生效日期 */
      materialEffectDt: string;

      /** materialEleId */
      materialEleId: string;

      /** 离职材料签订日期 */
      materialSignDt: string;

      /** 离职材料签署状态:1未签署、2已签署、3已作废  */
      materialSignStatus: number;

      /** 离职材料电子版本 */
      materialSpId: string;

      /** 离职材料电子合同状态:1未发起、2拟定中、3签署中、4作废中、5已完成、6已过期、7已撤回、9已作废、10 已发起作废  */
      materialStatus: number;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** processId */
      processId: number;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** 离职手续办理日期 */
      quitProcDate: string;

      /** 离职材料签订形式: 1,电子版 2,纸质版  */
      quitSignType: number;

      /** 离职任务编号 */
      quitTaskId: number;

      /** reduceDetailReason */
      reduceDetailReason: string;

      /** 减原详细原因名称 */
      reduceDetailReasonName: string;

      /** 减少原因:1正常减员2转至竞争对手3服务原因4准备撤单和已报撤单正在减员中5客户原因6其他原因7变更合同名称8变更服务项目9易才内部转单 */
      reduceReason: string;

      /** 减员原因名称 */
      reduceReasonName: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** sealDes */
      sealDes: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** 离职日期 */
      sepDt: string;

      /** 离职原因:1 合同到期终止2 试用期解除3 合同主动解除4 死亡5 合同被动解除10其它 */
      sepReason: string;

      /** 签订操作人 */
      signOperBy: number;

      /** 印章流程ID */
      signProcessId: string;

      /** 印章流程名 */
      signProcessName: string;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** 小合同ID */
      subcontractId: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;
    }

    export class CommonResponse<T0 = any> {
      /** bizCode */
      bizCode: number;

      /** code */
      code: number;

      /** data */
      data: T0;

      /** message */
      message: string;

      /** t */
      t: T0;
    }

    export class EcBusinessDto {
      /** 增员状态 */
      addConfirmStatus: string;

      /** 增员状态 decode值 NP-8383 */
      addConfirmStatusText: string;

      /** 接单客服id */
      assigneeCsId: string;

      /** 接单客服name */
      assigneeCsName: string;

      /** 接单方客服对应联系方式 */
      assigneeCsTel: string;

      /** 接单方 */
      assigneeProviderId: string;

      /** 派单客服id */
      assignerCsId: string;

      /** 派单客服name */
      assignerCsName: string;

      /** 派单方客服对应联系方式 */
      assignerCsTel: string;

      /** 派单方 */
      assignerProviderId: string;

      /** Hro业务办理编号 */
      businessId: string;

      /** 作废原因原因 */
      cancelHireReason: string;

      /** cityId */
      cityId: string;

      /** 城市 */
      cityName: string;

      /** 手机 */
      contactTel2: string;

      /** 大合同id */
      contractId: string;

      /** 员工类别 1代理 2派遣 3BPO 4外包项目 */
      contractTypeEx: string;

      /** 员工类别(显示) */
      contractTypeNameEx: string;

      /** 法人单位用章是否易才代操作(0否；1是) */
      corporationCtgProxy: string;

      /** 法人单位用章是否易才代操作名称 */
      corporationCtgProxyName: string;

      /** 法人单位ID */
      corporationId: string;

      /** 法人单位名称 */
      corporationName: string;

      /** 客户编号 */
      custCode: string;

      /** 客户id */
      custId: string;

      /** 客户名称 */
      custName: string;

      /** 客户规模，1大型客户，2中型客户，3小型客户 */
      customerSize: string;

      /** 客户规模(显示) */
      customerSizeName: string;

      /** 电子业务list */
      ecBusinessList: Array<defs.ec.EcBusinessDto>;

      /** 电子签章编号 */
      eleBusinessId: string;

      /** 电子业务签署标志(1未发起、2拟定中、3员工已签署、4作废中、5已完成、6已过期、7已撤回、9已作废、10 已发起作废、11客户已签署、12员工已作废) */
      eleBusinessStatus: number;

      /** 电子业务状态（显示） */
      eleBusinessStatusName: string;

      /** 员工唯一号 */
      empCode: string;

      /** 员工入离职id */
      empHireSepId: string;

      /** 入离职状态 */
      empHireStatus: string;

      /** 入离职状态 decode值 NP-8383 */
      empHireStatusText: string;

      /** empId */
      empId: string;

      /** 员工姓名 */
      empName: string;

      /** fileId */
      fileId: string;

      /** 文件名称 */
      fileName: string;

      /** 证件号码 */
      idCardNum: string;

      /** 证件类型 */
      idCardType: string;

      /** 证件类型name */
      idCardTypeName: string;

      /** 供应商客服 */
      providerCs: string;

      /** 供应商客服id */
      providerCsId: string;

      /** 申报入职时间 */
      rptHireDt: string;

      /** 导入规则id */
      ruleId: string;

      /** 用章对象(1员工；2客户) */
      sealObject: number;

      /** 用章对象名称 */
      sealObjectName: string;

      /** 用章类型（1:仅单位盖章，2:双方盖章，3:三方盖章） */
      sealType: number;

      /** 用章类型名称 */
      sealTypeName: string;

      /** 离职状态 */
      sepConfirmStatus: string;

      /** sepConfirmStatusText */
      sepConfirmStatusText: string;

      /** 签订操作人 */
      signBy: string;

      /** 签订操作人显示名称 */
      signByName: string;

      /** 电子业务办结时间 */
      signDt: string;

      /** 印章流程ID */
      signProcessId: string;

      /** 签章材料名称 */
      signProcessName: string;

      /** 签署状态 */
      signStatus: string;

      /** 签署状态显示名称 */
      signStatusName: string;

      /** 小合同id */
      subcontractId: string;

      /** 小合同名称 */
      subcontractName: string;

      /** 第三方盖章 */
      thirdCorporationId: string;

      /** thirdCorporationName名称 */
      thirdCorporationName: string;

      /** 第三方用章是否易才代操作(0否；1是) */
      thirdSealCtgProxy: string;

      /** 第三方用章是否易才代操作名称 */
      thirdSealCtgProxyName: string;

      /** 版本类型：1分公司版本、2客户版本、3集团版本 */
      versionType: string;
    }

    export class EcBusinessQuery {
      /** add */
      add: boolean;

      /** 增员状态(1增员未提交 20 等待接单方确认 22 接单方挂起 30 等待派单方确认40 增员完成) */
      addConfirmStatus: number;

      /** 接单方客服 */
      assigneeCsId: string;

      /** 接单方 */
      assigneeProviderId: string;

      /** 派单客服 */
      assignerCsId: string;

      /** 派单方 */
      assignerProviderId: string;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** Hro业务办理编号 */
      businessId: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** 客户id */
      custId: string;

      /** 客户名称 */
      custName: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** 电子签字办理状态(1未发起、2拟定中、3员工已签署、4作废中、5已完成、6已过期、7已撤回、9已作废、10 已发起作废、11客户已签署、12员工已作废) */
      eleBusinessStatus: number;

      /** 唯一号 */
      empCode: string;

      /** 入离职状态 */
      empHireStatus: number;

      /** 雇员姓名 */
      empName: string;

      /** endIndex */
      endIndex: number;

      /** 导入类型,扩充使用 */
      expType: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** 证件号码 */
      idCardNum: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** 是否薪资查询 */
      isWageQuery: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** 页数 */
      pageNum: number;

      /** 每页记录数,默认65536条 */
      pageSize: number;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 用章对象(1员工；2客户) */
      sealObject: number;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** 减员状态( 1离职未提交 20 等待接单方确认 23接单方驳回 30 等待派单方确认40 离职完成) */
      sepConfirmStatus: number;

      /** 业务办结时间止 */
      signEndDt: string;

      /** 签章材料 */
      signProcessId: string;

      /** 业务办结时间起 */
      signStartDt: string;

      /** 业务办理签署状态(0--未签署、1--已签署、2--已作废) */
      signStatus: number;

      /** startIndex */
      startIndex: number;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;
    }

    export class EcEleBusinessQuery {
      /** Hro业务办理编号 */
      businessId: string;

      /** endIndex */
      endIndex: number;

      /** 页数 */
      pageNum: number;

      /** 每页记录数 */
      pageSize: number;

      /** startIndex */
      startIndex: number;
    }

    export class EcQuitAll {
      /** 开户人姓名 */
      accountEmployeeName: string;

      /** 实际离职日期 */
      actualSepDate: string;

      /** 实际工作地 */
      actualWorkLoc: string;

      /** add */
      add: boolean;

      /** 增员确认人 */
      addConfirmBy: string;

      /** 增员确认时间 */
      addConfirmDate: string;

      /** 增员过程 */
      addConfirmPro: string;

      /** 增员状态:1增员未提交 20 等待接单方确认 22 接单方挂起 30 等待派单方确认40 增员完成 */
      addConfirmStatus: string;

      /** 增员状态名称 */
      addConfirmStatusName: string;

      /** 增员接单确认时间 */
      addPerfectBy: string;

      /** 增员接单确认人 */
      addPerfectDate: string;

      /** 增员原因:1.正常增员2.从竞争对手中转入 3.转移 4不详 */
      addReason: string;

      /** 增员备注 */
      addRemark: string;

      /** 年龄 */
      age: string;

      /** 变更确认人 */
      alterConfirmBy: string;

      /** 变更确认时间 */
      alterConfirmDate: string;

      /** 变更确认过程 */
      alterConfirmPro: string;

      /** 变更接单确认人 */
      alterPerfectBy: string;

      /** 变更接单确时间 */
      alterPerfectDate: string;

      /** 变更备注 */
      alterRemark: string;

      /** 变更状态:1变更未提交 20 等待接单方确认 25:派单方驳回 30 等待派单方确认 40 变更最终确认 */
      alterStatus: string;

      /** 变更状态名称 */
      alterStatusName: string;

      /** 大区类型 */
      areaType: string;

      /** 大区类型名称 */
      areaTypeName: string;

      /** 接单城市id */
      assigneeCityId: string;

      /** 接单客服 */
      assigneeCs: string;

      /** 接单客服id */
      assigneeCsId: string;

      /** assigneeCsName */
      assigneeCsName: string;

      /** assigneeCsTel */
      assigneeCsTel: string;

      /** 接单方 */
      assigneeProvider: string;

      /** 接单方id */
      assigneeProviderId: string;

      /** 派单客服 */
      assignerCs: string;

      /** 派单客服id */
      assignerCsId: string;

      /** assignerCsName */
      assignerCsName: string;

      /** assignerCsTel */
      assignerCsTel: string;

      /** 派单方 */
      assignerProvider: string;

      /** 派单方id */
      assignerProviderId: string;

      /** 派单类型1 执行单2 协调单3 收集单 */
      assignmentType: string;

      /** 关联状态 */
      associationStatus: string;

      /** 银行卡号 */
      bankAcct: string;

      /** 银行卡更新人 */
      bankCardUpdateBy: string;

      /** 银行卡更新时间 */
      bankCardUpdateDt: string;

      /** baseInfo主键 */
      baseInfoId: string;

      /** 批次号,用于生成社保服务信息 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 账单模板id */
      billTempltId: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** 作废原因 */
      cancelReason: string;

      /** 类型 */
      category: string;

      /** 离职证明法人单位id */
      certificateCorporationId: string;

      /** 离职证明法人单位名称 */
      certificateCorporationName: string;

      /** 电子离职证明开具日期 */
      certificateDate: string;

      /** 离职证明电子合同id */
      certificateEleId: string;

      /** 离职证明电子版本 */
      certificateSpId: string;

      /** 离职证明电子版本 */
      certificateSpIdName: string;

      /** 电子离职证明状态:1未发起、5已完成、6已过期、9已作废 */
      certificateStatus: number;

      /** 电子离职证明状态名称:1未发起、5已完成、6已过期、9已作废 */
      certificateStatusName: string;

      /** 编辑方式1:增员完成提交变更 2:增员确认了,只变更报价3:增员确认了,变更社保公积金 ,接单方是内部供应商4:增员确认了,变更社保公积金	  ,接单方是外部供应商 */
      changeMethod: string;

      /** 收费截至日期 */
      chargeEndDate: string;

      /** 城市id */
      cityId: string;

      /** 城市 */
      cityName: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 商保订单状态 */
      commInsurStatus: string;

      /** 商保订单状态name */
      commInsurStatusName: string;

      /** 经济补偿金收款账号 */
      compensationAccount: string;

      /** 经济补偿金收款银行 */
      compensationBank: string;

      /** 经济补偿金收款开户行全称 */
      compensationBankFull: string;

      /** 经济补偿金支付日期 */
      compensationDate: string;

      /** 经济补偿金（小写） */
      compensationLower: string;

      /** 经济补偿金（大写） */
      compensationUpper: string;

      /** 确认备注 */
      confirmRemark: string;

      /** 联系电话1，电话 */
      contactTel1: string;

      /** 手机号码 */
      contactTel2: string;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 合同编号 */
      contractCode: string;

      /** 合同id */
      contractId: string;

      /** 合同名称 */
      contractName: string;

      /** contractStartDate */
      contractStartDate: string;

      /** contractStopDate */
      contractStopDate: string;

      /** 法人单位id */
      corporationId: string;

      /** 法人单位名称 */
      corporationName: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** 客户编码 */
      custCode: string;

      /** 客户id */
      custId: string;

      /** 客户方内部编号 */
      custInternalNum: string;

      /** 客户名称 */
      custName: string;

      /** 缴费实体id */
      custPayEntityId: number;

      /** 缴费实体 */
      custPayEntityName: string;

      /** 客户类型 */
      custType: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** 客户规模 */
      customerSize: string;

      /** 类型: 1,正常 2,大客户  */
      dataType: number;

      /** 申报工资 */
      decSalary: string;

      /** decSalaryRemark */
      decSalaryRemark: string;

      /** 默认法人单位id */
      defaultCorporationId: string;

      /** 默认法人单位名称 */
      defaultcorporationName: string;

      /** del */
      del: boolean;

      /** email */
      email: string;

      /** 客户端增员ID */
      empAddId: string;

      /** 客户端变更ID */
      empAlterId: string;

      /** feeId数组 */
      empFeeIdArray: string;

      /** 历史表主键 */
      empHireSepHisId: string;

      /** 上下岗id */
      empHireSepId: string;

      /** 入职主记录ID */
      empHiresepMainId: string;

      /** 雇员id */
      empId: string;

      /** 员工姓名 */
      empName: string;

      /** 停缴id */
      empStopId: string;

      /** 停缴处理进程 0: 停缴未启动 1：停缴处理中 2：停缴完成 */
      empStopProcessState: string;

      /** 员工类别：1 常规雇员2 委托人事管理 3 BPO人员 */
      empType: string;

      /** 人员分类: 1 派遣 2 代理 3 BPO人员 4 外包 */
      empTypeId: number;

      /** 人员分类 */
      empTypeName: string;

      /** 雇员编码 */
      employeeCode: string;

      /** 费用段列表 */
      employeeFeeList: Array<defs.ec.EmployeeFee>;

      /** 雇员姓名 */
      employeeName: string;

      /** 雇员状态 */
      employeeStatus: string;

      /** endDt */
      endDt: string;

      /** endIndex */
      endIndex: number;

      /** 是否增强型代理 */
      enhancedAgent: string;

      /** 是否增强型代理名称 */
      enhancedAgentName: string;

      /** 外部供应商收费模板 */
      exFeeTemplt: string;

      /** 外部供应商账单id */
      exFeeTempltId: string;

      /** exQuotationFeeList */
      exQuotationFeeList: Array<defs.ec.EmployeeFee>;

      /** 导入类型,扩充使用 */
      expType: string;

      /** 收费月,月收:, 两月收:数月:偶数月.,三月收:,4,7,10;,5,8,11;,6,9,12.,半年收:1,7;2,8;3,9;4,10;5,11;6,12,年收:无 */
      feeMonth: string;

      /** 收费模板名称 */
      feeTemplt: string;

      /** 收费模板id */
      feeTempltId: string;

      /** 档案柜编号 */
      fileCabCode: string;

      /** 离职材料附件文件id */
      fileId: string;

      /** 离职材料附件文件 */
      fileName: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** 文件夹编号 */
      folderCode: string;

      /** 频率,1:月收、2:两月收、3:三月收、6:半年收、12:年收 */
      frequency: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** 是否关联订单 */
      hasAssociations: number;

      /** 入职竞争对手 */
      hireCompetitor: string;

      /** 入职时间 */
      hireDt: string;

      /** 入职时间止 */
      hireEndDt: string;

      /** 入职报价单 */
      hireQuotationId: string;

      /** 入职备注 */
      hireRemark: string;

      /** 入职时间起 */
      hireStartDt: string;

      /** 证件号码 */
      idCardNum: string;

      /** 证件类型 */
      idCardType: string;

      /** idCardTypeName */
      idCardTypeName: string;

      /** inId */
      inId: string;

      /** 内外部类型(1内部2外部3全部) */
      innerType: string;

      /** 是否需要实做 */
      isAddProcess: string;

      /** 增员是否需要实做 */
      isAddProcessName: string;

      /** 是否需要签订劳动合同 */
      isArchive: string;

      /** 是否归档名称 */
      isArchiveName: string;

      /** 银行卡是否上传 */
      isBankCardUpload: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** 是否入职呼叫 */
      isHireCall: string;

      /** 是否入职呼叫名称 */
      isHireCallName: string;

      /** 身份证是否上传 */
      isIDCardUpload: string;

      /** 是否单立户1 是0 否 */
      isIndependent: string;

      /** 劳动合同是否上传 */
      isLaborContractUpload: string;

      /** 是否需要签订劳动合同 */
      isNeedSign: string;

      /** 是否需要实做 */
      isReduceProcess: string;

      /** 是否退费 0否  1是 */
      isRefund: string;

      /** isRelated */
      isRelated: string;

      /** 是否集中一地投保 */
      isSameInsur: string;

      /** 是否集中一地投保中文 */
      isSameInsurName: string;

      /** 是否离职外呼1是，0否 */
      isSepCall: string;

      /** 是否离职呼叫名 */
      isSepCallName: string;

      /** 是否有统筹医疗 */
      isThereACoordinateHealth: string;

      /** 是否有统筹医疗名称 */
      isThereACoordinateHealthText: string;

      /** 是否有社保卡 */
      isThereSsCard: string;

      /** 是否有社保卡名称 */
      isThereSsCardText: string;

      /** 是否薪资查询 */
      isWageQuery: string;

      /** 离职动态模板json */
      jsonStr: Array<object>;

      /** 劳动关系单位 */
      laborRelationUnit: string;

      /** 责任客服 */
      liabilityCs: string;

      /** 离职材料生效日期 */
      materialEffectDt: string;

      /** 离职材料电子合同id */
      materialEleId: string;

      /** 材料列表 */
      materialList: Array<defs.ec.Material>;

      /** 离职材料签订日期 */
      materialSignDt: string;

      /** 离职材料签署状态:1未签署、2已签署、3已作废  */
      materialSignStatus: number;

      /** 离职材料签署状态:1未签署、2已签署、3已作废  */
      materialSignStatusName: string;

      /** 离职材料电子版本 */
      materialSpId: string;

      /** 离职材料电子版本 */
      materialSpIdName: string;

      /** 电子离职材料状态:1未发起、2拟定中、3签署中、4作废中、5已完成、6已过期、7已撤回、9已作废、10 已发起作废  */
      materialStatus: number;

      /** 电子离职材料状态:1未发起、2拟定中、3签署中、4作废中、5已完成、6已过期、7已撤回、9已作废、10 已发起作废  */
      materialStatusName: string;

      /** 模拟人 */
      mimicBy: string;

      /** 操作方式  单立户1、大户2 */
      modeOfOperation: string;

      /** 提前几个月收,默认为0，选项0-3 */
      monthInAdvance: string;

      /** 后指针 */
      nextPointer: string;

      /** noChange */
      noChange: boolean;

      /** 非社保列表 */
      nonSsGroupList: Array<defs.ec.EmployeeFee>;

      /** 银行名称 */
      openBankName: string;

      /** 页数 */
      pageNum: number;

      /** 每页记录数,默认65536条 */
      pageSize: number;

      /** 挂起原因 */
      pendingReason: string;

      /** 挂起原因中文 */
      pendingReasonName: string;

      /** 人员分类id */
      personCategoryId: string;

      /** 职位id */
      positionId: string;

      /** 前指针 */
      prevPointer: string;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 流程实例化id */
      processInsId: string;

      /** 供应商编码 */
      providerCode: string;

      /** 供应商客服 */
      providerCs: string;

      /** 供应商客服id */
      providerCsId: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 供应商类型1内部2外部 */
      providerType: string;

      /** 代理人 */
      proxyBy: string;

      /** 供应商集团id */
      prvdGroupId: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** 供应商集团 */
      prvdGroupName: string;

      /** 离职手续办理日期 */
      quitProcDate: string;

      /** 离职材料签订形式: 1,电子版 2,纸质版  */
      quitSignType: number;

      /** 离职材料签订形式名称  */
      quitSignTypeName: string;

      /** 离职任务编号 */
      quitTaskId: number;

      /** 报价单编码 */
      quotationCode: string;

      /** 报价单名称 */
      quotationName: string;

      /** reduceDetailReason */
      reduceDetailReason: string;

      /** 减原详细原因名称 */
      reduceDetailReasonName: string;

      /** 客户端减员ID */
      reduceId: string;

      /** 减少原因:1正常减员2转至竞争对手3服务原因4准备撤单和已报撤单正在减员中5客户原因6其他原因7变更合同名称8变更服务项目9易才内部转单 */
      reduceReason: string;

      /** 减员原因名称 */
      reduceReasonName: string;

      /** 参考日期，页面传入 */
      referDate: string;

      /** 备注 */
      remark: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 退回原因 */
      returnReason: string;

      /** riskPremiumRatio */
      riskPremiumRatio: string;

      /** riskSharingRatio */
      riskSharingRatio: string;

      /** 报入职人员id */
      rptHireBy: string;

      /** 报入职人 */
      rptHireByName: string;

      /** 报入职时间 */
      rptHireDt: string;

      /** 报入职日期止 */
      rptHireEndDt: string;

      /** 报入职日期起 */
      rptHireStartDt: string;

      /** 报离职人员id */
      rptSepBy: string;

      /** 报离职人 */
      rptSepByName: string;

      /** 报离职日期 */
      rptSepDt: string;

      /** 报离职结束日期 */
      rptSepEndDt: string;

      /** 报离职开始时间 */
      rptSepStartDt: string;

      /** 用章对象 */
      sealObject: string;

      /** 用章类型 */
      sealType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** 离职确认人 */
      sepConfirmBy: string;

      /** 离职确认日期 */
      sepConfirmDate: string;

      /** 离职确认历史 */
      sepConfirmHis: string;

      /** 离职确认进程 */
      sepConfirmPro: string;

      /** 减员状态 */
      sepConfirmStatus: string;

      /** 减员状态: 1离职未提交 20 等待接单方确认 23接单方驳回 30 等待派单方确认40 离职完成 */
      sepConfirmStatusName: string;

      /** 离职详细原因 */
      sepDetailReason: string;

      /** 离职详细原因名称 */
      sepDetailReasonName: string;

      /** 离职日期 */
      sepDt: string;

      /** 离职结束时间 */
      sepEndDt: string;

      /** 离职接单确认人 */
      sepPerfectBy: string;

      /** 离职接单确认时间 */
      sepPerfectDate: string;

      /** 离职手续办理状态:0  未完成   1  完成 */
      sepProcessStatus: string;

      /** 离职报价单 */
      sepQuotationId: string;

      /** 离职原因:1 合同到期终止2 试用期解除3 合同主动解除4 死亡5 合同被动解除10其它 */
      sepReason: string;

      /** 离职原因key */
      sepReasonKey: string;

      /** 离职原因名称:1 合同到期终止2 试用期解除3 合同主动解除4 死亡5 合同被动解除10其它 */
      sepReasonName: string;

      /** 离职备注 */
      sepRemark: string;

      /** 离职开始时间 */
      sepStartDt: string;

      /** 离职导出类型:1离职接单确认,2离职派单确认 */
      sepType: string;

      /** sigle */
      sigle: boolean;

      /** 签约方分公司抬头 */
      signBranchTitle: string;

      /** 签约方分公司抬头id */
      signBranchTitleId: string;

      /** 签约方分公司抬头name */
      signBranchTitleName: string;

      /** 签订操作人 */
      signOperBy: number;

      /** 签订操作人 */
      signOperByName: string;

      /** 签单供应商 */
      signProvider: string;

      /** 签单方 */
      signProviderId: string;

      /** signStatus */
      signStatus: number;

      /** 短信发送日期 */
      smsSendDt: string;

      /** 短信发送状态: 0未发送, 1成功, 2失败 */
      smsSendStatus: string;

      /** 短信发送状态中文: 未发送, 成功, 失败 */
      smsSendStatusStr: string;

      /** 分拆方分公司:分拆方客服 */
      splitServiceProviderCs: string;

      /** 社保列表 */
      ssGroupList: Array<defs.ec.EmployeeFee>;

      /** 员工社保参与地 */
      ssParticipateLocation: string;

      /** startDt */
      startDt: string;

      /** startIndex */
      startIndex: number;

      /** 入离职状态:1入职未生效2在职3离职 */
      status: string;

      /** 状态名称 */
      statusName: string;

      /** 小类名称 */
      subTypeName: string;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** 小合同编号 */
      subcontractCode: string;

      /** 小合同id */
      subcontractId: string;

      /** 小合同名字 */
      subcontractName: string;

      /** 大类名称 */
      superTypeName: string;

      /** 总收费日期 */
      totalFeeDt: string;

      /** eos转移id */
      transferId: number;

      /** 类型:1增员接单完善离职接单确认,2增员派单确认离职派单确认,3变更派单确认 */
      type: number;

      /** 机动分类项目1 */
      type1: string;

      /** 机动分类项目2 */
      type2: string;

      /** 机动分类项目3 */
      type3: string;

      /** 机动分类项目4 */
      type4: string;

      /** 机动分类项目5 */
      type5: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;

      /** uuid */
      uuid: string;

      /** workCity */
      workCity: string;

      /** workPost */
      workPost: string;

      /** workUnit */
      workUnit: string;
    }

    export class EcQuitCertificate {
      /** add */
      add: boolean;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** 离职证明作废原因 */
      certificateCancelReason: string;

      /** 离职证明编号 */
      certificateEleId: string;

      /** 离职证明名称 */
      certificateEleName: string;

      /** 离职证明电子合同状态:1未发起、5已完成、6已过期、9已作废 */
      certificateStatus: number;

      /** 离职证明电子合同状态:1未发起、5已完成、6已过期、9已作废 */
      certificateStatusName: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** 上下岗id */
      empHireSepId: number;

      /** 导入类型,扩充使用 */
      expType: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** 是否薪资查询 */
      isWageQuery: string;

      /** 离职材料电子合同id */
      materialEleId: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** id */
      quitCertificateId: number;

      /** 离职任务编号 */
      quitTaskId: number;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;
    }

    export class EcQuitFileBatch {
      /** add */
      add: boolean;

      /** 上传批次号 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 上传人id */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** 上传日期<= */
      createDtEt: string;

      /** 上传日期>= */
      createDtSt: string;

      /** 上传人姓名 */
      createbyName: string;

      /** 上传时间 */
      createdt: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** endIndex */
      endIndex: number;

      /** 导入类型,扩充使用 */
      expType: string;

      /** 上传记录数 */
      fileCount: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** 是否薪资查询 */
      isWageQuery: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** 页数 */
      pageNum: number;

      /** 每页记录数,默认65536条 */
      pageSize: number;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** remark */
      remark: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** startIndex */
      startIndex: number;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;
    }

    export class EcQuitFileBatchItem {
      /** add */
      add: boolean;

      /** 上传批次号 */
      batchId: string;

      /** batchItemId */
      batchItemId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 上传人id */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** 创建日期 */
      createDt: string;

      /** 上传人姓名 */
      createbyName: string;

      /** 上传时间 */
      createdt: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** 入离职id */
      empHireSepId: string;

      /** endIndex */
      endIndex: number;

      /** 失败原因 */
      errorInfo: string;

      /** 导入类型,扩充使用 */
      expType: string;

      /** 文件id */
      fileId: string;

      /** 文件名 */
      fileName: string;

      /** 文件路径 */
      filePath: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** 上传结果，0成功  1失败 */
      impTag: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** 是否薪资查询 */
      isWageQuery: string;

      /** 签署状态 */
      materialSignStatus: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** 页数 */
      pageNum: number;

      /** 每页记录数,默认65536条 */
      pageSize: number;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** 签署类型 */
      quitSignType: string;

      /** 任务id */
      quitTaskId: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** startIndex */
      startIndex: number;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** 上传方式：1单个功能上传 2批量上传 3 EOS上传 */
      uploadType: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;
    }

    export class EcQuitMaterial {
      /** add */
      add: boolean;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** 上下岗id */
      empHireSepId: string;

      /** 导入类型,扩充使用 */
      expType: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** 是否薪资查询 */
      isWageQuery: string;

      /** 离职材料作废原因 */
      materialCancelReason: string;

      /** 离职材料编号 */
      materialEleId: string;

      /** 离职材料名称 */
      materialEleName: string;

      /** 离职材料退回原因 */
      materialReturnReason: string;

      /** 离职材料电子合同状态:1未发起、2拟定中、3签署中、4作废中、5已完成、6已过期、7已撤回、9已作废、10 已发起作废 */
      materialStatus: number;

      /** 离职材料电子合同状态:1未发起、2拟定中、3签署中、4作废中、5已完成、6已过期、7已撤回、9已作废、10 已发起作废 */
      materialStatusName: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** quitMaterialId */
      quitMaterialId: number;

      /** 离职材料员工签署日期 */
      quitSignDt: string;

      /** 离职任务编号 */
      quitTaskId: number;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;
    }

    export class EcTaskCergificateDto {
      /** 实际离职日期 */
      actualSepDate: string;

      /** add */
      add: boolean;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** 离职证明法人单位id */
      certificateCorporationId: string;

      /** 离职证明法人单位名称 */
      certificateCorporationName: string;

      /** 离职证明电子版本 */
      certificateSpId: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** 导入类型,扩充使用 */
      expType: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** 是否薪资查询 */
      isWageQuery: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** 离职任务编号 */
      quitTaskId: number;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;
    }

    export class EcUserSync {
      /** add */
      add: boolean;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** email */
      email: string;

      /** endIndex */
      endIndex: number;

      /** 导入类型,扩充使用 */
      expType: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** isSyne */
      isSyne: string;

      /** 是否薪资查询 */
      isWageQuery: string;

      /** 模拟人 */
      mimicBy: string;

      /** mobileNo */
      mobileNo: string;

      /** noChange */
      noChange: boolean;

      /** 页数 */
      pageNum: number;

      /** 每页记录数,默认65536条 */
      pageSize: number;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** realName */
      realName: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** startIndex */
      startIndex: number;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** userId */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;

      /** userName */
      userName: string;

      /** userSyncId */
      userSyncId: string;
    }

    export class EmployeeFee {
      /** add */
      add: boolean;

      /** 调整情况分类：1费用添加、2费用清空、3比例调整、4基数调整、5基数+比例调整、6其他调整 */
      adjSituType: string;

      /** 金额 */
      amount: number;

      /** 金额(不含税) */
      amtNoTax: string;

      /** 附加税费 */
      atr: string;

      /** 基数绑定级次 */
      baseBindingLevel: string;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 账单起始月 */
      billStartMonth: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** 年缴计算顺序 1:先计算结果再乘以12；2:基数乘以12再计算结果  */
      calculationOrder: string;

      /** 类型 */
      category: string;

      /** 收费结束时间 */
      chargeEndDate: string;

      /** 缴费频率 */
      chargeRate: string;

      /** 收费起始时间 */
      chargeStartDate: string;

      /** cityId */
      cityId: number;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 套餐id */
      comboId: string;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** 企业附加金额 */
      eAdditionalAmt: number;

      /** 企业金额 */
      eAmt: number;

      /** 企业基数 */
      eBase: number;

      /** eBillTemplt */
      eBillTemplt: string;

      /** 企业账单模板id */
      eBillTempltId: string;

      /** 企业计算方法:4四舍五入,9见零进整,3截位,0先四舍五入再见零进整,8先截位再见零见整 */
      eCalculationMethod: string;

      /** 企业收费月,月收:, 两月收:数月:偶数月.,三月收:,4,7,10;,5,8,11;,6,9,12.,半年收:1,7;2,8;3,9;4,10;5,11;6,12,年收:无 */
      eFeeMonth: string;

      /** 收费模板名 */
      eFeeTemplt: string;

      /** 企业收费模板id */
      eFeeTempltId: string;

      /** 企业频率,1:月收、2:两月收、3:三月收、6:半年收、12:年收 */
      eFrequency: string;

      /** 企业最高比例 */
      eMaxRatio: number;

      /** 企业最低比例 */
      eMinRatio: number;

      /** 企业提前几个月收,默认为0，选项0-3 */
      eMonthInAdvance: string;

      /** 企业精确值0：0位小数1：1位小数2：2位小数5： 精确值 */
      ePrecision: string;

      /** 企业比例 */
      eRatio: number;

      /** 企业比例步长 */
      eRatioStep: number;

      /** 费用段历史id */
      empFeeHisId: string;

      /** 费用段id */
      empFeeId: string;

      /** 费用段操作id */
      empFeeOprId: string;

      /** 员工入离职id */
      empHireSepId: string;

      /** 员工id */
      empId: string;

      /** 外部供应商账单起始月 */
      exBillStartMonth: string;

      /** 供应商收费月 */
      exFeeMonth: string;

      /** 供应商收费模板名 */
      exFeeTemplt: string;

      /** 外部供应商收费模板id */
      exFeeTempltId: string;

      /** 导入类型,扩充使用 */
      expType: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** 是否一次性付费 */
      isOneTimePay: string;

      /** 是否显示 1:是0:否 */
      isShow: string;

      /** 是否更新月度表1:是0:否 */
      isUptFeeMon: string;

      /** 是否薪资查询 */
      isWageQuery: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** 原金额 */
      oldAmount: string;

      /** 原账单起始月 */
      oldBillStartMonth: string;

      /** 个人附加金额 */
      pAdditionalAmt: number;

      /** 个人金额 */
      pAmt: number;

      /** 个人基数 */
      pBase: number;

      /** pBillTemplt */
      pBillTemplt: string;

      /** 个人部分账单模板id */
      pBillTempltId: string;

      /** 个人计算方式:4四舍五入,9见零进整,3截位,0先四舍五入再见零进整,8先截位再见零见整 */
      pCalculationMethod: string;

      /** 个人收费月,月收:, 两月收:数月:偶数月.,三月收:,4,7,10;,5,8,11;,6,9,12.,半年收:1,7;2,8;3,9;4,10;5,11;6,12,年收:无 */
      pFeeMonth: string;

      /** 收费模板名 */
      pFeeTemplt: string;

      /** 个人收费模板id */
      pFeeTempltId: string;

      /** 个人频率,1:月收、2:两月收、3:三月收、6:半年收、12:年收 */
      pFrequency: string;

      /** 个人最高比例 */
      pMaxRatio: number;

      /** 个人最低比例 */
      pMinRatio: number;

      /** 个人提前几个月收,提前几个月收,默认为0，选项0-3 */
      pMonthInAdvance: string;

      /** 个人精度0：0位小数1：1位小数2：2位小数5： 精确值 */
      pPrecision: string;

      /** 个人比例 */
      pRatio: number;

      /** 个人比例步长 */
      pRatioStep: number;

      /** 支付频率1:月缴,2季度缴,3年缴(不足一年按年缴),4年缴(不足一年按月缴) */
      payFrequency: string;

      /** 支付最后服务年月 */
      payLastServiceMonth: string;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 产品id */
      productId: string;

      /** 产品名称 */
      productName: string;

      /** 产品比例id */
      productRatioId: string;

      /** 产品比例名称 */
      productRatioName: string;

      /** 产品类型id */
      productTypeId: number;

      /** 供应商id */
      providerId: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** 报价单id */
      quotationId: string;

      /** 报价单子项id */
      quotationItemId: string;

      /** 应收金额 */
      receivableAmt: number;

      /** 应收几个月 */
      receivableMonth: string;

      /** 备注 */
      remark: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** 社保组id */
      ssGroupId: string;

      /** 社保组名称 */
      ssGroupName: string;

      /** 社保福利包id */
      ssWelfarePkgId: string;

      /** 社保福利包名称 */
      ssWelfarePkgName: string;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** 标签 */
      tag: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;

      /** 增值税 */
      vat: string;

      /** 增值税率 */
      vatr: string;

      /** 实收金额 */
      verifyAmt: number;

      /** 实收金额(不含税) */
      verifyAmtNoTax: string;

      /** 实收金额增值税 */
      verifyAmtVat: string;
    }

    export class EmployeeHireSep {
      /** 开户人姓名 */
      accountEmployeeName: string;

      /** 实际工作地 */
      actualWorkLoc: string;

      /** add */
      add: boolean;

      /** 增员确认人 */
      addConfirmBy: string;

      /** 增员确认时间 */
      addConfirmDate: string;

      /** 增员过程 */
      addConfirmPro: string;

      /** 增员状态:1增员未提交 20 等待接单方确认 22 接单方挂起 30 等待派单方确认40 增员完成 */
      addConfirmStatus: string;

      /** 增员状态名称 */
      addConfirmStatusName: string;

      /** 增员接单确认时间 */
      addPerfectBy: string;

      /** 增员接单确认人 */
      addPerfectDate: string;

      /** 增员原因:1.正常增员2.从竞争对手中转入 3.转移 4不详 */
      addReason: string;

      /** 增员备注 */
      addRemark: string;

      /** 年龄 */
      age: string;

      /** 变更确认人 */
      alterConfirmBy: string;

      /** 变更确认时间 */
      alterConfirmDate: string;

      /** 变更确认过程 */
      alterConfirmPro: string;

      /** 变更接单确认人 */
      alterPerfectBy: string;

      /** 变更接单确时间 */
      alterPerfectDate: string;

      /** 变更备注 */
      alterRemark: string;

      /** 变更状态:1变更未提交 20 等待接单方确认 25:派单方驳回 30 等待派单方确认 40 变更最终确认 */
      alterStatus: string;

      /** 变更状态名称 */
      alterStatusName: string;

      /** 大区类型 */
      areaType: string;

      /** 大区类型名称 */
      areaTypeName: string;

      /** 接单城市id */
      assigneeCityId: string;

      /** 接单客服name */
      assigneeCs: string;

      /** 接单客服 */
      assigneeCsId: string;

      /** 接单方 */
      assigneeProvider: string;

      /** 接单方 */
      assigneeProviderId: string;

      /** 派单客服name */
      assignerCs: string;

      /** 派单客服 */
      assignerCsId: string;

      /** 派单方 */
      assignerProvider: string;

      /** 派单方 */
      assignerProviderId: string;

      /** 派单类型1 执行单2 协调单3 收集单 */
      assignmentType: string;

      /** 关联状态 */
      associationStatus: string;

      /** 银行卡号 */
      bankAcct: string;

      /** 银行卡更新人 */
      bankCardUpdateBy: string;

      /** 银行卡更新时间 */
      bankCardUpdateDt: string;

      /** baseInfo主键 */
      baseInfoId: string;

      /** 批次号,用于生成社保服务信息 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 账单模板id */
      billTempltId: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** 类型 */
      category: string;

      /** 离职证明电子版本 */
      certificateSpId: string;

      /** certificateStatusName */
      certificateStatusName: string;

      /** 编辑方式1:增员完成提交变更 2:增员确认了,只变更报价3:增员确认了,变更社保公积金 ,接单方是内部供应商4:增员确认了,变更社保公积金	  ,接单方是外部供应商 */
      changeMethod: string;

      /** 收费截至日期 */
      chargeEndDate: string;

      /** 城市id */
      cityId: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 商保订单状态 */
      commInsurStatus: string;

      /** 商保订单状态name */
      commInsurStatusName: string;

      /** 确认备注 */
      confirmRemark: string;

      /** 联系电话1，电话 */
      contactTel1: string;

      /** 联系电话2，手机 */
      contactTel2: string;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 合同编号 */
      contractCode: string;

      /** 合同id */
      contractId: string;

      /** 合同名称 */
      contractName: string;

      /** contractStartDate */
      contractStartDate: string;

      /** contractStopDate */
      contractStopDate: string;

      /** 法人单位id */
      corporationId: string;

      /** 法人单位名称 */
      corporationName: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** 客户编号 */
      custCode: string;

      /** 客户id */
      custId: string;

      /** 客户方内部编号 */
      custInternalNum: string;

      /** 客户姓名 */
      custName: string;

      /** 缴费实体id */
      custPayEntityId: number;

      /** 缴费实体 */
      custPayEntityName: string;

      /** 客户类型 */
      custType: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** 客户规模 */
      customerSize: string;

      /** 类型: 1,正常 2,大客户  */
      dataType: number;

      /** 申报工资 */
      decSalary: string;

      /** decSalaryRemark */
      decSalaryRemark: string;

      /** del */
      del: boolean;

      /** email */
      email: string;

      /** 客户端增员ID */
      empAddId: string;

      /** 客户端变更ID */
      empAlterId: string;

      /** feeId数组 */
      empFeeIdArray: string;

      /** 历史表主键 */
      empHireSepHisId: string;

      /** 员工入离职id */
      empHireSepId: string;

      /** 入职主记录ID */
      empHiresepMainId: string;

      /** 员工id */
      empId: string;

      /** 停缴id */
      empStopId: string;

      /** 停缴处理进程 0: 停缴未启动 1：停缴处理中 2：停缴完成 */
      empStopProcessState: string;

      /** 人员分类: 1 派遣 2 代理 3 BPO人员 4 外包 */
      empType: string;

      /** 人员分类: 1 派遣 2 代理 3 BPO人员 4 外包 */
      empTypeId: number;

      /** 人员分类 */
      empTypeName: string;

      /** 唯一号 */
      employeeCode: string;

      /** 费用段列表 */
      employeeFeeList: Array<defs.ec.EmployeeFee>;

      /** 雇员姓名 */
      employeeName: string;

      /** 雇员状态 */
      employeeStatus: string;

      /** endIndex */
      endIndex: number;

      /** enhancedAgent */
      enhancedAgent: string;

      /** enhancedAgentName */
      enhancedAgentName: string;

      /** 外部供应商收费模板 */
      exFeeTemplt: string;

      /** 外部供应商账单id */
      exFeeTempltId: string;

      /** exQuotationFeeList */
      exQuotationFeeList: Array<defs.ec.EmployeeFee>;

      /** 导入类型,扩充使用 */
      expType: string;

      /** 收费月,月收:, 两月收:数月:偶数月.,三月收:,4,7,10;,5,8,11;,6,9,12.,半年收:1,7;2,8;3,9;4,10;5,11;6,12,年收:无 */
      feeMonth: string;

      /** 收费模板名称 */
      feeTemplt: string;

      /** 收费模板id */
      feeTempltId: string;

      /** 档案柜编号 */
      fileCabCode: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** 文件夹编号 */
      folderCode: string;

      /** 频率,1:月收、2:两月收、3:三月收、6:半年收、12:年收 */
      frequency: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** 是否关联订单 */
      hasAssociations: number;

      /** 入职竞争对手 */
      hireCompetitor: string;

      /** 入职时间 */
      hireDt: string;

      /** 入职时间止 */
      hireEndDt: string;

      /** 入职报价单 */
      hireQuotationId: string;

      /** 入职备注 */
      hireRemark: string;

      /** 入职时间起 */
      hireStartDt: string;

      /** 证件号码 */
      idCardNum: string;

      /** 证件类型 */
      idCardType: string;

      /** 接单客服name */
      idCardTypeName: string;

      /** inId */
      inId: string;

      /** 内外部类型(1内部2外部3全部) */
      innerType: string;

      /** 是否需要实做 */
      isAddProcess: string;

      /** 增员是否需要实做 */
      isAddProcessName: string;

      /** 是否需要签订劳动合同 */
      isArchive: string;

      /** 是否归档名称 */
      isArchiveName: string;

      /** 银行卡是否上传 */
      isBankCardUpload: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** 是否入职呼叫 */
      isHireCall: string;

      /** 是否入职呼叫名称 */
      isHireCallName: string;

      /** 身份证是否上传 */
      isIDCardUpload: string;

      /** 是否单立户1 是0 否 */
      isIndependent: string;

      /** 劳动合同是否上传 */
      isLaborContractUpload: string;

      /** 是否需要签订劳动合同 */
      isNeedSign: string;

      /** 是否需要实做 */
      isReduceProcess: string;

      /** 是否退费 0否  1是 */
      isRefund: string;

      /** isRelated */
      isRelated: string;

      /** 是否集中一地投保 */
      isSameInsur: string;

      /** 是否集中一地投保中文 */
      isSameInsurName: string;

      /** 是否离职外呼1 呼叫中心通知  2 客服自行通知  3 不需通知  客户代通知 */
      isSepCall: string;

      /** 是否离职呼叫名 */
      isSepCallName: string;

      /** 是否有统筹医疗 */
      isThereACoordinateHealth: string;

      /** 是否有统筹医疗名称 */
      isThereACoordinateHealthText: string;

      /** 是否有社保卡 */
      isThereSsCard: string;

      /** 是否有社保卡名称 */
      isThereSsCardText: string;

      /** 是否薪资查询 */
      isWageQuery: string;

      /** 离职动态模板json */
      jsonStr: Array<object>;

      /** 劳动关系单位 */
      laborRelationUnit: string;

      /** 责任客服 */
      liabilityCs: string;

      /** 材料列表 */
      materialList: Array<defs.ec.Material>;

      /** materialSignStatus */
      materialSignStatus: number;

      /** materialSignStatusName */
      materialSignStatusName: string;

      /** 离职材料电子版本id */
      materialSpId: string;

      /** materialStatusName */
      materialStatusName: string;

      /** 模拟人 */
      mimicBy: string;

      /** 操作方式  单立户1、大户2 */
      modeOfOperation: string;

      /** 提前几个月收,默认为0，选项0-3 */
      monthInAdvance: string;

      /** 后指针 */
      nextPointer: string;

      /** noChange */
      noChange: boolean;

      /** 非社保列表 */
      nonSsGroupList: Array<defs.ec.EmployeeFee>;

      /** 银行名称 */
      openBankName: string;

      /** 页数 */
      pageNum: number;

      /** 每页记录数,默认65536条 */
      pageSize: number;

      /** 挂起原因 */
      pendingReason: string;

      /** 挂起原因中文 */
      pendingReasonName: string;

      /** 人员分类id */
      personCategoryId: string;

      /** 职位id */
      positionId: string;

      /** 前指针 */
      prevPointer: string;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 流程实例化id */
      processInsId: string;

      /** 供应商编码 */
      providerCode: string;

      /** 供应商客服 */
      providerCs: string;

      /** 供应商客服id */
      providerCsId: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 供应商类型1内部2外部 */
      providerType: string;

      /** 代理人 */
      proxyBy: string;

      /** 供应商集团id */
      prvdGroupId: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** 供应商集团 */
      prvdGroupName: string;

      /** 离职材料签订形式 */
      quitSignType: number;

      /** quitSignTypeName */
      quitSignTypeName: string;

      /** 电子离职合同任务主键 */
      quitTaskId: number;

      /** 报价单编码 */
      quotationCode: string;

      /** 报价单名称 */
      quotationName: string;

      /** 减少详细原因 */
      reduceDetailReason: string;

      /** 减原详细原因名称 */
      reduceDetailReasonName: string;

      /** 客户端减员ID */
      reduceId: string;

      /** 减少原因:1正常减员2转至竞争对手3服务原因4准备撤单和已报撤单正在减员中5客户原因6其他原因7变更合同名称8变更服务项目9易才内部转单 */
      reduceReason: string;

      /** 减员原因名称 */
      reduceReasonName: string;

      /** 参考日期，页面传入 */
      referDate: string;

      /** 备注 */
      remark: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** riskPremiumRatio */
      riskPremiumRatio: string;

      /** riskSharingRatio */
      riskSharingRatio: string;

      /** 报入职人员id */
      rptHireBy: string;

      /** 报入职人 */
      rptHireByName: string;

      /** 报入职时间 */
      rptHireDt: string;

      /** 报入职日期止 */
      rptHireEndDt: string;

      /** 报入职日期起 */
      rptHireStartDt: string;

      /** 报离职人员id */
      rptSepBy: string;

      /** 报离职人 */
      rptSepByName: string;

      /** 报离职日期 */
      rptSepDt: string;

      /** 报离职日期止 */
      rptSepEndDt: string;

      /** 报离职日期起 */
      rptSepStartDt: string;

      /** 用章对象 */
      sealObject: string;

      /** 用章类型 */
      sealType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** 离职确认人 */
      sepConfirmBy: string;

      /** 离职确认日期 */
      sepConfirmDate: string;

      /** 离职确认历史 */
      sepConfirmHis: string;

      /** 离职确认进程 */
      sepConfirmPro: string;

      /** 离职确认状态: 1离职未提交 20 等待接单方确认 23接单方驳回 30 等待派单方确认40 离职完成 */
      sepConfirmStatus: string;

      /** 离职状态名称 */
      sepConfirmStatusName: string;

      /** 离职详细原因 */
      sepDetailReason: string;

      /** 离职详细原因名称 */
      sepDetailReasonName: string;

      /** 离职日期 */
      sepDt: string;

      /** 离职时间止 */
      sepEndDt: string;

      /** 离职接单确认人 */
      sepPerfectBy: string;

      /** 离职接单确认时间 */
      sepPerfectDate: string;

      /** 离职手续办理状态:0  未完成   1  完成 */
      sepProcessStatus: string;

      /** 离职报价单 */
      sepQuotationId: string;

      /** 离职原因:1 合同到期终止2 试用期解除3 合同主动解除4 死亡5 合同被动解除10其它 */
      sepReason: string;

      /** 离职原因key */
      sepReasonKey: string;

      /** 离职原因名称 */
      sepReasonName: string;

      /** 离职备注 */
      sepRemark: string;

      /** 离职时间止 */
      sepStartDt: string;

      /** 离职导出类型:1离职接单确认,2离职派单确认 */
      sepType: string;

      /** sigle */
      sigle: boolean;

      /** 签约方分公司抬头 */
      signBranchTitle: string;

      /** 签约方分公司抬头id */
      signBranchTitleId: string;

      /** 签约方分公司抬头name */
      signBranchTitleName: string;

      /** 签单供应商 */
      signProvider: string;

      /** 签单方 */
      signProviderId: string;

      /** signStatus */
      signStatus: number;

      /** 短信发送日期 */
      smsSendDt: string;

      /** 短信发送状态: 0未发送, 1成功, 2失败 */
      smsSendStatus: string;

      /** 短信发送状态中文: 未发送, 成功, 失败 */
      smsSendStatusStr: string;

      /** 分拆方分公司:分拆方客服 */
      splitServiceProviderCs: string;

      /** 社保列表 */
      ssGroupList: Array<defs.ec.EmployeeFee>;

      /** 员工社保参与地 */
      ssParticipateLocation: string;

      /** startIndex */
      startIndex: number;

      /** 状态 1入职未生效2在职3离职 */
      status: string;

      /** 状态名称 */
      statusName: string;

      /** 小类名称 */
      subTypeName: string;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** 小合同编号 */
      subcontractCode: string;

      /** 小合同id */
      subcontractId: string;

      /** 小合同名称 */
      subcontractName: string;

      /** 大类名称 */
      superTypeName: string;

      /** 总收费日期 */
      totalFeeDt: string;

      /** eos转移id */
      transferId: number;

      /** 类型:1增员接单完善离职接单确认,2增员派单确认离职派单确认,3变更派单确认 */
      type: number;

      /** 机动分类项目1 */
      type1: string;

      /** 机动分类项目2 */
      type2: string;

      /** 机动分类项目3 */
      type3: string;

      /** 机动分类项目4 */
      type4: string;

      /** 机动分类项目5 */
      type5: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;

      /** uuid */
      uuid: string;
    }

    export class ExportQuery {
      /** 查询条件 */
      condition: object;

      /** 表头字段列表 */
      fieldArr: Array<string>;

      /** 表头字段中文拼接 */
      headStr: string;

      /** 表头字段类型列表 */
      typeArr: Array<string>;
    }

    export class FilterEntity {
      /** add */
      add: boolean;

      /** 增员确认状态 */
      addConfirmStatus: string;

      /** 变更确认状态 */
      alterStatus: string;

      /** 大区类型 */
      areaType: string;

      /** 接单客服id */
      assigneeCsId: string;

      /** 接单方id */
      assigneeProviderId: string;

      /** 派单客服id */
      assignerCsId: string;

      /** 派单方id */
      assignerProviderId: string;

      /** 派单类型1 执行单 2 协调单 3 收集单 */
      assignmentType: string;

      /** 是否关联订单状态 */
      associationStatus: string;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** 产品类型，1社保2公积金 */
      category: string;

      /** 变更方式 */
      changeMethod: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 纯商保状态 */
      commInsurStatus: string;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 合同id */
      contractId: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** 分拆方客服id */
      csId: string;

      /** 客户编码 */
      custCode: string;

      /** 客户id */
      custId: string;

      /** 客户方内部编号 */
      custInternalNum: string;

      /** 客户名称 */
      custName: string;

      /** 缴费实体id */
      custPayEntityId: number;

      /** 缴费实体名称 */
      custPayEntityName: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** 客户规模 1大型，2中型，3小型 */
      customerSize: string;

      /** del */
      del: boolean;

      /** 订单id */
      empHireSepId: string;

      /** 人员分类：1 常规雇员2 委托人事管理 3 BPO人员 */
      empType: string;

      /** 雇员编码 */
      employeeCode: string;

      /** 雇员姓名 */
      employeeName: string;

      /** endIndex */
      endIndex: number;

      /** enhancedAgent */
      enhancedAgent: string;

      /** 导入类型,扩充使用 */
      expType: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** 入职时间 */
      hireDt: string;

      /** 入职结束时间 */
      hireEndDt: string;

      /** 入职起始时间 */
      hireStartDt: string;

      /** 证件号码 */
      idCardNum: string;

      /** 证件类型 */
      idCardType: string;

      /** inId */
      inId: string;

      /** isAddProcess */
      isAddProcess: number;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** 是否入职呼叫 */
      isHireCall: string;

      /** isReduceProcess */
      isReduceProcess: number;

      /** 转移前后城市是否相同 */
      isSameTransferCity: number;

      /** 是否薪资查询 */
      isWageQuery: string;

      /** materialSignStatus */
      materialSignStatus: number;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** 页数 */
      pageNum: number;

      /** 每页记录数,默认65536条 */
      pageSize: number;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 供应商编码 */
      providerCode: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** 供应商集团编号 */
      prvdGroupCode: string;

      /** 供应商集团id */
      prvdGroupId: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** 供应商集团名称 */
      prvdGroupName: string;

      /** reduceReason */
      reduceReason: number;

      /** 参考日期 */
      referDate: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 报入职时间 */
      rptHireDt: string;

      /** 报入职时间结束 */
      rptHireEndDt: string;

      /** 报入职时间开始 */
      rptHireStartDt: string;

      /** 报离职时间 */
      rptSepDt: string;

      /** 报离职结束日期 */
      rptSepEndDt: string;

      /** 报离职开始时间 */
      rptSepStartDt: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** 离职确认状态 */
      sepConfirmStatus: string;

      /** 离职开始时间 */
      sepDt: string;

      /** 离职结束时间 */
      sepEndDt: string;

      /** sepReason */
      sepReason: number;

      /** 离职开始时间 */
      sepStartDt: string;

      /** 离职导出类型:1离职接单确认,2离职派单确认 */
      sepType: number;

      /** 签单方id */
      signProviderId: string;

      /** 短信发送截止日期 */
      smsSendEndDt: string;

      /** 短信发送开始日期 */
      smsSendStartDt: string;

      /** 短信发送状态: 0未发送,1 成功,2 失败 */
      smsSendStatus: string;

      /** 是否加分拆方条件 */
      splitFlag: number;

      /** startIndex */
      startIndex: number;

      /** 状态:1入职未生效2在职3离职 */
      status: string;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** 小合同id */
      subcontractId: string;

      /** 小合同名字 */
      subcontractName: string;

      /** 分拆方分公司id */
      svcProviderId: string;

      /** 转移状态:1提交转移、2转移处理中、3转移成功、4转移失败 */
      transferStatus: number;

      /** 类型，2增员=30离职=1,3增员40离职1or40变更30 */
      type: number;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;
    }

    export class Material {
      /** add */
      add: boolean;

      /** 批次号,用于备份 */
      batchId: string;

      /** 账单表别名,控制客户权限用 */
      billAlias: string;

      /** 财务大类 */
      bizCategory: string;

      /** 业务类型,控制小合同权限用 */
      bizmanType: string;

      /** sys_branch_title别名,控制合同权限用 */
      branchTitleAlias: string;

      /** clientOperation */
      clientOperation: number;

      /** flex是否行编号 */
      clientRowSeq: number;

      /** flex是否选择 */
      clientSelected: boolean;

      /** 合同表别名,控制合同权限用 */
      contractAlias: string;

      /** 创建人 */
      createBy: string;

      /** createBy2 */
      createBy2: string;

      /** createByEx */
      createByEx: string;

      /** 创建日期 */
      createDt: string;

      /** 客户表别名,控制客户权限用 */
      customerAlias: string;

      /** del */
      del: boolean;

      /** 导入类型,扩充使用 */
      expType: string;

      /** filterByAuthNum */
      filterByAuthNum: string;

      /** 提供查询是做为排除条件使用 */
      filterId: string;

      /** funBtnActiveStr */
      funBtnActiveStr: string;

      /** 登录人所属分公司,控制小合同权限用 */
      governingBranch: string;

      /** inId */
      inId: string;

      /** 是否账单查询 */
      isBillQuery: string;

      /** flex是否变化 */
      isChanged: boolean;

      /** 删除标记 */
      isDeleted: string;

      /** 是否薪资查询 */
      isWageQuery: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column HS_MATERIAL.MATERIAL_ID           ibatorgenerated Wed Nov 30 13:28:25 CST 2011 */
      materialId: number;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column HS_MATERIAL.MATERIAL_NAME           ibatorgenerated Wed Nov 30 13:28:25 CST 2011 */
      materialName: string;

      /** 模拟人 */
      mimicBy: string;

      /** noChange */
      noChange: boolean;

      /** 流程审批角色名字 */
      processAprRoleName: string;

      /** 供应商集团权限添加 */
      providerIdAlias: string;

      /** 代理人 */
      proxyBy: string;

      /** prvdGroupIdAlias */
      prvdGroupIdAlias: string;

      /** This field was generated by Apache iBATIS ibator.      This field corresponds to the database column HS_MATERIAL.REMARK           ibatorgenerated Wed Nov 30 13:28:25 CST 2011 */
      remark: string;

      /** 卡纯代发人员,默认过滤 */
      restrictPure: string;

      /** 卡权限 */
      restrictType: string;

      /** 查询时,是否带权限,空表示带权限 赋值了表示不带权限,否则不带 */
      selByAuth: string;

      /** 获取前台勾选key的字符串 */
      selectKeyStr: string;

      /** 小合同表别名,控制小合同权限用 */
      subcontractAlias: string;

      /** 修改人 */
      updateBy: string;

      /** 修改日期 */
      updateDt: string;

      /** upt */
      upt: boolean;

      /** 用户id,控制小合同权限用 */
      userId: string;

      /** 用户管理用户表别名,控制客户权限用 */
      userManageUserAlias: string;
    }

    export class Page {
      /** currentPage */
      currentPage: number;

      /** currentPageNo */
      currentPageNo: number;

      /** data */
      data: Array<object>;

      /** pageSize */
      pageSize: number;

      /** result */
      result: Array<object>;

      /** start */
      start: number;

      /** totalCount */
      totalCount: number;

      /** totalPage */
      totalPage: number;

      /** totalPageCount */
      totalPageCount: number;
    }
  }
}

declare namespace API {
  export namespace ec {
    /**
     * 每日电子合同信息同步
     */
    export namespace dailyFetchEleContract {
      /**
        * 每日同步
每日同步
        * /dailyFetchEleContract/generateEleContract
        */
      export namespace generateEleContract {
        export class Params {
          /** date */
          date: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.ec.CommonResponse<boolean>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 电子业务
     */
    export namespace ecBusiness {
      /**
        * 作废电子业务
作废电子业务
        * /ecBusiness/cancelEcEleBusiness
        */
      export namespace cancelEcEleBusiness {
        export class Params {}

        export type Response<T> = defs.ec.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.ec.EcBusinessDto,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.ec.EcBusinessDto,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 删除电子业务
删除电子业务
        * /ecBusiness/deleteEcEleBusiness
        */
      export namespace deleteEcEleBusiness {
        export class Params {}

        export type Response<T> = defs.ec.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.ec.EcBusinessDto,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.ec.EcBusinessDto,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 下载电子业务
下载电子业务
        * /ecBusiness/exportEleBusinessFile
        */
      export namespace exportEleBusinessFile {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: any;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.ec.EcBusinessDto,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.ec.EcBusinessDto,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 导出
导出
        * /ecBusiness/exportFile
        */
      export namespace exportFile {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: any;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.ec.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.ec.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 批量导入电子业务信息 下载模板
批量导入电子合同信息 下载模板
        * /ecBusiness/generateUploadTemplate
        */
      export namespace generateUploadTemplate {
        export class Params {
          /** addOrModify */
          addOrModify?: number;
          /** sealType */
          sealType?: number;
          /** signProcessId */
          signProcessId?: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: any;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 批量导入电子业务信息 下载模板
批量导入电子合同信息 下载模板
        * /ecBusiness/generateUploadTemplate
        */
      export namespace postGenerateUploadTemplate {
        export class Params {
          /** addOrModify */
          addOrModify?: number;
          /** sealType */
          sealType?: number;
          /** signProcessId */
          signProcessId?: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: any;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询电子业务列表
查询电子业务列表
        * /ecBusiness/getEcBusinessPage
        */
      export namespace getEcBusinessPage {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.ec.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.ec.EcBusinessQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.ec.EcBusinessQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据条件得到电子业务信息的分页查询结果
根据条件得到电子业务信息的分页查询结果
        * /ecBusiness/getEleBusinessInfo
        */
      export namespace getEleBusinessInfo {
        export class Params {}

        export type Response<T> = defs.ec.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.ec.EcEleBusinessQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.ec.EcEleBusinessQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 预览电子业务
预览电子业务
        * /ecBusiness/getViewUrl
        */
      export namespace getViewUrl {
        export class Params {}

        export type Response<T> = defs.ec.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.ec.EcBusinessDto,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.ec.EcBusinessDto,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 批量导入电子业务办理 导入文件接口
批量导入电子业务办理 导入文件接口
        * /ecBusiness/importDataEcBusiness
        */
      export namespace importDataEcBusiness {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: string;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.ec.EcBusinessDto,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.ec.EcBusinessDto,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 新增电子业务
新增电子业务
        * /ecBusiness/insertEcBusiness
        */
      export namespace insertEcBusiness {
        export class Params {}

        export type Response<T> = defs.ec.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.ec.EcBusinessDto,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.ec.EcBusinessDto,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 撤销状态变更(重新发起电子业务)
撤销状态变更(重新发起电子业务)
        * /ecBusiness/reEleBusinessSend
        */
      export namespace reEleBusinessSend {
        export class Params {}

        export type Response<T> = defs.ec.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.ec.EcBusinessDto,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.ec.EcBusinessDto,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 撤销状态变更（单修改状态为已撤销）
撤销状态变更（单修改状态为已撤销）
        * /ecBusiness/returnEcEleBusiness
        */
      export namespace returnEcEleBusiness {
        export class Params {}

        export type Response<T> = defs.ec.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.ec.EcBusinessDto,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.ec.EcBusinessDto,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 电子业务盖章
电子业务盖章
        * /ecBusiness/signEcEleBusiness
        */
      export namespace signEcEleBusiness {
        export class Params {}

        export type Response<T> = defs.ec.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.ec.EcBusinessDto,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.ec.EcBusinessDto,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 发起电子业务批量
发起电子业务批量
        * /ecBusiness/startEcEleBusiness
        */
      export namespace startEcEleBusiness {
        export class Params {}

        export type Response<T> = defs.ec.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.ec.EcBusinessDto,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.ec.EcBusinessDto,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 修改电子业务
修改电子业务
        * /ecBusiness/updateEcBusiness
        */
      export namespace updateEcBusiness {
        export class Params {}

        export type Response<T> = defs.ec.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.ec.EcBusinessDto,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.ec.EcBusinessDto,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 退回修改电子业务
退回修改电子业务
        * /ecBusiness/updateEcEleBusiness
        */
      export namespace updateEcEleBusiness {
        export class Params {}

        export type Response<T> = defs.ec.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.ec.EcBusinessDto,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.ec.EcBusinessDto,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 填写电子业务
填写电子业务
        * /ecBusiness/writeEcEleBusiness
        */
      export namespace writeEcEleBusiness {
        export class Params {}

        export type Response<T> = defs.ec.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.ec.EcBusinessDto,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.ec.EcBusinessDto,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 批量上传离职合同附件
     */
    export namespace ecQuitFileBatch {
      /**
        * 上传附件
上传附件,要传batchId，fileId
        * /ecQuitFileBatch/batchUploadFile
        */
      export namespace batchUploadFile {
        export class Params {}

        export type Response<T> = defs.ec.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: Array<defs.ec.EcQuitFileBatchItem>,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Array<defs.ec.EcQuitFileBatchItem>,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 导出上传结果
导出上传结果
        * /ecQuitFileBatch/fileBatchReport
        */
      export namespace fileBatchReport {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: any;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.ec.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.ec.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 根据quitTaskId查上传记录
根据quitTaskId查上传记录,传quitTaskId
        * /ecQuitFileBatch/getUploadFileList
        */
      export namespace getUploadFileList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.ec.EcQuitFileBatchItem;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.ec.EcQuitFileBatchItem,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.ec.EcQuitFileBatchItem,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 生成上传批次ID
生成上传批次ID，传fileCount
        * /ecQuitFileBatch/insertEcQuitFileBatch
        */
      export namespace insertEcQuitFileBatch {
        export class Params {}

        export type Response<T> = defs.ec.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.ec.EcQuitFileBatch,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.ec.EcQuitFileBatch,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 上传批次明细查询
上传批次明细查询，传batchId
        * /ecQuitFileBatch/selectFileBatchItemList
        */
      export namespace selectFileBatchItemList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.ec.CommonResponse<defs.ec.Page>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.ec.EcQuitFileBatch,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.ec.EcQuitFileBatch,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 上传批次查询
上传批次查询
        * /ecQuitFileBatch/selectFileBatchList
        */
      export namespace selectFileBatchList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.ec.CommonResponse<defs.ec.Page>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.ec.EcQuitFileBatch,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.ec.EcQuitFileBatch,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 单个上传附件-用于劳动合同其它地方上传附件关联日志
上传附件,传fileId,quitTaskId或empHireSepId
        * /ecQuitFileBatch/uploadFile
        */
      export namespace uploadFile {
        export class Params {}

        export type Response<T> = defs.ec.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.ec.EcQuitFileBatchItem,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.ec.EcQuitFileBatchItem,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * Ec SSO Controller
     */
    export namespace ecSso {
      /**
        * 根据userId获取token信息
根据userId获取token信息
        * /ecsso/token
        */
      export namespace token {
        export class Params {}

        export type Response<T> = defs.ec.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 电子签员工信息同步
     */
    export namespace ecUser {
      /**
        * 新增
新增
        * /ecUserSync/addEcUser
        */
      export namespace addEcUser {
        export class Params {}

        export type Response<T> = defs.ec.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.ec.EcUserSync,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.ec.EcUserSync,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 批量删除
批量删除
        * /ecUserSync/batchDeleteEcUser
        */
      export namespace batchDeleteEcUser {
        export class Params {
          /** userSyncIds */
          userSyncIds: string;
        }

        export type Response<T> = defs.ec.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 批量同步
批量同步
        * /ecUserSync/batchSyncEcUser
        */
      export namespace batchSyncEcUser {
        export class Params {
          /** userSyncIds */
          userSyncIds: string;
        }

        export type Response<T> = defs.ec.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 单个删除
单个删除
        * /ecUserSync/deleteEcUser
        */
      export namespace deleteEcUser {
        export class Params {}

        export type Response<T> = defs.ec.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.ec.EcUserSync,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.ec.EcUserSync,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询
查询
        * /ecUserSync/getEcUserSyncList
        */
      export namespace getEcUserSyncList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.ec.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.ec.EcUserSync,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.ec.EcUserSync,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }

    /**
     * 电子离职
     */
    export namespace quitTask {
      /**
        * 删除离职材料
删除离职材料
        * /quitTask/deleteFileBatchItem
        */
      export namespace deleteFileBatchItem {
        export class Params {
          /** empHireSepId */
          empHireSepId: number;
        }

        export type Response<T> = defs.ec.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 批量删除离职证明
批量删除离职证明
        * /quitTask/deleteQuitCert
        */
      export namespace deleteQuitCert {
        export class Params {}

        export type Response<T> = defs.ec.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: Array<defs.ec.EcQuitAll>,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Array<defs.ec.EcQuitAll>,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 批量删除离职任务
批量删除离职任务
        * /quitTask/deleteQuitTask
        */
      export namespace deleteQuitTask {
        export class Params {}

        export type Response<T> = defs.ec.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: Array<defs.ec.EcQuitAll>,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Array<defs.ec.EcQuitAll>,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 导出离职任务
导出离职任务
        * /quitTask/downloadQuitTask
        */
      export namespace toDownLoad {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: any;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.ec.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.ec.ExportQuery,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 全部电子业务办理签章材料名称
全部电子业务办理签章材料名称
        * /quitTask/getAllEleSignProcessDropdownList
        */
      export namespace getAllEleSignProcessDropdownList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.ec.CommonResponse<Array<defs.ec.BaseEntity>>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.ec.EmployeeHireSep,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.ec.EmployeeHireSep,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 法人单位名称
法人单位名称
        * /quitTask/getCorporationDropdownList
        */
      export namespace getCorporationDropdownList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.ec.CommonResponse<Array<defs.ec.BaseEntity>>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.ec.EmployeeHireSep,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.ec.EmployeeHireSep,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取默认法人单位名称
获取默认法人单位名称
        * /quitTask/getDefaultCorp
        */
      export namespace getDefaultCorp {
        export class Params {
          /** empHireSepId */
          empHireSepId: string;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.ec.CommonResponse<defs.ec.BaseEntity>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          params: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查询离职任务
查询离职任务
        * /quitTask/getEcQuitAll
        */
      export namespace getEcQuitAll {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.ec.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.ec.EcQuitAll,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.ec.EcQuitAll,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 电子业务办理法人单位名称
电子业务办理法人单位名称
        * /quitTask/getEleCorporationDropdownList
        */
      export namespace getEleCorporationDropdownList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.ec.CommonResponse<Array<defs.ec.BaseEntity>>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.ec.EmployeeHireSep,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.ec.EmployeeHireSep,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 电子业务办理签章材料名称
电子业务办理签章材料名称
        * /quitTask/getEleSignProcessDropdownList
        */
      export namespace getEleSignProcessDropdownList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.ec.CommonResponse<Array<defs.ec.BaseEntity>>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.ec.EmployeeHireSep,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.ec.EmployeeHireSep,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 开具离职证明合同信息
开具离职证明合同信息 传EcQuitAll
        * /quitTask/getLaborContract
        */
      export namespace getLaborContract {
        export class Params {}

        export type Response<T> = defs.ec.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.ec.EcQuitAll,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.ec.EcQuitAll,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 电子业务根据上下岗下载材料
电子业务根据上下岗下载材料
        * /quitTask/getLaborContractMinioPath
        */
      export namespace getLaborContractMinioPath {
        export class Params {}

        export type Response<T> = defs.ec.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.ec.EmployeeHireSep,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.ec.EmployeeHireSep,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 获取文件是否存在
获取文件是否存在
        * /quitTask/getResignFileByOrderId
        */
      export namespace getResignFileByOrderId {
        export class Params {
          /** empHireSepId */
          empHireSepId: number;
        }

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.ec.EcQuitFileBatchItem;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Params,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 离职材料版本名称
离职材料版本名称
        * /quitTask/getSignProcessDropdownList
        */
      export namespace getSignProcessDropdownList {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.ec.CommonResponse<Array<defs.ec.BaseEntity>>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.ec.EmployeeHireSep,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.ec.EmployeeHireSep,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 电子离职证明版本名称
电子离职证明版本名称
        * /quitTask/getSignProcessDropdownListForCert
        */
      export namespace getSignProcessDropdownListForCert {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.ec.CommonResponse<Array<defs.ec.BaseEntity>>;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.ec.EmployeeHireSep,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.ec.EmployeeHireSep,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 预览电子离职材料
预览电子离职材料
        * /quitTask/getViewUrl
        */
      export namespace getViewUrl {
        export class Params {}

        export type Response<T> = defs.ec.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.ec.EcQuitAll,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.ec.EcQuitAll,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 新增离职任务并提交
新增离职任务并提交
        * /quitTask/insertAndSumitQuitTask
        */
      export namespace insertAndSumitQuitTask {
        export class Params {}

        export type Response<T> = defs.ec.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.ec.BaseEntity,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.ec.BaseEntity,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 新增离职任务
新增离职任务
        * /quitTask/insertQuitTask
        */
      export namespace insertQuitTask {
        export class Params {}

        export type Response<T> = defs.ec.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.ec.BaseEntity,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.ec.BaseEntity,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 作废电子离职证明
作废电子离职证明
        * /quitTask/invalidEleResignCertificate
        */
      export namespace invalidEleResignCertificate {
        export class Params {}

        export type Response<T> = defs.ec.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.ec.EcQuitAll,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.ec.EcQuitAll,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 作废电子离职材料
作废电子离职材料
        * /quitTask/invalidEleResignMaterial
        */
      export namespace invalidEleResignMaterial {
        export class Params {}

        export type Response<T> = defs.ec.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.ec.EcQuitAll,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.ec.EcQuitAll,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 撤销状态变更
撤销状态变更
        * /quitTask/reSendEleResignMaterial
        */
      export namespace reSendEleResignMaterial {
        export class Params {}

        export type Response<T> = defs.ec.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.ec.EcQuitAll,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.ec.EcQuitAll,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 撤销电子离职材料
撤销电子离职材料
        * /quitTask/revokeEleResignMaterial
        */
      export namespace revokeEleResignMaterial {
        export class Params {}

        export type Response<T> = defs.ec.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.ec.EcQuitAll,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.ec.EcQuitAll,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 填写客户版离职证明
填写客户版离职证明
        * /quitTask/saveEleResignCertificate
        */
      export namespace saveEleResignCertificate {
        export class Params {}

        export type Response<T> = defs.ec.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.ec.EcQuitAll,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.ec.EcQuitAll,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 填写客户版离职材料信息
填写客户版离职材料信息
        * /quitTask/saveEleResignMaterial
        */
      export namespace saveEleResignMaterial {
        export class Params {}

        export type Response<T> = defs.ec.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.ec.EcQuitAll,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.ec.EcQuitAll,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查看电子离职证明
查看电子离职证明，传quitTaskId
        * /quitTask/selectCertificate
        */
      export namespace selectCertificate {
        export class Params {}

        export type Response<T> = defs.ec.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.ec.EcQuitCertificate,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.ec.EcQuitCertificate,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 新增任务时选择订单
新增任务时选择订单
        * /quitTask/selectEmp
        */
      export namespace selectEmp {
        export class Params {}

        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export type Response<T = any> = {
          code: number;
          data: defs.ec.Page;
          message: string;
        };

        export const init: Response;
        export function request<T = any>(
          data: defs.ec.FilterEntity,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.ec.FilterEntity,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 查看电子离职材料信息
查看电子离职材料信息，传quitTaskId
        * /quitTask/selectMaterial
        */
      export namespace selectMaterial {
        export class Params {}

        export type Response<T> = defs.ec.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.ec.EcQuitMaterial,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.ec.EcQuitMaterial,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 发起电子离职材料
发起电子离职材料
        * /quitTask/sendEleResignMaterial
        */
      export namespace sendEleResignMaterial {
        export class Params {}

        export type Response<T> = defs.ec.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: Array<defs.ec.EcQuitAll>,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: Array<defs.ec.EcQuitAll>,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 电子离职证明盖章
电子离职证明盖章
        * /quitTask/stampEleResignCertificate
        */
      export namespace stampEleResignCertificate {
        export class Params {}

        export type Response<T> = defs.ec.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.ec.EcQuitAll,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.ec.EcQuitAll,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 电子离职材料盖章
电子离职材料盖章
        * /quitTask/stampEleResignMaterial
        */
      export namespace stampEleResignMaterial {
        export class Params {}

        export type Response<T> = defs.ec.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.ec.EcQuitAll,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.ec.EcQuitAll,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 开具离职证明-保存
开具离职证明-保存
        * /quitTask/submitQuitCergificate
        */
      export namespace submitQuitCergificate {
        export class Params {}

        export type Response<T> = defs.ec.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.ec.EcTaskCergificateDto,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.ec.EcTaskCergificateDto,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 提交按钮
提交按钮
        * /quitTask/submitQuitTask
        */
      export namespace submitQuitTask {
        export class Params {}

        export type Response<T> = defs.ec.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.ec.EcQuitAll,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.ec.EcQuitAll,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 修改离职任务并提交
修改离职任务并提交
        * /quitTask/updateAndSumitQuitTask
        */
      export namespace updateAndSumitQuitTask {
        export class Params {}

        export type Response<T> = defs.ec.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.ec.BaseEntity,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.ec.BaseEntity,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 退回电子离职材料
退回电子离职材料
        * /quitTask/updateEleResignMaterial
        */
      export namespace updateEleResignMaterial {
        export class Params {}

        export type Response<T> = defs.ec.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.ec.EcQuitAll,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.ec.EcQuitAll,
          options?: APIRequestConfig,
        ): Promise<T>;
      }

      /**
        * 修改离职任务
修改离职任务
        * /quitTask/updateQuitTask
        */
      export namespace updateQuitTask {
        export class Params {}

        export type Response<T> = defs.ec.CommonResponse<T>;

        export const init: Response<object>;
        export const url: string;
        export const initialUrl: string;
        export const cacheKey: string;
        export function request<T = any>(
          data: defs.ec.BaseEntity,
          options?: APIRequestConfig,
        ): Promise<Response<T>>;
        export function requests<T = any>(
          data: defs.ec.BaseEntity,
          options?: APIRequestConfig,
        ): Promise<T>;
      }
    }
  }
}
